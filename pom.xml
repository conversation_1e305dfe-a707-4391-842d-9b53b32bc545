<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bidv.ibank</groupId>
        <artifactId>ibank-framework-dependencies</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.bidv.ibank.microservice</groupId>
    <artifactId>dvc-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>DVC Microservice</name>
    <description></description>

    <properties>
        <properties-maven-plugin.version>1.2.1</properties-maven-plugin.version>
        <sonar-maven-plugin.version>3.11.0.3922</sonar-maven-plugin.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-external-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-test-starter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-utility-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-integrate</artifactId>
            <version>3.3.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-cache-remote</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank.txn</groupId>
            <artifactId>txn-common</artifactId>
            <version>0.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-utility</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-remote-config-database</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bidv.ibank</groupId>
            <artifactId>ibank-quartz-starter</artifactId>
        </dependency>
        
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <release>${java.version}</release>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
                <version>${properties-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>initialize</phase>
                        <goals>
                            <goal>read-project-properties</goal>
                        </goals>
                        <configuration>
                            <files>
                                <file>sonar-project.properties</file>
                            </files>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar-maven-plugin.version}</version>
                <configuration>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>bidv_repo</id>
            <url>http://10.53.121.87:8085/repository/bidv-maven-group</url>
        </repository>
    </repositories>
</project>