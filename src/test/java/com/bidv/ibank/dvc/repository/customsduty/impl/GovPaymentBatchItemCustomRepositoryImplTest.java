package com.bidv.ibank.dvc.repository.customsduty.impl;

import com.bidv.ibank.common.txn.util.constant.FeeMethodEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GovPaymentBatchItemCustomRepositoryImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private Query query;

    private GovPaymentBatchItemCustomRepositoryImpl repository;

    @BeforeEach
    void setUp() {
        repository = new GovPaymentBatchItemCustomRepositoryImpl(entityManager);
    }

    @Test
    void testUpdate_WithValidBatchItemList_ShouldUpdateAllItems() {
        // Given
        GOVPaymentBatchItemEntity item1 = createBatchItemEntity("id1");
        GOVPaymentBatchItemEntity item2 = createBatchItemEntity("id2");
        List<GOVPaymentBatchItemEntity> batchItemEntityList = Arrays.asList(item1, item2);

        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // When
        repository.update(batchItemEntityList);

        // Then
        verify(entityManager, times(2)).createQuery(anyString());
        verify(query, times(2)).executeUpdate();
        
        // The repository reuses the same params map, so parameters are set for both items
        // but we can't predict the exact number of times each parameter is called
        verify(query, atLeast(1)).setParameter("columnValue", "id1");
        verify(query, atLeast(1)).setParameter("columnValue", "id2");
    }

    @Test
    void testUpdate_WithEmptyList_ShouldNotExecuteAnyUpdate() {
        // Given
        List<GOVPaymentBatchItemEntity> emptyList = Collections.emptyList();

        // When
        repository.update(emptyList);

        // Then
        verify(entityManager, never()).createQuery(anyString());
        verify(query, never()).executeUpdate();
    }

    @Test
    void testUpdate_WithNullValues_ShouldOnlyUpdateNonNullFields() {
        // Given
        GOVPaymentBatchItemEntity item = new GOVPaymentBatchItemEntity();
        item.setId("test-id");
        item.setFeeAccNo("ACC123");
        item.setFeeCcy("USD");
        // Leave other fields as null
        
        List<GOVPaymentBatchItemEntity> batchItemEntityList = Collections.singletonList(item);

        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // When
        repository.update(batchItemEntityList);

        // Then
        ArgumentCaptor<String> queryCaptor = ArgumentCaptor.forClass(String.class);
        verify(entityManager).createQuery(queryCaptor.capture());
        
        String capturedQuery = queryCaptor.getValue();
        // Should contain updates for non-null fields
        assertTrue(capturedQuery.contains("feeAccNo"));
        assertTrue(capturedQuery.contains("feeCcy"));
        
        verify(query).setParameter("columnValue", "test-id");
        verify(query).setParameter("feeAccNo", "ACC123");
        verify(query).setParameter("feeCcy", "USD");
        verify(query).executeUpdate();
    }

    @Test
    void testUpdate_WithAllFieldsPopulated_ShouldUpdateAllFields() {
        // Given
        GOVPaymentBatchItemEntity item = createFullyPopulatedBatchItemEntity();
        List<GOVPaymentBatchItemEntity> batchItemEntityList = Collections.singletonList(item);

        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // When
        repository.update(batchItemEntityList);

        // Then
        ArgumentCaptor<String> queryCaptor = ArgumentCaptor.forClass(String.class);
        verify(entityManager).createQuery(queryCaptor.capture());
        
        String capturedQuery = queryCaptor.getValue();
        
        // Verify all field mappings are present in the query
        assertTrue(capturedQuery.contains("feeAccNo"));
        assertTrue(capturedQuery.contains("feeCcy"));
        assertTrue(capturedQuery.contains("feeOpt"));
        assertTrue(capturedQuery.contains("feeFreq"));
        assertTrue(capturedQuery.contains("feeMethod"));
        assertTrue(capturedQuery.contains("feeBrcd"));
        assertTrue(capturedQuery.contains("feeTotal"));
        assertTrue(capturedQuery.contains("feeCode"));
        assertTrue(capturedQuery.contains("feeAmount"));
        assertTrue(capturedQuery.contains("feeVAT"));
        assertTrue(capturedQuery.contains("vatRate"));
        assertTrue(capturedQuery.contains("feeOriginal"));

        // Verify all parameters are set - note that feeMethod uses enum value
        verify(query).setParameter("columnValue", "test-id");
        verify(query).setParameter("feeAccNo", "ACC123");
        verify(query).setParameter("feeCcy", "USD");
        verify(query).setParameter("feeOpt", "OPT1");
        verify(query).setParameter("feeFreq", "FREQ1");
        verify(query).setParameter("feeMethod", FeeMethodEnum.O); // Actual enum value, not string
        verify(query).setParameter("feeBrcd", "BRCD1");
        verify(query).setParameter("feeTotal", new BigDecimal("1000.00"));
        verify(query).setParameter("feeCode", "FEE001");
        verify(query).setParameter("feeAmount", new BigDecimal("800.00"));
        verify(query).setParameter("feeVAT", new BigDecimal("200.00"));
        verify(query).setParameter("vatRate", new BigDecimal("0.25"));
        verify(query).setParameter("feeOriginal", new BigDecimal("950.00"));
        
        verify(query).executeUpdate();
    }

    @Test
    void testUpdate_WithOnlyMandatoryFields_ShouldUpdateOnlyThoseFields() {
        // Given
        GOVPaymentBatchItemEntity item = new GOVPaymentBatchItemEntity();
        item.setId("mandatory-id");
        item.setFeeAmount(new BigDecimal("500.00"));
        
        List<GOVPaymentBatchItemEntity> batchItemEntityList = Collections.singletonList(item);

        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // When
        repository.update(batchItemEntityList);

        // Then
        verify(query).setParameter("columnValue", "mandatory-id");
        verify(query).setParameter("feeAmount", new BigDecimal("500.00"));
        verify(query).executeUpdate();
    }

    @Test
    void testUpdate_WithMultipleItemsHavingDifferentFields_ShouldUpdateEachCorrectly() {
        // Given
        GOVPaymentBatchItemEntity item1 = new GOVPaymentBatchItemEntity();
        item1.setId("id1");
        item1.setFeeAccNo("ACC1");
        item1.setFeeAmount(new BigDecimal("100.00"));

        GOVPaymentBatchItemEntity item2 = new GOVPaymentBatchItemEntity();
        item2.setId("id2");
        item2.setFeeCcy("EUR");
        item2.setFeeTotal(new BigDecimal("200.00"));

        List<GOVPaymentBatchItemEntity> batchItemEntityList = Arrays.asList(item1, item2);

        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // When
        repository.update(batchItemEntityList);

        // Then
        verify(entityManager, times(2)).createQuery(anyString());
        verify(query, times(2)).executeUpdate();
        
        // Verify each item's parameters were set - note the repository reuses the params map
        // so we need to account for the fact that parameters may be called multiple times
        verify(query, atLeast(1)).setParameter("columnValue", "id1");
        verify(query, atLeast(1)).setParameter("columnValue", "id2");
        verify(query, atLeast(1)).setParameter("feeAmount", new BigDecimal("100.00"));
        verify(query, atLeast(1)).setParameter("feeCcy", "EUR");
        verify(query, atLeast(1)).setParameter("feeTotal", new BigDecimal("200.00"));
    }

    private GOVPaymentBatchItemEntity createBatchItemEntity(String id) {
        GOVPaymentBatchItemEntity entity = new GOVPaymentBatchItemEntity();
        entity.setId(id);
        entity.setFeeAccNo("ACC123");
        entity.setFeeCcy("USD");
        entity.setFeeAmount(new BigDecimal("500.00"));
        return entity;
    }

    private GOVPaymentBatchItemEntity createFullyPopulatedBatchItemEntity() {
        GOVPaymentBatchItemEntity entity = new GOVPaymentBatchItemEntity();
        entity.setId("test-id");
        entity.setFeeAccNo("ACC123");
        entity.setFeeCcy("USD");
        entity.setFeeOpt("OPT1");
        entity.setFeeFreq("FREQ1");
        entity.setFeeMethod(FeeMethodEnum.O);
        entity.setFeeBrcd("BRCD1");
        entity.setFeeTotal(new BigDecimal("1000.00"));
        entity.setFeeCode("FEE001");
        entity.setFeeAmount(new BigDecimal("800.00"));
        entity.setFeeVAT(new BigDecimal("200.00"));
        entity.setVatRate(new BigDecimal("0.25"));
        entity.setFeeOriginal(new BigDecimal("950.00"));
        return entity;
    }
}
