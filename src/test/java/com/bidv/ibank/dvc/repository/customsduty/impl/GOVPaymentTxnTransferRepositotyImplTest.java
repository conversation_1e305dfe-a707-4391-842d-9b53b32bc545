package com.bidv.ibank.dvc.repository.customsduty.impl;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.dto.TxnExportReportDto;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.framework.domain.response.Result;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GOVPaymentTxnTransferRepositotyImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private TypedQuery<TxnExportReportDto> typedQuery;

    private GOVPaymentTxnTransferRepositotyImpl repository;

    @BeforeEach
    void setUp() {
        repository = new GOVPaymentTxnTransferRepositotyImpl(entityManager);
    }

    @Test
    void testFindTxnSummary_WithValidRequest_ShouldReturnSuccessResult() {
        // Given
        TxnExportReq req = createBasicTxnExportReq();
        Long cusId = 12345L;
        List<String> debitAccNos = Arrays.asList("ACC001", "ACC002");
        List<String> notInStatuses = Arrays.asList(TransactionStatusEnum.DELETED.name());
        
        List<TxnExportReportDto> mockResults = Collections.singletonList(createMockTxnExportReportDto());
        
        when(entityManager.createQuery(anyString(), eq(TxnExportReportDto.class))).thenReturn(typedQuery);
        when(typedQuery.setParameter(anyString(), any())).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(mockResults);

        // When
        Result<List<TxnExportReportDto>> result = repository.findTxnSummary(req, cusId, debitAccNos, notInStatuses);

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        
        verify(entityManager).createQuery(anyString(), eq(TxnExportReportDto.class));
        verify(typedQuery).setParameter("cusId", cusId);
        verify(typedQuery).setParameter("debitAccNos", debitAccNos);
        verify(typedQuery).setParameter("notInStatuses", notInStatuses);
        verify(typedQuery).getResultList();
    }

    @Test
    void testFindTxnSummary_WithSearchParameter_ShouldSetSearchParameter() {
        // Given
        TxnExportReq req = createTxnExportReqWithSearch("TXN123");
        Long cusId = 12345L;
        List<String> debitAccNos = Arrays.asList("ACC001");
        List<String> notInStatuses = Arrays.asList(TransactionStatusEnum.DELETED.name());
        
        when(entityManager.createQuery(anyString(), eq(TxnExportReportDto.class))).thenReturn(typedQuery);
        when(typedQuery.setParameter(anyString(), any())).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(Collections.emptyList());

        // When
        repository.findTxnSummary(req, cusId, debitAccNos, notInStatuses);

        // Then
        verify(typedQuery).setParameter(eq("search"), contains("txn123"));
    }

    @Test
    void testFindTxnSummary_WithDateRange_ShouldSetDateParameters() {
        // Given
        TxnExportReq req = createTxnExportReqWithDateRange();
        Long cusId = 12345L;
        List<String> debitAccNos = Arrays.asList("ACC001");
        List<String> notInStatuses = Arrays.asList(TransactionStatusEnum.DELETED.name());
        
        when(entityManager.createQuery(anyString(), eq(TxnExportReportDto.class))).thenReturn(typedQuery);
        when(typedQuery.setParameter(anyString(), any())).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(Collections.emptyList());

        // When
        repository.findTxnSummary(req, cusId, debitAccNos, notInStatuses);

        // Then
        verify(typedQuery).setParameter(eq("startDate"), any());
        verify(typedQuery).setParameter(eq("endDate"), any());
    }

    @Test
    void testFindTxnSummary_WithAmountRange_ShouldSetAmountParameters() {
        // Given
        TxnExportReq req = createTxnExportReqWithAmountRange();
        Long cusId = 12345L;
        List<String> debitAccNos = Arrays.asList("ACC001");
        List<String> notInStatuses = Arrays.asList(TransactionStatusEnum.DELETED.name());
        
        when(entityManager.createQuery(anyString(), eq(TxnExportReportDto.class))).thenReturn(typedQuery);
        when(typedQuery.setParameter(anyString(), any())).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(Collections.emptyList());

        // When
        repository.findTxnSummary(req, cusId, debitAccNos, notInStatuses);

        // Then
        verify(typedQuery).setParameter("minAmount", new BigDecimal("100"));
        verify(typedQuery).setParameter("maxAmount", new BigDecimal("1000"));
    }

    @Test
    void testFindTxnSummary_WithCurrenciesAndStatuses_ShouldSetListParameters() {
        // Given
        TxnExportReq req = createTxnExportReqWithLists();
        Long cusId = 12345L;
        List<String> debitAccNos = Arrays.asList("ACC001");
        List<String> notInStatuses = Arrays.asList(TransactionStatusEnum.DELETED.name());
        
        when(entityManager.createQuery(anyString(), eq(TxnExportReportDto.class))).thenReturn(typedQuery);
        when(typedQuery.setParameter(anyString(), any())).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(Collections.emptyList());

        // When
        repository.findTxnSummary(req, cusId, debitAccNos, notInStatuses);

        // Then
        verify(typedQuery).setParameter("ccys", Arrays.asList("USD", "EUR"));
        verify(typedQuery).setParameter("statuses", Arrays.asList(TransactionStatusEnum.SUCCESS.name()));
        verify(typedQuery).setParameter("txnTypes", Arrays.asList("TYPE1", "TYPE2"));
        verify(typedQuery).setParameter("channels", Arrays.asList("WEB", "MOBILE"));
    }

    @Test
    void testFindTxnSummary_WithStringFilters_ShouldSetLikeParameters() {
        // Given
        TxnExportReq req = createTxnExportReqWithStringFilters();
        Long cusId = 12345L;
        List<String> debitAccNos = Arrays.asList("ACC001");
        List<String> notInStatuses = Arrays.asList(TransactionStatusEnum.DELETED.name());
        
        when(entityManager.createQuery(anyString(), eq(TxnExportReportDto.class))).thenReturn(typedQuery);
        when(typedQuery.setParameter(anyString(), any())).thenReturn(typedQuery);
        when(typedQuery.getResultList()).thenReturn(Collections.emptyList());

        // When
        repository.findTxnSummary(req, cusId, debitAccNos, notInStatuses);

        // Then
        verify(typedQuery).setParameter("debitAccNo", "%ACC123%");
        verify(typedQuery).setParameter("taxCode", "%TAX123%");
        verify(typedQuery).setParameter("declarationNo", "%DECL123%");
        verify(typedQuery).setParameter("batchNo", "%BATCH123%");
        verify(typedQuery).setParameter("tccRefNo", "%REF123%");
    }

    @Test
    void testRenderSqlExport_WithBasicRequest_ShouldGenerateCorrectSql() {
        // Given
        TxnExportReq req = createBasicTxnExportReq();

        // When
        StringBuilder sql = repository.renderSqlExport(req);

        // Then
        String sqlString = sql.toString();
        assertTrue(sqlString.contains("FROM GOVPaymentTransactionEntity e"));
        assertTrue(sqlString.contains("LEFT JOIN e.govPaymentItemList item"));
        assertTrue(sqlString.contains("WHERE e.cusId = :cusId"));
        assertTrue(sqlString.contains("AND e.status NOT IN (:notInStatuses)"));
        assertTrue(sqlString.contains("AND e.debitAccNo IN :debitAccNos"));
    }

    @Test
    void testRenderSqlExport_WithSearchFilter_ShouldIncludeSearchCondition() {
        // Given
        TxnExportReq req = createTxnExportReqWithSearch("test");

        // When
        StringBuilder sql = repository.renderSqlExport(req);

        // Then
        String sqlString = sql.toString();
        assertTrue(sqlString.contains("AND ("));
        assertTrue(sqlString.contains("lower(e.id) LIKE :search"));
        assertTrue(sqlString.contains("OR TO_CHAR(e.amount) LIKE :search"));
        assertTrue(sqlString.contains("OR lower(e.debitAccNo) LIKE :search"));
        assertTrue(sqlString.contains("OR lower(e.orgId) LIKE :search"));
    }

    @Test
    void testRenderSqlExport_WithDateFilters_ShouldIncludeDateConditions() {
        // Given
        TxnExportReq req = createTxnExportReqWithDateRange();

        // When
        StringBuilder sql = repository.renderSqlExport(req);

        // Then
        String sqlString = sql.toString();
        assertTrue(sqlString.contains("AND e.createdDate >= :startDate"));
        assertTrue(sqlString.contains("AND e.createdDate <= :endDate"));
    }

    @Test
    void testRenderSqlExport_WithTxnItemId_ShouldAddDisjunctionCondition() {
        // Given
        TxnExportReq req = createBasicTxnExportReq();
        req.setTxnItemId("ITEM123");

        // When
        StringBuilder sql = repository.renderSqlExport(req);

        // Then
        String sqlString = sql.toString();
        assertTrue(sqlString.contains("AND 1 = 0"));
    }

    private TxnExportReq createBasicTxnExportReq() {
        TxnExportReq req = new TxnExportReq();
        return req;
    }

    private TxnExportReq createTxnExportReqWithSearch(String search) {
        TxnExportReq req = new TxnExportReq();
        req.setSearch(search);
        return req;
    }

    private TxnExportReq createTxnExportReqWithDateRange() {
        TxnExportReq req = new TxnExportReq();
        req.setStartDate(LocalDate.of(2025, 1, 1));
        req.setEndDate(LocalDate.of(2025, 12, 31));
        return req;
    }

    private TxnExportReq createTxnExportReqWithAmountRange() {
        TxnExportReq req = new TxnExportReq();
        req.setMinAmount(new BigDecimal("100"));
        req.setMaxAmount(new BigDecimal("1000"));
        return req;
    }

    private TxnExportReq createTxnExportReqWithLists() {
        TxnExportReq req = new TxnExportReq();
        req.setCcys(Arrays.asList("USD", "EUR"));
        req.setStatuses(Arrays.asList(TransactionStatusEnum.SUCCESS.name(), TransactionStatusEnum.DELETED.name()));
        req.setTxnTypes(Arrays.asList("TYPE1", "TYPE2"));
        req.setChannels(Arrays.asList("web", "mobile"));
        return req;
    }

    private TxnExportReq createTxnExportReqWithStringFilters() {
        TxnExportReq req = new TxnExportReq();
        req.setDebitAccNo("ACC123");
        req.setTaxCode("TAX123");
        req.setDeclarationNo("DECL123");
        req.setBatchNo("BATCH123");
        req.setTccRefNo("REF123");
        return req;
    }

    private TxnExportReportDto createMockTxnExportReportDto() {
        return TxnExportReportDto.builder()
            .createdDate("23/07/2025")
            .status("SUCCESS")
            .txnId("TXN123")
            .txnType("PAYMENT")
            .debitAccNo("ACC001")
            .taxCode("TAX123")
            .payerName("Test Payer")
            .declarationNo("DECL123")
            .declarationDate("23/07/2025")
            .treasuryCode("TREASURY001")
            .treasuryName("Treasury Name")
            .benBankCode("BANK001")
            .benBankName("Bank Name")
            .revAccCode("REV001")
            .revAccName("Revenue Account")
            .revAuthCode("AUTH001")
            .revAuthName("Authority Name")
            .admAreaCode("AREA001")
            .admAreaName("Area Name")
            .chapterCode("CHAP001")
            .chapterName("Chapter Name")
            .ecCode("EC001")
            .ecName("EC Name")
            .parAmount("1000.00")
            .itemAmount("1000.00")
            .tccErrDesc(null)
            .tccSibsErrDesc(null)
            .tccTransErrDesc(null)
            .transDesc("Transaction Description")
            .ccy("USD")
            .batchNo("BATCH001")
            .createdBy("admin")
            .approvalUsers("approver1")
            .approvedDate("23/07/2025")
            .feeTotal("10.00")
            .state("SUCCESS")
            .tccIdCore("CORE123")
            .tccRefNo("REF123")
            .channel("WEB")
            .orgId("ORG001")
            .build();
    }
}
