package com.bidv.ibank.dvc.repository.customsduty.impl;

import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.util.constant.TccAccountingEnum;
import com.bidv.ibank.dvc.util.constant.TccQueryTxnStatusEnum;
import com.bidv.ibank.dvc.util.constant.TccTransferEnum;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingRes;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingResult;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingResultSibsInfo;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingResultTransferInfo;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocResult;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoRow;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GovPaymentTransactionCustomRepositoryImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private Query query;

    private GovPaymentTransactionCustomRepositoryImpl repository;

    @BeforeEach
    void setUp() {
        repository = new GovPaymentTransactionCustomRepositoryImpl(entityManager);
        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);
    }

    @Test
    void testUpdateAccountingFailed_ShouldSetFailedStatusAndErrorInfo() {
        // Given
        String txnId = "TXN123";
        String errCode = "ERR001";
        String errDesc = "Accounting failed";

        // When
        repository.updateAccountingFailed(txnId, errCode, errDesc);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.FAILED.name());
        verify(query).setParameter("state", TransactionStateEnum.FAILED.name());
        verify(query).setParameter("tccErrCode", errCode);
        verify(query).setParameter("tccErrDesc", errDesc);
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingUnderBalance_ShouldSetUnderBalanceStatusAndRetryInfo() {
        // Given
        String txnId = "TXN123";

        // When
        repository.updateAccountingUnderBalance(txnId);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.BANK_PROCESSING.name());
        verify(query).setParameter("state", TransactionStateEnum.UNDER_BALANCE.name());
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingUndefined_ShouldSetUndefinedStatus() {
        // Given
        String txnId = "TXN123";

        // When
        repository.updateAccountingUndefined(txnId);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.UNDEFINED.name());
        verify(query).setParameter("state", TransactionStateEnum.UNDEFINED.name());
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingResult_WithSuccessfulAccounting_ShouldSetSuccessStatus() {
        // Given
        String txnId = "TXN123";
        TccCreateDocResult tccCreateDocResult = createMockTccCreateDocResult();
        TccAccountingRes tccAccountingRes = createMockTccAccountingRes(
                TccAccountingEnum.SUCCESS.getValue(),
                TccTransferEnum.SUCCESS.getValue());

        // When
        repository.updateAccountingResult(txnId, tccCreateDocResult, tccAccountingRes);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.SUCCESS.name());
        verify(query).setParameter("state", TransactionStateEnum.SUCCESS.name());
        verify(query).setParameter("tccDocId", "DOC123");
        verify(query).setParameter("tccDocSign", "SIGN123");
        verify(query).setParameter("tccDocNo", "NO123");
        verify(query).setParameter("coreRef", "REF123");
        verify(query).setParameter("tccRmNo", "RM123");
        verify(query).setParameter("tccIdCore", "CORE123");
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingResult_WithFailedAccounting_ShouldSetFailedStatusAndError() {
        // Given
        String txnId = "TXN123";
        TccCreateDocResult tccCreateDocResult = createMockTccCreateDocResult();
        TccAccountingRes tccAccountingRes = createMockTccAccountingRes(
                TccAccountingEnum.FAILED.getValue(),
                TccTransferEnum.SUCCESS.getValue());

        // When
        repository.updateAccountingResult(txnId, tccCreateDocResult, tccAccountingRes);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.FAILED.name());
        verify(query).setParameter("state", TransactionStateEnum.FAILED.name());
        verify(query).setParameter("tccErrCode", "SIBS_ERR");
        verify(query).setParameter("tccErrDesc", "SIBS Error Description");
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingResult_WithUndefinedAccounting_ShouldSetUndefinedStatus() {
        // Given
        String txnId = "TXN123";
        TccCreateDocResult tccCreateDocResult = createMockTccCreateDocResult();
        TccAccountingRes tccAccountingRes = createMockTccAccountingRes(
                TccAccountingEnum.UNDEFINED.getValue(),
                TccTransferEnum.SUCCESS.getValue());

        // When
        repository.updateAccountingResult(txnId, tccCreateDocResult, tccAccountingRes);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.UNDEFINED.name());
        verify(query).setParameter("state", TransactionStateEnum.UNDEFINED.name());
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingResult_WithFailedTransfer_ShouldSetTransferFailedState() {
        // Given
        String txnId = "TXN123";
        TccCreateDocResult tccCreateDocResult = createMockTccCreateDocResult();
        TccAccountingRes tccAccountingRes = createMockTccAccountingRes(
                TccAccountingEnum.SUCCESS.getValue(),
                TccTransferEnum.FAILED.getValue());

        // When
        repository.updateAccountingResult(txnId, tccCreateDocResult, tccAccountingRes);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("state", TransactionStateEnum.FAIL_TRANS_TCC.name());
        verify(query).setParameter("tccErrCode", "TRANS_ERR");
        verify(query).setParameter("tccErrDesc", "Transfer Error Description");
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingResultAfterQuery_WithSuccessCtu_ShouldSetSuccessStatus() {
        // Given
        String txnId = "TXN123";
        TccQueryTxnInfoRow infoRow = createMockTccQueryTxnInfoRow(
                TccQueryTxnStatusEnum.SUCCESS_CTU.getValue(),
                TccQueryTxnStatusEnum.SUCCESS.getValue(),
                TccQueryTxnStatusEnum.SUCCESS.getValue());

        // When
        repository.updateTxnStatusUndefined(txnId, infoRow);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.SUCCESS.name());
        verify(query).setParameter("state", TransactionStateEnum.SUCCESS.name());
        verify(query).setParameter("tccDocId", "ID123");
        verify(query).setParameter("tccDocSign", "SIGN123");
        verify(query).setParameter("tccDocNo", "NO123");
        verify(query).setParameter("coreRef", "REF123");
        verify(query).setParameter("tccRmNo", "RM123");
        verify(query).setParameter("tccIdCore", "CORE123");
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingResultAfterQuery_WithUndefinedCtu_ShouldSetUndefinedStatus() {
        // Given
        String txnId = "TXN123";
        TccQueryTxnInfoRow infoRow = createMockTccQueryTxnInfoRow(
                TccQueryTxnStatusEnum.UNDEFINED_CTU.getValue(),
                TccQueryTxnStatusEnum.SUCCESS.getValue(),
                TccQueryTxnStatusEnum.SUCCESS.getValue());

        // When
        repository.updateTxnStatusUndefined(txnId, infoRow);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("status", TransactionStatusEnum.UNDEFINED.name());
        verify(query).setParameter("state", TransactionStateEnum.UNDEFINED.name());
        verify(query).setParameter("tccErrCode", "CTU_ERR");
        verify(query).setParameter("tccErrDesc", "CTU Error");
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateAccountingResultAfterQuery_WithFailedTransfer_ShouldSetTransferFailedState() {
        // Given
        String txnId = "TXN123";
        TccQueryTxnInfoRow infoRow = createMockTccQueryTxnInfoRow(
                TccQueryTxnStatusEnum.SUCCESS_CTU.getValue(),
                TccQueryTxnStatusEnum.SUCCESS.getValue(),
                "10" // Not success (11)
        );

        // When
        repository.updateTxnStatusUndefined(txnId, infoRow);

        // Then
        verify(query).setParameter("columnValue", txnId);
        verify(query).setParameter("state", TransactionStateEnum.FAIL_TRANS_TCC.name());
        verify(query).setParameter("tccErrCode", "HQA_ERR");
        verify(query).setParameter("tccErrDesc", "HQA Error");
        verify(query).executeUpdate();
    }

    private TccCreateDocResult createMockTccCreateDocResult() {
        TccCreateDocResult result = new TccCreateDocResult();
        result.setIdCtuHdr("DOC123");
        result.setKyhieuCt("SIGN123");
        result.setSoCt("NO123");
        return result;
    }

    private TccAccountingRes createMockTccAccountingRes(String sibsStatus, String transferStatus) {
        TccAccountingRes accountingRes = new TccAccountingRes();
        TccAccountingResult result = new TccAccountingResult();

        TccAccountingResultSibsInfo sibsInfo = new TccAccountingResultSibsInfo();
        sibsInfo.setStatus(sibsStatus);
        sibsInfo.setRefNo("REF123");
        sibsInfo.setRmNo("RM123");
        sibsInfo.setIdCore("CORE123");
        sibsInfo.setErrCode("SIBS_ERR");
        sibsInfo.setErrDesc("SIBS Error Description");

        TccAccountingResultTransferInfo transferInfo = new TccAccountingResultTransferInfo();
        transferInfo.setStatus(transferStatus);
        transferInfo.setErrCode("TRANS_ERR");
        transferInfo.setErrDesc("Transfer Error Description");

        result.setSibsInfo(sibsInfo);
        result.setTransferInfo(transferInfo);
        accountingRes.setResult(result);

        return accountingRes;
    }

    private TccQueryTxnInfoRow createMockTccQueryTxnInfoRow(String statusCtu, String statusCore, String ttHqa) {
        TccQueryTxnInfoRow infoRow = new TccQueryTxnInfoRow();
        infoRow.setStatusCtu(statusCtu);
        infoRow.setStatusCore(statusCore);
        infoRow.setTtHqa(ttHqa);
        infoRow.setTgKy("10:30:45 23/07/2025");
        infoRow.setIdCtHdr("ID123");
        infoRow.setKyHieuCt("SIGN123");
        infoRow.setSoCtu("NO123");
        infoRow.setRefcore("REF123");
        infoRow.setRmNo("RM123");
        infoRow.setIdCore("CORE123");
        infoRow.setErrorcodeCtu("CTU_ERR");
        infoRow.setErrordescCtu("CTU Error");
        infoRow.setErrcodeCore("CORE_ERR");
        infoRow.setErrdescCore("Core Error");
        infoRow.setErrcodeHqa("HQA_ERR");
        infoRow.setErrdescHqa("HQA Error");
        return infoRow;
    }
}
