package com.bidv.ibank.dvc.repository;

import com.bidv.ibank.dvc.util.constant.FieldEnum;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BaseRepositoryTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private Query query;

    private TestableBaseRepository baseRepository;

    // Test implementation of BaseRepository
    private static class TestableBaseRepository extends BaseRepository {
        public TestableBaseRepository(EntityManager entityManager) {
            super(entityManager);
        }

        // Expose protected methods for testing
        public <T extends FieldEnum, S> void testUpdateByColumn(String columnName, String columnValue,
                Map<T, Object> params, Class<S> entityClass) {
            updateByColumn(columnName, columnValue, params, entityClass);
        }

        public <T> void testPutIfNotNull(Map<T, Object> map, T key, Object value) {
            putIfNotNull(map, key, value);
        }
    }

    // Test enum implementation
    private enum TestFieldEnum implements FieldEnum {
        FIELD1("field1"),
        FIELD2("field2"),
        FIELD3("field3");

        private final String fieldCode;

        TestFieldEnum(String fieldCode) {
            this.fieldCode = fieldCode;
        }

        @Override
        public String fieldCode() {
            return fieldCode;
        }
    }

    private static class TestEntity {
        // Test entity for updateByColumn
    }

    @BeforeEach
    void setUp() {
        baseRepository = new TestableBaseRepository(entityManager);
    }

    @Test
    void testUpdateByColumn_WithValidParams_ShouldExecuteUpdate() {
        // Given
        String columnName = "id";
        String columnValue = "test-id";
        Map<TestFieldEnum, Object> params = new HashMap<>();
        params.put(TestFieldEnum.FIELD1, "value1");
        params.put(TestFieldEnum.FIELD2, "value2");

        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // When
        baseRepository.testUpdateByColumn(columnName, columnValue, params, TestEntity.class);

        // Then
        ArgumentCaptor<String> queryCaptor = ArgumentCaptor.forClass(String.class);
        verify(entityManager).createQuery(queryCaptor.capture());

        String capturedQuery = queryCaptor.getValue();
        assertTrue(capturedQuery.contains("UPDATE TestEntity t SET"));
        assertTrue(capturedQuery.contains("t.field1 = :field1"));
        assertTrue(capturedQuery.contains("t.field2 = :field2"));
        assertTrue(capturedQuery.contains("WHERE id = :columnValue"));

        verify(query).setParameter("columnValue", columnValue);
        verify(query).setParameter("field1", "value1");
        verify(query).setParameter("field2", "value2");
        verify(query).executeUpdate();
    }

    @Test
    void testUpdateByColumn_WithBlankColumnValue_ShouldNotExecute() {
        // Given
        String columnName = "id";
        String columnValue = "";
        Map<TestFieldEnum, Object> params = new HashMap<>();
        params.put(TestFieldEnum.FIELD1, "value1");

        // When
        baseRepository.testUpdateByColumn(columnName, columnValue, params, TestEntity.class);

        // Then
        verify(entityManager, never()).createQuery(anyString());
    }

    @Test
    void testUpdateByColumn_WithNullColumnValue_ShouldNotExecute() {
        // Given
        String columnName = "id";
        String columnValue = null;
        Map<TestFieldEnum, Object> params = new HashMap<>();
        params.put(TestFieldEnum.FIELD1, "value1");

        // When
        baseRepository.testUpdateByColumn(columnName, columnValue, params, TestEntity.class);

        // Then
        verify(entityManager, never()).createQuery(anyString());
    }

    @Test
    void testUpdateByColumn_WithEmptyParams_ShouldNotExecute() {
        // Given
        String columnName = "id";
        String columnValue = "test-id";
        Map<TestFieldEnum, Object> params = new HashMap<>();

        // When
        baseRepository.testUpdateByColumn(columnName, columnValue, params, TestEntity.class);

        // Then
        verify(entityManager, never()).createQuery(anyString());
    }

    @Test
    void testUpdateByColumn_WithNullParams_ShouldNotExecute() {
        // Given
        String columnName = "id";
        String columnValue = "test-id";

        // When
        baseRepository.testUpdateByColumn(columnName, columnValue, null, TestEntity.class);

        // Then
        verify(entityManager, never()).createQuery(anyString());
    }

    @Test
    void testUpdateByColumn_WithSingleParam_ShouldGenerateCorrectQuery() {
        // Given
        String columnName = "customColumn";
        String columnValue = "customValue";
        Map<TestFieldEnum, Object> params = new HashMap<>();
        params.put(TestFieldEnum.FIELD1, "singleValue");

        when(entityManager.createQuery(anyString())).thenReturn(query);
        when(query.setParameter(anyString(), any())).thenReturn(query);

        // When
        baseRepository.testUpdateByColumn(columnName, columnValue, params, TestEntity.class);

        // Then
        ArgumentCaptor<String> queryCaptor = ArgumentCaptor.forClass(String.class);
        verify(entityManager).createQuery(queryCaptor.capture());

        String capturedQuery = queryCaptor.getValue();
        assertTrue(capturedQuery.contains("UPDATE TestEntity t SET t.field1 = :field1 WHERE customColumn = :columnValue"));
        assertFalse(capturedQuery.contains(", "));
    }

    @Test
    void testPutIfNotNull_WithNotNullValue_ShouldPutValue() {
        // Given
        Map<String, Object> map = new HashMap<>();
        String key = "testKey";
        String value = "testValue";

        // When
        baseRepository.testPutIfNotNull(map, key, value);

        // Then
        assertEquals(1, map.size());
        assertEquals(value, map.get(key));
    }

    @Test
    void testPutIfNotNull_WithNullValue_ShouldNotPutValue() {
        // Given
        Map<String, Object> map = new HashMap<>();
        String key = "testKey";

        // When
        baseRepository.testPutIfNotNull(map, key, null);

        // Then
        assertTrue(map.isEmpty());
        assertNull(map.get(key));
    }

    @Test
    void testPutIfNotNull_WithExistingKey_ShouldOverrideValue() {
        // Given
        Map<String, Object> map = new HashMap<>();
        String key = "testKey";
        String oldValue = "oldValue";
        String newValue = "newValue";
        map.put(key, oldValue);

        // When
        baseRepository.testPutIfNotNull(map, key, newValue);

        // Then
        assertEquals(1, map.size());
        assertEquals(newValue, map.get(key));
    }

    @Test
    void testPutIfNotNull_WithZeroValue_ShouldPutValue() {
        // Given
        Map<String, Object> map = new HashMap<>();
        String key = "testKey";
        Integer value = 0;

        // When
        baseRepository.testPutIfNotNull(map, key, value);

        // Then
        assertEquals(1, map.size());
        assertEquals(value, map.get(key));
    }

    @Test
    void testPutIfNotNull_WithEmptyString_ShouldPutValue() {
        // Given
        Map<String, Object> map = new HashMap<>();
        String key = "testKey";
        String value = "";

        // When
        baseRepository.testPutIfNotNull(map, key, value);

        // Then
        assertEquals(1, map.size());
        assertEquals(value, map.get(key));
    }
}
