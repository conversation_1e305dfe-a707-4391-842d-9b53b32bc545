package com.bidv.ibank.dvc.annotation.validator;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import com.bidv.ibank.dvc.model.dto.AmountDto;
import jakarta.validation.ConstraintValidatorContext;

class AmountValidatorTest {

    private AmountValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @BeforeEach
    void setUp() {
        validator = new AmountValidator();
    }

    @Test
    void whenAmountIsBlank_thenValid() {
        AmountDto dto = createAmountDto("", "VND");
        assertThat(validator.isValid(dto, context)).isTrue();
    }

    @Test
    void whenCcyIsBlank_thenInvalid() {
        AmountDto dto = createAmountDto("1000", "");
        assertThat(validator.isValid(dto, context)).isFalse();
    }

    @Test
    void whenCcyIsInvalid_thenInvalid() {
        AmountDto dto = createAmountDto("1000", "USD");
        assertThat(validator.isValid(dto, context)).isFalse();
    }

    @Test
    void whenAmountIsNegative_thenInvalid() {
        AmountDto dto = createAmountDto("-1000", "VND");
        assertThat(validator.isValid(dto, context)).isFalse();
    }

    @Test
    void whenVNDAmountExceeds16Digits_thenInvalid() {
        AmountDto dto = createAmountDto("10000000000000000", "VND"); // 17 digits
        assertThat(validator.isValid(dto, context)).isFalse();
    }

    @Test
    void whenVNDAmountIs16Digits_thenValid() {
        AmountDto dto = createAmountDto("100000000000000", "VND"); // 15 digits
        assertThat(validator.isValid(dto, context)).isTrue();
    }

    @Test
    void whenAmountIsNotNumeric_thenInvalid() {
        AmountDto dto = createAmountDto("abc", "VND");
        assertThat(validator.isValid(dto, context)).isFalse();
    }

    @Test
    void whenValidVNDAmount_thenValid() {
        AmountDto dto = createAmountDto("1000", "VND");
        assertThat(validator.isValid(dto, context)).isTrue();
    }

    private AmountDto createAmountDto(String amount, String ccy) {
        return new AmountDto() {
            @Override
            public String getCcy() {
                return ccy;
            }

            @Override
            public String getAmount() {
                return amount;
            }

            @Override
            public void setCcy(String ccy) {
            }

            @Override
            public void setAmount(String amount) {
            }
        };
    }
}