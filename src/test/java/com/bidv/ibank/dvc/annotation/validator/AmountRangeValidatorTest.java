package com.bidv.ibank.dvc.annotation.validator;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import com.bidv.ibank.dvc.model.filter.AmountRangeFilter;

import jakarta.validation.ConstraintValidatorContext;

class AmountRangeValidatorTest {

    private AmountRangeValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @BeforeEach
    void setUp() {
        validator = new AmountRangeValidator();
    }

    @Test
    void whenBothAmountsAreNull_thenValid() {
        AmountRangeFilter filter = createAmountRangeFilter(null, null);
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenMinAmountExceedsMaxAmount_thenInvalid() {
        AmountRangeFilter filter = createAmountRangeFilter(BigDecimal.TEN, BigDecimal.ONE);
        assertThat(validator.isValid(filter, context)).isFalse();
    }

    @Test
    void whenAmountsAreEqual_thenValid() {
        AmountRangeFilter filter = createAmountRangeFilter(BigDecimal.ONE, BigDecimal.ONE);
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenValidAmountRange_thenValid() {
        AmountRangeFilter filter = createAmountRangeFilter(BigDecimal.ONE, BigDecimal.TEN);
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    private AmountRangeFilter createAmountRangeFilter(BigDecimal minAmount, BigDecimal maxAmount) {
        return new AmountRangeFilter() {
            @Override
            public BigDecimal getMinAmount() {
                return minAmount;
            }

            @Override
            public BigDecimal getMaxAmount() {
                return maxAmount;
            }

            @Override
            public List<String> getCcys() {
                return List.of("VND");
            }
        };
    }
}