package com.bidv.ibank.dvc.annotation.validator;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.annotation.ValidStatuses;
import com.bidv.ibank.dvc.model.filter.StatusesFilter;

import jakarta.validation.ConstraintValidatorContext;

class StatusesValidatorTest {

    private StatusesValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ValidStatuses validStatuses;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        validator = new StatusesValidator();
    }

    @Test
    void whenStatusesIsNull_thenValid() {
        validator.initialize(validStatuses);
        StatusesFilter filter = createStatusesFilter(null);
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenNoValidStatusesSpecified_thenAllStatusesAreValid() {
        when(validStatuses.statuses()).thenReturn(new TransactionStatusEnum[] {});
        validator.initialize(validStatuses);

        StatusesFilter filter = createStatusesFilter(Arrays.asList(
                TransactionStatusEnum.INIT.name(),
                TransactionStatusEnum.APPROVED.name()));
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenValidStatusesSpecified_thenOnlyThoseAreValid() {
        when(validStatuses.statuses()).thenReturn(new TransactionStatusEnum[] {
                TransactionStatusEnum.INIT,
                TransactionStatusEnum.APPROVED
        });
        validator.initialize(validStatuses);

        StatusesFilter filter = createStatusesFilter(Arrays.asList(
                TransactionStatusEnum.INIT.name(),
                TransactionStatusEnum.APPROVED.name()));
        assertThat(validator.isValid(filter, context)).isTrue();

        filter = createStatusesFilter(Arrays.asList(
                TransactionStatusEnum.INIT.name(),
                TransactionStatusEnum.REJECTED.name()));
        assertThat(validator.isValid(filter, context)).isFalse();
    }

    @Test
    void whenStatusesAreCaseInsensitive_thenValid() {
        when(validStatuses.statuses()).thenReturn(new TransactionStatusEnum[] {
                TransactionStatusEnum.INIT,
                TransactionStatusEnum.APPROVED
        });
        validator.initialize(validStatuses);

        StatusesFilter filter = createStatusesFilter(Arrays.asList(
                "init",
                "APPROVED",
                "Approved"));
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenInvalidStatusProvided_thenInvalid() {
        when(validStatuses.statuses()).thenReturn(new TransactionStatusEnum[] {
                TransactionStatusEnum.INIT,
                TransactionStatusEnum.APPROVED
        });
        validator.initialize(validStatuses);

        StatusesFilter filter = createStatusesFilter(Arrays.asList(
                "INVALID_STATUS",
                TransactionStatusEnum.INIT.name()));
        assertThat(validator.isValid(filter, context)).isFalse();
    }

    private StatusesFilter createStatusesFilter(List<String> statuses) {
        return new StatusesFilter() {
            @Override
            public List<String> getStatuses() {
                return statuses;
            }
        };
    }
}