package com.bidv.ibank.dvc.annotation.validator;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import com.bidv.ibank.dvc.model.dto.TxnGeneralInfoDto;

import jakarta.validation.ConstraintValidatorContext;

class GeneralInfoValidatorTest {

    private GeneralInfoValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @BeforeEach
    void setUp() {
        validator = new GeneralInfoValidator();
    }

    @Test
    void whenAltTaxCodeIsBlank_thenValid() {
        TxnGeneralInfoDto dto = createGeneralInfoDto("", "Test Name", "Test Address");
        assertThat(validator.isValid(dto, context)).isTrue();
        assertThat(dto.getAltPayerName()).isNull();
        assertThat(dto.getAltPayerAddr()).isNull();
    }

    @Test
    void whenAltTaxCodeIsSetButNameOrAddressIsBlank_thenInvalid() {
        TxnGeneralInfoDto dto = createGeneralInfoDto("123456", "", "Test Address");
        assertThat(validator.isValid(dto, context)).isFalse();

        dto = createGeneralInfoDto("123456", "Test Name", "");
        assertThat(validator.isValid(dto, context)).isFalse();

        dto = createGeneralInfoDto("123456", "", "");
        assertThat(validator.isValid(dto, context)).isFalse();
    }

    @Test
    void whenAllAlternativeFieldsAreSet_thenValid() {
        TxnGeneralInfoDto dto = createGeneralInfoDto("123456", "Test Name", "Test Address");
        assertThat(validator.isValid(dto, context)).isTrue();
    }

    @Test
    void whenAllFieldsAreBlank_thenValid() {
        TxnGeneralInfoDto dto = createGeneralInfoDto("", "", "");
        assertThat(validator.isValid(dto, context)).isTrue();
        assertThat(dto.getAltPayerName()).isNull();
        assertThat(dto.getAltPayerAddr()).isNull();
    }

    private TxnGeneralInfoDto createGeneralInfoDto(String altTaxCode, String altPayerName, String altPayerAddr) {
        return new TxnGeneralInfoDto() {
            private String altTaxCodeValue = altTaxCode;
            private String altPayerNameValue = altPayerName;
            private String altPayerAddrValue = altPayerAddr;

            @Override
            public String getAltTaxCode() {
                return altTaxCodeValue;
            }

            @Override
            public String getAltPayerName() {
                return altPayerNameValue;
            }

            @Override
            public String getAltPayerAddr() {
                return altPayerAddrValue;
            }

            @Override
            public void setAltPayerName(String value) {
                this.altPayerNameValue = value;
            }

            @Override
            public void setAltPayerAddr(String value) {
                this.altPayerAddrValue = value;
            }

            @Override
            public String getTaxCode() {
                return "0123456789";
            }

            @Override
            public String getPayerName() {
                return "Test Payer";
            }

            @Override
            public String getPayerAddr() {
                return "Test Address";
            }
        };
    }
}