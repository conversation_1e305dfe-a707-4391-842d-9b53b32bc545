package com.bidv.ibank.dvc.annotation.validator;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDate;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import com.bidv.ibank.dvc.model.filter.DateRangeFilter;

import jakarta.validation.ConstraintValidatorContext;

class DateRangeValidatorTest {

    private DateRangeValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @BeforeEach
    void setUp() {
        validator = new DateRangeValidator();
    }

    @Test
    void whenBothDatesAreNull_thenValid() {
        DateRangeFilter filter = createDateRangeFilter(null, null);
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenStartDateIsAfterEndDate_thenInvalid() {
        DateRangeFilter filter = createDateRangeFilter(
                LocalDate.now().minusDays(1),
                LocalDate.now().minusDays(2));
        assertThat(validator.isValid(filter, context)).isFalse();
    }

    @Test
    void whenDatesAreEqual_thenValid() {
        LocalDate today = LocalDate.now();
        DateRangeFilter filter = createDateRangeFilter(today, today);
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenOnlyStartDateIsSet_thenValidIfNotFuture() {
        DateRangeFilter filter = createDateRangeFilter(LocalDate.now().minusDays(1), null);
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenOnlyEndDateIsSet_thenValidIfNotFuture() {
        DateRangeFilter filter = createDateRangeFilter(null, LocalDate.now().minusDays(1));
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    @Test
    void whenValidDateRange_thenValid() {
        DateRangeFilter filter = createDateRangeFilter(
                LocalDate.now().minusDays(2),
                LocalDate.now().minusDays(1));
        assertThat(validator.isValid(filter, context)).isTrue();
    }

    private DateRangeFilter createDateRangeFilter(LocalDate startDate, LocalDate endDate) {
        return new DateRangeFilter() {
            @Override
            public LocalDate getStartDate() {
                return startDate;
            }

            @Override
            public LocalDate getEndDate() {
                return endDate;
            }
        };
    }
}