package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class BatchTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        BatchTypeEnum[] types = BatchTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            BatchTypeEnum.INQUIRY,
            BatchTypeEnum.PAYMENT
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(BatchTypeEnum.valueOf("INQUIRY")).isEqualTo(BatchTypeEnum.INQUIRY);
        assertThat(BatchTypeEnum.valueOf("PAYMENT")).isEqualTo(BatchTypeEnum.PAYMENT);
    }
} 