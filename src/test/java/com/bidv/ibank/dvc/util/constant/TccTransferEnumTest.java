package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class TccTransferEnumTest {
    @Test
    void values_ShouldContainAllStatuses() {
        TccTransferEnum[] statuses = TccTransferEnum.values();
        assertThat(statuses).containsExactlyInAnyOrder(
            TccTransferEnum.SUCCESS,
            TccTransferEnum.FAILED
        );
    }

    @Test
    void getValue_ShouldReturnCorrectValue() {
        assertThat(TccTransferEnum.SUCCESS.getValue()).isEqualTo("0");
        assertThat(TccTransferEnum.FAILED.getValue()).isEqualTo("1");
    }

    @Test
    void fromValue_ShouldReturnCorrectEnumName() {
        assertThat(TccTransferEnum.fromValue("0")).isEqualTo("SUCCESS");
        assertThat(TccTransferEnum.fromValue("1")).isEqualTo("FAILED");
        assertThat(TccTransferEnum.fromValue("unknown")).isEqualTo("FAILED");
    }
} 