package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class ServiceGroupTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        ServiceGroupTypeEnum[] types = ServiceGroupTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            ServiceGroupTypeEnum.ACC_INQ,
            ServiceGroupTypeEnum.ACC_FIN
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(ServiceGroupTypeEnum.valueOf("ACC_INQ")).isEqualTo(ServiceGroupTypeEnum.ACC_INQ);
        assertThat(ServiceGroupTypeEnum.valueOf("ACC_FIN")).isEqualTo(ServiceGroupTypeEnum.ACC_FIN);
    }
} 