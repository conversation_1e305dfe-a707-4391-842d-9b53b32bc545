package com.bidv.ibank.dvc.util.excel.converter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class ExtractCodeFromTextConverterTest {
    private ExtractCodeFromTextConverter converter;

    @BeforeEach
    void setUp() {
        converter = new ExtractCodeFromTextConverter();
    }

    @Test
    void convert_ShouldReturnNull_WhenInputIsNull() throws Exception {
        assertThat(converter.convert(null)).isNull();
    }

    @Test
    void convert_ShouldReturnNull_WhenInputIsEmpty() throws Exception {
        assertThat(converter.convert("")).isNull();
        assertThat(converter.convert("   ")).isNull();
    }

    @Test
    void convert_ShouldReturnFirstPart_WhenInputHasDash() throws Exception {
        assertThat(converter.convert("ABC-123")).isEqualTo("ABC");
        assertThat(converter.convert("  CODE - description ")).isEqualTo("CODE");
        assertThat(converter.convert("A-B-C-D")).isEqualTo("A");
    }

    @Test
    void convert_ShouldReturnTrimmedInput_WhenNoDash() throws Exception {
        assertThat(converter.convert("CODE")).isEqualTo("CODE");
        assertThat(converter.convert("  CODE  ")).isEqualTo("CODE");
    }

    @Test
    void convert_ShouldHandleSpecialCases() throws Exception {
        assertThat(converter.convert("-ONLYDASH")).isEqualTo("");
        assertThat(converter.convert("--DOUBLE")).isEqualTo("");
        assertThat(converter.convert(" ")).isNull();
    }
} 