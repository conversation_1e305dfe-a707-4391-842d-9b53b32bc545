package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class BatchItemStatusEnumTest {
    @Test
    void values_ShouldContainAllStatuses() {
        BatchItemStatusEnum[] statuses = BatchItemStatusEnum.values();
        assertThat(statuses).containsExactlyInAnyOrder(
            BatchItemStatusEnum.VALID,
            BatchItemStatusEnum.INVALID
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(BatchItemStatusEnum.valueOf("VALID")).isEqualTo(BatchItemStatusEnum.VALID);
        assertThat(BatchItemStatusEnum.valueOf("INVALID")).isEqualTo(BatchItemStatusEnum.INVALID);
    }
} 