package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class ResponseCodeTest {

    private MockedStatic<Translator> translatorMock;
    private static final String TRANSLATED_MESSAGE = "Translated Message";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void code_ShouldReturnCorrectCode() {
        assertThat(ResponseCode.SUCCESS.code()).isEqualTo("0");
        assertThat(ResponseCode.SESSION_TIMEOUT_01.code()).isEqualTo("GOV0001");
        assertThat(ResponseCode.TIMEOUT_01.code()).isEqualTo("GOV0002");
        assertThat(ResponseCode.INPUT_01.code()).isEqualTo("GOV0003");
    }

    @Test
    void message_ShouldReturnTranslatedMessage() {
        translatorMock.when(() -> Translator.toLocale(any(String.class)))
                .thenReturn(TRANSLATED_MESSAGE);

        assertThat(ResponseCode.SUCCESS.message()).isEqualTo(TRANSLATED_MESSAGE);
        assertThat(ResponseCode.SESSION_TIMEOUT_01.message()).isEqualTo(TRANSLATED_MESSAGE);
        assertThat(ResponseCode.TIMEOUT_01.message()).isEqualTo(TRANSLATED_MESSAGE);
        assertThat(ResponseCode.INPUT_01.message()).isEqualTo(TRANSLATED_MESSAGE);
    }

    @Test
    void message_ShouldUseCorrectTranslationKey() {
        ResponseCode.SUCCESS.message();
        translatorMock.verify(() -> Translator.toLocale("response.code.0"));

        ResponseCode.SESSION_TIMEOUT_01.message();
        translatorMock.verify(() -> Translator.toLocale("response.code.GOV0001"));

        ResponseCode.TIMEOUT_01.message();
        translatorMock.verify(() -> Translator.toLocale("response.code.GOV0002"));

        ResponseCode.INPUT_01.message();
        translatorMock.verify(() -> Translator.toLocale("response.code.GOV0003"));
    }

    @Test
    void valueOf_WhenValidName_ShouldReturnCorrectEnum() {
        assertThat(ResponseCode.valueOf("SUCCESS")).isEqualTo(ResponseCode.SUCCESS);
        assertThat(ResponseCode.valueOf("SESSION_TIMEOUT_01")).isEqualTo(ResponseCode.SESSION_TIMEOUT_01);
        assertThat(ResponseCode.valueOf("TIMEOUT_01")).isEqualTo(ResponseCode.TIMEOUT_01);
        assertThat(ResponseCode.valueOf("INPUT_01")).isEqualTo(ResponseCode.INPUT_01);
    }

    @Test
    void toString_ShouldReturnEnumName() {
        assertThat(ResponseCode.SUCCESS.toString()).isEqualTo("SUCCESS");
        assertThat(ResponseCode.SESSION_TIMEOUT_01.toString()).isEqualTo("SESSION_TIMEOUT_01");
        assertThat(ResponseCode.TIMEOUT_01.toString()).isEqualTo("TIMEOUT_01");
        assertThat(ResponseCode.INPUT_01.toString()).isEqualTo("INPUT_01");
    }

    @Test
    void ordinal_ShouldReturnCorrectOrder() {
        assertThat(ResponseCode.SUCCESS.ordinal()).isZero();
        assertThat(ResponseCode.SESSION_TIMEOUT_01.ordinal()).isEqualTo(1);
        assertThat(ResponseCode.TIMEOUT_01.ordinal()).isEqualTo(2);
        assertThat(ResponseCode.INPUT_01.ordinal()).isEqualTo(3);
    }

    @Test
    void equals_ShouldWorkCorrectly() {
        ResponseCode success1 = ResponseCode.SUCCESS;
        ResponseCode success2 = ResponseCode.SUCCESS;
        ResponseCode timeout = ResponseCode.TIMEOUT_01;

        assertThat(success1).isEqualTo(success2);
        assertThat(success1).isNotEqualTo(timeout);
        assertThat(success1).isNotEqualTo(null);
        assertThat(success1).isNotEqualTo("SUCCESS");
    }

    @Test
    void hashCode_ShouldBeConsistent() {
        ResponseCode success1 = ResponseCode.SUCCESS;
        ResponseCode success2 = ResponseCode.SUCCESS;
        ResponseCode timeout = ResponseCode.TIMEOUT_01;

        assertThat(success1.hashCode()).isEqualTo(success2.hashCode());
        assertThat(success1.hashCode()).isNotEqualTo(timeout.hashCode());
    }

    @Test
    void implementsIResponseCode() {
        assertThat(ResponseCode.SUCCESS).isInstanceOf(com.bidv.ibank.framework.util.result.IResponseCode.class);
    }

    @Test
    void allEnumValues_ShouldReturnCorrectCodeAndMessage() {
        translatorMock.when(() -> Translator.toLocale(any(String.class)))
                .thenReturn(TRANSLATED_MESSAGE);
        for (ResponseCode rc : ResponseCode.values()) {
            // code() should return the code as in the enum constructor
            assertThat(rc.code()).isNotNull();
            // message() should call Translator with the correct key
            String expectedKey = "response.code." + rc.code();
            rc.message();
            translatorMock.verify(() -> Translator.toLocale(expectedKey));
        }
    }

    @Test
    void valueOf_WhenInvalidName_ShouldThrowException() {
        org.junit.jupiter.api.Assertions.assertThrows(IllegalArgumentException.class, () -> {
            ResponseCode.valueOf("NOT_A_REAL_CODE");
        });
    }
}