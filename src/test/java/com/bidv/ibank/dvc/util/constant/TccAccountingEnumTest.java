package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class TccAccountingEnumTest {
    @Test
    void values_ShouldContainAllStatuses() {
        TccAccountingEnum[] statuses = TccAccountingEnum.values();
        assertThat(statuses).containsExactlyInAnyOrder(
            TccAccountingEnum.SUCCESS,
            TccAccountingEnum.UNDEFINED,
            TccAccountingEnum.FAILED
        );
    }

    @Test
    void getValue_ShouldReturnCorrectValue() {
        assertThat(TccAccountingEnum.SUCCESS.getValue()).isEqualTo("0");
        assertThat(TccAccountingEnum.UNDEFINED.getValue()).isEqualTo("8");
        assertThat(TccAccountingEnum.FAILED.getValue()).isEqualTo("9");
    }

    @Test
    void fromValue_ShouldReturnCorrectEnumName() {
        assertThat(TccAccountingEnum.fromValue("0")).isEqualTo("SUCCESS");
        assertThat(TccAccountingEnum.fromValue("8")).isEqualTo("UNDEFINED");
        assertThat(TccAccountingEnum.fromValue("9")).isEqualTo("FAILED");
        assertThat(TccAccountingEnum.fromValue("unknown")).isEqualTo("UNDEFINED");
    }
} 