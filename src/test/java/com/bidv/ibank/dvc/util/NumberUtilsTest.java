package com.bidv.ibank.dvc.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.math.BigDecimal;

import org.junit.jupiter.api.Test;

class NumberUtilsTest {

    @Test
    void testToDoubleWithValue() {
        BigDecimal value = new BigDecimal("123.45");
        Double result = NumberUtils.toDouble(value);
        assertEquals(123.45, result);
    }

    @Test
    void testToDoubleWithNull() {
        Double result = NumberUtils.toDouble(null);
        assertNull(result);
    }

    @Test
    void testToDoubleWithDefaultValue() {
        BigDecimal value = new BigDecimal("123.45");
        Double result = NumberUtils.toDouble(value, 0.0);
        assertEquals(123.45, result);
    }

    @Test
    void testToDoubleWithNullAndDefaultValue() {
        Double result = NumberUtils.toDouble(null, 0.0);
        assertEquals(0.0, result);
    }

    @Test
    void testToDoubleWithZeroValue() {
        BigDecimal value = BigDecimal.ZERO;
        Double result = NumberUtils.toDouble(value);
        assertEquals(0.0, result);
    }

    @Test
    void testToDoubleWithNegativeValue() {
        BigDecimal value = new BigDecimal("-123.45");
        Double result = NumberUtils.toDouble(value);
        assertEquals(-123.45, result);
    }

    @Test
    void testToDoubleWithLargeValue() {
        BigDecimal value = new BigDecimal("999999999999.99");
        Double result = NumberUtils.toDouble(value);
        assertEquals(999999999999.99, result);
    }
}