package com.bidv.ibank.dvc.util;

import com.bidv.ibank.dvc.util.constant.CcyEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class CcyUtilsTest {
    @Test
    void getCodeByCcy_ShouldReturnUppercaseOrEmpty() {
        assertThat(CcyUtils.getCodeByCcy("vnd")).isEqualTo("VND");
        assertThat(CcyUtils.getCodeByCcy("KRW")).isEqualTo("KRW");
        assertThat(CcyUtils.getCodeByCcy("usd")).isEqualTo("");
        assertThat(CcyUtils.getCodeByCcy(null)).isEqualTo("");
    }

    @Test
    void isValidStringAmount_ShouldValidateByCcy() {
        assertThat(CcyUtils.isValidStringAmount("123456", "VND")).isTrue();
        assertThat(CcyUtils.isValidStringAmount("123.45", "VND")).isFalse();
        assertThat(CcyUtils.isValidStringAmount("123.45", "USD")).isTrue();
        assertThat(CcyUtils.isValidStringAmount(null, "VND")).isTrue();
    }

    @Test
    void isValidMaxLengthAmount_ShouldValidateByCcy() {
        String longAmount = "***************"; // 15 digits
        assertThat(CcyUtils.isValidMaxLengthAmount(longAmount, "VND")).isTrue();
        assertThat(CcyUtils.isValidMaxLengthAmount(longAmount + "1", "VND")).isFalse();
        // For USD, max length is 16
        assertThat(CcyUtils.isValidMaxLengthAmount("12345678901234.5", "USD")).isTrue(); // 16 chars
        assertThat(CcyUtils.isValidMaxLengthAmount("***************.5", "USD")).isFalse(); // 17 chars
        assertThat(CcyUtils.isValidMaxLengthAmount(null, "VND")).isTrue();
    }

    @Test
    void getMaxLengthAmountByCcy_ShouldReturnCorrectLength() {
        assertThat(CcyUtils.getMaxLengthAmountByCcy("VND")).isEqualTo(AppConstants.BATCH_FIELD_LENGTH.MAX_AMOUNT_LENGTH);
        assertThat(CcyUtils.getMaxLengthAmountByCcy("USD")).isEqualTo(AppConstants.BATCH_FIELD_LENGTH.MAX_AMOUNT_WITH_DOT_LENGTH);
    }

    @Test
    void getRegexAmountByCcy_ShouldReturnCorrectRegex() {
        assertThat(CcyUtils.getRegexAmountByCcy("VND")).isEqualTo(AppConstants.REGEX_VALIDATION.NUMBER);
        assertThat(CcyUtils.getRegexAmountByCcy("USD")).isEqualTo(AppConstants.REGEX_VALIDATION.NUMBER_AND_DOT);
    }

    @Test
    void getResponseCodeByCcy_ShouldReturnCorrectResponseCode() {
        assertThat(CcyUtils.getResponseCodeByCcy("VND")).isEqualTo(ResponseCode.AMOUNT_EXCEED_MAX_LENGTH);
        assertThat(CcyUtils.getResponseCodeByCcy("USD")).isEqualTo(ResponseCode.AMOUNT_WITH_DOT_EXCEED_MAX_LENGTH);
    }
} 