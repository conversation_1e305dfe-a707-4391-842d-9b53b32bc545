package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class CcyEnumTest {
    @Test
    void values_ShouldContainAllCurrencies() {
        CcyEnum[] ccys = CcyEnum.values();
        assertThat(ccys).containsExactlyInAnyOrder(
            CcyEnum.VND,
            CcyEnum.CLP,
            CcyEnum.JYP,
            CcyEnum.KRW
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(CcyEnum.valueOf("VND")).isEqualTo(CcyEnum.VND);
        assertThat(CcyEnum.valueOf("CLP")).isEqualTo(CcyEnum.CLP);
        assertThat(CcyEnum.valueOf("JYP")).isEqualTo(CcyEnum.JYP);
        assertThat(CcyEnum.valueOf("KRW")).isEqualTo(CcyEnum.KRW);
    }
} 