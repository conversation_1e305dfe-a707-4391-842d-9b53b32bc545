package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class StatusEnumTest {

    @Test
    void getValue_WhenActive_ShouldReturnOne() {
        assertThat(StatusEnum.ACTIVE.getValue()).isEqualTo("1");
    }

    @Test
    void getValue_WhenInactive_ShouldReturnZero() {
        assertThat(StatusEnum.INACTIVE.getValue()).isEqualTo("0");
    }

    @Test
    void values_ShouldContainAllStatuses() {
        StatusEnum[] statuses = StatusEnum.values();

        assertThat(statuses).hasSize(2);
        assertThat(statuses).containsExactlyInAnyOrder(
                StatusEnum.ACTIVE,
                StatusEnum.INACTIVE);
    }

    @Test
    void valueOf_WhenValidName_ShouldReturnCorrectEnum() {
        assertThat(StatusEnum.valueOf("ACTIVE")).isEqualTo(StatusEnum.ACTIVE);
        assertThat(StatusEnum.valueOf("INACTIVE")).isEqualTo(StatusEnum.INACTIVE);
    }

    @Test
    void toString_ShouldReturnEnumName() {
        assertThat(StatusEnum.ACTIVE.toString()).isEqualTo("ACTIVE");
        assertThat(StatusEnum.INACTIVE.toString()).isEqualTo("INACTIVE");
    }

    @Test
    void ordinal_ShouldReturnCorrectOrder() {
        assertThat(StatusEnum.ACTIVE.ordinal()).isZero();
        assertThat(StatusEnum.INACTIVE.ordinal()).isEqualTo(1);
    }

    @Test
    void equals_ShouldWorkCorrectly() {
        StatusEnum active1 = StatusEnum.ACTIVE;
        StatusEnum active2 = StatusEnum.ACTIVE;
        StatusEnum inactive = StatusEnum.INACTIVE;

        assertThat(active1).isEqualTo(active2);
        assertThat(active1).isNotEqualTo(inactive);
        assertThat(active1).isNotEqualTo(null);
        assertThat(active1).isNotEqualTo("ACTIVE");
    }

    @Test
    void hashCode_ShouldBeConsistent() {
        StatusEnum active1 = StatusEnum.ACTIVE;
        StatusEnum active2 = StatusEnum.ACTIVE;
        StatusEnum inactive = StatusEnum.INACTIVE;

        assertThat(active1.hashCode()).isEqualTo(active2.hashCode());
        assertThat(active1.hashCode()).isNotEqualTo(inactive.hashCode());
    }
}