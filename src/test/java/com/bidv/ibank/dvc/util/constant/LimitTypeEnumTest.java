package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class LimitTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        LimitTypeEnum[] types = LimitTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            LimitTypeEnum.TOTAL_LIMIT_CIF,
            LimitTypeEnum.TOTAL_LIMIT_ACC,
            LimitTypeEnum.TOTAL_LIMIT_USER
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(LimitTypeEnum.valueOf("TOTAL_LIMIT_CIF")).isEqualTo(LimitTypeEnum.TOTAL_LIMIT_CIF);
        assertThat(LimitTypeEnum.valueOf("TOTAL_LIMIT_ACC")).isEqualTo(LimitTypeEnum.TOTAL_LIMIT_ACC);
        assertThat(LimitTypeEnum.valueOf("TOTAL_LIMIT_USER")).isEqualTo(LimitTypeEnum.TOTAL_LIMIT_USER);
    }
} 