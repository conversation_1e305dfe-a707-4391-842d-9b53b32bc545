package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class FileTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        FileTypeEnum[] types = FileTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            FileTypeEnum.XLSX,
            FileTypeEnum.XLS
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(FileTypeEnum.valueOf("XLSX")).isEqualTo(FileTypeEnum.XLSX);
        assertThat(FileTypeEnum.valueOf("XLS")).isEqualTo(FileTypeEnum.XLS);
    }
} 