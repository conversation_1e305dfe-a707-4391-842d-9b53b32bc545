package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class TransactionFieldEnumTest {
    @Test
    void values_ShouldContainAllFields() {
        TransactionFieldEnum[] fields = TransactionFieldEnum.values();
        assertThat(fields).contains(
                TransactionFieldEnum.TCC_ERR_CODE,
                TransactionFieldEnum.TCC_ERR_DESC,
                TransactionFieldEnum.STATUS,
                TransactionFieldEnum.STATE,
                TransactionFieldEnum.PMT_TIME,
                TransactionFieldEnum.TCC_DOC_ID,
                TransactionFieldEnum.TCC_DOC_SIGN,
                TransactionFieldEnum.TCC_DOC_NO,
                TransactionFieldEnum.TCC_ID_CORE,
                TransactionFieldEnum.CORE_REF,
                TransactionFieldEnum.TCC_RM_NO,
                TransactionFieldEnum.RETRY_INSFCT_BAL);
    }

    @Test
    void getFieldCode_ShouldReturnCorrectValue() {
        assertThat(TransactionFieldEnum.TCC_ERR_CODE.getFieldCode()).isEqualTo("tccErrCode");
        assertThat(TransactionFieldEnum.TCC_ERR_DESC.getFieldCode()).isEqualTo("tccErrDesc");
        assertThat(TransactionFieldEnum.STATUS.getFieldCode()).isEqualTo("status");
        assertThat(TransactionFieldEnum.STATE.getFieldCode()).isEqualTo("state");
        assertThat(TransactionFieldEnum.PMT_TIME.getFieldCode()).isEqualTo("pmtTime");
        assertThat(TransactionFieldEnum.TCC_DOC_ID.getFieldCode()).isEqualTo("tccDocId");
        assertThat(TransactionFieldEnum.TCC_DOC_SIGN.getFieldCode()).isEqualTo("tccDocSign");
        assertThat(TransactionFieldEnum.TCC_DOC_NO.getFieldCode()).isEqualTo("tccDocNo");
        assertThat(TransactionFieldEnum.TCC_ID_CORE.getFieldCode()).isEqualTo("tccIdCore");
        assertThat(TransactionFieldEnum.CORE_REF.getFieldCode()).isEqualTo("coreRef");
        assertThat(TransactionFieldEnum.TCC_RM_NO.getFieldCode()).isEqualTo("tccRmNo");
    }
}