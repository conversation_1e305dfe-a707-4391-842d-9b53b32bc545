package com.bidv.ibank.dvc.util.excel.converter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.assertj.core.api.Assertions.assertThat;

class FormatStringExcelConverterTest {

    private FormatStringExcelConverter converter;

    @BeforeEach
    void setUp() {
        converter = new FormatStringExcelConverter();
    }

    @Test
    void convert_ShouldHandleNull() {
        assertThat(converter.convert(null)).isNull();
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "",
        " ",
        "  "
    })
    void convert_ShouldHandleEmptyAndWhitespaceStrings(String input) {
        assertThat(converter.convert(input)).isEmpty();
    }

    @Test
    void convert_ShouldHandleQuotedStrings() {
        assertThat(converter.convert("\"hello\"")).isEqualTo("hello");
        assertThat(converter.convert("\"  hello  \"")).isEqualTo("  hello  ");
        assertThat(converter.convert("\"hello world\"")).isEqualTo("hello world");
    }

    @Test
    void convert_ShouldHandleUnquotedStrings() {
        assertThat(converter.convert("hello")).isEqualTo("hello");
        assertThat(converter.convert("  hello  ")).isEqualTo("hello");
        assertThat(converter.convert("hello world")).isEqualTo("hello world");
    }

    @Test
    void convert_ShouldHandleSpecialCharacters() {
        assertThat(converter.convert("123")).isEqualTo("123");
        assertThat(converter.convert("!@#$%")).isEqualTo("!@#$%");
        assertThat(converter.convert("\tTab\t")).isEqualTo("Tab");
        assertThat(converter.convert("New\nLine")).isEqualTo("New\nLine");
    }

    @Test
    void convert_ShouldHandleMultipleSpaces() {
        assertThat(converter.convert("   abc   def   ")).isEqualTo("abc   def");
        assertThat(converter.convert("abc\t\tdef")).isEqualTo("abc\t\tdef");
        assertThat(converter.convert("   \"abc\"   ")).isEqualTo("abc");
    }

    @Test
    void convert_ShouldHandleQuoteEdgeCases() {
        assertThat(converter.convert("\"\"")).isEmpty();
        assertThat(converter.convert("\"")).isEmpty();
        assertThat(converter.convert("\"hello\"world\"")).isEqualTo("hello\"world");
        assertThat(converter.convert("hello\"world")).isEqualTo("hello\"world");
    }
} 