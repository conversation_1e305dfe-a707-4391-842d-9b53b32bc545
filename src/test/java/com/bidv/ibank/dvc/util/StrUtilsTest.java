package com.bidv.ibank.dvc.util;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

class StrUtilsTest {
    @Test
    void combineCodeName_ShouldCombineCorrectly() {
        assertThat(StrUtils.combineCodeName("A", "B")).isEqualTo("A - B");
        assertThat(StrUtils.combineCodeName("A", "")).isEqualTo("A");
        assertThat(StrUtils.combineCodeName("", "B")).isEqualTo("B");
        assertThat(StrUtils.combineCodeName("", "")).isEqualTo("");
        assertThat(StrUtils.combineCodeName(null, null)).isEqualTo("");
    }

    @Test
    void formatWithThousandSeparator_ShouldFormatCorrectly() {
        assertThat(StrUtils.formatWithThousandSeparator(new BigDecimal("1234567.89"))).isEqualTo("1,234,567.89");
        assertThat(StrUtils.formatWithThousandSeparator(BigDecimal.ZERO)).isEqualTo("0");
        assertThat(StrUtils.formatWithThousandSeparator((BigDecimal) null)).isEqualTo("");
    }

    @Test
    void concatNonBlank_ShouldJoinNonBlankStrings() {
        assertThat(StrUtils.concatNonBlank("A", "", null, "B", " ", "C")).isEqualTo("A;B;C");
        assertThat(StrUtils.concatNonBlank(" ", null, "")).isEqualTo("");
        assertThat(StrUtils.concatNonBlank("A")).isEqualTo("A");
    }
}
