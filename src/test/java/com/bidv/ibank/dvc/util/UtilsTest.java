package com.bidv.ibank.dvc.util;

import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class UtilsTest {
    @Test
    void convetStringToListLong_ShouldReturnEmptyList_WhenNullOrEmpty() {
        assertThat(Utils.convetStringToListLong(null)).isEmpty();
        assertThat(Utils.convetStringToListLong("")).isEmpty();
        assertThat(Utils.convetStringToListLong("   ")).isEmpty();
    }

    @Test
    void convetStringToListLong_ShouldReturnListOfLongs_WhenValidString() {
        assertThat(Utils.convetStringToListLong("1,2,3")).containsExactly(1L, 2L, 3L);
        assertThat(Utils.convetStringToListLong(" 10 , 20 , 30 ")).containsExactly(10L, 20L, 30L);
    }

    @Test
    void convetStringToListLong_ShouldIgnoreInvalidNumbers() {
        assertThat(Utils.convetStringToListLong("1,abc,2,xyz,3")).containsExactly(1L, 2L, 3L);
        assertThat(Utils.convetStringToListLong("abc,xyz")).isEmpty();
    }

    @Test
    void convetStringToListLong_ShouldHandleSingleValue() {
        assertThat(Utils.convetStringToListLong("42")).containsExactly(42L);
    }
} 