package com.bidv.ibank.dvc.util;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;

class ParamUtilsTest {

    @Test
    void testGetEcName() {
        TccDmNdktEntity entity = new TccDmNdktEntity();
        entity.setMaNdkt("EC001");
        entity.setTen("EC Name");
        List<TccDmNdktEntity> list = Arrays.asList(entity);

        assertThat(ParamUtils.getEcName(list, "EC001")).isEqualTo("EC Name");
        assertThat(ParamUtils.getEcName(list, "INVALID")).isNull();
        assertThat(ParamUtils.getEcName(Collections.emptyList(), "EC001")).isNull();
    }

    @Test
    void testGetCcName() {
        TccDmLoaitienhqaEntity entity = new TccDmLoaitienhqaEntity();
        entity.setMaLthq("CC001");
        entity.setTenLthq("CC Name");
        List<TccDmLoaitienhqaEntity> list = Arrays.asList(entity);

        assertThat(ParamUtils.getCcName(list, "CC001")).isEqualTo("CC Name");
        assertThat(ParamUtils.getCcName(list, "INVALID")).isNull();
        assertThat(ParamUtils.getCcName(Collections.emptyList(), "CC001")).isNull();
    }

    @Test
    void testGetChapterName() {
        TccDmChuongEntity entity = new TccDmChuongEntity();
        entity.setMaChuong("CH001");
        entity.setTen("Chapter Name");
        List<TccDmChuongEntity> list = Arrays.asList(entity);

        assertThat(ParamUtils.getChapterName(list, "CH001")).isEqualTo("Chapter Name");
        assertThat(ParamUtils.getChapterName(list, "INVALID")).isNull();
        assertThat(ParamUtils.getChapterName(Collections.emptyList(), "CH001")).isNull();
    }

    @Test
    void testGetRevAccName() {
        TccDmTkNsnnEntity entity = new TccDmTkNsnnEntity();
        entity.setMaTk("RA001");
        entity.setTen("Revenue Account Name");
        List<TccDmTkNsnnEntity> list = Arrays.asList(entity);

        assertThat(ParamUtils.getRevAccName(list, "RA001")).isEqualTo("Revenue Account Name");
        assertThat(ParamUtils.getRevAccName(list, "INVALID")).isNull();
        assertThat(ParamUtils.getRevAccName(Collections.emptyList(), "RA001")).isNull();
    }

    @Test
    void testGetTaxTypeName() {
        TccDmSthueHqaEntity entity = new TccDmSthueHqaEntity();
        entity.setMaSthue("TT001");
        entity.setTenSthue("Tax Type Name");
        List<TccDmSthueHqaEntity> list = Arrays.asList(entity);

        assertThat(ParamUtils.getTaxTypeName(list, "TT001")).isEqualTo("Tax Type Name");
        assertThat(ParamUtils.getTaxTypeName(list, "INVALID")).isNull();
        assertThat(ParamUtils.getTaxTypeName(Collections.emptyList(), "TT001")).isNull();
    }

    @Test
    void testGetAdmAreaCode() {
        TccDmKhobacEntity khobacEntity = new TccDmKhobacEntity();
        khobacEntity.setShkb("KB001");

        TccDmDbhcEntity dbhcEntity = new TccDmDbhcEntity();
        dbhcEntity.setMaDbhc("AA001");
        khobacEntity.setTccDmDbhcEntity(dbhcEntity);

        List<TccDmKhobacEntity> list = Arrays.asList(khobacEntity);

        assertThat(ParamUtils.getAdmAreaCode(list, "KB001")).isEqualTo("AA001");
        assertThat(ParamUtils.getAdmAreaCode(list, "INVALID")).isNull();
        assertThat(ParamUtils.getAdmAreaCode(Collections.emptyList(), "KB001")).isNull();

        // Test null dbhc entity
        khobacEntity.setTccDmDbhcEntity(null);
        assertThat(ParamUtils.getAdmAreaCode(list, "KB001")).isNull();
    }

    @Test
    void testGetAdmAreaName() {
        TccDmKhobacEntity khobacEntity = new TccDmKhobacEntity();
        khobacEntity.setShkb("KB001");

        TccDmDbhcEntity dbhcEntity = new TccDmDbhcEntity();
        dbhcEntity.setTen("Admin Area Name");
        khobacEntity.setTccDmDbhcEntity(dbhcEntity);

        List<TccDmKhobacEntity> list = Arrays.asList(khobacEntity);

        assertThat(ParamUtils.getAdmAreaName(list, "KB001")).isEqualTo("Admin Area Name");
        assertThat(ParamUtils.getAdmAreaName(list, "INVALID")).isNull();
        assertThat(ParamUtils.getAdmAreaName(Collections.emptyList(), "KB001")).isNull();

        // Test null dbhc entity
        khobacEntity.setTccDmDbhcEntity(null);
        assertThat(ParamUtils.getAdmAreaName(list, "KB001")).isNull();
    }

    @Test
    void testGetBenBankCodeByTreasuryCode() {
        com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto dto = new com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto();
        dto.setTreasuryCode("KB001");
        dto.setBenBankCode("BANKCODE");
        List<com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto> list = Arrays.asList(dto);
        assertThat(ParamUtils.getBenBankCodeByTreasuryCode(list, "KB001")).isEqualTo("BANKCODE");
        assertThat(ParamUtils.getBenBankCodeByTreasuryCode(list, "INVALID")).isNull();
        assertThat(ParamUtils.getBenBankCodeByTreasuryCode(Arrays.asList(), "KB001")).isNull();
    }

    @Test
    void testGetBenBankNameByTreasuryCode() {
        com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto dto = new com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto();
        dto.setTreasuryCode("KB001");
        dto.setBenBankName("BANKNAME");
        List<com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto> list = Arrays.asList(dto);
        assertThat(ParamUtils.getBenBankNameByTreasuryCode(list, "KB001")).isEqualTo("BANKNAME");
        assertThat(ParamUtils.getBenBankNameByTreasuryCode(list, "INVALID")).isNull();
        assertThat(ParamUtils.getBenBankNameByTreasuryCode(Arrays.asList(), "KB001")).isNull();
    }
}