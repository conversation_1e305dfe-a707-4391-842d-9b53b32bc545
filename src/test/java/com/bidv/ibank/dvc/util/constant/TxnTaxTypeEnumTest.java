package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class TxnTaxTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        TxnTaxTypeEnum[] types = TxnTaxTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            TxnTaxTypeEnum.DOMESTIC_TAX,
            TxnTaxTypeEnum.SEAPORT_INFRASTRUCTURE_FEE,
            TxnTaxTypeEnum.CUSTOMS_TAX
        );
    }

    @Test
    void getCode_ShouldReturnCorrectValue() {
        assertThat(TxnTaxTypeEnum.DOMESTIC_TAX.getCode()).isEqualTo("01");
        assertThat(TxnTaxTypeEnum.SEAPORT_INFRASTRUCTURE_FEE.getCode()).isEqualTo("03");
        assertThat(TxnTaxTypeEnum.CUSTOMS_TAX.getCode()).isEqualTo("04");
    }

    @Test
    void getMessage_ShouldReturnCorrectValue() {
        assertThat(TxnTaxTypeEnum.DOMESTIC_TAX.getMessage()).isEqualTo("Thuế nội địa");
        assertThat(TxnTaxTypeEnum.SEAPORT_INFRASTRUCTURE_FEE.getMessage()).isEqualTo("Phí hạ tầng cảng biển");
        assertThat(TxnTaxTypeEnum.CUSTOMS_TAX.getMessage()).isEqualTo("Thuế hải quan");
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(TxnTaxTypeEnum.valueOf("DOMESTIC_TAX")).isEqualTo(TxnTaxTypeEnum.DOMESTIC_TAX);
        assertThat(TxnTaxTypeEnum.valueOf("SEAPORT_INFRASTRUCTURE_FEE")).isEqualTo(TxnTaxTypeEnum.SEAPORT_INFRASTRUCTURE_FEE);
        assertThat(TxnTaxTypeEnum.valueOf("CUSTOMS_TAX")).isEqualTo(TxnTaxTypeEnum.CUSTOMS_TAX);
    }
} 