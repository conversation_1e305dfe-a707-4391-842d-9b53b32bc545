package com.bidv.ibank.dvc.util;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class ArrUtilsTest {
    static class TestObj {
        String a;
        int b;
        TestObj(String a, int b) { this.a = a; this.b = b; }
        String getA() { return a; }
        int getB() { return b; }
    }

    @Test
    void distinctList_ShouldReturnDistinctBySingleField() {
        List<TestObj> list = Arrays.asList(
                new TestObj("x", 1),
                new TestObj("x", 2),
                new TestObj("y", 1),
                new TestObj("x", 1)
        );
        List<TestObj> result = ArrUtils.distinctList(list, TestObj::getA);
        assertThat(result).hasSize(2);
        assertThat(result).extracting(TestObj::getA).containsExactlyInAnyOrder("x", "y");
    }

    @Test
    void distinctList_ShouldReturnDistinctByMultipleFields() {
        List<TestObj> list = Arrays.asList(
                new TestObj("x", 1),
                new TestObj("x", 2),
                new TestObj("y", 1),
                new TestObj("x", 1)
        );
        List<TestObj> result = ArrUtils.distinctList(list, TestObj::getA, TestObj::getB);
        assertThat(result).hasSize(3);
        assertThat(result).extracting(t -> t.a + t.b).containsExactlyInAnyOrder("x1", "x2", "y1");
    }

    @Test
    void distinctList_ShouldHandleEmptyList() {
        List<TestObj> result = ArrUtils.distinctList(Arrays.asList(), TestObj::getA);
        assertThat(result).isEmpty();
    }
} 