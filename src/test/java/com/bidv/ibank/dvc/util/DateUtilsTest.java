package com.bidv.ibank.dvc.util;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.junit.jupiter.api.Test;

class DateUtilsTest {

    @Test
    void testMinusWorkingDays() {
        LocalDate date = LocalDate.of(2024, 3, 15); // Friday
        LocalDate result = DateUtils.minusWorkingDays(date, 3);

        // Should be Tuesday (skipping weekend)
        assertThat(result).isEqualTo(LocalDate.of(2024, 3, 12));

        // Test with date that includes multiple weekends
        date = LocalDate.of(2024, 3, 15); // Friday
        result = DateUtils.minusWorkingDays(date, 7);

        // Should be previous week's Wednesday
        assertThat(result).isEqualTo(LocalDate.of(2024, 3, 6));
    }

    @Test
    void testConvertStringToDate() {
        String dateStr = "15/03/2024";
        LocalDate result = DateUtils.convertStringToDate(dateStr, "dd/MM/yyyy");
        assertThat(result).isEqualTo(LocalDate.of(2024, 3, 15));

        // Test invalid format
        assertThat(DateUtils.convertStringToDate("invalid-date", "dd/MM/yyyy")).isNull();

        // Test invalid date
        assertThat(DateUtils.convertStringToDate("32/13/2024", "dd/MM/yyyy")).isNull();

        // Test null and empty
        assertThat(DateUtils.convertStringToDate(null, "dd/MM/yyyy")).isNull();
        assertThat(DateUtils.convertStringToDate("", "dd/MM/yyyy")).isNull();
    }

    @Test
    void testConvertDateTimeToString() {
        LocalDateTime dateTime = LocalDateTime.of(2024, 3, 15, 14, 30, 45);

        // Test with custom format
        String result = DateUtils.convertDateToString(dateTime, "yyyy-MM-dd HH:mm:ss");
        assertThat(result).isEqualTo("2024-03-15 14:30:45");

        // Test with default format
        result = DateUtils.convertDateToString(dateTime);
        assertThat(result).isEqualTo("15/03/2024 14:30:45");

        // Test null
        assertThat(DateUtils.convertDateToString((LocalDateTime) null)).isEmpty();
    }

    @Test
    void testConvertDateToString() {
        LocalDate date = LocalDate.of(2024, 3, 15);

        // Test with custom format
        String result = DateUtils.convertDateToString(date, "yyyy-MM-dd");
        assertThat(result).isEqualTo("2024-03-15");

        // Test with default format
        result = DateUtils.convertDateToString(date);
        assertThat(result).isEqualTo("15/03/2024");

        // Test null
        assertThat(DateUtils.convertDateToString((LocalDate) null)).isEmpty();
    }

    @Test
    void testParseDuration() {
        // Test valid duration
        Duration duration = DateUtils.parseDuration("01:30:45");
        assertThat(duration).isEqualTo(Duration.ofHours(1).plusMinutes(30).plusSeconds(45));

        // Test zero duration
        duration = DateUtils.parseDuration("00:00:00");
        assertThat(duration).isEqualTo(Duration.ZERO);

        // Test null or empty
        assertThat(DateUtils.parseDuration(null)).isEqualTo(Duration.ZERO);
        assertThat(DateUtils.parseDuration("")).isEqualTo(Duration.ZERO);

        // Test invalid formats
        assertThatThrownBy(() -> DateUtils.parseDuration("invalid"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Invalid time format. Please provide the time in HH:MM:SS format.");

        assertThatThrownBy(() -> DateUtils.parseDuration("aa:bb:cc"))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Invalid time format. Please provide the time in HH:MM:SS format.");

        // Test large values (should work according to current implementation)
        Duration largeDuration = DateUtils.parseDuration("25:00:00");
        assertThat(largeDuration).isEqualTo(Duration.ofHours(25));

        largeDuration = DateUtils.parseDuration("00:60:00");
        assertThat(largeDuration).isEqualTo(Duration.ofMinutes(60));
    }

    @Test
    void testConvertStringToDateTime() {
        String dateTimeStr = "15/03/2024 14:30:45";
        assertThat(DateUtils.convertStringToDateTime(dateTimeStr, "dd/MM/yyyy HH:mm:ss"))
            .isEqualTo(LocalDateTime.of(2024, 3, 15, 14, 30, 45));
        // Invalid format
        assertThat(DateUtils.convertStringToDateTime("invalid", "dd/MM/yyyy HH:mm:ss")).isNull();
        // Null and empty
        assertThat(DateUtils.convertStringToDateTime(null, "dd/MM/yyyy HH:mm:ss")).isNull();
        assertThat(DateUtils.convertStringToDateTime("", "dd/MM/yyyy HH:mm:ss")).isNull();
    }

    @Test
    void testIsValidDateFormat() {
        assertThat(DateUtils.isValidDateFormat("15/03/2024")).isTrue();
        assertThat(DateUtils.isValidDateFormat("31/12/2024")).isTrue();
        assertThat(DateUtils.isValidDateFormat("32/13/2024")).isFalse(); // invalid date
        assertThat(DateUtils.isValidDateFormat("2024-03-15")).isFalse(); // wrong format
        assertThat(DateUtils.isValidDateFormat("")).isFalse();
        assertThat(DateUtils.isValidDateFormat(null)).isFalse();
    }

    @Test
    void testIsValidTxnEffDate() {
        LocalDate today = LocalDate.now();
        assertThat(DateUtils.isValidTxnEffDate(today, 5)).isTrue();
        assertThat(DateUtils.isValidTxnEffDate(today.plusDays(1), 5)).isFalse(); // future
        assertThat(DateUtils.isValidTxnEffDate(today.minusDays(6), 5)).isFalse(); // too old
        assertThat(DateUtils.isValidTxnEffDate(today.minusDays(5), 5)).isTrue(); // edge
    }
}