package com.bidv.ibank.dvc.util.excel.converter;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class FormatNumberExcelConverterTest {

    private FormatNumberExcelConverter converter;

    @BeforeEach
    void setUp() {
        converter = new FormatNumberExcelConverter();
    }

    @Test
    void testNullValue() {
        assertNull(converter.convert(null));
    }

    @Test
    void testEmptyString() {
        assertEquals("", converter.convert(""));
        assertEquals("", converter.convert(" "));
    }

    @Test
    void testSimpleNumbers() {
        assertEquals("123", converter.convert("123"));
        assertEquals("123.45", converter.convert("123.45"));
        assertEquals("-123", converter.convert("-123"));
        assertEquals("-123.45", converter.convert("-123.45"));
    }

    @Test
    void testNumbersWithCommas() {
        assertEquals("1234567", converter.convert("1,234,567"));
        assertEquals("1234567.89", converter.convert("1,234,567.89"));
    }

    @Test
    void testNumbersWithParentheses() {
        assertEquals("-123", converter.convert("(123)"));
        assertEquals("-1234567.89", converter.convert("(1,234,567.89)"));
    }

    @Test
    void testNumbersWithQuotes() {
        assertEquals("123", converter.convert("\"123\""));
        assertEquals("1234567.89", converter.convert("\"1,234,567.89\""));
    }

    @Test
    void testZeroValues() {
        assertEquals("0", converter.convert("0"));
        assertEquals("0", converter.convert("0.00"));
        assertEquals("0", converter.convert("0.0"));
        assertEquals("0", converter.convert(".00"));
    }

    @Test
    void testTrailingZeros() {
        assertEquals("123", converter.convert("123.00"));
        assertEquals("123.45", converter.convert("123.4500"));
        assertEquals("123.4", converter.convert("123.40"));
    }

    @Test
    void testNonNumericStrings() {
        assertEquals("abc", converter.convert("abc"));
        assertEquals("123", converter.convert("123abc"));
        assertEquals("123", converter.convert("abc123"));
    }

    @Test
    void testSpecialFormats() {
        assertEquals("1234567.89", converter.convert(" 1,234,567.89 "));
        assertEquals("-1234567.89", converter.convert("\"(1,234,567.89)\""));
        assertEquals("1234567", converter.convert("1234567."));
    }
}