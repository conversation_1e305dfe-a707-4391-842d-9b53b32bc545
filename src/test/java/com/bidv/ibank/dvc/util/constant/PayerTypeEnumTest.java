package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class PayerTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        PayerTypeEnum[] types = PayerTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            PayerTypeEnum.UNDEFINED,
            PayerTypeEnum.BUSINESS,
            PayerTypeEnum.INDIVIDUAL
        );
    }

    @Test
    void getValue_ShouldReturnCorrectValue() {
        assertThat(PayerTypeEnum.UNDEFINED.getValue()).isEqualTo(0);
        assertThat(PayerTypeEnum.BUSINESS.getValue()).isEqualTo(1);
        assertThat(PayerTypeEnum.INDIVIDUAL.getValue()).isEqualTo(2);
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(PayerTypeEnum.valueOf("UNDEFINED")).isEqualTo(PayerTypeEnum.UNDEFINED);
        assertThat(PayerTypeEnum.valueOf("BUSINESS")).isEqualTo(PayerTypeEnum.BUSINESS);
        assertThat(PayerTypeEnum.valueOf("INDIVIDUAL")).isEqualTo(PayerTypeEnum.INDIVIDUAL);
    }
} 