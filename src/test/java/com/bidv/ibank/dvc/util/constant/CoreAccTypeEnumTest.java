package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class CoreAccTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        CoreAccTypeEnum[] types = CoreAccTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            CoreAccTypeEnum.ALL,
            CoreAccTypeEnum.DDA,
            CoreAccTypeEnum.CD,
            CoreAccTypeEnum.L,
            CoreAccTypeEnum.BG,
            CoreAccTypeEnum.LN,
            CoreAccTypeEnum.COM,
            CoreAccTypeEnum.CBL,
            CoreAccTypeEnum.RC
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(CoreAccTypeEnum.valueOf("ALL")).isEqualTo(CoreAccTypeEnum.ALL);
        assertThat(CoreAccTypeEnum.valueOf("DDA")).isEqualTo(CoreAccTypeEnum.DDA);
        assertThat(CoreAccTypeEnum.valueOf("CD")).isEqualTo(CoreAccTypeEnum.CD);
        assertThat(CoreAccTypeEnum.valueOf("L")).isEqualTo(CoreAccTypeEnum.L);
        assertThat(CoreAccTypeEnum.valueOf("BG")).isEqualTo(CoreAccTypeEnum.BG);
        assertThat(CoreAccTypeEnum.valueOf("LN")).isEqualTo(CoreAccTypeEnum.LN);
        assertThat(CoreAccTypeEnum.valueOf("COM")).isEqualTo(CoreAccTypeEnum.COM);
        assertThat(CoreAccTypeEnum.valueOf("CBL")).isEqualTo(CoreAccTypeEnum.CBL);
        assertThat(CoreAccTypeEnum.valueOf("RC")).isEqualTo(CoreAccTypeEnum.RC);
    }
} 