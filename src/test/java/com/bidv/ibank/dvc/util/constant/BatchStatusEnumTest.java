package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class BatchStatusEnumTest {
    @Test
    void values_ShouldContainAllStatuses() {
        BatchStatusEnum[] statuses = BatchStatusEnum.values();
        assertThat(statuses).containsExactlyInAnyOrder(
            BatchStatusEnum.PROCESSING,
            BatchStatusEnum.PROCESSED,
            BatchStatusEnum.CHECKED,
            BatchStatusEnum.ERROR,
            BatchStatusEnum.DELETED
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(BatchStatusEnum.valueOf("PROCESSING")).isEqualTo(BatchStatusEnum.PROCESSING);
        assertThat(BatchStatusEnum.valueOf("PROCESSED")).isEqualTo(BatchStatusEnum.PROCESSED);
        assertThat(BatchStatusEnum.valueOf("CHECKED")).isEqualTo(BatchStatusEnum.CHECKED);
        assertThat(BatchStatusEnum.valueOf("ERROR")).isEqualTo(BatchStatusEnum.ERROR);
        assertThat(BatchStatusEnum.valueOf("DELETED")).isEqualTo(BatchStatusEnum.DELETED);
    }
} 