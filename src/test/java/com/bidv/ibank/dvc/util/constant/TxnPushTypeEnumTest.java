package com.bidv.ibank.dvc.util.constant;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class TxnPushTypeEnumTest {
    @Test
    void values_ShouldContainAllTypes() {
        TxnPushTypeEnum[] types = TxnPushTypeEnum.values();
        assertThat(types).containsExactlyInAnyOrder(
            TxnPushTypeEnum.PUSH_SAVE,
            TxnPushTypeEnum.PUSH_EDIT,
            TxnPushTypeEnum.PUSH
        );
    }

    @Test
    void valueOf_ShouldReturnCorrectEnum() {
        assertThat(TxnPushTypeEnum.valueOf("PUSH_SAVE")).isEqualTo(TxnPushTypeEnum.PUSH_SAVE);
        assertThat(TxnPushTypeEnum.valueOf("PUSH_EDIT")).isEqualTo(TxnPushTypeEnum.PUSH_EDIT);
        assertThat(TxnPushTypeEnum.valueOf("PUSH")).isEqualTo(TxnPushTypeEnum.PUSH);
    }
} 