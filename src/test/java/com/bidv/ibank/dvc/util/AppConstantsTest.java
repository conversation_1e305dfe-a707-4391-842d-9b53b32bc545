package com.bidv.ibank.dvc.util;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

import java.lang.reflect.Constructor;

import org.junit.jupiter.api.Test;

class AppConstantsTest {

    @Test
    void testLanguageConstants() {
        assertThat(AppConstants.LANGUAGE.TAX_PAYER_TYPE).isEqualTo("tax.payer.type");
        assertThat(AppConstants.LANGUAGE.RESPONSE_CODE).isEqualTo("response.code");
        assertThat(AppConstants.LANGUAGE.TXN_STATUS).isEqualTo("txn.status");
    }

    @Test
    void testBankCode() {
        assertThat(AppConstants.BANK_CODE_202).isEqualTo("202");
    }

    @Test
    void testPrivateConstructor() throws Exception {
        Constructor<AppConstants> constructor = AppConstants.class.getDeclaredConstructor();
        constructor.setAccessible(true);

        assertThatCode(constructor::newInstance)
                .doesNotThrowAnyException();
    }

    @Test
    void testDateFormatConstants() {
        assertThat(AppConstants.DATE_FORMAT_DD_MM_YYYY).isEqualTo("dd/MM/yyyy");
        assertThat(AppConstants.DATE_FORMAT_DD_MM_YYYY_HH_MM_SS).isEqualTo("dd/MM/yyyy HH:mm:ss");
        assertThat(AppConstants.DATE_FORMAT_YYYY_MM_DD).isEqualTo("yyyy-MM-dd");
        assertThat(AppConstants.DATE_FORMAT_HH_MM_SS).isEqualTo("HH:mm:ss");
    }

    @Test
    void testRegexValidationConstants() {
        assertThat(AppConstants.REGEX_VALIDATION.NUMBER).isEqualTo("^[0-9]*$");
        assertThat(AppConstants.REGEX_VALIDATION.LETTER).isEqualTo("^[A-Za-z]*$");
        assertThat(AppConstants.REGEX_VALIDATION.NUMBER_AND_DOT).containsPattern("\\d");
    }

    @Test
    void testBatchFieldLengthConstants() {
        assertThat(AppConstants.BATCH_FIELD_LENGTH.MAX_DEBIT_ACC_NO_LENGTH).isEqualTo(14);
        assertThat(AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH).isEqualTo(20);
        assertThat(AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_NAME_LENGTH).isEqualTo(140);
    }

    @Test
    void testDocumentParamNameConstants() {
        assertThat(AppConstants.DOCUMENT_PARAM_NAME.PAYER_NAME).isEqualTo("payerName");
        assertThat(AppConstants.DOCUMENT_PARAM_NAME.TAX_CODE).isEqualTo("taxCode");
        assertThat(AppConstants.DOCUMENT_PARAM_NAME.PAYER_ADDR).isEqualTo("payerAddr");
    }

    @Test
    void testResponseCodeFieldCodeMap() {
        // Just check a few mappings
        assertThat(AppConstants.RESPONSE_CODE_FIELD_CODE_MAP)
            .containsEntry(
                com.bidv.ibank.dvc.util.constant.ResponseCode.DEBIT_ACCNO_REQUIRED.code(),
                AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO
            );
        assertThat(AppConstants.RESPONSE_CODE_FIELD_CODE_MAP)
            .containsEntry(
                com.bidv.ibank.dvc.util.constant.ResponseCode.PAYER_NAME_REQUIRED.code(),
                AppConstants.BATCH_FIELD_CODE.PAYER_NAME
            );
    }
}