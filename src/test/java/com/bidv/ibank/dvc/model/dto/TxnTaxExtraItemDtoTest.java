package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnTaxExtraItemDtoTest {

    private TxnTaxExtraItemDto dto;
    private LocalDate declarationDate;

    @BeforeEach
    void setUp() {
        declarationDate = LocalDate.of(2024, 3, 21);
        dto = TxnTaxExtraItemDto.builder()
                .eiTypeCode("EI001")
                .taxTypeCode("TAX001")
                .ccCode("CC001")
                .chapterCode("CH001")
                .ecCode("EC001")
                .amount("1000000")
                .ccy("VND")
                .declarationDate(declarationDate)
                .declarationNo("DEC123")
                .transDesc("Test Transaction")
                .ccName("CC Name")
                .chapterName("Chapter Name")
                .ecName("EC Name")
                .eiTypeName("EI Type Name")
                .taxTypeName("Tax Type Name")
                .build();
    }

    @Test
    void testGetCcName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("CC001", "CC Name"))
                    .thenReturn("Translated CC Name");

            assertEquals("Translated CC Name", dto.getCcName());
        }
    }

    @Test
    void testGetChapterName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("CH001", "Chapter Name"))
                    .thenReturn("Translated Chapter Name");

            assertEquals("Translated Chapter Name", dto.getChapterName());
        }
    }

    @Test
    void testGetEcName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("EC001", "EC Name"))
                    .thenReturn("Translated EC Name");

            assertEquals("Translated EC Name", dto.getEcName());
        }
    }

    @Test
    void testGetEiTypeName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("EI001", "EI Type Name"))
                    .thenReturn("Translated EI Type Name");

            assertEquals("Translated EI Type Name", dto.getEiTypeName());
        }
    }

    @Test
    void testGetTaxTypeName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("TAX001", "Tax Type Name"))
                    .thenReturn("Translated Tax Type Name");

            assertEquals("Translated Tax Type Name", dto.getTaxTypeName());
        }
    }

    @Test
    void testInheritedFields() {
        assertEquals("EI001", dto.getEiTypeCode());
        assertEquals("TAX001", dto.getTaxTypeCode());
        assertEquals("CC001", dto.getCcCode());
        assertEquals("CH001", dto.getChapterCode());
        assertEquals("EC001", dto.getEcCode());
        assertEquals("1000000", dto.getAmount());
        assertEquals("VND", dto.getCcy());
        assertEquals(declarationDate, dto.getDeclarationDate());
        assertEquals("DEC123", dto.getDeclarationNo());
        assertEquals("Test Transaction", dto.getTransDesc());
    }

    @Test
    void testNoArgsConstructor() {
        TxnTaxExtraItemDto emptyDto = new TxnTaxExtraItemDto();

        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(null);

            assertNull(emptyDto.getCcName());
            assertNull(emptyDto.getChapterName());
            assertNull(emptyDto.getEcName());
            assertNull(emptyDto.getEiTypeName());
            assertNull(emptyDto.getTaxTypeName());
        }
    }

    @Test
    void testAllArgsConstructor() {
        TxnTaxExtraItemDto fullDto = TxnTaxExtraItemDto.builder()
                .ccCode("CC001")
                .chapterCode("CH001")
                .ecCode("EC001")
                .eiTypeCode("EI001")
                .taxTypeCode("TAX001")
                .ccName("CC Name")
                .chapterName("Chapter Name")
                .ecName("EC Name")
                .eiTypeName("EI Type Name")
                .taxTypeName("Tax Type Name")
                .build();

        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("CC001", "CC Name"))
                    .thenReturn("Translated CC Name");
            mockedTranslator.when(() -> Translator.toLocale("CH001", "Chapter Name"))
                    .thenReturn("Translated Chapter Name");
            mockedTranslator.when(() -> Translator.toLocale("EC001", "EC Name"))
                    .thenReturn("Translated EC Name");
            mockedTranslator.when(() -> Translator.toLocale("EI001", "EI Type Name"))
                    .thenReturn("Translated EI Type Name");
            mockedTranslator.when(() -> Translator.toLocale("TAX001", "Tax Type Name"))
                    .thenReturn("Translated Tax Type Name");

            assertEquals("Translated CC Name", fullDto.getCcName());
            assertEquals("Translated Chapter Name", fullDto.getChapterName());
            assertEquals("Translated EC Name", fullDto.getEcName());
            assertEquals("Translated EI Type Name", fullDto.getEiTypeName());
            assertEquals("Translated Tax Type Name", fullDto.getTaxTypeName());
        }
    }
}