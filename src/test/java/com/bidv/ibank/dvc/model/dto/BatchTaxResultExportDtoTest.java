package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class BatchTaxResultExportDtoTest {

    private BatchTaxResultExportDto batchTaxResultExportDto;

    @BeforeEach
    void setUp() {
        batchTaxResultExportDto = new BatchTaxResultExportDto();
    }

    @Test
    void testDefaultConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() to handle null status
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenReturn(null);

            // When
            BatchTaxResultExportDto dto = new BatchTaxResultExportDto();

            // Then
            assertNotNull(dto);
            assertNull(dto.getStatus());
            assertNull(dto.getDescription());
            assertNull(dto.getDeclarationNo());
            assertNull(dto.getDeclarationYear());
            assertNull(dto.getErrCodes());
            assertNull(dto.getTccErrMsg());
        }
    }

    @Test
    void testAllArgsConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() for getStatus() method
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenAnswer(invocation -> "Translated: " + invocation.getArgument(0));

            // Mock Translator.toLocale() for error codes in getDescription() method
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> "Error: " + invocation.getArgument(1));

            // Given
            String status = "SUCCESS";
            String description = "Processing completed successfully";
            String declarationNo = "30644771632";
            String declarationYear = "2024";
            String errCodes = "ERR001,ERR002";
            String tccErrMsg = "TCC error message";

            // When
            BatchTaxResultExportDto dto = new BatchTaxResultExportDto(
                    status, description, declarationNo, declarationYear, errCodes, tccErrMsg
            );

            // Then
            assertNotNull(dto);
            assertNotNull(dto.getStatus()); // getStatus() returns translated value
            assertEquals("Error: ERR001;Error: ERR002;TCC error message", dto.getDescription()); // getDescription() processes errCodes and tccErrMsg
            assertEquals(declarationNo, dto.getDeclarationNo());
            assertEquals(declarationYear, dto.getDeclarationYear());
            assertEquals(errCodes, dto.getErrCodes());
            assertEquals(tccErrMsg, dto.getTccErrMsg());
        }
    }

    @Test
    void testBuilder() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() for getStatus() method
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenAnswer(invocation -> "Translated: " + invocation.getArgument(0));

            // Given
            String status = "FAILED";
            String description = "Processing failed";
            String declarationNo = "20231225001";
            String declarationYear = "2023";

            // When
            BatchTaxResultExportDto dto = BatchTaxResultExportDto.builder()
                    .status(status)
                    .description(description)
                    .declarationNo(declarationNo)
                    .declarationYear(declarationYear)
                    .build();

            // Then
            assertNotNull(dto);
            assertNotNull(dto.getStatus()); // getStatus() returns translated value
            assertNull(dto.getDescription()); // getDescription() returns null when errCodes is null and tccErrMsg is null
            assertEquals(declarationNo, dto.getDeclarationNo());
            assertEquals(declarationYear, dto.getDeclarationYear());
        }
    }

    @Test
    void testSettersAndGetters() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() for the getStatus() method
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenAnswer(invocation -> "Translated: " + invocation.getArgument(0));

            // Mock Translator.toLocale() for error codes in getDescription() method
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> "Error: " + invocation.getArgument(1));

            // Given
            String status = "PENDING";
            String description = "Processing in progress";
            String declarationNo = "98765432109";
            String declarationYear = "2025";
            String errCodes = "ERR003";
            String tccErrMsg = "Connection timeout";

            // When
            batchTaxResultExportDto.setStatus(status);
            batchTaxResultExportDto.setDescription(description);
            batchTaxResultExportDto.setDeclarationNo(declarationNo);
            batchTaxResultExportDto.setDeclarationYear(declarationYear);
            batchTaxResultExportDto.setErrCodes(errCodes);
            batchTaxResultExportDto.setTccErrMsg(tccErrMsg);

            // Then
            assertNotNull(batchTaxResultExportDto.getStatus()); // getStatus() returns translated value, not original
            assertEquals("Error: ERR003;Connection timeout", batchTaxResultExportDto.getDescription()); // getDescription() processes errCodes and tccErrMsg
            assertEquals(declarationNo, batchTaxResultExportDto.getDeclarationNo());
            assertEquals(declarationYear, batchTaxResultExportDto.getDeclarationYear());
            assertEquals(errCodes, batchTaxResultExportDto.getErrCodes());
            assertEquals(tccErrMsg, batchTaxResultExportDto.getTccErrMsg());
        }
    }

    @Test
    void testGetStatusWithTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return translated status
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenReturn("Thành công");

            // Given
            batchTaxResultExportDto.setStatus("SUCCESS");

            // When
            String result = batchTaxResultExportDto.getStatus();

            // Then
            assertEquals("Thành công", result);
        }
    }

    @Test
    void testGetDescriptionWithErrCodesOnly() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return error messages
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> "Error: " + invocation.getArgument(1));

            // Given
            batchTaxResultExportDto.setErrCodes("ERR001,ERR002");
            batchTaxResultExportDto.setTccErrMsg(null);

            // When
            String result = batchTaxResultExportDto.getDescription();

            // Then
            assertEquals("Error: ERR001;Error: ERR002", result);
        }
    }

    @Test
    void testGetDescriptionWithTccErrMsgOnly() {
        // Given
        batchTaxResultExportDto.setErrCodes(null);
        batchTaxResultExportDto.setTccErrMsg("TCC connection failed");

        // When
        String result = batchTaxResultExportDto.getDescription();

        // Then
        assertEquals("TCC connection failed", result);
    }

    @Test
    void testGetDescriptionWithBothErrCodesAndTccErrMsg() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return error messages
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> "Error: " + invocation.getArgument(1));

            // Given
            batchTaxResultExportDto.setErrCodes("ERR001,ERR002");
            batchTaxResultExportDto.setTccErrMsg("TCC timeout");

            // When
            String result = batchTaxResultExportDto.getDescription();

            // Then
            assertEquals("Error: ERR001;Error: ERR002;TCC timeout", result);
        }
    }

    @Test
    void testGetDescriptionWithBlankErrCodesAndTccErrMsg() {
        // Given
        batchTaxResultExportDto.setErrCodes("");
        batchTaxResultExportDto.setTccErrMsg("TCC service unavailable");

        // When
        String result = batchTaxResultExportDto.getDescription();

        // Then
        assertEquals("TCC service unavailable", result);
    }

    @Test
    void testGetDescriptionWithSingleErrorCode() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return error message
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Validation failed");

            // Given
            batchTaxResultExportDto.setErrCodes("ERR001");
            batchTaxResultExportDto.setTccErrMsg(null);

            // When
            String result = batchTaxResultExportDto.getDescription();

            // Then
            assertEquals("Validation failed", result);
        }
    }

    @Test
    void testGetDescriptionWithMultipleErrorCodes() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return specific error messages
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> {
                        String code = invocation.getArgument(1).toString();
                        switch (code) {
                            case "ERR001": return "Invalid tax code";
                            case "ERR002": return "Missing amount";
                            case "ERR003": return "Invalid date format";
                            default: return "Unknown error: " + code;
                        }
                    });

            // Given
            batchTaxResultExportDto.setErrCodes("ERR001,ERR002,ERR003");
            batchTaxResultExportDto.setTccErrMsg("");

            // When
            String result = batchTaxResultExportDto.getDescription();

            // Then
            assertEquals("Invalid tax code;Missing amount;Invalid date format", result);
        }
    }

    @Test
    void testGetDescriptionWithWhitespaceInErrorCodes() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return error messages
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> "Error: " + invocation.getArgument(1));

            // Given - Error codes with whitespace
            batchTaxResultExportDto.setErrCodes(" ERR001 , ERR002 , ERR003 ");
            batchTaxResultExportDto.setTccErrMsg(null);

            // When
            String result = batchTaxResultExportDto.getDescription();

            // Then - The split preserves whitespace around error codes
            assertEquals("Error:  ERR001 ;Error:  ERR002 ;Error:  ERR003 ", result);
        }
    }

    @Test
    void testSeverity() {
        // When
        String severity = batchTaxResultExportDto.getSeverity();

        // Then
        assertEquals("I", severity);
    }

    @Test
    void testExportableImplementation() {
        // Given
        BatchTaxResultExportDto dto = new BatchTaxResultExportDto();

        // When & Then
        assertTrue(dto instanceof com.bidv.ibank.util.excel.Exportable);
        assertEquals("I", dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() for the getStatus() method called by equals()
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenReturn("SUCCESS");

            // Given
            String status = "SUCCESS";
            String declarationNo = "***********";
            String declarationYear = "2024";

            BatchTaxResultExportDto dto1 = BatchTaxResultExportDto.builder()
                    .status(status)
                    .declarationNo(declarationNo)
                    .declarationYear(declarationYear)
                    .build();

            BatchTaxResultExportDto dto2 = BatchTaxResultExportDto.builder()
                    .status(status)
                    .declarationNo(declarationNo)
                    .declarationYear(declarationYear)
                    .build();

            // Then
            assertEquals(dto1, dto2);
            assertEquals(dto1.hashCode(), dto2.hashCode());
        }
    }

    @Test
    void testToString() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() for the getStatus() method called by toString()
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenReturn("PROCESSING");

            // Given
            batchTaxResultExportDto.setStatus("PROCESSING");
            batchTaxResultExportDto.setDeclarationNo("30644771632");
            batchTaxResultExportDto.setDeclarationYear("2024");

            // When
            String result = batchTaxResultExportDto.toString();

            // Then
            assertNotNull(result);
            assertTrue(result.contains("BatchTaxResultExportDto"));
            assertTrue(result.contains("status"));
            assertTrue(result.contains("declarationNo"));
            assertTrue(result.contains("declarationYear"));
        }
    }

    @Test
    void testComplexTaxDeclarationScenario() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator for realistic tax processing scenario
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenReturn("Đã xử lý");
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> {
                        String code = invocation.getArgument(1).toString();
                        switch (code) {
                            case "TAX001": return "Mã số thuế không hợp lệ";
                            case "TAX002": return "Số tiền vượt quá giới hạn";
                            case "TAX003": return "Ngày tờ khai không đúng định dạng";
                            default: return "Lỗi: " + code;
                        }
                    });

            // Given - Complex tax declaration processing result
            BatchTaxResultExportDto complexDto = BatchTaxResultExportDto.builder()
                    .status("PROCESSED")
                    .declarationNo("30644771632")
                    .declarationYear("2024")
                    .errCodes("TAX001,TAX002,TAX003")
                    .tccErrMsg("Kết nối TCC gián đoạn")
                    .build();

            // When
            String status = complexDto.getStatus();
            String description = complexDto.getDescription();

            // Then
            assertEquals("Đã xử lý", status);
            assertEquals("Mã số thuế không hợp lệ;Số tiền vượt quá giới hạn;Ngày tờ khai không đúng định dạng;Kết nối TCC gián đoạn", description);
            assertEquals("30644771632", complexDto.getDeclarationNo());
            assertEquals("2024", complexDto.getDeclarationYear());
        }
    }

    @Test
    void testNullAndEmptyHandling() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() to handle null status
            mockedTranslator.when(() -> Translator.toLocale(anyString()))
                    .thenReturn(null); // Return null for null status

            // Given
            BatchTaxResultExportDto dto = BatchTaxResultExportDto.builder()
                    .status(null)
                    .description(null)
                    .declarationNo("")
                    .declarationYear("")
                    .errCodes(null)
                    .tccErrMsg("")
                    .build();

            // When & Then
            assertNull(dto.getStatus());
            assertEquals("", dto.getDescription()); // getDescription() returns tccErrMsg which is "" when errCodes is null
            assertEquals("", dto.getDeclarationNo());
            assertEquals("", dto.getDeclarationYear());
            assertNull(dto.getErrCodes());
            assertEquals("", dto.getTccErrMsg());
        }
    }

    @Test
    void testDifferentStatusScenarios() {
        // Test various batch processing statuses
        String[] statuses = {"SUCCESS", "FAILED", "PROCESSING", "PENDING", "COMPLETED", "ERROR"};

        for (String status : statuses) {
            try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
                // Mock status translation
                mockedTranslator.when(() -> Translator.toLocale(anyString()))
                        .thenReturn("Translated: " + status);

                // Given
                BatchTaxResultExportDto dto = BatchTaxResultExportDto.builder()
                        .status(status)
                        .build();

                // When
                String translatedStatus = dto.getStatus();

                // Then
                assertEquals("Translated: " + status, translatedStatus);
            }
        }
    }
}
