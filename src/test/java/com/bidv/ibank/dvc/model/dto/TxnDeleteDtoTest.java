package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class TxnDeleteDtoTest {

    private TxnDeleteDto txnDeleteDto;

    @BeforeEach
    void setUp() {
        txnDeleteDto = new TxnDeleteDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnDeleteDto dto = new TxnDeleteDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getCode());
        assertNull(dto.getTxnId());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String code = "DELETE_SUCCESS";
        String txnId = "TXN123456";

        // When
        TxnDeleteDto dto = new TxnDeleteDto(code, txnId);

        // Then
        assertEquals(code, dto.getCode());
        assertEquals(txnId, dto.getTxnId());
    }

    @Test
    void testBuilderPattern() {
        // Given
        String code = "DELETE_SUCCESS";
        String txnId = "TXN123456";

        // When
        TxnDeleteDto dto = TxnDeleteDto.builder()
                .code(code)
                .txnId(txnId)
                .build();

        // Then
        assertEquals(code, dto.getCode());
        assertEquals(txnId, dto.getTxnId());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String code = "DELETE_SUCCESS";
        String txnId = "TXN123456";

        // When
        txnDeleteDto.setCode(code);
        txnDeleteDto.setTxnId(txnId);

        // Then
        assertEquals(code, txnDeleteDto.getCode());
        assertEquals(txnId, txnDeleteDto.getTxnId());
    }

    @Test
    void testGetMessage_WithValidCode() {
        // Given
        String code = "DELETE_SUCCESS";
        String expectedMessage = "Transaction deleted successfully";
        String expectedKey = AppConstants.LANGUAGE.RESPONSE_CODE + "." + code;

        txnDeleteDto.setCode(code);

        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(expectedKey, code))
                    .thenReturn(expectedMessage);

            // When
            String result = txnDeleteDto.getMessage();

            // Then
            assertEquals(expectedMessage, result);
            mockedTranslator.verify(() -> Translator.toLocale(expectedKey, code));
        }
    }

    @Test
    void testGetMessage_WithNullCode() {
        // Given
        String expectedKey = AppConstants.LANGUAGE.RESPONSE_CODE + "." + null;
        String expectedMessage = "Unknown error";

        txnDeleteDto.setCode(null);

        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(expectedKey, (String) null))
                    .thenReturn(expectedMessage);

            // When
            String result = txnDeleteDto.getMessage();

            // Then
            assertEquals(expectedMessage, result);
            mockedTranslator.verify(() -> Translator.toLocale(expectedKey, (String) null));
        }
    }

    @Test
    void testGetMessage_TranslatorReturnsFallback() {
        // Given
        String code = "UNKNOWN_CODE";
        String expectedKey = AppConstants.LANGUAGE.RESPONSE_CODE + "." + code;

        txnDeleteDto.setCode(code);

        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(expectedKey, code))
                    .thenReturn(code); // Translator returns the fallback

            // When
            String result = txnDeleteDto.getMessage();

            // Then
            assertEquals(code, result);
            mockedTranslator.verify(() -> Translator.toLocale(expectedKey, code));
        }
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String code = "DELETE_SUCCESS";
        String txnId = "TXN123456";

        TxnDeleteDto dto1 = TxnDeleteDto.builder()
                .code(code)
                .txnId(txnId)
                .build();

        TxnDeleteDto dto2 = TxnDeleteDto.builder()
                .code(code)
                .txnId(txnId)
                .build();

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String code = "DELETE_SUCCESS";
        String txnId = "TXN123456";

        txnDeleteDto.setCode(code);
        txnDeleteDto.setTxnId(txnId);

        // When
        String result = txnDeleteDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("code"));
        assertTrue(result.contains("txnId"));
    }
}
