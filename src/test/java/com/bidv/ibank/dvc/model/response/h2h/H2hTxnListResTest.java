package com.bidv.ibank.dvc.model.response.h2h;

import com.bidv.ibank.framework.util.Translator;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;

class H2hTxnListResTest {

    @Test
    void testBuilderAndGetters() {
        LocalDateTime now = LocalDateTime.now();
        H2hTxnListRes res = H2hTxnListRes.builder()
                .txnId("txn1")
                .debitAccNo("acc1")
                .taxCode("tax1")
                .declarationNo("decl1")
                .amount(BigDecimal.TEN)
                .ccy("VND")
                .batchNo("batch1")
                .status("SUCCESS")
                .createdDate(now)
                .createdBy("user1")
                .txnType("PAYMENT")
                .txnItemId("item1")
                .build();

        assertEquals("txn1", res.getTxnId());
        assertEquals("acc1", res.getDebitAccNo());
        assertEquals("tax1", res.getTaxCode());
        assertEquals("decl1", res.getDeclarationNo());
        assertEquals(BigDecimal.TEN, res.getAmount());
        assertEquals("VND", res.getCcy());
        assertEquals("batch1", res.getBatchNo());
        assertEquals("SUCCESS", res.getStatus());
        assertEquals(now, res.getCreatedDate());
        assertEquals("user1", res.getCreatedBy());
        assertEquals("PAYMENT", res.getTxnType());
        assertEquals("item1", res.getTxnItemId());
    }

    @Test
    void testGetTxnTypeName() {
        H2hTxnListRes res = H2hTxnListRes.builder().txnType("PAYMENT").build();
        try (MockedStatic<Translator> translatorMock = org.mockito.Mockito.mockStatic(Translator.class)) {
            translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Thanh toán");
            String name = res.getTxnTypeName();
            assertEquals("Thanh toán", name);
        }
    }
}