package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;

@DisplayName("BatchDetailRes Tests")
class BatchDetailResTest {

    private BatchItemDetailDto validItem1;
    private BatchItemDetailDto validItem2;
    private BatchItemDetailDto invalidItem1;
    private BatchItemDetailDto invalidItem2;
    private List<BatchItemDetailDto> validItemsList;
    private List<BatchItemDetailDto> invalidItemsList;

    @BeforeEach
    void setUp() {
        validItem1 = BatchItemDetailDto.builder()
                .batchItemId("VALID001")
                .debitAccNo("**********")
                .taxCode("TAX001")
                .payerName("John Doe")
                .build();

        validItem2 = BatchItemDetailDto.builder()
                .batchItemId("VALID002")
                .debitAccNo("**********")
                .taxCode("TAX002")
                .payerName("Jane Doe")
                .build();

        invalidItem1 = BatchItemDetailDto.builder()
                .batchItemId("INVALID001")
                .debitAccNo("9876543210")
                .taxCode("TAX003")
                .payerName("Alice Smith")
                .build();

        invalidItem2 = BatchItemDetailDto.builder()
                .batchItemId("INVALID002")
                .debitAccNo("9876543211")
                .taxCode("TAX004")
                .payerName("Bob Smith")
                .build();

        validItemsList = Arrays.asList(validItem1, validItem2);
        invalidItemsList = Arrays.asList(invalidItem1, invalidItem2);
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("NoArgsConstructor should create BatchDetailRes with empty lists")
        void noArgsConstructor_ShouldCreateBatchDetailResWithEmptyLists() {
            BatchDetailRes res = new BatchDetailRes();

            assertThat(res.getValidItems()).isNotNull().isEmpty();
            assertThat(res.getInvalidItems()).isNotNull().isEmpty();
            assertThat(res.getTotalRecords()).isZero();
            assertThat(res.getTotalValidRecords()).isZero();
            assertThat(res.getTotalInvalidRecords()).isZero();
        }

        @Test
        @DisplayName("All args constructor should create BatchDetailRes with provided lists")
        void allArgsConstructor_ShouldCreateBatchDetailResWithProvidedLists() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(validItemsList)
                    .invalidItems(invalidItemsList)
                    .fileSize(1000L)
                    .fileName("test.csv")
                    .batchNo("BDR20240321123456")
                    .batchType("PAYMENT")
                    .build();

            assertThat(res.getValidItems()).isEqualTo(validItemsList);
            assertThat(res.getInvalidItems()).isEqualTo(invalidItemsList);
            assertThat(res.getTotalRecords()).isEqualTo(4);
            assertThat(res.getTotalValidRecords()).isEqualTo(2);
            assertThat(res.getTotalInvalidRecords()).isEqualTo(2);
            assertThat(res.getFileSize()).isEqualTo(1000L);
            assertThat(res.getFileName()).isEqualTo("test.csv");
            assertThat(res.getBatchNo()).isEqualTo("BDR20240321123456");
            assertThat(res.getBatchType()).isEqualTo("PAYMENT");
        }
    }

    @Nested
    @DisplayName("Builder Tests")
    class BuilderTests {

        @Test
        @DisplayName("Builder with no values should create BatchDetailRes with empty lists")
        void builder_WithNoValues_ShouldCreateBatchDetailResWithEmptyLists() {
            BatchDetailRes res = BatchDetailRes.builder().build();

            assertThat(res.getValidItems()).isNotNull().isEmpty();
            assertThat(res.getInvalidItems()).isNotNull().isEmpty();
            assertThat(res.getTotalRecords()).isZero();
            assertThat(res.getTotalValidRecords()).isZero();
            assertThat(res.getTotalInvalidRecords()).isZero();
        }

        @Test
        @DisplayName("Builder with null values should create BatchDetailRes with empty lists")
        void builder_WithNullValues_ShouldCreateBatchDetailResWithEmptyLists() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(null)
                    .invalidItems(null)
                    .build();

            assertThat(res.getValidItems()).isNull();
            assertThat(res.getInvalidItems()).isNull();
            assertThat(res.getTotalRecords()).isZero();
            assertThat(res.getTotalValidRecords()).isZero();
            assertThat(res.getTotalInvalidRecords()).isZero();
        }

        @Test
        @DisplayName("Builder with provided lists should create BatchDetailRes with those lists")
        void builder_WithProvidedLists_ShouldCreateBatchDetailResWithThoseLists() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(validItemsList)
                    .invalidItems(invalidItemsList)
                    .build();

            assertThat(res.getValidItems()).isEqualTo(validItemsList);
            assertThat(res.getInvalidItems()).isEqualTo(invalidItemsList);
            assertThat(res.getTotalRecords()).isEqualTo(4);
            assertThat(res.getTotalValidRecords()).isEqualTo(2);
            assertThat(res.getTotalInvalidRecords()).isEqualTo(2);
        }

        @Test
        @DisplayName("Builder with all values should create BatchDetailRes with provided values")
        void builder_WithAllValues_ShouldCreateBatchDetailResWithProvidedValues() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(validItemsList)
                    .invalidItems(invalidItemsList)
                    .fileSize(1000L)
                    .fileName("test.csv")
                    .batchNo("BDR20240321123456")
                    .batchType("PAYMENT")
                    .build();

            assertThat(res.getValidItems()).isEqualTo(validItemsList);
            assertThat(res.getInvalidItems()).isEqualTo(invalidItemsList);
            assertThat(res.getTotalRecords()).isEqualTo(4);
            assertThat(res.getTotalValidRecords()).isEqualTo(2);
            assertThat(res.getTotalInvalidRecords()).isEqualTo(2);
            assertThat(res.getFileSize()).isEqualTo(1000L);
            assertThat(res.getFileName()).isEqualTo("test.csv");
            assertThat(res.getBatchNo()).isEqualTo("BDR20240321123456");
        }
    }

    @Nested
    @DisplayName("Getter and Setter Tests")
    class GetterSetterTests {

        @Test
        @DisplayName("Setters and Getters should work correctly")
        void settersAndGetters_ShouldWorkCorrectly() {
            BatchDetailRes res = new BatchDetailRes();

            res.setValidItems(validItemsList);
            res.setInvalidItems(invalidItemsList);

            assertThat(res.getValidItems()).isEqualTo(validItemsList);
            assertThat(res.getInvalidItems()).isEqualTo(invalidItemsList);
            assertThat(res.getTotalRecords()).isEqualTo(4);
            assertThat(res.getTotalValidRecords()).isEqualTo(2);
            assertThat(res.getTotalInvalidRecords()).isEqualTo(2);
        }

        @Test
        @DisplayName("Setting null should work correctly")
        void settingNull_ShouldWorkCorrectly() {
            BatchDetailRes res = new BatchDetailRes();
            res.setValidItems(null);
            res.setInvalidItems(null);

            assertThat(res.getValidItems()).isNull();
            assertThat(res.getInvalidItems()).isNull();
            assertThat(res.getTotalRecords()).isZero();
            assertThat(res.getTotalValidRecords()).isZero();
            assertThat(res.getTotalInvalidRecords()).isZero();
        }
    }

    @Nested
    @DisplayName("Total Records Calculation Tests")
    class TotalRecordsCalculationTests {

        @Test
        @DisplayName("getTotalValidRecords should handle null validItems")
        void getTotalValidRecords_ShouldHandleNullValidItems() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(null)
                    .build();

            assertThat(res.getTotalValidRecords()).isZero();
        }

        @Test
        @DisplayName("getTotalInvalidRecords should handle null invalidItems")
        void getTotalInvalidRecords_ShouldHandleNullInvalidItems() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .invalidItems(null)
                    .build();

            assertThat(res.getTotalInvalidRecords()).isZero();
        }

        @Test
        @DisplayName("getTotalRecords should handle both lists null")
        void getTotalRecords_ShouldHandleBothListsNull() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(null)
                    .invalidItems(null)
                    .build();

            assertThat(res.getTotalRecords()).isZero();
        }

        @Test
        @DisplayName("getTotalRecords should handle one list null")
        void getTotalRecords_ShouldHandleOneListNull() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(validItemsList)
                    .invalidItems(null)
                    .build();

            assertThat(res.getTotalRecords()).isEqualTo(2);
        }

        @Test
        @DisplayName("getTotalRecords should calculate sum correctly")
        void getTotalRecords_ShouldCalculateSumCorrectly() {
            BatchDetailRes res = BatchDetailRes.builder()
                    .validItems(validItemsList)
                    .invalidItems(invalidItemsList)
                    .build();

            assertThat(res.getTotalRecords()).isEqualTo(4);
        }
    }
}