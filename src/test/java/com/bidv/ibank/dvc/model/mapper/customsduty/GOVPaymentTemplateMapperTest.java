package com.bidv.ibank.dvc.model.mapper.customsduty;

import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.param.*;
import com.bidv.ibank.dvc.model.request.TemplateSaveReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.i18n.CompositeMessageSource;
import com.bidv.ibank.framework.util.Translator;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GOVPaymentTemplateMapperTest {

    @InjectMocks
    private GOVPaymentTemplateMapper mapper;

    @Mock
    private CompositeMessageSource messageSource;

    private GOVPaymentTransactionEntity txnEntity;
    private GOVPaymentTemplateEntity templateEntity;
    private GOVPaymentItemEntity txnItemEntity;
    private GOVPaymentTemplateItemEntity templateItemEntity;
    private TemplateSaveReq templateSaveReq;

    @BeforeEach
    void setUp() {
        // Setup transaction entity
        txnEntity = GOVPaymentTransactionEntity.builder()
                .taxCode("**********")
                .altTaxCode("**********")
                .payerName("Test Payer")
                .altPayerName("Alt Payer")
                .payerAddr("Test Address")
                .altPayerAddr("Alt Address")
                .payerType(PayerTypeEnum.BUSINESS)
                .shkb("SHKB")
                .maDbhc("DBHC")
                .maCqthu("CQTHU")
                .maTk("TK001")
                .build();

        // Setup template save request
        templateSaveReq = TemplateSaveReq.builder()
                .templateName("Test Template")
                .isPublic(true)
                .build();

        // Setup transaction item entity
        txnItemEntity = GOVPaymentItemEntity.builder()
                .declarationNo("DEC123")
                .declarationDate(LocalDate.now())
                .maNdkt("NDKT")
                .maChuong("CHU")
                .maSthue("STHUE")
                .maLh("LH")
                .maLthq("LTHQ")
                .amount(BigDecimal.valueOf(1000))
                .ccy("VND")
                .transDesc("Test Transaction")
                .build();

        // Setup template entity with all related entities
        TccDmKhobacEntity khobacEntity = new TccDmKhobacEntity();
        khobacEntity.setTen("Treasury Name");

        TccDmDbhcEntity dbhcEntity = new TccDmDbhcEntity();
        dbhcEntity.setTen("Area Name");

        TccDmCqthuEntity cqthuEntity = new TccDmCqthuEntity();
        cqthuEntity.setTen("Auth Name");

        TccDmTkNsnnEntity tkNsnnEntity = new TccDmTkNsnnEntity();
        tkNsnnEntity.setTen("Account Name");

        templateEntity = GOVPaymentTemplateEntity.builder()
                .id(UUID.randomUUID().toString())
                .name("Test Template")
                .taxCode("**********")
                .altTaxCode("**********")
                .payerName("Test Payer")
                .altPayerName("Alt Payer")
                .payerAddr("Test Address")
                .altPayerAddr("Alt Address")
                .payerType(PayerTypeEnum.BUSINESS)
                .shkb("SHKB")
                .maDbhc("DBHC")
                .maCqthu("CQTHU")
                .maTk("TK001")
                .isPublic(true)
                .build();

        templateEntity.setTccDmKhobacEntity(khobacEntity);
        templateEntity.setTccDmDbhcEntity(dbhcEntity);
        templateEntity.setTccDmCqthuEntity(cqthuEntity);
        templateEntity.setTccDmTkNsnnEntity(tkNsnnEntity);

        // Setup template item entity with all related entities
        TccDmChuongEntity chuongEntity = new TccDmChuongEntity();
        chuongEntity.setTen("Chapter Name");

        TccDmNdktEntity ndktEntity = new TccDmNdktEntity();
        ndktEntity.setTen("Economic Content");

        TccDmLhxnkEntity lhxnkEntity = new TccDmLhxnkEntity();
        lhxnkEntity.setTen("Export Import Type");

        TccDmSthueHqaEntity sthueEntity = new TccDmSthueHqaEntity();
        sthueEntity.setTenSthue("Tax Type");

        templateItemEntity = GOVPaymentTemplateItemEntity.builder()
                .id(UUID.randomUUID().toString())
                .templateId(templateEntity.getId())
                .declarationNo("DEC123")
                .declarationDate(LocalDate.now())
                .maNdkt("NDKT")
                .maChuong("CHU")
                .maSthue("STHUE")
                .maLh("LH")
                .maLthq("LTHQ")
                .amount(BigDecimal.valueOf(1000))
                .ccy("VND")
                .transDesc("Test Transaction")
                .build();

        templateItemEntity.setTccDmChuongEntity(chuongEntity);
        templateItemEntity.setTccDmNdktEntity(ndktEntity);
        templateItemEntity.setTccDmLhxnkEntity(lhxnkEntity);
        templateItemEntity.setTccDmSthueHqaEntity(sthueEntity);

        Set<GOVPaymentTemplateItemEntity> items = new LinkedHashSet<>();
        items.add(templateItemEntity);
        templateEntity.setGovPaymentTemplateItemList(items);
    }

    @Test
    void toDto_shouldThrowUnsupportedOperationException() {
        assertThrows(UnsupportedOperationException.class, () -> mapper.toDto(new Object()));
    }

    @Test
    void toEntity_withObjectParam_shouldThrowUnsupportedOperationException() {
        assertThrows(UnsupportedOperationException.class, () -> mapper.toEntity(new Object()));
    }

    @Test
    void toEntity_withTransactionAndRequest_shouldMapCorrectly() {
        GOVPaymentTemplateEntity result = mapper.toEntity(txnEntity, templateSaveReq);

        assertNotNull(result);
        assertEquals(templateSaveReq.getTemplateName(), result.getName());
        assertEquals(txnEntity.getTaxCode(), result.getTaxCode());
        assertEquals(txnEntity.getAltTaxCode(), result.getAltTaxCode());
        assertEquals(txnEntity.getPayerName(), result.getPayerName());
        assertEquals(txnEntity.getAltPayerName(), result.getAltPayerName());
        assertEquals(txnEntity.getPayerAddr(), result.getPayerAddr());
        assertEquals(txnEntity.getAltPayerAddr(), result.getAltPayerAddr());
        assertEquals(txnEntity.getPayerType(), result.getPayerType());
        assertEquals(txnEntity.getShkb(), result.getShkb());
        assertEquals(txnEntity.getMaDbhc(), result.getMaDbhc());
        assertEquals(txnEntity.getMaCqthu(), result.getMaCqthu());
        assertEquals(txnEntity.getMaTk(), result.getMaTk());
        assertEquals(templateSaveReq.getIsPublic(), result.getIsPublic());
    }

    @Test
    void toItemEntity_shouldMapCorrectly() {
        String templateId = UUID.randomUUID().toString();
        GOVPaymentTemplateItemEntity result = mapper.toItemEntity(txnItemEntity, templateId);

        assertNotNull(result);
        assertEquals(templateId, result.getTemplateId());
        assertEquals(txnItemEntity.getDeclarationNo(), result.getDeclarationNo());
        assertEquals(txnItemEntity.getDeclarationDate(), result.getDeclarationDate());
        assertEquals(txnItemEntity.getMaNdkt(), result.getMaNdkt());
        assertEquals(txnItemEntity.getMaChuong(), result.getMaChuong());
        assertEquals(txnItemEntity.getMaSthue(), result.getMaSthue());
        assertEquals(txnItemEntity.getMaLh(), result.getMaLh());
        assertEquals(txnItemEntity.getMaLthq(), result.getMaLthq());
        assertEquals(txnItemEntity.getAmount(), result.getAmount());
        assertEquals(txnItemEntity.getCcy(), result.getCcy());
        assertEquals(txnItemEntity.getTransDesc(), result.getTransDesc());
    }

    @Test
    void toListDto_shouldMapCorrectly() {
        ReflectionTestUtils.setField(Translator.class, "messageSource", messageSource);

        // Mock translations to return the actual entity names
        when(messageSource.getMessage(any(), any(), any(), any())).thenAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            String defaultMessage = (String) args[2];
            return defaultMessage;
        });

        TemplateListRes result = mapper.toListDto(templateEntity);

        assertNotNull(result);
        assertEquals(templateEntity.getId(), result.getTemplateId());
        assertEquals(templateEntity.getName(), result.getTemplateName());
        assertEquals(templateEntity.getTaxCode(), result.getTaxCode());
        assertEquals(templateEntity.getAltTaxCode(), result.getAltTaxCode());
        assertEquals(templateEntity.getPayerName(), result.getPayerName());
        assertEquals(templateEntity.getAltPayerName(), result.getAltPayerName());
        assertEquals(templateEntity.getPayerAddr(), result.getPayerAddr());
        assertEquals(templateEntity.getAltPayerAddr(), result.getAltPayerAddr());
        assertEquals(templateEntity.getPayerType().getValue(), result.getPayerType());
        assertEquals(templateEntity.getShkb(), result.getTreasuryCode());
        assertEquals("Treasury Name", result.getTreasuryName());
        assertEquals(templateEntity.getMaDbhc(), result.getAdmAreaCode());
        assertEquals("Area Name", result.getAdmAreaName());
        assertEquals(templateEntity.getMaTk(), result.getRevAccCode());
        assertEquals("Account Name", result.getRevAccName());
        assertEquals(templateEntity.getMaCqthu(), result.getRevAuthCode());
        assertEquals("Auth Name", result.getRevAuthName());

        // Verify tax items
        assertNotNull(result.getTaxItems());
        assertEquals(1, result.getTaxItems().size());
        TxnTaxFullItemDto taxItem = result.getTaxItems().get(0);
        assertEquals(templateItemEntity.getDeclarationNo(), taxItem.getDeclarationNo());
        assertEquals(templateItemEntity.getMaNdkt(), taxItem.getEcCode());
        assertEquals("Economic Content", taxItem.getEcName());
        assertEquals(templateItemEntity.getMaChuong(), taxItem.getChapterCode());
        assertEquals("Chapter Name", taxItem.getChapterName());
        assertEquals(templateItemEntity.getMaSthue(), taxItem.getTaxTypeCode());
        assertEquals("Tax Type", taxItem.getTaxTypeName());
        assertEquals(templateItemEntity.getMaLh(), taxItem.getEiTypeCode());
        assertEquals("Export Import Type", taxItem.getEiTypeName());
        assertEquals(templateItemEntity.getAmount().toString(), taxItem.getAmount());
    }

    @Test
    void toListDto_withNullRelatedEntities_shouldMapCorrectly() {
        ReflectionTestUtils.setField(Translator.class, "messageSource", messageSource);
        when(messageSource.getMessage(any(), any(), any(), any())).thenAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            String defaultMessage = (String) args[2];
            return defaultMessage;
        });

        templateEntity.setTccDmKhobacEntity(null);
        templateEntity.setTccDmDbhcEntity(null);
        templateEntity.setTccDmCqthuEntity(null);
        templateEntity.setTccDmTkNsnnEntity(null);
        templateItemEntity.setTccDmChuongEntity(null);
        templateItemEntity.setTccDmNdktEntity(null);
        templateItemEntity.setTccDmLhxnkEntity(null);
        templateItemEntity.setTccDmSthueHqaEntity(null);

        TemplateListRes result = mapper.toListDto(templateEntity);

        assertNotNull(result);
        assertNull(result.getTreasuryName());
        assertNull(result.getAdmAreaName());
        assertNull(result.getRevAuthName());
        assertNull(result.getRevAccName());

        TxnTaxFullItemDto taxItem = result.getTaxItems().get(0);
        assertNull(taxItem.getChapterName());
        assertNull(taxItem.getEcName());
        assertNull(taxItem.getEiTypeName());
        assertNull(taxItem.getTaxTypeName());
    }
}