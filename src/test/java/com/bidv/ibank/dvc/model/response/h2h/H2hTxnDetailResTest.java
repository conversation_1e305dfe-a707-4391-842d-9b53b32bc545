package com.bidv.ibank.dvc.model.response.h2h;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class H2hTxnDetailResTest {

    private H2hTxnDetailRes h2hTxnDetailRes;

    @BeforeEach
    void setUp() {
        h2hTxnDetailRes = new H2hTxnDetailRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        H2hTxnDetailRes res = new H2hTxnDetailRes();

        // Then
        assertNotNull(res);
        assertNull(res.getTaxCode());
        assertNull(res.getPayerName());
        assertNull(res.getDebitAccNo());
        assertNull(res.getTxnId());
        assertNull(res.getAmount());
        assertNull(res.getCcy());
        assertNull(res.getBatchNo());
        assertNull(res.getStatus());
        assertNull(res.getCreatedDate());
        assertNull(res.getUpdatedDate());
        assertNull(res.getCreatedBy());
        assertNull(res.getTccRefNo());
        assertNull(res.getFeeTotal());
        assertNull(res.getFeeOpt());
        assertNull(res.getFeeCcy());
        assertNull(res.getOrgId());
        assertNull(res.getDescription());
        assertNull(res.getChannel());
        assertNull(res.getTaxItems());
        assertNull(res.getPayerType());
    }

    @Test
    void testNoArgsConstructor() {
        // When
        H2hTxnDetailRes res = H2hTxnDetailRes.builder().build();

        // Then
        assertNotNull(res);
        assertNull(res.getTaxCode());
        assertNull(res.getPayerName());
        assertNull(res.getAmount());
    }

    @Test
    void testBuilderPattern() {
        // Given
        String taxCode = "**********";
        String payerName = "Test Company Ltd";
        String debitAccNo = "**********";
        String txnId = "TXN001";
        String debitAccName = "Test Account Name";
        BigDecimal amount = new BigDecimal("*********");
        String ccy = "VND";
        String batchNo = "BATCH001";
        String status = "SUCCESS";
        LocalDateTime createdDate = LocalDateTime.of(2023, 1, 1, 12, 0, 0);
        LocalDateTime updatedDate = LocalDateTime.of(2023, 1, 1, 13, 0, 0);
        String createdBy = "88060ktv";
        String tccRefNo = "88060ktv";
        BigDecimal feeTotal = new BigDecimal("10000");
        String feeOpt = "Phí khoán";
        String feeCcy = "VND";
        String orgId = "************";
        String description = "Test transaction description";
        String channel = "WEB";
        Integer payerType = 1;

        // When
        H2hTxnDetailRes res = H2hTxnDetailRes.builder()
                .taxCode(taxCode)
                .payerName(payerName)
                .debitAccNo(debitAccNo)
                .txnId(txnId)
                .debitAccName(debitAccName)
                .amount(amount)
                .ccy(ccy)
                .batchNo(batchNo)
                .status(status)
                .createdDate(createdDate)
                .updatedDate(updatedDate)
                .createdBy(createdBy)
                .tccRefNo(tccRefNo)
                .feeTotal(feeTotal)
                .feeOpt(feeOpt)
                .feeCcy(feeCcy)
                .orgId(orgId)
                .description(description)
                .channel(channel)
                .payerType(payerType)
                .build();

        // Then
        assertEquals(taxCode, res.getTaxCode());
        assertEquals(payerName, res.getPayerName());
        assertEquals(debitAccNo, res.getDebitAccNo());
        assertEquals(txnId, res.getTxnId());
        assertEquals(debitAccName, res.getDebitAccName());
        assertEquals(amount, res.getAmount());
        assertEquals(ccy, res.getCcy());
        assertEquals(batchNo, res.getBatchNo());
        assertEquals(status, res.getStatus());
        assertEquals(createdDate, res.getCreatedDate());
        assertEquals(updatedDate, res.getUpdatedDate());
        assertEquals(createdBy, res.getCreatedBy());
        assertEquals(tccRefNo, res.getTccRefNo());
        assertEquals(feeTotal, res.getFeeTotal());
        assertEquals(feeOpt, res.getFeeOpt());
        assertEquals(feeCcy, res.getFeeCcy());
        assertEquals(orgId, res.getOrgId());
        assertEquals(description, res.getDescription());
        assertEquals(channel, res.getChannel());
        assertEquals(payerType, res.getPayerType());
    }

    @Test
    void testSettersAndGetters() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            String taxCode = "**********";
            String altTaxCode = "ALT**********";
            String payerName = "Test Company";
            String altPayerName = "Alternative Test Company";
            String payerAddr = "123 Test Street";
            String altPayerAddr = "456 Alt Street";
            String treasuryCode = "T001";
            String treasuryName = "Treasury Name";
            String admAreaCode = "A001";
            String admAreaName = "Admin Area Name";
            String revAccCode = "R001";
            String revAccName = "Revenue Account Name";
            String revAuthCode = "RA001";
            String revAuthName = "Revenue Authority Name";
            String benBankCode = "BB001";
            String benBankName = "Ben Bank Name";
            Integer payerType = 1;
            String debitAccNo = "**********";
            String txnId = "TXN001";
            String debitAccName = "Test Account Name";
            BigDecimal amount = new BigDecimal("100000");
            String ccy = "VND";
            String batchNo = "BATCH001";
            String status = "SUCCESS";
            LocalDateTime createdDate = LocalDateTime.now();
            LocalDateTime updatedDate = LocalDateTime.now();
            String createdBy = "testuser";
            String tccRefNo = "REF001";
            BigDecimal feeTotal = new BigDecimal("5000");
            String feeOpt = "Fixed Fee";
            String feeCcy = "VND";
            String orgId = "ORG001";
            String description = "Test Description";
            String channel = "WEB";

            // When
            h2hTxnDetailRes.setTaxCode(taxCode);
            h2hTxnDetailRes.setAltTaxCode(altTaxCode);
            h2hTxnDetailRes.setPayerName(payerName);
            h2hTxnDetailRes.setAltPayerName(altPayerName);
            h2hTxnDetailRes.setPayerAddr(payerAddr);
            h2hTxnDetailRes.setAltPayerAddr(altPayerAddr);
            h2hTxnDetailRes.setTreasuryCode(treasuryCode);
            h2hTxnDetailRes.setTreasuryName(treasuryName);
            h2hTxnDetailRes.setAdmAreaCode(admAreaCode);
            h2hTxnDetailRes.setAdmAreaName(admAreaName);
            h2hTxnDetailRes.setRevAccCode(revAccCode);
            h2hTxnDetailRes.setRevAccName(revAccName);
            h2hTxnDetailRes.setRevAuthCode(revAuthCode);
            h2hTxnDetailRes.setRevAuthName(revAuthName);
            h2hTxnDetailRes.setBenBankCode(benBankCode);
            h2hTxnDetailRes.setBenBankName(benBankName);
            h2hTxnDetailRes.setPayerType(payerType);
            h2hTxnDetailRes.setDebitAccNo(debitAccNo);
            h2hTxnDetailRes.setTxnId(txnId);
            h2hTxnDetailRes.setDebitAccName(debitAccName);
            h2hTxnDetailRes.setAmount(amount);
            h2hTxnDetailRes.setCcy(ccy);
            h2hTxnDetailRes.setBatchNo(batchNo);
            h2hTxnDetailRes.setStatus(status);
            h2hTxnDetailRes.setCreatedDate(createdDate);
            h2hTxnDetailRes.setUpdatedDate(updatedDate);
            h2hTxnDetailRes.setCreatedBy(createdBy);
            h2hTxnDetailRes.setTccRefNo(tccRefNo);
            h2hTxnDetailRes.setFeeTotal(feeTotal);
            h2hTxnDetailRes.setFeeOpt(feeOpt);
            h2hTxnDetailRes.setFeeCcy(feeCcy);
            h2hTxnDetailRes.setOrgId(orgId);
            h2hTxnDetailRes.setDescription(description);
            h2hTxnDetailRes.setChannel(channel);

            // Then
            assertEquals(taxCode, h2hTxnDetailRes.getTaxCode());
            assertEquals(altTaxCode, h2hTxnDetailRes.getAltTaxCode());
            assertEquals(payerName, h2hTxnDetailRes.getPayerName());
            assertEquals(altPayerName, h2hTxnDetailRes.getAltPayerName());
            assertEquals(payerAddr, h2hTxnDetailRes.getPayerAddr());
            assertEquals(altPayerAddr, h2hTxnDetailRes.getAltPayerAddr());
            assertEquals(treasuryCode, h2hTxnDetailRes.getTreasuryCode());
            assertEquals(treasuryName, h2hTxnDetailRes.getTreasuryName());
            assertEquals(admAreaCode, h2hTxnDetailRes.getAdmAreaCode());
            assertEquals(admAreaName, h2hTxnDetailRes.getAdmAreaName());
            assertEquals(revAccCode, h2hTxnDetailRes.getRevAccCode());
            assertEquals(revAccName, h2hTxnDetailRes.getRevAccName());
            assertEquals(revAuthCode, h2hTxnDetailRes.getRevAuthCode());
            assertEquals(revAuthName, h2hTxnDetailRes.getRevAuthName());
            assertEquals(benBankCode, h2hTxnDetailRes.getBenBankCode());
            assertEquals(benBankName, h2hTxnDetailRes.getBenBankName());
            assertEquals(payerType, h2hTxnDetailRes.getPayerType());
            assertEquals(debitAccNo, h2hTxnDetailRes.getDebitAccNo());
            assertEquals(txnId, h2hTxnDetailRes.getTxnId());
            assertEquals(debitAccName, h2hTxnDetailRes.getDebitAccName());
            assertEquals(amount, h2hTxnDetailRes.getAmount());
            assertEquals(ccy, h2hTxnDetailRes.getCcy());
            assertEquals(batchNo, h2hTxnDetailRes.getBatchNo());
            assertEquals(status, h2hTxnDetailRes.getStatus());
            assertEquals(createdDate, h2hTxnDetailRes.getCreatedDate());
            assertEquals(updatedDate, h2hTxnDetailRes.getUpdatedDate());
            assertEquals(createdBy, h2hTxnDetailRes.getCreatedBy());
            assertEquals(tccRefNo, h2hTxnDetailRes.getTccRefNo());
            assertEquals(feeTotal, h2hTxnDetailRes.getFeeTotal());
            assertEquals(feeOpt, h2hTxnDetailRes.getFeeOpt());
            assertEquals(feeCcy, h2hTxnDetailRes.getFeeCcy());
            assertEquals(orgId, h2hTxnDetailRes.getOrgId());
            assertEquals(description, h2hTxnDetailRes.getDescription());
            assertEquals(channel, h2hTxnDetailRes.getChannel());
        }
    }

    @Test
    void testPayerTypeNameTranslation() {
        // Given
        Integer payerType = 1;
        String expectedTranslatedName = "Doanh nghiệp";
        String expectedKey = AppConstants.LANGUAGE.TAX_PAYER_TYPE + "." + payerType;

        h2hTxnDetailRes.setPayerType(payerType);

        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(expectedKey, payerType.toString()))
                    .thenReturn(expectedTranslatedName);

            // When
            String result = h2hTxnDetailRes.getPayerTypeName();

            // Then
            assertEquals(expectedTranslatedName, result);
            mockedTranslator.verify(() -> Translator.toLocale(expectedKey, payerType.toString()));
        }
    }

    @Test
    void testPayerTypeNameWithNullPayerType() {
        // Given
        h2hTxnDetailRes.setPayerType(null);

        // When
        String result = h2hTxnDetailRes.getPayerTypeName();

        // Then
        assertNull(result);
    }

    @Test
    void testAmountTextGeneration() {
        // Given
        BigDecimal amount = new BigDecimal("1234567890");
        String ccy = "VND";
        h2hTxnDetailRes.setAmount(amount);
        h2hTxnDetailRes.setCcy(ccy);

        // When
        String amountText = h2hTxnDetailRes.getAmountText();

        // Then
        assertNotNull(amountText);
        assertFalse(amountText.isEmpty());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String taxCode = "**********";
        String payerName = "Test Company";
        BigDecimal amount = new BigDecimal("100000");

        H2hTxnDetailRes res1 = H2hTxnDetailRes.builder()
                .taxCode(taxCode)
                .payerName(payerName)
                .amount(amount)
                .build();

        H2hTxnDetailRes res2 = H2hTxnDetailRes.builder()
                .taxCode(taxCode)
                .payerName(payerName)
                .amount(amount)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String taxCode = "**********";
        String payerName = "Test Company";
        BigDecimal amount = new BigDecimal("100000");

        h2hTxnDetailRes.setTaxCode(taxCode);
        h2hTxnDetailRes.setPayerName(payerName);
        h2hTxnDetailRes.setAmount(amount);

        // When
        String result = h2hTxnDetailRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void testTaxItemsHandling() {
        // Given
        TxnTaxFullItemDto taxItem1 = new TxnTaxFullItemDto();
        TxnTaxFullItemDto taxItem2 = new TxnTaxFullItemDto();
        List<TxnTaxFullItemDto> taxItems = Arrays.asList(taxItem1, taxItem2);

        // When
        h2hTxnDetailRes.setTaxItems(taxItems);

        // Then
        assertEquals(taxItems, h2hTxnDetailRes.getTaxItems());
        assertEquals(2, h2hTxnDetailRes.getTaxItems().size());
    }

    @Test
    void testInheritanceFromTxnDetailDto() {
        // Given
        H2hTxnDetailRes res = new H2hTxnDetailRes();

        // Then
        assertInstanceOf(com.bidv.ibank.dvc.model.dto.contracts.TxnDetailDto.class, res);
    }

    @Test
    void testJsonPropertyOrderAnnotation() {
        // Given
        H2hTxnDetailRes res = H2hTxnDetailRes.builder()
                .taxCode("TAX001")
                .payerName("Company Name")
                .amount(new BigDecimal("1000"))
                .build();

        // Then
        assertNotNull(res);
        assertEquals("TAX001", res.getTaxCode());
        assertEquals("Company Name", res.getPayerName());
        assertEquals(new BigDecimal("1000"), res.getAmount());
    }

    @Test
    void testSuperBuilderFunctionality() {
        // Given & When
        H2hTxnDetailRes res = H2hTxnDetailRes.builder()
                .taxCode("**********")
                .payerName("Test Company")
                .debitAccNo("**********")
                .amount(new BigDecimal("100000"))
                .ccy("VND")
                .status("SUCCESS")
                .build();

        // Then
        assertNotNull(res);
        assertEquals("**********", res.getTaxCode());
        assertEquals("Test Company", res.getPayerName());
        assertEquals("**********", res.getDebitAccNo());
        assertEquals(new BigDecimal("100000"), res.getAmount());
        assertEquals("VND", res.getCcy());
        assertEquals("SUCCESS", res.getStatus());
    }

    @Test
    void testTranslatorMethods() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String treasuryCode = "T001";
            String treasuryName = "Treasury Name";
            String admAreaCode = "A001";
            String admAreaName = "Admin Area Name";
            String revAccCode = "R001";
            String revAccName = "Revenue Account Name";
            String revAuthCode = "RA001";
            String revAuthName = "Revenue Authority Name";
            String benBankName = "Ben Bank Name";

            h2hTxnDetailRes.setTreasuryCode(treasuryCode);
            h2hTxnDetailRes.setTreasuryName(treasuryName);
            h2hTxnDetailRes.setAdmAreaCode(admAreaCode);
            h2hTxnDetailRes.setAdmAreaName(admAreaName);
            h2hTxnDetailRes.setRevAccCode(revAccCode);
            h2hTxnDetailRes.setRevAccName(revAccName);
            h2hTxnDetailRes.setRevAuthCode(revAuthCode);
            h2hTxnDetailRes.setRevAuthName(revAuthName);
            h2hTxnDetailRes.setBenBankName(benBankName);

            mockedTranslator.when(() -> Translator.toLocale(treasuryCode, treasuryName))
                    .thenReturn(treasuryName);
            mockedTranslator.when(() -> Translator.toLocale(admAreaCode, admAreaName))
                    .thenReturn(admAreaName);
            mockedTranslator.when(() -> Translator.toLocale(revAccCode, revAccName))
                    .thenReturn(revAccName);
            mockedTranslator.when(() -> Translator.toLocale(revAuthCode, revAuthName))
                    .thenReturn(revAuthName);

            // When & Then
            assertEquals(treasuryName, h2hTxnDetailRes.getTreasuryName());
            assertEquals(admAreaName, h2hTxnDetailRes.getAdmAreaName());
            assertEquals(revAccName, h2hTxnDetailRes.getRevAccName());
            assertEquals(revAuthName, h2hTxnDetailRes.getRevAuthName());
            assertEquals(benBankName, h2hTxnDetailRes.getBenBankName());
        }
    }
}
