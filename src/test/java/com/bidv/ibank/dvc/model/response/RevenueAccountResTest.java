package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class RevenueAccountResTest {

    private MockedStatic<Translator> translatorMock;
    private RevenueAccountRes revenueAccountRes;
    private static final String REV_ACC_CODE = "RA001";
    private static final String REV_ACC_NAME = "Revenue Account 1";
    private static final String TRANSLATED_NAME = "Translated Revenue Account 1";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        revenueAccountRes = RevenueAccountRes.builder()
                .revAccCode(REV_ACC_CODE)
                .revAccName(REV_ACC_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(REV_ACC_NAME);

        assertThat(revenueAccountRes).isNotNull();
        assertThat(revenueAccountRes.getRevAccCode()).isEqualTo(REV_ACC_CODE);
        assertThat(revenueAccountRes.getRevAccName()).isEqualTo(REV_ACC_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        RevenueAccountRes emptyAccount = RevenueAccountRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyAccount).isNotNull();
        assertThat(emptyAccount.getRevAccCode()).isNull();
        assertThat(emptyAccount.getRevAccName()).isNull();
    }

    @Test
    void getRevAccName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = revenueAccountRes.getRevAccName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getRevAccName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(REV_ACC_NAME);

        String result = revenueAccountRes.getRevAccName();

        assertThat(result).isEqualTo(REV_ACC_NAME);
    }

    @Test
    void getRevAccName_WhenRevAccCodeIsNull_ShouldHandleNullGracefully() {
        RevenueAccountRes nullCodeAccount = RevenueAccountRes.builder()
                .revAccName(REV_ACC_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeAccount.getRevAccName();

        assertThat(result).isNull();
    }

    @Test
    void getRevAccName_WhenRevAccNameIsNull_ShouldHandleNullGracefully() {
        RevenueAccountRes nullNameAccount = RevenueAccountRes.builder()
                .revAccCode(REV_ACC_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameAccount.getRevAccName();

        assertThat(result).isNull();
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        RevenueAccountRes account1 = RevenueAccountRes.builder()
                .revAccCode(REV_ACC_CODE)
                .revAccName(REV_ACC_NAME)
                .build();

        RevenueAccountRes account2 = RevenueAccountRes.builder()
                .revAccCode(REV_ACC_CODE)
                .revAccName(REV_ACC_NAME)
                .build();

        assertThat(account1).isEqualTo(account2);
        assertThat(account1.hashCode()).isEqualTo(account2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        RevenueAccountRes account1 = RevenueAccountRes.builder()
                .revAccCode(REV_ACC_CODE)
                .revAccName(REV_ACC_NAME)
                .build();

        RevenueAccountRes account2 = RevenueAccountRes.builder()
                .revAccCode("RA002")
                .revAccName("Revenue Account 2")
                .build();

        assertThat(account1).isNotEqualTo(account2);
        assertThat(account1.hashCode()).isNotEqualTo(account2.hashCode());
    }
}