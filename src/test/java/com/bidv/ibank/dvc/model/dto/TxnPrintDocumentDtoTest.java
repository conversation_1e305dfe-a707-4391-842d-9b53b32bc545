package com.bidv.ibank.dvc.model.dto;

import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.client.common.dto.workflow.WfTxnInfoResponse;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnPrintDocumentDtoTest {

    private TxnPrintDocumentDto.TxnPrintDocumentDtoBuilder baseBuilder;

    @BeforeEach
    void setUp() {
        baseBuilder = TxnPrintDocumentDto.builder()
                .txnId("TXN123")
                .txnCode("GOV01")
                .status(TransactionStatusEnum.SUCCESS.name())
                .admAreaCode("01")
                .tccDocSign("TCC_SIGN")
                .tccDocNo("TCC123")
                .coreRef("CORE123")
                .tccRmNo("RM123")
                .refNumber("REF123")
                .payerName("Test Company Ltd")
                .taxCode("**********")
                .payerAddr("123 Test Street, Hanoi")
                .altTaxCode("**********")
                .altPayerName("Alternative Payer")
                .altPayerAddr("456 Alt Street, HCMC")
                .debitAccNo("**********")
                .revAuthCode("RA001")
                .revAuthName("Revenue Authority")
                .revAccCode("RAC001")
                .revAccName("Revenue Account")
                .paymentDate("2024-01-15")
                .paymentMonth("01")
                .paymentYear("2024")
                .pmtTime("14:30:00")
                .amount("1000000")
                .debitAmount("1050000")
                .feeTotal("50000")
                .feeVat("5000")
                .ccy("VND")
                .treasuryName("State Treasury")
                .benBankName("BIDV Bank");
    }

    private WfTxnInfoResponse createMockWfTxnInfoResponse() {
        return mock(WfTxnInfoResponse.class);
    }

    @Test
    void testBuilder() {
        // When
        TxnPrintDocumentDto dto = TxnPrintDocumentDto.builder()
                .txnId("TEST_TXN")
                .txnCode("GOV02")
                .status("PENDING")
                .payerName("Builder Test Company")
                .amount("2000000")
                .ccy("USD")
                .build();

        // Then
        assertNotNull(dto);
        assertEquals("TEST_TXN", dto.getTxnId());
        assertEquals("GOV02", dto.getTxnCode());
        assertEquals("PENDING", dto.getStatus());
        assertEquals("Builder Test Company", dto.getPayerName());
        assertEquals("2000000", dto.getAmount());
        assertEquals("USD", dto.getCcy());
    }

    @Test
    void testGettersAndSetters() {
        // Given
        TxnPrintDocumentDto dto = baseBuilder.build();

        // When
        dto.setTxnId("SETTER_TEST");
        dto.setTxnCode("GOV03");
        dto.setStatus("FAILED");
        dto.setPayerName("Setter Test Company");
        dto.setTaxCode("1111111111");
        dto.setAmount("3000000");
        dto.setCcy("EUR");

        // Then
        assertEquals("SETTER_TEST", dto.getTxnId());
        assertEquals("GOV03", dto.getTxnCode());
        assertEquals("FAILED", dto.getStatus());
        assertEquals("Setter Test Company", dto.getPayerName());
        assertEquals("1111111111", dto.getTaxCode());
        assertEquals("3000000", dto.getAmount());
        assertEquals("EUR", dto.getCcy());
    }

    @Test
    void testGetAmountText() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.getLocale() to return a specific locale
            mockedTranslator.when(Translator::getLocale).thenReturn(new Locale("vi", "VN"));

            // Given
            TxnPrintDocumentDto dto = baseBuilder.build();

            // When
            String result = dto.getAmountText();

            // Then
            assertNotNull(result);
            // The actual result depends on ReadNumber.formatAmountToText implementation
            // We're testing that the method executes without error
        }
    }

    @Test
    void testGetParamFirstPageWithSuccessStatus() {
        // Given
        TxnPrintDocumentDto dto = baseBuilder
                .status(TransactionStatusEnum.SUCCESS.name())
                .tccDocSign("SUCCESS_SIGN")
                .tccDocNo("SUCCESS_DOC_123")
                .build();

        // When
        Map<String, Object> params = dto.getParamFirstPage();

        // Then
        assertNotNull(params);
        assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.PAYER_NAME));
        assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.TAX_CODE));
        assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.PAYER_ADDR));
        assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC_NO));
        assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.REV_AUTH_NAME));

        assertEquals("Test Company Ltd", params.get(AppConstants.DOCUMENT_PARAM_NAME.PAYER_NAME));
        assertEquals("**********", params.get(AppConstants.DOCUMENT_PARAM_NAME.TAX_CODE));
        assertEquals("123 Test Street, Hanoi", params.get(AppConstants.DOCUMENT_PARAM_NAME.PAYER_ADDR));
        assertEquals("**********", params.get(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC_NO));
        assertEquals("Revenue Authority", params.get(AppConstants.DOCUMENT_PARAM_NAME.REV_AUTH_NAME));

        // Success status specific params
        assertEquals("SUCCESS_SIGN", params.get(AppConstants.DOCUMENT_PARAM_NAME.TCC_DOC_SIGN));
        assertEquals(AppConstants.DOCUMENT_PARAM_NAME.TEXT_TCC_DOC_NO + "SUCCESS_DOC_123", params.get(AppConstants.DOCUMENT_PARAM_NAME.TCC_DOC_NO));
    }

    @Test
    void testGetParamFirstPageWithNonSuccessStatus() {
        // Given
        TxnPrintDocumentDto dto = baseBuilder
                .status("FAILED")
                .build();

        // When
        Map<String, Object> params = dto.getParamFirstPage();

        // Then
        assertNotNull(params);
        assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.PAYER_NAME));
        assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.TAX_CODE));

        // Non-success status should not have these params (they return null when not present)
        assertNull(params.get(AppConstants.DOCUMENT_PARAM_NAME.TCC_DOC_SIGN));
        assertNull(params.get(AppConstants.DOCUMENT_PARAM_NAME.TCC_DOC_NO));
        assertNull(params.get(AppConstants.DOCUMENT_PARAM_NAME.REF_NUMBER));
    }

    @Test
    void testGetParamFirstPageWithGOV01TxnCode() {
        // Given
        TxnPrintDocumentDto dto = baseBuilder
                .txnCode("GOV01")
                .status(TransactionStatusEnum.SUCCESS.name())
                .coreRef("CORE_REF_123")
                .build();

        // When
        Map<String, Object> params = dto.getParamFirstPage();

        // Then
        assertNotNull(params);
        assertEquals("CORE_REF_123", params.get(AppConstants.DOCUMENT_PARAM_NAME.REF_NUMBER));
    }

    @Test
    void testGetParamFirstPageWithNonGOV01TxnCode() {
        // Given
        TxnPrintDocumentDto dto = baseBuilder
                .txnCode("GOV02")
                .status(TransactionStatusEnum.SUCCESS.name())
                .tccRmNo("TCC_RM_456")
                .build();

        // When
        Map<String, Object> params = dto.getParamFirstPage();

        // Then
        assertNotNull(params);
        assertEquals("TCC_RM_456", params.get(AppConstants.DOCUMENT_PARAM_NAME.REF_NUMBER));
    }

    @Test
    void testGetParamSecondPageWithSuccessStatus() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.getLocale() for getAmountText()
            mockedTranslator.when(Translator::getLocale).thenReturn(new Locale("vi", "VN"));

            // Given
            TxnPrintDocumentItemDto item1 = new TxnPrintDocumentItemDto(
                    "TXN123", "1", "20240101001", "2024-01-01",
                    "Test Item 1", "333333", "333333", "3063", "01");
            TxnPrintDocumentItemDto item2 = new TxnPrintDocumentItemDto(
                    "TXN123", "2", "20240101002", "2024-01-01",
                    "Test Item 2", "333333", "333333", "3063", "01");
            TxnPrintDocumentItemDto item3 = new TxnPrintDocumentItemDto(
                    "TXN123", "3", "20240101003", "2024-01-01",
                    "Test Item 3", "333334", "333334", "3063", "01");

            List<TxnPrintDocumentItemDto> items = Arrays.asList(item1, item2, item3);
            TxnPrintDocumentDto dto = baseBuilder
                    .txnPrintDocumentItems(items)
                    .status(TransactionStatusEnum.SUCCESS.name())
                    .build();

            WfTxnInfoResponse mockTxnInfo = createMockWfTxnInfoResponse();

            // When
            Map<String, Object> params = dto.getParamSecondPage(mockTxnInfo);

            // Then
            assertNotNull(params);
            assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.TXN_PRINT_DOCUMENT_ITEM));
            assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.AMOUNT_TEXT));
            assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.TOTAL_AMOUNT));

            assertEquals("1,000,000", params.get(AppConstants.DOCUMENT_PARAM_NAME.TOTAL_AMOUNT));

            // Success status specific params
            assertEquals("RA001", params.get(AppConstants.DOCUMENT_PARAM_NAME.REV_AUTH_CODE));
            assertEquals("**********", params.get(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC_NO));
            assertEquals("2024-01-15", params.get(AppConstants.DOCUMENT_PARAM_NAME.PAYMENT_DATE));
        }
    }

    @Test
    void testGetParamSecondPageWithNonSuccessStatus() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.getLocale() for getAmountText()
            mockedTranslator.when(Translator::getLocale).thenReturn(new Locale("vi", "VN"));

            // Given
            TxnPrintDocumentDto dto = baseBuilder
                    .status("FAILED")
                    .build();

            WfTxnInfoResponse mockTxnInfo = createMockWfTxnInfoResponse();

            // When
            Map<String, Object> params = dto.getParamSecondPage(mockTxnInfo);

            // Then
            assertNotNull(params);
            assertFalse(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.TXN_PRINT_DOCUMENT_ITEM));
            assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.AMOUNT_TEXT));
            assertTrue(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.TOTAL_AMOUNT));

            // Non-success status should not have these specific params (they return null when not present)
            assertNull(params.get(AppConstants.DOCUMENT_PARAM_NAME.REV_AUTH_CODE));
            assertNull(params.get(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC_NO));
            assertNull(params.get(AppConstants.DOCUMENT_PARAM_NAME.PAYMENT_DATE));
        }
    }

    @Test
    void testGetParamSecondPageWithNullItems() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.getLocale() for getAmountText()
            mockedTranslator.when(Translator::getLocale).thenReturn(new Locale("vi", "VN"));

            // Given
            TxnPrintDocumentDto dto = baseBuilder
                    .txnPrintDocumentItems(null)
                    .build();

            WfTxnInfoResponse mockTxnInfo = createMockWfTxnInfoResponse();

            // When
            Map<String, Object> params = dto.getParamSecondPage(mockTxnInfo);

            // Then
            assertNotNull(params);
            assertFalse(params.containsKey(AppConstants.DOCUMENT_PARAM_NAME.TXN_PRINT_DOCUMENT_ITEM));
            // Should handle null items gracefully due to CollectionUtils.emptyIfNull
        }
    }

    @Test
    void testWithNullValues() {
        // Given
        TxnPrintDocumentDto dto = TxnPrintDocumentDto.builder()
                .txnId(null)
                .payerName(null)
                .amount(null)
                .ccy(null)
                .build();

        // When & Then
        assertNull(dto.getTxnId());
        assertNull(dto.getPayerName());
        assertNull(dto.getAmount());
        assertNull(dto.getCcy());
    }

    @Test
    void testComplexScenario() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.getLocale()
            mockedTranslator.when(Translator::getLocale).thenReturn(new Locale("en", "US"));

            // Given - Complex tax payment document scenario
            List<TxnPrintDocumentItemDto> items = Arrays.asList(
                    new TxnPrintDocumentItemDto(
                            "COMPLEX_TXN_001", "1", "20241201001", "2024-12-01",
                            "Value Added Tax", "800000", "800000", "3063", "01"),
                    new TxnPrintDocumentItemDto(
                            "COMPLEX_TXN_001", "2", "20241201002", "2024-12-01",
                            "Corporate Income Tax", "200000", "200000", "2050", "01"));

            TxnPrintDocumentDto complexDto = TxnPrintDocumentDto.builder()
                    .txnId("COMPLEX_TXN_001")
                    .txnCode("GOV01")
                    .status(TransactionStatusEnum.SUCCESS.name())
                    .payerName("ABC Manufacturing Co., Ltd")
                    .taxCode("0**********12")
                    .payerAddr("123 Industrial Park, Hanoi, Vietnam")
                    .debitAccNo("**********123456")
                    .amount("1000000")
                    .ccy("VND")
                    .revAuthCode("HN_TAX_001")
                    .revAuthName("Hanoi Tax Department")
                    .paymentDate("2024-12-15")
                    .paymentMonth("12")
                    .paymentYear("2024")
                    .txnPrintDocumentItems(items)
                    .build();

            WfTxnInfoResponse mockTxnInfo = createMockWfTxnInfoResponse();

            // When
            Map<String, Object> firstPageParams = complexDto.getParamFirstPage();
            Map<String, Object> secondPageParams = complexDto.getParamSecondPage(mockTxnInfo);

            // Then
            assertNotNull(firstPageParams);
            assertNotNull(secondPageParams);

            assertEquals("ABC Manufacturing Co., Ltd", firstPageParams.get(AppConstants.DOCUMENT_PARAM_NAME.PAYER_NAME));
            assertEquals("0**********12", firstPageParams.get(AppConstants.DOCUMENT_PARAM_NAME.TAX_CODE));
            assertEquals("**********123456", firstPageParams.get(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC_NO));

            assertEquals("1,000,000", secondPageParams.get(AppConstants.DOCUMENT_PARAM_NAME.TOTAL_AMOUNT));
            assertEquals("HN_TAX_001", secondPageParams.get(AppConstants.DOCUMENT_PARAM_NAME.REV_AUTH_CODE));
            assertEquals("2024-12-15", secondPageParams.get(AppConstants.DOCUMENT_PARAM_NAME.PAYMENT_DATE));
        }
    }
}
