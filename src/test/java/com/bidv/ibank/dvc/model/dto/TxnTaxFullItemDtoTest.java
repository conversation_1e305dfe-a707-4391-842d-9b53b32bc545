package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnTaxFullItemDtoTest {

    private TxnTaxFullItemDto dto;
    private LocalDate declarationDate;

    @BeforeEach
    void setUp() {
        declarationDate = LocalDate.of(2024, 3, 21);
        dto = TxnTaxFullItemDto.builder()
                .eiTypeCode("EI001")
                .taxTypeCode("TAX001")
                .ccCode("CC001")
                .chapterCode("CH001")
                .ecCode("EC001")
                .amount("1000000")
                .ccy("VND")
                .declarationDate(declarationDate)
                .declarationNo("DEC123")
                .transDesc("Test Transaction")
                .ccName("CC Name")
                .chapterName("Chapter Name")
                .ecName("EC Name")
                .eiTypeName("EI Type Name")
                .taxTypeName("Tax Type Name")
                .treasuryCode("TR001")
                .treasuryName("Treasury Name")
                .admAreaCode("ADM001")
                .admAreaName("Admin Area Name")
                .revAccCode("REV001")
                .revAccName("Revenue Account Name")
                .revAuthCode("AUTH001")
                .revAuthName("Revenue Authority Name")
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .build();
    }

    @Test
    void testGetTreasuryName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("TR001", "Treasury Name"))
                    .thenReturn("Translated Treasury Name");

            assertEquals("Translated Treasury Name", dto.getTreasuryName());
        }
    }

    @Test
    void testGetAdmAreaName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("ADM001", "Admin Area Name"))
                    .thenReturn("Translated Admin Area Name");

            assertEquals("Translated Admin Area Name", dto.getAdmAreaName());
        }
    }

    @Test
    void testGetRevAccName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("REV001", "Revenue Account Name"))
                    .thenReturn("Translated Revenue Account Name");

            assertEquals("Translated Revenue Account Name", dto.getRevAccName());
        }
    }

    @Test
    void testGetRevAuthName() {
        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale("AUTH001", "Revenue Authority Name"))
                    .thenReturn("Translated Revenue Authority Name");

            assertEquals("Translated Revenue Authority Name", dto.getRevAuthName());
        }
    }

    @Test
    void testGetPayerType() {
        assertEquals(1, dto.getPayerType());
    }

    @Test
    void testInheritedFields() {
        assertEquals("EI001", dto.getEiTypeCode());
        assertEquals("TAX001", dto.getTaxTypeCode());
        assertEquals("CC001", dto.getCcCode());
        assertEquals("CH001", dto.getChapterCode());
        assertEquals("EC001", dto.getEcCode());
        assertEquals("1000000", dto.getAmount());
        assertEquals("VND", dto.getCcy());
        assertEquals(declarationDate, dto.getDeclarationDate());
        assertEquals("DEC123", dto.getDeclarationNo());
        assertEquals("Test Transaction", dto.getTransDesc());
    }

    @Test
    void testNoArgsConstructor() {
        TxnTaxFullItemDto emptyDto = new TxnTaxFullItemDto();

        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(Mockito.anyString(), Mockito.anyString()))
                    .thenReturn(null);

            assertNull(emptyDto.getTreasuryCode());
            assertNull(emptyDto.getTreasuryName());
            assertNull(emptyDto.getAdmAreaCode());
            assertNull(emptyDto.getAdmAreaName());
            assertNull(emptyDto.getRevAccCode());
            assertNull(emptyDto.getRevAccName());
            assertNull(emptyDto.getRevAuthCode());
            assertNull(emptyDto.getRevAuthName());
            assertNull(emptyDto.getPayerType());

            // Test inherited fields
            assertNull(emptyDto.getCcName());
            assertNull(emptyDto.getChapterName());
            assertNull(emptyDto.getEcName());
            assertNull(emptyDto.getEiTypeName());
            assertNull(emptyDto.getTaxTypeName());
        }
    }

    @Test
    void testAllArgsConstructor() {
        TxnTaxFullItemDto fullDto = new TxnTaxFullItemDto(
                "TR001", "Treasury Name", "ADM001", "Admin Area Name",
                "REV001", "Revenue Account Name", "AUTH001", "Revenue Authority Name", "BBC01", "Ben Bank Name",
                PayerTypeEnum.BUSINESS.getValue());

        try (MockedStatic<Translator> mockedTranslator = Mockito.mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(Mockito.anyString(), Mockito.anyString()))
                    .thenReturn("Translated Name");

            assertEquals("Translated Name", fullDto.getTreasuryName());
            assertEquals("Translated Name", fullDto.getAdmAreaName());
            assertEquals("Translated Name", fullDto.getRevAccName());
            assertEquals("Translated Name", fullDto.getRevAuthName());
            assertEquals(PayerTypeEnum.BUSINESS.getValue(), fullDto.getPayerType());
        }
    }
}