package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TxnPrintDocumentItemDtoTest {

    private TxnPrintDocumentItemDto txnPrintDocumentItemDto;

    @BeforeEach
    void setUp() {
        txnPrintDocumentItemDto = new TxnPrintDocumentItemDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnPrintDocumentItemDto dto = new TxnPrintDocumentItemDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getTxnId());
        assertNull(dto.getOrder());
        assertNull(dto.getDeclarationNo());
        assertNull(dto.getDeclarationDate());
        assertNull(dto.getTransDesc());
        assertNull(dto.getAmount());
        assertNull(dto.getAmountVnd());
        assertNull(dto.getChapterCode());
        assertNull(dto.getEcCode());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String txnId = "TXN123";
        String order = "1";
        String declarationNo = "30644771632";
        String declarationDate = "2024-01-15";
        String transDesc = "Value Added Tax Payment";
        String amount = "1000000";
        String amountVnd = "1000000";
        String chapterCode = "755";
        String ecCode = "3063";
        String admAreaCode = "01";

        // When
        TxnPrintDocumentItemDto dto = new TxnPrintDocumentItemDto(
                txnId, order, declarationNo, declarationDate, transDesc,
                amount, amountVnd, chapterCode, ecCode);

        // Then
        assertNotNull(dto);
        assertEquals(txnId, dto.getTxnId());
        assertEquals(order, dto.getOrder());
        assertEquals(declarationNo, dto.getDeclarationNo());
        assertEquals(declarationDate, dto.getDeclarationDate());
        assertEquals(transDesc, dto.getTransDesc());
        assertEquals(amount, dto.getAmount());
        assertEquals(amountVnd, dto.getAmountVnd());
        assertEquals(chapterCode, dto.getChapterCode());
        assertEquals(ecCode, dto.getEcCode());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String txnId = "SETTER_TXN_456";
        String order = "2";
        String declarationNo = "20231225001";
        String declarationDate = "2023-12-25";
        String transDesc = "Corporate Income Tax";
        String amount = "2500000";
        String amountVnd = "2500000";
        String chapterCode = "700";
        String ecCode = "2050";
        String admAreaCode = "02";

        // When
        txnPrintDocumentItemDto.setTxnId(txnId);
        txnPrintDocumentItemDto.setOrder(order);
        txnPrintDocumentItemDto.setDeclarationNo(declarationNo);
        txnPrintDocumentItemDto.setDeclarationDate(declarationDate);
        txnPrintDocumentItemDto.setTransDesc(transDesc);
        txnPrintDocumentItemDto.setAmount(amount);
        txnPrintDocumentItemDto.setAmountVnd(amountVnd);
        txnPrintDocumentItemDto.setChapterCode(chapterCode);
        txnPrintDocumentItemDto.setEcCode(ecCode);

        // Then
        assertEquals(txnId, txnPrintDocumentItemDto.getTxnId());
        assertEquals(order, txnPrintDocumentItemDto.getOrder());
        assertEquals(declarationNo, txnPrintDocumentItemDto.getDeclarationNo());
        assertEquals(declarationDate, txnPrintDocumentItemDto.getDeclarationDate());
        assertEquals(transDesc, txnPrintDocumentItemDto.getTransDesc());
        assertEquals(amount, txnPrintDocumentItemDto.getAmount());
        assertEquals(amountVnd, txnPrintDocumentItemDto.getAmountVnd());
        assertEquals(chapterCode, txnPrintDocumentItemDto.getChapterCode());
        assertEquals(ecCode, txnPrintDocumentItemDto.getEcCode());
    }

    @Test
    void testSettersAndGettersWithNullValues() {
        // When
        txnPrintDocumentItemDto.setTxnId(null);
        txnPrintDocumentItemDto.setOrder(null);
        txnPrintDocumentItemDto.setDeclarationNo(null);
        txnPrintDocumentItemDto.setDeclarationDate(null);
        txnPrintDocumentItemDto.setTransDesc(null);
        txnPrintDocumentItemDto.setAmount(null);
        txnPrintDocumentItemDto.setAmountVnd(null);
        txnPrintDocumentItemDto.setChapterCode(null);
        txnPrintDocumentItemDto.setEcCode(null);

        // Then
        assertNull(txnPrintDocumentItemDto.getTxnId());
        assertNull(txnPrintDocumentItemDto.getOrder());
        assertNull(txnPrintDocumentItemDto.getDeclarationNo());
        assertNull(txnPrintDocumentItemDto.getDeclarationDate());
        assertNull(txnPrintDocumentItemDto.getTransDesc());
        assertNull(txnPrintDocumentItemDto.getAmount());
        assertNull(txnPrintDocumentItemDto.getAmountVnd());
        assertNull(txnPrintDocumentItemDto.getChapterCode());
        assertNull(txnPrintDocumentItemDto.getEcCode());
    }

    @Test
    void testSettersAndGettersWithEmptyStrings() {
        // When
        txnPrintDocumentItemDto.setTxnId("");
        txnPrintDocumentItemDto.setOrder("");
        txnPrintDocumentItemDto.setDeclarationNo("");
        txnPrintDocumentItemDto.setDeclarationDate("");
        txnPrintDocumentItemDto.setTransDesc("");
        txnPrintDocumentItemDto.setAmount("");
        txnPrintDocumentItemDto.setAmountVnd("");
        txnPrintDocumentItemDto.setChapterCode("");
        txnPrintDocumentItemDto.setEcCode("");

        // Then
        assertEquals("", txnPrintDocumentItemDto.getTxnId());
        assertEquals("", txnPrintDocumentItemDto.getOrder());
        assertEquals("", txnPrintDocumentItemDto.getDeclarationNo());
        assertEquals("", txnPrintDocumentItemDto.getDeclarationDate());
        assertEquals("", txnPrintDocumentItemDto.getTransDesc());
        assertEquals("", txnPrintDocumentItemDto.getAmount());
        assertEquals("", txnPrintDocumentItemDto.getAmountVnd());
        assertEquals("", txnPrintDocumentItemDto.getChapterCode());
        assertEquals("", txnPrintDocumentItemDto.getEcCode());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String txnId = "EQUALS_TEST_TXN";
        String order = "1";
        String declarationNo = "12345678901";
        String amount = "1500000";

        TxnPrintDocumentItemDto dto1 = new TxnPrintDocumentItemDto(
                txnId, order, declarationNo, "2024-01-01", "Test Description",
                amount, amount, "755", "3063");

        TxnPrintDocumentItemDto dto2 = new TxnPrintDocumentItemDto(
                txnId, order, declarationNo, "2024-01-01", "Test Description",
                amount, amount, "755", "3063");

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        txnPrintDocumentItemDto.setTxnId("TOSTRING_TXN");
        txnPrintDocumentItemDto.setOrder("1");
        txnPrintDocumentItemDto.setDeclarationNo("98765432109");
        txnPrintDocumentItemDto.setAmount("3000000");

        // When
        String result = txnPrintDocumentItemDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("TxnPrintDocumentItemDto"));
        assertTrue(result.contains("txnId"));
        assertTrue(result.contains("order"));
        assertTrue(result.contains("declarationNo"));
        assertTrue(result.contains("amount"));
    }

    @Test
    void testTaxDeclarationScenario() {
        // Given - Realistic tax declaration item scenario
        String txnId = "TAX_DECL_001";
        String order = "1";
        String declarationNo = "30644771632";
        String declarationDate = "24/05/2024";
        String transDesc = "Tờ khai thuế GTGT";
        String amount = "5000000";
        String amountVnd = "5000000";
        String chapterCode = "755";
        String ecCode = "3063";

        // When
        TxnPrintDocumentItemDto dto = new TxnPrintDocumentItemDto(
                txnId, order, declarationNo, declarationDate, transDesc,
                amount, amountVnd, chapterCode, ecCode);

        // Then
        assertNotNull(dto);
        assertEquals("TAX_DECL_001", dto.getTxnId());
        assertEquals("1", dto.getOrder());
        assertEquals("30644771632", dto.getDeclarationNo());
        assertEquals("24/05/2024", dto.getDeclarationDate());
        assertEquals("Tờ khai thuế GTGT", dto.getTransDesc());
        assertEquals("5000000", dto.getAmount());
        assertEquals("5000000", dto.getAmountVnd());
        assertEquals("755", dto.getChapterCode());
        assertEquals("3063", dto.getEcCode());
    }

    @Test
    void testMultipleItemsScenario() {
        // Given - Multiple tax items for a single transaction
        TxnPrintDocumentItemDto item1 = new TxnPrintDocumentItemDto(
                "MULTI_TXN_001", "1", "20240101001", "2024-01-01",
                "VAT Payment", "2000000", "2000000", "755", "3063");

        TxnPrintDocumentItemDto item2 = new TxnPrintDocumentItemDto(
                "MULTI_TXN_001", "2", "20240101002", "2024-01-01",
                "CIT Payment", "3000000", "3000000", "700", "2050");

        TxnPrintDocumentItemDto item3 = new TxnPrintDocumentItemDto(
                "MULTI_TXN_001", "3", "20240101003", "2024-01-01",
                "PIT Payment", "1000000", "1000000", "600", "1040");

        // Then
        assertEquals("MULTI_TXN_001", item1.getTxnId());
        assertEquals("MULTI_TXN_001", item2.getTxnId());
        assertEquals("MULTI_TXN_001", item3.getTxnId());

        assertEquals("1", item1.getOrder());
        assertEquals("2", item2.getOrder());
        assertEquals("3", item3.getOrder());

        assertEquals("VAT Payment", item1.getTransDesc());
        assertEquals("CIT Payment", item2.getTransDesc());
        assertEquals("PIT Payment", item3.getTransDesc());

        assertEquals("2000000", item1.getAmount());
        assertEquals("3000000", item2.getAmount());
        assertEquals("1000000", item3.getAmount());
    }

    @Test
    void testForeignCurrencyScenario() {
        // Given - Foreign currency transaction item
        TxnPrintDocumentItemDto foreignCurrencyItem = new TxnPrintDocumentItemDto(
                "FOREX_TXN_001", "1", "20240115001", "2024-01-15",
                "Import Duty Payment", "1000", "25000000", "800", "4070");

        // Then
        assertEquals("FOREX_TXN_001", foreignCurrencyItem.getTxnId());
        assertEquals("1000", foreignCurrencyItem.getAmount()); // USD amount
        assertEquals("25000000", foreignCurrencyItem.getAmountVnd()); // VND equivalent
        assertEquals("Import Duty Payment", foreignCurrencyItem.getTransDesc());
        assertEquals("800", foreignCurrencyItem.getChapterCode());
        assertEquals("4070", foreignCurrencyItem.getEcCode());
    }
}
