package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class CustomsCurrencyResTest {

    private MockedStatic<Translator> translatorMock;
    private CustomsCurrencyRes currencyRes;
    private static final String CC_CODE = "USD";
    private static final String CC_NAME = "US Dollar";
    private static final String TRANSLATED_NAME = "Đ<PERSON> la Mỹ";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        currencyRes = CustomsCurrencyRes.builder()
                .ccCode(CC_CODE)
                .ccName(CC_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(CC_NAME);

        assertThat(currencyRes).isNotNull();
        assertThat(currencyRes.getCcCode()).isEqualTo(CC_CODE);
        assertThat(currencyRes.getCcName()).isEqualTo(CC_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        CustomsCurrencyRes emptyCurrency = CustomsCurrencyRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyCurrency).isNotNull();
        assertThat(emptyCurrency.getCcCode()).isNull();
        assertThat(emptyCurrency.getCcName()).isNull();
    }

    @Test
    void getCcName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = currencyRes.getCcName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getCcName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(CC_NAME);

        String result = currencyRes.getCcName();

        assertThat(result).isEqualTo(CC_NAME);
    }

    @Test
    void getCcName_WhenCcCodeIsNull_ShouldHandleNullGracefully() {
        CustomsCurrencyRes nullCodeCurrency = CustomsCurrencyRes.builder()
                .ccName(CC_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeCurrency.getCcName();

        assertThat(result).isNull();
    }

    @Test
    void getCcName_WhenCcNameIsNull_ShouldHandleNullGracefully() {
        CustomsCurrencyRes nullNameCurrency = CustomsCurrencyRes.builder()
                .ccCode(CC_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameCurrency.getCcName();

        assertThat(result).isNull();
    }

    @Test
    void noArgsConstructor_ShouldCreateEmptyInstance() {
        CustomsCurrencyRes currency = new CustomsCurrencyRes();

        assertThat(currency).isNotNull();
        assertThat(currency.getCcCode()).isNull();
        assertThat(currency.getCcName()).isNull();
    }

    @Test
    void allArgsConstructor_ShouldCreatePopulatedInstance() {
        CustomsCurrencyRes currency = new CustomsCurrencyRes(CC_CODE, CC_NAME);

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(CC_NAME);

        assertThat(currency.getCcCode()).isEqualTo(CC_CODE);
        assertThat(currency.getCcName()).isEqualTo(CC_NAME);
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        CustomsCurrencyRes currency1 = CustomsCurrencyRes.builder()
                .ccCode(CC_CODE)
                .ccName(CC_NAME)
                .build();

        CustomsCurrencyRes currency2 = CustomsCurrencyRes.builder()
                .ccCode(CC_CODE)
                .ccName(CC_NAME)
                .build();

        assertThat(currency1).isEqualTo(currency2);
        assertThat(currency1.hashCode()).isEqualTo(currency2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        CustomsCurrencyRes currency1 = CustomsCurrencyRes.builder()
                .ccCode(CC_CODE)
                .ccName(CC_NAME)
                .build();

        CustomsCurrencyRes currency2 = CustomsCurrencyRes.builder()
                .ccCode("EUR")
                .ccName("Euro")
                .build();

        assertThat(currency1).isNotEqualTo(currency2);
        assertThat(currency1.hashCode()).isNotEqualTo(currency2.hashCode());
    }
}