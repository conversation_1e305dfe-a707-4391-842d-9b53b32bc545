package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmKhobacCodeNameEntityDtoTest {

    private TccDmKhobacCodeNameEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmKhobacCodeNameEntityDto();
        dto.setShkb("KB001");
        dto.setTenKhobac("Test Treasury");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("KB001", dto.getShkb());
        assertEquals("Test Treasury", dto.getTenKhobac());
    }

    @Test
    void testGetMaTenKhobac() {
        assertEquals("KB001 - Test Treasury", dto.getMaTenKhobac());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmKhobacCodeNameEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmKhobacCodeNameEntityDto dto1 = new TccDmKhobacCodeNameEntityDto("KB001", "Test Treasury");
        TccDmKhobacCodeNameEntityDto dto2 = new TccDmKhobacCodeNameEntityDto("KB001", "Test Treasury");
        TccDmKhobacCodeNameEntityDto dto3 = new TccDmKhobacCodeNameEntityDto("KB002", "Other Treasury");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmKhobacCodeNameEntityDto dto = new TccDmKhobacCodeNameEntityDto("KB001", "Test Treasury");
        assertEquals("KB001", dto.getShkb());
        assertEquals("Test Treasury", dto.getTenKhobac());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmKhobacCodeNameEntityDto dto = new TccDmKhobacCodeNameEntityDto();
        assertNull(dto.getShkb());
        assertNull(dto.getTenKhobac());
    }
}