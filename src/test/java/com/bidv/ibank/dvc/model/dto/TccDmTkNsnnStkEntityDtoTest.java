package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmTkNsnnStkEntityDtoTest {

    private TccDmTkNsnnStkEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmTkNsnnStkEntityDto();
        dto.setTk("TK001");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("TK001", dto.getTk());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmTkNsnnStkEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmTkNsnnStkEntityDto dto1 = new TccDmTkNsnnStkEntityDto("TK001", "Ten TK001");
        TccDmTkNsnnStkEntityDto dto2 = new TccDmTkNsnnStkEntityDto("TK001", "Ten TK001");
        TccDmTkNsnnStkEntityDto dto3 = new TccDmTkNsnnStkEntityDto("TK002", "Ten TK002");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmTkNsnnStkEntityDto dto = new TccDmTkNsnnStkEntityDto("TK001", "Ten TK001");
        assertEquals("TK001", dto.getTk());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmTkNsnnStkEntityDto dto = new TccDmTkNsnnStkEntityDto();
        assertNull(dto.getTk());
    }
}