package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class BatchItemDetailDtoTest {

    private BatchItemDetailDto batchItemDetailDto;

    @BeforeEach
    void setUp() {
        batchItemDetailDto = new BatchItemDetailDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        BatchItemDetailDto dto = new BatchItemDetailDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getTaxCode());
        assertNull(dto.getPayerName());
        assertNull(dto.getAmount());
        assertNull(dto.getCcy());
        assertNull(dto.getDeclarationNo());
        assertNull(dto.getErrCode());
        assertNull(dto.getTccErrCode());
        assertNull(dto.getTccErrMsg());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String taxCode = "**********";
        String payerName = "Test Company Ltd";
        String amount = "1000000";
        String ccy = "VND";
        String declarationNo = "***********";
        LocalDateTime createdDate = LocalDateTime.now();

        // When
        BatchItemDetailDto dto = BatchItemDetailDto.builder()
                .taxCode(taxCode)
                .payerName(payerName)
                .amount(amount)
                .ccy(ccy)
                .declarationNo(declarationNo)
                .createdDate(createdDate)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(taxCode, dto.getTaxCode());
        assertEquals(payerName, dto.getPayerName());
        assertEquals(amount, dto.getAmount());
        assertEquals(ccy, dto.getCcy());
        assertEquals(declarationNo, dto.getDeclarationNo());
        assertEquals(createdDate, dto.getCreatedDate());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String taxCode = "9876543210";
        String payerName = "Another Company";
        String amount = "2500000";
        String debitAccNo = "1234567890";
        String treasuryCode = "T001";
        String revAuthCode = "RA001";

        // When
        batchItemDetailDto.setTaxCode(taxCode);
        batchItemDetailDto.setPayerName(payerName);
        batchItemDetailDto.setAmount(amount);
        batchItemDetailDto.setDebitAccNo(debitAccNo);
        batchItemDetailDto.setTreasuryCode(treasuryCode);
        batchItemDetailDto.setRevAuthCode(revAuthCode);

        // Then
        assertEquals(taxCode, batchItemDetailDto.getTaxCode());
        assertEquals(payerName, batchItemDetailDto.getPayerName());
        assertEquals(amount, batchItemDetailDto.getAmount());
        assertEquals(debitAccNo, batchItemDetailDto.getDebitAccNo());
        assertEquals(treasuryCode, batchItemDetailDto.getTreasuryCode());
        assertEquals(revAuthCode, batchItemDetailDto.getRevAuthCode());
    }

    @Test
    void testGetAmountTextWithValidAmount() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.getLocale()
            mockedTranslator.when(() -> Translator.getLocale()).thenReturn(new Locale("vi", "VN"));

            // Given
            batchItemDetailDto.setAmount("1000000");
            batchItemDetailDto.setCcy("VND");
            batchItemDetailDto.setErrors(null); // No errors

            // When
            String result = batchItemDetailDto.getAmountText();

            // Then
            assertNotNull(result);
        }
    }

    @Test
    void testGetAmountTextWithAmountErrors() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.getLocale()
            mockedTranslator.when(() -> Translator.getLocale()).thenReturn(new Locale("vi", "VN"));

            // Given - Use the correct error code for AMOUNT_REQUIRED
            batchItemDetailDto.setErrCode("GOV0163"); // This is ResponseCode.AMOUNT_REQUIRED.code()
            batchItemDetailDto.setAmount("1000000");
            batchItemDetailDto.setCcy("VND");

            // When
            String result = batchItemDetailDto.getAmountText();

            // Then
            assertNull(result);
        }
    }

    @Test
    void testGetErrorsWithTccErrorOnly() {
        // Given
        batchItemDetailDto.setTccErrCode("TCC001");
        batchItemDetailDto.setTccErrMsg("TCC connection failed");

        // When
        List<ErrMsgDto> errors = batchItemDetailDto.getErrors();

        // Then
        assertNotNull(errors);
        assertEquals(1, errors.size());
        assertEquals("TCC001", errors.get(0).getCode());
        assertEquals("TCC connection failed", errors.get(0).getMessage());
    }

    @Test
    void testGetErrorsWithErrCodeOnly() {
        // Given
        batchItemDetailDto.setErrCode("ERR001,ERR002,ERR003");

        // When
        List<ErrMsgDto> errors = batchItemDetailDto.getErrors();

        // Then
        assertNotNull(errors);
        assertEquals(3, errors.size());
        assertEquals("ERR001", errors.get(0).getCode());
        assertEquals("ERR002", errors.get(1).getCode());
        assertEquals("ERR003", errors.get(2).getCode());
    }

    @Test
    void testGetErrorsWithBothTccAndErrCodes() {
        // Given
        batchItemDetailDto.setTccErrCode("TCC001");
        batchItemDetailDto.setTccErrMsg("TCC timeout");
        batchItemDetailDto.setErrCode("ERR001,ERR002");

        // When
        List<ErrMsgDto> errors = batchItemDetailDto.getErrors();

        // Then
        assertNotNull(errors);
        assertEquals(3, errors.size());
        // TCC error should be first
        assertEquals("TCC001", errors.get(0).getCode());
        assertEquals("TCC timeout", errors.get(0).getMessage());
        // Then ERR codes
        assertEquals("ERR001", errors.get(1).getCode());
        assertEquals("ERR002", errors.get(2).getCode());
    }

    @Test
    void testGetErrorsWithDirectlySetErrors() {
        // Given - Note: The actual implementation doesn't include directly set errors
        // This test documents the current behavior
        List<ErrMsgDto> directErrors = Arrays.asList(
                ErrMsgDto.builder().code("DIRECT001").message("Direct error 1").build(),
                ErrMsgDto.builder().code("DIRECT002").message("Direct error 2").build()
        );
        batchItemDetailDto.setErrors(directErrors);

        // When
        List<ErrMsgDto> errors = batchItemDetailDto.getErrors();

        // Then - The actual implementation only returns errors from tccErrCode and errCode, not the errors field
        assertNotNull(errors);
        assertTrue(errors.isEmpty()); // Because no tccErrCode or errCode is set
    }

    @Test
    void testGetErrorsWithAllTypesOfErrors() {
        // Given
        List<ErrMsgDto> directErrors = Arrays.asList(
                ErrMsgDto.builder().code("DIRECT001").message("Direct error").build()
        );
        batchItemDetailDto.setErrors(directErrors);
        batchItemDetailDto.setTccErrCode("TCC001");
        batchItemDetailDto.setTccErrMsg("TCC error");
        batchItemDetailDto.setErrCode("ERR001,ERR002");

        // When
        List<ErrMsgDto> errors = batchItemDetailDto.getErrors();

        // Then - The actual implementation only returns TCC and ERR code errors
        assertNotNull(errors);
        assertEquals(3, errors.size());
        // TCC error first
        assertEquals("TCC001", errors.get(0).getCode());
        // ERR codes last
        assertEquals("ERR001", errors.get(1).getCode());
        assertEquals("ERR002", errors.get(2).getCode());
    }

    @Test
    void testGetErrorsWithEmptyAndNullValues() {
        // Given
        batchItemDetailDto.setTccErrCode("");
        batchItemDetailDto.setTccErrMsg("");
        batchItemDetailDto.setErrCode("");

        // When
        List<ErrMsgDto> errors = batchItemDetailDto.getErrors();

        // Then
        assertNotNull(errors);
        assertTrue(errors.isEmpty());
    }

    @Test
    void testGetErrorsWithWhitespaceInErrCodes() {
        // Given
        batchItemDetailDto.setErrCode(" ERR001 , ERR002 ,  , ERR003 ");

        // When
        List<ErrMsgDto> errors = batchItemDetailDto.getErrors();

        // Then
        assertNotNull(errors);
        assertEquals(3, errors.size()); // Empty strings should be filtered out
        assertEquals("ERR001", errors.get(0).getCode());
        assertEquals("ERR002", errors.get(1).getCode());
        assertEquals("ERR003", errors.get(2).getCode());
    }

    @Test
    void testGetTreasuryNameWithoutErrors() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale()
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Kho bạc Nhà nước Hà Nội");

            // Given
            batchItemDetailDto.setTreasuryCode("T001");
            batchItemDetailDto.setTreasuryName("Treasury Hanoi");

            // When
            String result = batchItemDetailDto.getTreasuryName();

            // Then
            assertEquals("Kho bạc Nhà nước Hà Nội", result);
        }
    }

    @Test
    void testGetTreasuryNameWithErrors() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() - though it shouldn't be called due to error condition
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Should not be called");

            // Given - Use the actual error code value, not the enum name
            batchItemDetailDto.setErrCode("GOV0134"); // This is ResponseCode.TREASURY_CODE_INVALID_FORMAT.code()
            batchItemDetailDto.setTreasuryCode("T001");
            batchItemDetailDto.setTreasuryName("Treasury Name");

            // When
            String result = batchItemDetailDto.getTreasuryName();

            // Then
            assertEquals("", result);
        }
    }

    @Test
    void testGetPayerTypeNameWithValidType() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale()
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Doanh nghiệp");

            // Given
            batchItemDetailDto.setPayerType("1");

            // When
            String result = batchItemDetailDto.getPayerTypeName();

            // Then
            assertEquals("Doanh nghiệp", result);
        }
    }

    @Test
    void testGetPayerTypeNameWithInvalidType() {
        // Given
        batchItemDetailDto.setPayerType("invalid");

        // When
        String result = batchItemDetailDto.getPayerTypeName();

        // Then
        assertEquals("", result);
    }

    @Test
    void testGetPayerTypeNameWithNullType() {
        // Given
        batchItemDetailDto.setPayerType(null);

        // When
        String result = batchItemDetailDto.getPayerTypeName();

        // Then
        assertEquals("", result);
    }

    @Test
    void testGetBenBankNameWithErrors() {
        // Given
        batchItemDetailDto.setErrCode("SOME_ERROR");
        batchItemDetailDto.setBenBankName("Test Bank");

        // When
        String result = batchItemDetailDto.getBenBankName();

        // Then
        assertEquals("", result);
    }

    @Test
    void testGetBenBankNameWithoutErrors() {
        // Given
        batchItemDetailDto.setBenBankName("BIDV Bank");

        // When
        String result = batchItemDetailDto.getBenBankName();

        // Then
        assertEquals("BIDV Bank", result);
    }

    @Test
    void testComplexTaxDeclarationScenario() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator for realistic scenario
            mockedTranslator.when(() -> Translator.getLocale()).thenReturn(new Locale("vi", "VN"));
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback

            // Given - Complex tax declaration item
            BatchItemDetailDto complexDto = BatchItemDetailDto.builder()
                    .taxCode("**********012")
                    .payerName("Công ty TNHH ABC Manufacturing")
                    .payerAddr("123 Đường Công Nghiệp, KCN Thăng Long, Hà Nội")
                    .declarationNo("***********")
                    .declarationDate("24/05/2024")
                    .declarationYear("2024")
                    .amount("5000000")
                    .ccy("VND")
                    .debitAccNo("1234567890123456")
                    .treasuryCode("101")
                    .treasuryName("Kho bạc Nhà nước Hà Nội")
                    .revAuthCode("RA001")
                    .revAuthName("Cục Thuế Thành phố Hà Nội")
                    .chapterCode("755")
                    .chapterName("Thuế GTGT")
                    .ecCode("3063")
                    .ecName("Thu thuế GTGT")
                    .transDesc("Nộp thuế GTGT quý 1/2024")
                    .feeTotal(new BigDecimal("50000"))
                    .feeCcy("VND")
                    .createdDate(LocalDateTime.now())
                    .build();

            // When
            String treasuryName = complexDto.getTreasuryName();
            String revAuthName = complexDto.getRevAuthName();
            String chapterName = complexDto.getChapterName();
            String ecName = complexDto.getEcName();
            List<ErrMsgDto> errors = complexDto.getErrors();

            // Then
            assertEquals("Kho bạc Nhà nước Hà Nội", treasuryName);
            assertEquals("Cục Thuế Thành phố Hà Nội", revAuthName);
            assertEquals("Thuế GTGT", chapterName);
            assertEquals("Thu thuế GTGT", ecName);
            assertTrue(errors.isEmpty());
            assertEquals("**********012", complexDto.getTaxCode());
            assertEquals("***********", complexDto.getDeclarationNo());
            assertEquals("5000000", complexDto.getAmount());
        }
    }

    @Test
    void testAllNameMethodsWithSpecificErrors() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock Translator.toLocale() - though it shouldn't be called due to error conditions
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Should not be called");

            // Given - Set specific error codes using actual ResponseCode values, not enum names
            batchItemDetailDto.setErrCode("GOV0134,GOV0140,GOV0147"); // TREASURY_CODE_INVALID_FORMAT, REV_AUTH_CODE_INVALID_FORMAT, CHAPTER_CODE_INVALID_FORMAT
            batchItemDetailDto.setTreasuryCode("T001");
            batchItemDetailDto.setTreasuryName("Treasury");
            batchItemDetailDto.setRevAuthCode("RA001");
            batchItemDetailDto.setRevAuthName("Revenue Authority");
            batchItemDetailDto.setChapterCode("755");
            batchItemDetailDto.setChapterName("Chapter");

            // When & Then
            assertEquals("", batchItemDetailDto.getTreasuryName());
            assertEquals("", batchItemDetailDto.getRevAuthName());
            assertEquals("", batchItemDetailDto.getChapterName());
        }
    }

    @Test
    void testFieldValidation() {
        // Test all major fields can be set and retrieved
        batchItemDetailDto.setBatchItemId("item-123");
        batchItemDetailDto.setOrgId("org-456");
        batchItemDetailDto.setPayerType("1");
        batchItemDetailDto.setAltTaxCode("alt-tax-123");
        batchItemDetailDto.setAltPayerName("Alternative Payer");

        assertEquals("item-123", batchItemDetailDto.getBatchItemId());
        assertEquals("org-456", batchItemDetailDto.getOrgId());
        assertEquals("1", batchItemDetailDto.getPayerType());
        assertEquals("alt-tax-123", batchItemDetailDto.getAltTaxCode());
        assertEquals("Alternative Payer", batchItemDetailDto.getAltPayerName());
    }
}
