package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmDbhcCodeNameEntityDtoTest {

    private TccDmDbhcCodeNameEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmDbhcCodeNameEntityDto();
        dto.setMaDbhc("DB001");
        dto.setTenDbhc("Test DBHC");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("DB001", dto.getMaDbhc());
        assertEquals("Test DBHC", dto.getTenDbhc());
    }

    @Test
    void testGetMaTenDbhc() {
        assertEquals("DB001 - Test DBHC", dto.getMaTenDbhc());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmDbhcCodeNameEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmDbhcCodeNameEntityDto dto1 = new TccDmDbhcCodeNameEntityDto("DB001", "Test DBHC");
        TccDmDbhcCodeNameEntityDto dto2 = new TccDmDbhcCodeNameEntityDto("DB001", "Test DBHC");
        TccDmDbhcCodeNameEntityDto dto3 = new TccDmDbhcCodeNameEntityDto("DB002", "Other DBHC");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmDbhcCodeNameEntityDto dto = new TccDmDbhcCodeNameEntityDto("DB001", "Test DBHC");
        assertEquals("DB001", dto.getMaDbhc());
        assertEquals("Test DBHC", dto.getTenDbhc());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmDbhcCodeNameEntityDto dto = new TccDmDbhcCodeNameEntityDto();
        assertNull(dto.getMaDbhc());
        assertNull(dto.getTenDbhc());
    }
}