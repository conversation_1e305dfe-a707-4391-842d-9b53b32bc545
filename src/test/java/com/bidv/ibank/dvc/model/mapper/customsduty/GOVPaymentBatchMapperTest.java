package com.bidv.ibank.dvc.model.mapper.customsduty;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;
import com.bidv.ibank.dvc.model.dto.BatchItemExportDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxItemExportDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxResultExportDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.param.*;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.framework.util.mapper.ModelMapperUtils;

@ExtendWith(MockitoExtension.class)
class GOVPaymentBatchMapperTest {

    @InjectMocks
    private GOVPaymentBatchMapper mapper;

    @Mock
    private CurrentUser currentUser;

    @Mock
    private CurrentUser.UserInfo userInfo;

    private static final Long TEST_CUS_ID = 123L;
    private static final String TEST_BATCH_ID = "TEST_BATCH_ID";

    private GOVPaymentBatchEntity mockBatchEntity;
    private GOVPaymentBatchItemEntity mockBatchItemEntity;
    private BatchTaxImportItemDto mockImportItemDto;
    private CustomerDto mockCustomerDto;
    private InquiryCustomsDutyRes mockInquiryRes;

    @BeforeEach
    void setUp() {
        // Setup mock batch entity
        mockBatchEntity = GOVPaymentBatchEntity.builder()
                .id("BATCH001")
                .batchNo("BN001")
                .name("Test Batch")
                .status(BatchStatusEnum.PROCESSING)
                .batchType(BatchTypeEnum.INQUIRY)
                .cusId(TEST_CUS_ID)
                .checksum("checksum")
                .fileKey("fileKey")
                .build();

        // Setup mock batch item entity with related entities
        mockBatchItemEntity = GOVPaymentBatchItemEntity.builder()
                .id("ITEM001")
                .batchId("BATCH001")
                .status(BatchItemStatusEnum.VALID)
                .declarationNo("DEC123")
                .declarationYear("2024")
                .taxCode("0*********")
                .errCode("ERR001,ERR002")
                .tccErrCode("TCC001")
                .tccErrMsg("TCC Error")
                .build();

        // Setup mock import item DTO
        mockImportItemDto = BatchTaxImportItemDto.builder()
                .declarationNo("DEC123")
                .declarationYear("2024")
                .tccErrCode("TCC001")
                .tccErrMsg("TCC Error")
                .build();
        mockImportItemDto.setRownum(1);

        // Setup mock customer DTO
        mockCustomerDto = new CustomerDto();
        mockCustomerDto.setTaxCode("0*********");

        // Setup mock inquiry response with payer type
        mockInquiryRes = new InquiryCustomsDutyRes();
        mockInquiryRes.setPayerType(1);
    }

    @Test
    void toEntity_WithValidFile_ShouldMapAllFields() {
        try (MockedStatic<AuthenticationUtils> authUtils = mockStatic(AuthenticationUtils.class)) {
            when(currentUser.getUser()).thenReturn(userInfo);
            when(userInfo.getCusId()).thenReturn(TEST_CUS_ID);
            authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test.csv",
                    "text/csv",
                    "test content".getBytes());
            String checksum = "testChecksum";
            String fileKey = "testFileKey";
            BatchTypeEnum batchType = BatchTypeEnum.PAYMENT;

            GOVPaymentBatchEntity result = mapper.toEntity(file, checksum, fileKey, batchType);

            assertNotNull(result);
            assertTrue(result.getBatchNo().startsWith("BDR"));
            assertEquals(BatchStatusEnum.PROCESSING, result.getStatus());
            assertEquals(checksum, result.getChecksum());
            assertEquals(TEST_CUS_ID, result.getCusId());
            assertEquals(fileKey, result.getFileKey());
            assertEquals(batchType, result.getBatchType());
            assertEquals(file.getSize(), result.getFileSize());
            assertEquals("test", result.getName());
        }
    }

    @Test
    void toItemEntity_WithValidBatchItem_ShouldMapAllFields() {
        BatchImportItemDto batchItem = createValidBatchImportItemDto();

        GOVPaymentBatchItemEntity result = mapper.toItemEntity(batchItem, TEST_BATCH_ID);

        assertNotNull(result);
        assertEquals(TEST_BATCH_ID, result.getBatchId());
        assertEquals(BatchItemStatusEnum.VALID, result.getStatus());
        assertEquals(batchItem.getDebitAccNo(), result.getDebitAccNo());
        assertEquals(batchItem.getTaxCode(), result.getTaxCode());
        assertEquals(batchItem.getPayerName(), result.getPayerName());
        assertEquals(batchItem.getTreasuryCode(), result.getShkb());
        assertEquals(batchItem.getRevAccCode(), result.getMaTk());
        assertEquals(batchItem.getRevAuthCode(), result.getMaCqthu());
        assertEquals(batchItem.getAmount(), result.getAmount());
        assertEquals(batchItem.getRownum(), result.getBatchOrder());
    }

    @Test
    void toItemEntity_WithInvalidBatchItem_ShouldMapErrorCodes() {
        BatchImportItemDto batchItem = createInvalidBatchImportItemDto();

        GOVPaymentBatchItemEntity result = mapper.toItemEntity(batchItem, TEST_BATCH_ID);

        assertNotNull(result);
        assertEquals(BatchItemStatusEnum.INVALID, result.getStatus());
        assertEquals(ResponseCode.TIMEOUT_01.code(), result.getErrCode());
        assertEquals("TCC-ERR", result.getTccErrCode());
    }

    @Test
    void toListDto_WithValidEntity_ShouldMapAllFields() {
        BatchListRes result = mapper.toListDto(mockBatchEntity);

        assertNotNull(result);
        assertEquals(mockBatchEntity.getId(), result.getBatchId());
        assertEquals(mockBatchEntity.getBatchNo(), result.getBatchNo());
        assertEquals(mockBatchEntity.getName(), result.getBatchName());
        assertEquals(mockBatchEntity.getStatus().name(), result.getStatus());
    }

    @Test
    void toExportDto_WithValidItems_ShouldMapAllFields() {
        try (MockedStatic<Translator> translator = mockStatic(Translator.class);
                MockedStatic<ModelMapperUtils> modelMapper = mockStatic(ModelMapperUtils.class)) {

            String translatedStatus = "Translated Status";
            translator.when(() -> Translator.toLocale(anyString())).thenReturn(translatedStatus);

            GOVPaymentBatchItemEntity itemEntity = createValidBatchItemEntity();
            BatchItemDetailDto detailDto = createBatchItemDetailDto();
            BatchItemExportDto exportDto = createBatchItemExportDto();

            modelMapper.when(() -> ModelMapperUtils.map(any(BatchItemDetailDto.class), eq(BatchItemExportDto.class)))
                    .thenReturn(exportDto);

            Set<GOVPaymentBatchItemEntity> items = Set.of(itemEntity);

            List<BatchItemExportDto> result = mapper.toExportDto(items);

            assertNotNull(result);
            assertEquals(1, result.size());
            BatchItemExportDto resultDto = result.get(0);
            assertEquals("1", resultDto.getBatchOrder());
            assertEquals(translatedStatus, resultDto.getStatus());
            assertEquals(detailDto.getTreasuryName(), resultDto.getTreasuryName());
            assertEquals(detailDto.getRevAccName(), resultDto.getRevAccName());
            assertEquals(detailDto.getRevAuthName(), resultDto.getRevAuthName());
            assertEquals(detailDto.getAdmAreaName(), resultDto.getAdmAreaName());
        }
    }

    @Test
    void toExportDto_WithEmptyItems_ShouldReturnEmptyList() {
        List<BatchItemExportDto> result = mapper.toExportDto(Set.of());
        assertTrue(result.isEmpty());
    }

    @Test
    void toDetailDto_WithValidEntity_ShouldMapAllFields() {
        GOVPaymentBatchEntity entity = createValidBatchEntity();
        Set<GOVPaymentBatchItemEntity> items = new HashSet<>(Arrays.asList(
                createValidBatchItemEntity(),
                createInvalidBatchItemEntity()));
        entity.setGovPaymentBatchItemList(items);

        BatchDetailRes result = mapper.toDetailDto(entity);

        assertNotNull(result);
        assertEquals(1, result.getValidItems().size());
        assertEquals(1, result.getInvalidItems().size());
        assertEquals(entity.getFileSize(), result.getFileSize());
        assertEquals(entity.getName(), result.getFileName());
        assertEquals(entity.getBatchNo(), result.getBatchNo());
    }

    @Test
    void toDetailDto_WithNullItems_ShouldReturnEmptyLists() {
        GOVPaymentBatchEntity entity = createValidBatchEntity();
        entity.setGovPaymentBatchItemList(null);

        BatchDetailRes result = mapper.toDetailDto(entity);

        assertNotNull(result);
        assertTrue(result.getValidItems().isEmpty());
        assertTrue(result.getInvalidItems().isEmpty());
    }

    @Test
    void testToTaxResultExportDto() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString())).thenReturn(BatchItemStatusEnum.VALID.name());

            List<GOVPaymentBatchItemEntity> batchItems = Arrays.asList(mockBatchItemEntity);
            List<BatchTaxResultExportDto> results = mapper.toTaxResultExportDto(batchItems);

            assertNotNull(results);
            assertEquals(1, results.size());
            BatchTaxResultExportDto result = results.get(0);

            assertEquals(mockBatchItemEntity.getStatus().name(), result.getStatus());
            assertEquals(mockBatchItemEntity.getErrCode(), result.getErrCodes());
            assertEquals(mockBatchItemEntity.getTccErrMsg(), result.getTccErrMsg());
            assertEquals(mockBatchItemEntity.getDeclarationNo(), result.getDeclarationNo());
            assertEquals(mockBatchItemEntity.getDeclarationYear(), result.getDeclarationYear());
        }
    }

    @Test
    void testToTaxItemExportDto() {
        mockBatchItemEntity.setDeclarationDate("01/01/2024");
        List<GOVPaymentBatchItemEntity> batchItems = Arrays.asList(mockBatchItemEntity);
        List<BatchTaxItemExportDto> results = mapper.toTaxItemExportDto(batchItems);

        assertNotNull(results);
        assertEquals(1, results.size());
        BatchTaxItemExportDto result = results.get(0);

        assertEquals("1", result.getBatchOrder());
        assertEquals(mockBatchItemEntity.getTaxCode(), result.getTaxCode());
        assertEquals(mockBatchItemEntity.getDeclarationNo(), result.getDeclarationNo());
    }

    @Test
    void testToBatchTaxItemEntity() {
        GOVPaymentBatchItemEntity result = mapper.toBatchTaxItemEntity(
                mockImportItemDto, "BATCH001", mockCustomerDto, mockInquiryRes);

        assertNotNull(result);
        assertEquals("BATCH001", result.getBatchId());
        assertEquals(BatchItemStatusEnum.INVALID, result.getStatus());
        assertEquals(mockImportItemDto.getDeclarationNo(), result.getDeclarationNo());
        assertEquals(mockImportItemDto.getDeclarationYear(), result.getDeclarationYear());
        assertEquals(mockCustomerDto.getTaxCode(), result.getTaxCode());
        assertEquals(mockImportItemDto.getTccErrCode(), result.getTccErrCode());
        assertEquals(mockImportItemDto.getTccErrMsg(), result.getTccErrMsg());
        assertEquals(mockImportItemDto.getRownum(), result.getBatchOrder());
        assertEquals("1", result.getPayerType());
    }

    private BatchImportItemDto createValidBatchImportItemDto() {
        BatchImportItemDto dto = BatchImportItemDto.builder()
                .debitAccNo("*********")
                .taxCode("TAX123")
                .payerName("Test Payer")
                .treasuryCode("TREA")
                .revAccCode("REV1")
                .revAuthCode("AUTH1")
                .amount("1000")
                .build();
        dto.setRownum(1);
        return dto;
    }

    private BatchImportItemDto createInvalidBatchImportItemDto() {
        Map<String, Set<String>> fieldErrors = new HashMap<>();
        fieldErrors.put("field1", Set.of(ResponseCode.TIMEOUT_01.code()));

        return BatchImportItemDto.builder()
                .fieldErrors(fieldErrors)
                .tccErrCode("TCC-ERR")
                .build();
    }

    private GOVPaymentBatchEntity createValidBatchEntity() {
        GOVPaymentBatchEntity entity = new GOVPaymentBatchEntity();
        entity.setId(TEST_BATCH_ID);
        entity.setName("Test Batch");
        entity.setBatchNo("BDR123");
        entity.setStatus(BatchStatusEnum.PROCESSING);
        entity.setFileSize(1000L);
        entity.setCreatedDate(LocalDateTime.now());
        entity.setBatchType(BatchTypeEnum.PAYMENT);
        return entity;
    }

    private GOVPaymentBatchItemEntity createValidBatchItemEntity() {
        TccDmKhobacEntity khobac = new TccDmKhobacEntity();
        khobac.setTen("Treasury Name");

        TccDmTkNsnnEntity tkNsnn = new TccDmTkNsnnEntity();
        tkNsnn.setTen("Revenue Account Name");

        TccDmCqthuEntity cqthu = new TccDmCqthuEntity();
        cqthu.setTen("Authority Name");

        TccDmDbhcEntity dbhc = new TccDmDbhcEntity();
        dbhc.setTen("Area Name");

        return GOVPaymentBatchItemEntity.builder()
                .id("ITEM1")
                .batchId(TEST_BATCH_ID)
                .status(BatchItemStatusEnum.VALID)
                .debitAccNo("*********")
                .taxCode("TAX123")
                .payerName("Test Payer")
                .shkb("TREASURY1")
                .maTk("REV1")
                .maCqthu("AUTH1")
                .amount("1000")
                .batchOrder(1)
                .tccDmKhobacEntity(khobac)
                .tccDmTkNsnnEntity(tkNsnn)
                .tccDmCqthuEntity(cqthu)
                .tccDmDbhcEntity(dbhc)
                .declarationDate("01/01/2024")
                .build();
    }

    private GOVPaymentBatchItemEntity createInvalidBatchItemEntity() {
        return GOVPaymentBatchItemEntity.builder()
                .id("ITEM2")
                .batchId(TEST_BATCH_ID)
                .status(BatchItemStatusEnum.INVALID)
                .errCode("TIMEOUT_01")
                .batchOrder(2)
                .declarationDate("01/01/2024")
                .build();
    }

    private BatchItemDetailDto createBatchItemDetailDto() {
        return BatchItemDetailDto.builder()
                .batchItemId("ITEM1")
                .debitAccNo("*********")
                .taxCode("TAX123")
                .payerName("Test Payer")
                .treasuryCode("TREASURY1")
                .treasuryName("Treasury Name")
                .revAccCode("REV1")
                .revAccName("Revenue Account Name")
                .revAuthCode("AUTH1")
                .revAuthName("Authority Name")
                .admAreaCode("ADM1")
                .admAreaName("Area Name")
                .amount("1000")
                .build();
    }

    private BatchItemExportDto createBatchItemExportDto() {
        return BatchItemExportDto.builder()
                .batchItemId("ITEM1")
                .debitAccNo("*********")
                .taxCode("TAX123")
                .payerName("Test Payer")
                .treasuryCode("TREASURY1")
                .treasuryName("Treasury Name")
                .revAccCode("REV1")
                .revAccName("Revenue Account Name")
                .revAuthCode("AUTH1")
                .revAuthName("Authority Name")
                .admAreaCode("ADM1")
                .admAreaName("Area Name")
                .amount("1000")
                .build();
    }

    @Test
    void toDto_ShouldThrowUnsupportedOperationException() {
        assertThrows(UnsupportedOperationException.class, () -> mapper.toDto(new Object()));
    }

    @Test
    void toEntity_ShouldThrowUnsupportedOperationException() {
        assertThrows(UnsupportedOperationException.class, () -> mapper.toEntity(new Object()));
    }
}