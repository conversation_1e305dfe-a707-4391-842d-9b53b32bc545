package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

class TxnPendingListResTest {

    @Test
    void testBuilder() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            LocalDateTime createdDate = LocalDateTime.now();
            TxnPendingListRes res = TxnPendingListRes.builder()
                    .txnId("TXN001")
                    .debitAccNo("**********")
                    .taxCode("**********")
                    .declarationNo("DN001")
                    .amount(BigDecimal.valueOf(1000000))
                    .ccy("VND")
                    .treasuryCode("TC001")
                    .treasuryName("Treasury Name")
                    .batchNo("BATCH001")
                    .status("PENDING")
                    .createdDate(createdDate)
                    .build();

            mockedTranslator.when(() -> Translator.toLocale("TC001", "Treasury Name"))
                    .thenReturn("Translated Treasury Name");

            assertThat(res.getTxnId()).isEqualTo("TXN001");
            assertThat(res.getDebitAccNo()).isEqualTo("**********");
            assertThat(res.getTaxCode()).isEqualTo("**********");
            assertThat(res.getDeclarationNo()).isEqualTo("DN001");
            assertThat(res.getAmount()).isEqualTo(BigDecimal.valueOf(1000000));
            assertThat(res.getCcy()).isEqualTo("VND");
            assertThat(res.getTreasuryCode()).isEqualTo("TC001");
            assertThat(res.getTreasuryName()).isEqualTo("Translated Treasury Name");
            assertThat(res.getBatchNo()).isEqualTo("BATCH001");
            assertThat(res.getStatus()).isEqualTo("PENDING");
            assertThat(res.getCreatedDate()).isEqualTo(createdDate);
        }
    }

    @Test
    void testTreasuryNameTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            TxnPendingListRes res = TxnPendingListRes.builder()
                    .treasuryCode("TC001")
                    .treasuryName("Treasury Name")
                    .build();

            mockedTranslator.when(() -> Translator.toLocale("TC001", "Treasury Name"))
                    .thenReturn("Translated Treasury Name");

            assertThat(res.getTreasuryName()).isEqualTo("Translated Treasury Name");
        }
    }

    @Test
    void testStatusNameTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            TxnPendingListRes res = TxnPendingListRes.builder()
                    .status("PENDING")
                    .build();

            mockedTranslator.when(() -> Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + ".PENDING", "PENDING"))
                    .thenReturn("Chờ xử lý");

            assertThat(res.getStatusName()).isEqualTo("Chờ xử lý");
        }
    }

    @Test
    void testNoArgsConstructor() {
        TxnPendingListRes res = new TxnPendingListRes();
        assertThat(res).isNotNull();
        assertThat(res.getTxnId()).isNull();
        assertThat(res.getAmount()).isNull();
        assertThat(res.getCreatedDate()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            String txnId = "TXN001";
            String debitAccNo = "**********";
            String taxCode = "**********";
            String declarationNo = "DN001";
            BigDecimal amount = BigDecimal.valueOf(1000000);
            String ccy = "VND";
            String treasuryCode = "TC001";
            String treasuryName = "Treasury Name";
            String batchNo = "BATCH001";
            String status = "PENDING";
            LocalDateTime createdDate = LocalDateTime.now();
            String admAreaCode = "ADM001";
            String admAreaName = "Adm Area Name";
            String revAccCode = "REV001";
            String revAccName = "Rev Acc Name";
            String revAuthCode = "REV001";
            String revAuthName = "Rev Auth Name";

            mockedTranslator.when(() -> Translator.toLocale("TC001", "Treasury Name"))
                    .thenReturn("Translated Treasury Name");

            TxnPendingListRes res = TxnPendingListRes.builder()
                    .txnId(txnId)
                    .debitAccNo(debitAccNo)
                    .taxCode(taxCode)
                    .declarationNo(declarationNo)
                    .amount(amount)
                    .ccy(ccy)
                    .batchNo(batchNo)
                    .status(status)
                    .createdDate(createdDate)
                    .treasuryCode(treasuryCode)
                    .treasuryName(treasuryName)
                    .admAreaCode(admAreaCode)
                    .admAreaName(admAreaName)
                    .revAccCode(revAccCode)
                    .revAccName(revAccName)
                    .revAuthCode(revAuthCode)
                    .revAuthName(revAuthName)
                    .priority(false)
                    .build();

            assertThat(res.getTxnId()).isEqualTo(txnId);
            assertThat(res.getDebitAccNo()).isEqualTo(debitAccNo);
            assertThat(res.getTaxCode()).isEqualTo(taxCode);
            assertThat(res.getDeclarationNo()).isEqualTo(declarationNo);
            assertThat(res.getAmount()).isEqualTo(amount);
            assertThat(res.getCcy()).isEqualTo(ccy);
            assertThat(res.getTreasuryCode()).isEqualTo(treasuryCode);
            assertThat(res.getTreasuryName()).isEqualTo("Translated Treasury Name");
            assertThat(res.getBatchNo()).isEqualTo(batchNo);
            assertThat(res.getStatus()).isEqualTo(status);
            assertThat(res.getCreatedDate()).isEqualTo(createdDate);
        }
    }
}