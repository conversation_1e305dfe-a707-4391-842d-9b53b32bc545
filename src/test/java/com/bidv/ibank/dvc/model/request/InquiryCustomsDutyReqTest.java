package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDate;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class InquiryCustomsDutyReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testBuilder() {
        Integer declarationYear = LocalDate.now().minusYears(0).getYear();
        InquiryCustomsDutyReq req = InquiryCustomsDutyReq.builder()
                .taxCode("**********")
                .declarationNo("***********")
                .declarationYear(String.valueOf(declarationYear))
                .build();

        assertThat(req.getTaxCode()).isEqualTo("**********");
        assertThat(req.getDeclarationNo()).isEqualTo("***********");
        assertThat(req.getDeclarationYear()).isEqualTo(String.valueOf(declarationYear));
    }

    @Test
    void whenTaxCodeIsNull_thenValidationFails() {
        InquiryCustomsDutyReq req = InquiryCustomsDutyReq.builder()
                .declarationNo("***********")
                .build();

        Set<ConstraintViolation<InquiryCustomsDutyReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("taxCode");
    }

    @Test
    void whenTaxCodeExceeds13Characters_thenValidationFails() {
        InquiryCustomsDutyReq req = InquiryCustomsDutyReq.builder()
                .taxCode("**********000000000000")
                .build();

        Set<ConstraintViolation<InquiryCustomsDutyReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("taxCode");
    }

    @Test
    void whenDeclarationNoExceeds30Characters_thenValidationFails() {
        InquiryCustomsDutyReq req = InquiryCustomsDutyReq.builder()
                .taxCode("**********")
                .declarationNo("***********0000000000000000000000")
                .build();

        Set<ConstraintViolation<InquiryCustomsDutyReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("declarationNo");
    }

    @Test
    void whenAllFieldsAreValid_thenValidationSucceeds() {
        InquiryCustomsDutyReq req = InquiryCustomsDutyReq.builder()
                .taxCode("**********")
                .declarationNo("***********")
                .declarationYear(String.valueOf(LocalDate.now().getYear()))
                .build();

        Set<ConstraintViolation<InquiryCustomsDutyReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void testNoArgsConstructor() {
        InquiryCustomsDutyReq req = new InquiryCustomsDutyReq();
        assertThat(req).isNotNull();
        assertThat(req.getTaxCode()).isNull();
        assertThat(req.getDeclarationNo()).isNull();
        assertThat(req.getDeclarationYear()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        Integer declarationYear = LocalDate.now().getYear();
        InquiryCustomsDutyReq req = new InquiryCustomsDutyReq(
                "**********",
                "***********",
                String.valueOf(declarationYear));

        assertThat(req.getTaxCode()).isEqualTo("**********");
        assertThat(req.getDeclarationNo()).isEqualTo("***********");
        assertThat(req.getDeclarationYear()).isEqualTo(String.valueOf(declarationYear));
    }
}