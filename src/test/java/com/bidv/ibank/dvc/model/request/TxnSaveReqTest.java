package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnSaveReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testBuilder() {
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey("TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86")
                .build();

        assertThat(req.getTransKey()).isEqualTo("TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86");
    }

    @Test
    void whenTransKeyIsNull_thenValidationFails() {
        TxnSaveReq req = TxnSaveReq.builder().build();

        Set<ConstraintViolation<TxnSaveReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("transKey");
    }

    @Test
    void whenTransKeyIsEmpty_thenValidationFails() {
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey("")
                .build();

        Set<ConstraintViolation<TxnSaveReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("transKey");
    }

    @Test
    void whenTransKeyExceeds150Characters_thenValidationFails() {
        String longTransKey = "TXNKEY_REQUEST_APPROVAL_" + "x".repeat(150);
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey(longTransKey)
                .build();

        Set<ConstraintViolation<TxnSaveReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("transKey");
    }

    @Test
    void whenTransKeyIsValid_thenValidationSucceeds() {
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey("TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86")
                .build();

        Set<ConstraintViolation<TxnSaveReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void testNoArgsConstructor() {
        TxnSaveReq req = new TxnSaveReq();
        assertThat(req).isNotNull();
        assertThat(req.getTransKey()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        String transKey = "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86";
        TxnSaveReq req = new TxnSaveReq(transKey);

        assertThat(req.getTransKey()).isEqualTo(transKey);
    }
}