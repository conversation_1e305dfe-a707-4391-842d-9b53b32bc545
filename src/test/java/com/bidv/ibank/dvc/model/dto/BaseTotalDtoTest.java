package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class BaseTotalDtoTest {

    private BaseTotalDto baseTotalDto;

    @BeforeEach
    void setUp() {
        baseTotalDto = new BaseTotalDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        BaseTotalDto dto = new BaseTotalDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getTotal());
        assertNull(dto.getTotalSuccess());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Long total = 100L;
        Long totalSuccess = 80L;

        // When
        BaseTotalDto dto = new BaseTotalDto(total, totalSuccess);

        // Then
        assertEquals(total, dto.getTotal());
        assertEquals(totalSuccess, dto.getTotalSuccess());
    }

    @Test
    void testBuilderPattern() {
        // Given
        Long total = 100L;
        Long totalSuccess = 80L;

        // When
        BaseTotalDto dto = BaseTotalDto.builder()
                .total(total)
                .totalSuccess(totalSuccess)
                .build();

        // Then
        assertEquals(total, dto.getTotal());
        assertEquals(totalSuccess, dto.getTotalSuccess());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        Long total = 100L;
        Long totalSuccess = 80L;

        // When
        baseTotalDto.setTotal(total);
        baseTotalDto.setTotalSuccess(totalSuccess);

        // Then
        assertEquals(total, baseTotalDto.getTotal());
        assertEquals(totalSuccess, baseTotalDto.getTotalSuccess());
    }

    @Test
    void testGetTotalFail_ValidValues() {
        // Given
        Long total = 100L;
        Long totalSuccess = 80L;
        Long expectedTotalFail = 20L;

        baseTotalDto.setTotal(total);
        baseTotalDto.setTotalSuccess(totalSuccess);

        // When
        Long result = baseTotalDto.getTotalFail();

        // Then
        assertEquals(expectedTotalFail, result);
    }

    @Test
    void testGetTotalFail_ZeroSuccess() {
        // Given
        Long total = 100L;
        Long totalSuccess = 0L;
        Long expectedTotalFail = 100L;

        baseTotalDto.setTotal(total);
        baseTotalDto.setTotalSuccess(totalSuccess);

        // When
        Long result = baseTotalDto.getTotalFail();

        // Then
        assertEquals(expectedTotalFail, result);
    }

    @Test
    void testGetTotalFail_AllSuccess() {
        // Given
        Long total = 100L;
        Long totalSuccess = 100L;
        Long expectedTotalFail = 0L;

        baseTotalDto.setTotal(total);
        baseTotalDto.setTotalSuccess(totalSuccess);

        // When
        Long result = baseTotalDto.getTotalFail();

        // Then
        assertEquals(expectedTotalFail, result);
    }

    @Test
    void testGetTotalFail_NegativeResult() {
        // Given - edge case where totalSuccess > total
        Long total = 50L;
        Long totalSuccess = 80L;
        Long expectedTotalFail = -30L;

        baseTotalDto.setTotal(total);
        baseTotalDto.setTotalSuccess(totalSuccess);

        // When
        Long result = baseTotalDto.getTotalFail();

        // Then
        assertEquals(expectedTotalFail, result);
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        Long total = 100L;
        Long totalSuccess = 80L;

        BaseTotalDto dto1 = BaseTotalDto.builder()
                .total(total)
                .totalSuccess(totalSuccess)
                .build();

        BaseTotalDto dto2 = BaseTotalDto.builder()
                .total(total)
                .totalSuccess(totalSuccess)
                .build();

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        Long total = 100L;
        Long totalSuccess = 80L;

        baseTotalDto.setTotal(total);
        baseTotalDto.setTotalSuccess(totalSuccess);

        // When
        String result = baseTotalDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("total"));
        assertTrue(result.contains("totalSuccess"));
    }

    @Test
    void testLargeNumbers() {
        // Given
        Long total = Long.MAX_VALUE;
        Long totalSuccess = Long.MAX_VALUE - 1000L;

        baseTotalDto.setTotal(total);
        baseTotalDto.setTotalSuccess(totalSuccess);

        // When
        Long result = baseTotalDto.getTotalFail();

        // Then
        assertEquals(1000L, result);
    }
}
