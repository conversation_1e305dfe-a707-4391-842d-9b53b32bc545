package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TemplateSaveReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testBuilder() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName("Mẫu nộp thuế 1")
                .isPublic(true)
                .build();

        assertThat(req.getTxnId()).isEqualTo("DVC01704202411252339");
        assertThat(req.getTemplateName()).isEqualTo("Mẫu nộp thuế 1");
        assertThat(req.getIsPublic()).isTrue();
    }

    @Test
    void testDefaultConstructor() {
        TemplateSaveReq req = new TemplateSaveReq();
        assertThat(req).isNotNull();
        assertThat(req.getIsPublic()).isFalse(); // Default value
    }

    @Test
    void whenTxnIdIsNull_thenValidationFails() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId(null)
                .templateName("Mẫu nộp thuế 1")
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTxnIdViolation = violations.stream()
                .anyMatch(v -> "txnId".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdViolation).isTrue();
    }

    @Test
    void whenTxnIdIsBlank_thenValidationFails() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("")
                .templateName("Mẫu nộp thuế 1")
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTxnIdViolation = violations.stream()
                .anyMatch(v -> "txnId".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdViolation).isTrue();
    }

    @Test
    void whenTemplateNameIsNull_thenValidationFails() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName(null)
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTemplateNameViolation = violations.stream()
                .anyMatch(v -> "templateName".equals(v.getPropertyPath().toString()));
        assertThat(hasTemplateNameViolation).isTrue();
    }

    @Test
    void whenTemplateNameIsBlank_thenValidationFails() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName("")
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTemplateNameViolation = violations.stream()
                .anyMatch(v -> "templateName".equals(v.getPropertyPath().toString()));
        assertThat(hasTemplateNameViolation).isTrue();
    }

    @Test
    void whenTxnIdExceedsMaxLength_thenValidationFails() {
        String longTxnId = "DVC".repeat(20); // Exceeds max length
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId(longTxnId)
                .templateName("Mẫu nộp thuế 1")
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTxnIdViolation = violations.stream()
                .anyMatch(v -> "txnId".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdViolation).isTrue();
    }

    @Test
    void whenTemplateNameExceedsMaxLength_thenValidationFails() {
        String longTemplateName = "Mẫu nộp thuế ".repeat(20); // Exceeds max length
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName(longTemplateName)
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTemplateNameViolation = violations.stream()
                .anyMatch(v -> "templateName".equals(v.getPropertyPath().toString()));
        assertThat(hasTemplateNameViolation).isTrue();
    }

    @Test
    void whenTxnIdHasInvalidPattern_thenValidationFails() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC@#$%^&*()")
                .templateName("Mẫu nộp thuế 1")
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTxnIdViolation = violations.stream()
                .anyMatch(v -> "txnId".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdViolation).isTrue();
    }

    @Test
    void whenAllFieldsAreValid_thenValidationPasses() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName("Tax Template 1") // Changed to use only allowed characters
                .isPublic(true)
                .build();

        Set<ConstraintViolation<TemplateSaveReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void testEqualsAndHashCode() {
        TemplateSaveReq req1 = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName("Tax Template 1") // Changed to use only allowed characters
                .isPublic(true)
                .build();

        TemplateSaveReq req2 = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName("Tax Template 1") // Changed to use only allowed characters
                .isPublic(true)
                .build();

        TemplateSaveReq req3 = TemplateSaveReq.builder()
                .txnId("DVC98765432109876543")
                .templateName("Mẫu nộp thuế khác")
                .isPublic(false)
                .build();

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testSettersAndGetters() {
        TemplateSaveReq req = new TemplateSaveReq();
        
        req.setTxnId("DVC01704202411252339");
        req.setTemplateName("Mẫu nộp thuế 1");
        req.setIsPublic(true);
        
        assertThat(req.getTxnId()).isEqualTo("DVC01704202411252339");
        assertThat(req.getTemplateName()).isEqualTo("Mẫu nộp thuế 1");
        assertThat(req.getIsPublic()).isTrue();
    }

    @Test
    void testToString() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName("Mẫu nộp thuế 1")
                .isPublic(true)
                .build();
        
        String toString = req.toString();
        assertThat(toString).contains("TemplateSaveReq");
        assertThat(toString).contains("txnId");
        assertThat(toString).contains("templateName");
        assertThat(toString).contains("isPublic");
    }

    @Test
    void testDefaultIsPublicValue() {
        TemplateSaveReq req = TemplateSaveReq.builder()
                .txnId("DVC01704202411252339")
                .templateName("Mẫu nộp thuế 1")
                .build();
        
        assertThat(req.getIsPublic()).isFalse(); // Should use default value
    }
}
