package com.bidv.ibank.dvc.model.request;

import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class BatchDetailReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        // Arrange & Act
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20250529172533579")
                .build();

        // Assert
        assertThat(req.getBatchNo()).isEqualTo("BDR20250529172533579");
    }

    @Test
    void noArgsConstructor_ShouldCreateEmptyInstance() {
        // Arrange & Act
        BatchDetailReq req = new BatchDetailReq();

        // Assert
        assertThat(req.getBatchNo()).isNull();
    }

    @Test
    void allArgsConstructor_ShouldCreateInstanceWithAllFields() {
        // Arrange & Act
        BatchDetailReq req = new BatchDetailReq("BDR20250529172533579");

        // Assert
        assertThat(req.getBatchNo()).isEqualTo("BDR20250529172533579");
    }

    @Test
    void setterAndGetter_ShouldWorkCorrectly() {
        // Arrange
        BatchDetailReq req = new BatchDetailReq();

        // Act
        req.setBatchNo("BDR20250529172533579");

        // Assert
        assertThat(req.getBatchNo()).isEqualTo("BDR20250529172533579");
    }

    @Test
    void validate_WhenBatchNoExceedsMaxLength_ShouldHaveViolation() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20250529172533579TOO202505291725335792025052917253357920250529172533579LONG") // More than 50 characters
                .build();

        // Act & Assert
        assertThat(validator.validate(req))
                .hasSize(1)
                .first()
                .extracting("propertyPath.currentLeafNode.name")
                .isEqualTo("batchNo");
    }

    @Test
    void validate_WhenBatchNoWithinMaxLength_ShouldHaveNoViolations() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20250529172533579") // Exactly 20 characters
                .build();

        // Act & Assert
        assertThat(validator.validate(req)).isEmpty();
    }

    @Test
    void validate_WhenBatchNoIsNull_ShouldHaveViolations() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(null)
                .build();

        // Act & Assert
        assertThat(validator.validate(req)).isNotEmpty().hasSize(1);
    }

    @Test
    void validate_WhenBatchNoIsEmpty_ShouldHaveViolations() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("")
                .build();

        // Act & Assert
        assertThat(validator.validate(req)).isNotEmpty().hasSize(1);
    }
}