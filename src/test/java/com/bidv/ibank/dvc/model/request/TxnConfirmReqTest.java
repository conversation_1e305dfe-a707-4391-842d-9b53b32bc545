package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnConfirmReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void whenTransKeyExceeds150Characters_thenValidationFails() {
        String longTransKey = "TXNKEY_REQUEST_APPROVAL_" + "x".repeat(150);
        TxnConfirmReq req = TxnConfirmReq.builder()
                .transKey(longTransKey)
                .confirmValue("CONFIRM_VALUE")
                .build();

        Set<ConstraintViolation<TxnConfirmReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("transKey");
    }

    @Test
    void whenTransKeyIsBlank_thenValidationFails() {
        TxnConfirmReq req = TxnConfirmReq.builder()
                .transKey("")
                .confirmValue("CONFIRM_VALUE")
                .build();

        Set<ConstraintViolation<TxnConfirmReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("transKey");
    }

    @Test
    void whenConfirmValueIsBlank_thenValidationFails() {
        TxnConfirmReq req = TxnConfirmReq.builder()
                .transKey("TEST_KEY")
                .confirmValue("")
                .build();

        Set<ConstraintViolation<TxnConfirmReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("confirmValue");
    }

    @Test
    void whenValidRequest_thenValidationSucceeds() {
        TxnConfirmReq req = TxnConfirmReq.builder()
                .transKey("TEST_KEY")
                .confirmValue("CONFIRM_VALUE")
                .build();

        Set<ConstraintViolation<TxnConfirmReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }
}