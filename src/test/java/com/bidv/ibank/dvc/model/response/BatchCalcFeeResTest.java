package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class BatchCalcFeeResTest {

    private BatchCalcFeeRes batchCalcFeeRes;

    @BeforeEach
    void setUp() {
        batchCalcFeeRes = new BatchCalcFeeRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        BatchCalcFeeRes res = new BatchCalcFeeRes();

        // Then
        assertNotNull(res);
        assertNull(res.getFileSize());
        assertNull(res.getFileName());
        assertNull(res.getBatchNo());
        assertNull(res.getBatchType());
        assertNull(res.getTotalAmount());
        assertNull(res.getTotalFee());
        assertNull(res.getCcy());
        assertNull(res.getFeeCcy());
        assertNull(res.getFeeOpt());
        assertNotNull(res.getItems()); // @Builder.Default creates empty list
    }

    @Test
    void testBuilderPattern() {
        // Given
        Long fileSize = 1024L;
        String fileName = "test_batch.xlsx";
        String batchNo = "BATCH001";
        String batchType = "TAX_PAYMENT";
        BigDecimal totalAmount = new BigDecimal("1000000");
        BigDecimal totalFee = new BigDecimal("10000");
        String ccy = "VND";
        String feeCcy = "VND";
        String feeOpt = "Phí khoán";
        List<BatchItemDetailDto> items = Arrays.asList(new BatchItemDetailDto(), new BatchItemDetailDto());

        // When
        BatchCalcFeeRes res = BatchCalcFeeRes.builder()
                .fileSize(fileSize)
                .fileName(fileName)
                .batchNo(batchNo)
                .batchType(batchType)
                .totalAmount(totalAmount)
                .totalFee(totalFee)
                .ccy(ccy)
                .feeCcy(feeCcy)
                .feeOpt(feeOpt)
                .items(items)
                .build();

        // Then
        assertEquals(fileSize, res.getFileSize());
        assertEquals(fileName, res.getFileName());
        assertEquals(batchNo, res.getBatchNo());
        assertEquals(batchType, res.getBatchType());
        assertEquals(totalAmount, res.getTotalAmount());
        assertEquals(totalFee, res.getTotalFee());
        assertEquals(ccy, res.getCcy());
        assertEquals(feeCcy, res.getFeeCcy());
        assertEquals(feeOpt, res.getFeeOpt());
        assertEquals(items, res.getItems());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        Long fileSize = 2048L;
        String fileName = "batch_test.csv";
        String batchNo = "B2023001";
        String batchType = "CUSTOMS_DUTY";
        BigDecimal totalAmount = new BigDecimal("5000000");
        BigDecimal totalFee = new BigDecimal("50000");
        String ccy = "USD";
        String feeCcy = "VND";
        String feeOpt = "Phí theo tỷ lệ";

        // When
        batchCalcFeeRes.setFileSize(fileSize);
        batchCalcFeeRes.setFileName(fileName);
        batchCalcFeeRes.setBatchNo(batchNo);
        batchCalcFeeRes.setBatchType(batchType);
        batchCalcFeeRes.setTotalAmount(totalAmount);
        batchCalcFeeRes.setTotalFee(totalFee);
        batchCalcFeeRes.setCcy(ccy);
        batchCalcFeeRes.setFeeCcy(feeCcy);
        batchCalcFeeRes.setFeeOpt(feeOpt);

        // Then
        assertEquals(fileSize, batchCalcFeeRes.getFileSize());
        assertEquals(fileName, batchCalcFeeRes.getFileName());
        assertEquals(batchNo, batchCalcFeeRes.getBatchNo());
        assertEquals(batchType, batchCalcFeeRes.getBatchType());
        assertEquals(totalAmount, batchCalcFeeRes.getTotalAmount());
        assertEquals(totalFee, batchCalcFeeRes.getTotalFee());
        assertEquals(ccy, batchCalcFeeRes.getCcy());
        assertEquals(feeCcy, batchCalcFeeRes.getFeeCcy());
        assertEquals(feeOpt, batchCalcFeeRes.getFeeOpt());
    }

    @Test
    void testGetTotalItemsWithNullItems() {
        // Given
        batchCalcFeeRes.setItems(null);

        // When
        Integer totalItems = batchCalcFeeRes.getTotalItems();

        // Then
        assertEquals(0, totalItems);
    }

    @Test
    void testGetTotalItemsWithEmptyList() {
        // Given
        batchCalcFeeRes.setItems(new ArrayList<>());

        // When
        Integer totalItems = batchCalcFeeRes.getTotalItems();

        // Then
        assertEquals(0, totalItems);
    }

    @Test
    void testGetTotalItemsWithItems() {
        // Given
        List<BatchItemDetailDto> items = Arrays.asList(
                new BatchItemDetailDto(),
                new BatchItemDetailDto(),
                new BatchItemDetailDto()
        );
        batchCalcFeeRes.setItems(items);

        // When
        Integer totalItems = batchCalcFeeRes.getTotalItems();

        // Then
        assertEquals(3, totalItems);
    }

    @Test
    void testGetTotalAmountTextWithNullAmount() {
        // Given
        batchCalcFeeRes.setTotalAmount(null);
        batchCalcFeeRes.setCcy("VND");

        // When
        String amountText = batchCalcFeeRes.getTotalAmountText();

        // Then
        assertNull(amountText);
    }

    @Test
    void testGetTotalAmountTextWithValidAmount() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            BigDecimal totalAmount = new BigDecimal("1234567890");
            String ccy = "VND";
            batchCalcFeeRes.setTotalAmount(totalAmount);
            batchCalcFeeRes.setCcy(ccy);

            mockedTranslator.when(Translator::getLocale).thenReturn(Locale.forLanguageTag("vi-VN"));

            // When
            String amountText = batchCalcFeeRes.getTotalAmountText();

            // Then
            assertNotNull(amountText);
            assertFalse(amountText.isEmpty());
        }
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String batchNo = "BATCH001";
        BigDecimal totalAmount = new BigDecimal("1000000");

        BatchCalcFeeRes res1 = BatchCalcFeeRes.builder()
                .batchNo(batchNo)
                .totalAmount(totalAmount)
                .build();

        BatchCalcFeeRes res2 = BatchCalcFeeRes.builder()
                .batchNo(batchNo)
                .totalAmount(totalAmount)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String batchNo = "BATCH001";
        BigDecimal totalAmount = new BigDecimal("1000000");

        batchCalcFeeRes.setBatchNo(batchNo);
        batchCalcFeeRes.setTotalAmount(totalAmount);

        // When
        String result = batchCalcFeeRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void testJsonPropertyOrderAnnotation() {
        // Given
        BatchCalcFeeRes res = BatchCalcFeeRes.builder()
                .batchNo("BATCH001")
                .fileName("test.xlsx")
                .totalAmount(new BigDecimal("1000"))
                .build();

        // Then - Verify the object can be created and contains expected values
        // JsonPropertyOrder annotation ensures alphabetic ordering in JSON serialization
        assertNotNull(res);
        assertEquals("BATCH001", res.getBatchNo());
        assertEquals("test.xlsx", res.getFileName());
        assertEquals(new BigDecimal("1000"), res.getTotalAmount());
    }

    @Test
    void testBatchGeneralInfoDtoInterface() {
        // Given
        BatchCalcFeeRes res = new BatchCalcFeeRes();

        // Then
        assertNotNull(res);
    }
}
