package com.bidv.ibank.dvc.model.dto.messagequeue;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NotifyMessageDtoTest {

    private NotifyMessageDto notifyMessageDto;

    @BeforeEach
    void setUp() {
        notifyMessageDto = new NotifyMessageDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        NotifyMessageDto dto = new NotifyMessageDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getParams());
        assertNull(dto.getAttachments());
        assertNull(dto.getRecipients());
        assertNull(dto.getInfo());
        assertNull(dto.getEmails());
        assertNull(dto.getAdditionalInfo());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("key1", "value1");

        List<String> attachments = Arrays.asList("file1.pdf", "file2.xlsx");
        List<Long> recipients = Arrays.asList(123L, 456L, 789L);

        NotifyMessageInfoDto info = NotifyMessageInfoDto.builder()
                .productCode("DVC")
                .subProductCode("TAX_PAYMENT")
                .eventType("PAYMENT_SUCCESS")
                .build();

        List<String> emails = Arrays.asList("<EMAIL>", "<EMAIL>");
        String additionalInfo = "Additional information";

        // When
        NotifyMessageDto dto = new NotifyMessageDto(params, attachments, recipients, info, emails, additionalInfo);

        // Then
        assertNotNull(dto);
        assertEquals(params, dto.getParams());
        assertEquals(attachments, dto.getAttachments());
        assertEquals(recipients, dto.getRecipients());
        assertEquals(info, dto.getInfo());
        assertEquals(emails, dto.getEmails());
        assertEquals(additionalInfo, dto.getAdditionalInfo());
    }

    @Test
    void testBuilder() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("transactionId", "TXN123");
        params.put("amount", "1000000");

        List<String> attachments = Arrays.asList("receipt.pdf", "confirmation.doc");
        List<Long> recipients = Arrays.asList(100L, 200L);

        NotifyMessageInfoDto info = NotifyMessageInfoDto.builder()
                .productCode("GOV")
                .subProductCode("DECLARATION")
                .eventType("SUBMISSION_COMPLETED")
                .build();

        List<String> emails = Arrays.asList("<EMAIL>");
        String additionalInfo = "Batch processing completed successfully";

        // When
        NotifyMessageDto dto = NotifyMessageDto.builder()
                .params(params)
                .attachments(attachments)
                .recipients(recipients)
                .info(info)
                .emails(emails)
                .additionalInfo(additionalInfo)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(params, dto.getParams());
        assertEquals(attachments, dto.getAttachments());
        assertEquals(recipients, dto.getRecipients());
        assertEquals(info, dto.getInfo());
        assertEquals(emails, dto.getEmails());
        assertEquals(additionalInfo, dto.getAdditionalInfo());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("userId", "user123");
        params.put("batchId", "BATCH456");

        List<String> attachments = Arrays.asList("report.xlsx", "summary.pdf");
        List<Long> recipients = Arrays.asList(111L, 222L, 333L);

        NotifyMessageInfoDto info = NotifyMessageInfoDto.builder()
                .productCode("H2H")
                .subProductCode("BATCH_TRANSFER")
                .eventType("PROCESSING_FAILED")
                .build();

        List<String> emails = Arrays.asList("<EMAIL>", "<EMAIL>");
        String additionalInfo = "Error details and recovery instructions";

        // When
        notifyMessageDto.setParams(params);
        notifyMessageDto.setAttachments(attachments);
        notifyMessageDto.setRecipients(recipients);
        notifyMessageDto.setInfo(info);
        notifyMessageDto.setEmails(emails);
        notifyMessageDto.setAdditionalInfo(additionalInfo);

        // Then
        assertEquals(params, notifyMessageDto.getParams());
        assertEquals(attachments, notifyMessageDto.getAttachments());
        assertEquals(recipients, notifyMessageDto.getRecipients());
        assertEquals(info, notifyMessageDto.getInfo());
        assertEquals(emails, notifyMessageDto.getEmails());
        assertEquals(additionalInfo, notifyMessageDto.getAdditionalInfo());
    }

    @Test
    void testSettersAndGettersWithNullValues() {
        // When
        notifyMessageDto.setParams(null);
        notifyMessageDto.setAttachments(null);
        notifyMessageDto.setRecipients(null);
        notifyMessageDto.setInfo(null);
        notifyMessageDto.setEmails(null);
        notifyMessageDto.setAdditionalInfo(null);

        // Then
        assertNull(notifyMessageDto.getParams());
        assertNull(notifyMessageDto.getAttachments());
        assertNull(notifyMessageDto.getRecipients());
        assertNull(notifyMessageDto.getInfo());
        assertNull(notifyMessageDto.getEmails());
        assertNull(notifyMessageDto.getAdditionalInfo());
    }

    @Test
    void testSettersAndGettersWithEmptyCollections() {
        // Given
        Map<String, String> emptyParams = new HashMap<>();
        List<String> emptyAttachments = Arrays.asList();
        List<Long> emptyRecipients = Arrays.asList();
        List<String> emptyEmails = Arrays.asList();

        // When
        notifyMessageDto.setParams(emptyParams);
        notifyMessageDto.setAttachments(emptyAttachments);
        notifyMessageDto.setRecipients(emptyRecipients);
        notifyMessageDto.setEmails(emptyEmails);

        // Then
        assertNotNull(notifyMessageDto.getParams());
        assertTrue(notifyMessageDto.getParams().isEmpty());
        assertNotNull(notifyMessageDto.getAttachments());
        assertTrue(notifyMessageDto.getAttachments().isEmpty());
        assertNotNull(notifyMessageDto.getRecipients());
        assertTrue(notifyMessageDto.getRecipients().isEmpty());
        assertNotNull(notifyMessageDto.getEmails());
        assertTrue(notifyMessageDto.getEmails().isEmpty());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("key1", "value1");

        List<String> attachments = Arrays.asList("file1.txt");
        List<Long> recipients = Arrays.asList(123L);

        NotifyMessageInfoDto info = NotifyMessageInfoDto.builder()
                .productCode("DVC")
                .subProductCode("TEST")
                .eventType("TEST_EVENT")
                .build();

        List<String> emails = Arrays.asList("<EMAIL>");
        String additionalInfo = "Test info";

        NotifyMessageDto dto1 = NotifyMessageDto.builder()
                .params(params)
                .attachments(attachments)
                .recipients(recipients)
                .info(info)
                .emails(emails)
                .additionalInfo(additionalInfo)
                .build();

        NotifyMessageDto dto2 = NotifyMessageDto.builder()
                .params(params)
                .attachments(attachments)
                .recipients(recipients)
                .info(info)
                .emails(emails)
                .additionalInfo(additionalInfo)
                .build();

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("testKey", "testValue");
        notifyMessageDto.setParams(params);
        notifyMessageDto.setAdditionalInfo("Test additional info");

        // When
        String result = notifyMessageDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("NotifyMessageDto"));
        assertTrue(result.contains("params"));
        assertTrue(result.contains("additionalInfo"));
    }

    @Test
    void testSerializable() throws Exception {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("serialKey", "serialValue");

        List<String> attachments = Arrays.asList("serialFile.pdf");
        List<Long> recipients = Arrays.asList(999L);

        NotifyMessageInfoDto info = NotifyMessageInfoDto.builder()
                .productCode("SERIAL")
                .subProductCode("TEST")
                .eventType("SERIALIZATION")
                .build();

        List<String> emails = Arrays.asList("<EMAIL>");

        NotifyMessageDto originalDto = NotifyMessageDto.builder()
                .params(params)
                .attachments(attachments)
                .recipients(recipients)
                .info(info)
                .emails(emails)
                .additionalInfo("Serialization test")
                .build();

        // When - Serialize
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(originalDto);
        oos.close();

        // When - Deserialize
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        NotifyMessageDto deserializedDto = (NotifyMessageDto) ois.readObject();
        ois.close();

        // Then
        assertNotNull(deserializedDto);
        assertEquals(originalDto.getParams(), deserializedDto.getParams());
        assertEquals(originalDto.getAttachments(), deserializedDto.getAttachments());
        assertEquals(originalDto.getRecipients(), deserializedDto.getRecipients());
        assertEquals(originalDto.getInfo().getProductCode(), deserializedDto.getInfo().getProductCode());
        assertEquals(originalDto.getEmails(), deserializedDto.getEmails());
        assertEquals(originalDto.getAdditionalInfo(), deserializedDto.getAdditionalInfo());
    }

    @Test
    void testBuilderWithPartialData() {
        // When
        NotifyMessageDto dto = NotifyMessageDto.builder()
                .params(new HashMap<>())
                .info(NotifyMessageInfoDto.builder()
                        .productCode("DVC")
                        .build())
                .additionalInfo("Partial data test")
                .build();

        // Then
        assertNotNull(dto);
        assertNotNull(dto.getParams());
        assertTrue(dto.getParams().isEmpty());
        assertNull(dto.getAttachments());
        assertNull(dto.getRecipients());
        assertNotNull(dto.getInfo());
        assertEquals("DVC", dto.getInfo().getProductCode());
        assertNull(dto.getEmails());
        assertEquals("Partial data test", dto.getAdditionalInfo());
    }

    @Test
    void testModifyCollections() {
        // Given
        List<String> attachments = Arrays.asList("initial.pdf");
        List<Long> recipients = Arrays.asList(100L);
        List<String> emails = Arrays.asList("<EMAIL>");

        notifyMessageDto.setAttachments(attachments);
        notifyMessageDto.setRecipients(recipients);
        notifyMessageDto.setEmails(emails);

        // Then
        assertEquals(1, notifyMessageDto.getAttachments().size());
        assertEquals("initial.pdf", notifyMessageDto.getAttachments().get(0));
        assertEquals(1, notifyMessageDto.getRecipients().size());
        assertEquals(Long.valueOf(100L), notifyMessageDto.getRecipients().get(0));
        assertEquals(1, notifyMessageDto.getEmails().size());
        assertEquals("<EMAIL>", notifyMessageDto.getEmails().get(0));
    }

    @Test
    void testComplexScenario() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("batchId", "BATCH_2024_001");
        params.put("totalTransactions", "150");
        params.put("successfulTransactions", "147");
        params.put("failedTransactions", "3");
        params.put("processingTime", "00:05:23");

        List<String> attachments = Arrays.asList(
            "batch_report_2024_001.xlsx",
            "error_details_2024_001.pdf",
            "transaction_summary.csv"
        );

        List<Long> recipients = Arrays.asList(1001L, 1002L, 1003L, 2001L);

        NotifyMessageInfoDto info = NotifyMessageInfoDto.builder()
                .productCode("DVC")
                .subProductCode("BATCH_TAX_PAYMENT")
                .eventType("BATCH_PROCESSING_COMPLETED_WITH_ERRORS")
                .build();

        List<String> emails = Arrays.asList(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        );

        String additionalInfo = "Batch processing completed with 3 failed transactions. " +
                               "Please review the error report for failed transaction details.";

        // When
        NotifyMessageDto dto = NotifyMessageDto.builder()
                .params(params)
                .attachments(attachments)
                .recipients(recipients)
                .info(info)
                .emails(emails)
                .additionalInfo(additionalInfo)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(5, dto.getParams().size());
        assertEquals("BATCH_2024_001", dto.getParams().get("batchId"));
        assertEquals("3", dto.getParams().get("failedTransactions"));

        assertEquals(3, dto.getAttachments().size());
        assertTrue(dto.getAttachments().contains("batch_report_2024_001.xlsx"));

        assertEquals(4, dto.getRecipients().size());
        assertTrue(dto.getRecipients().contains(2001L));

        assertEquals("DVC", dto.getInfo().getProductCode());
        assertEquals("BATCH_TAX_PAYMENT", dto.getInfo().getSubProductCode());
        assertEquals("BATCH_PROCESSING_COMPLETED_WITH_ERRORS", dto.getInfo().getEventType());

        assertEquals(3, dto.getEmails().size());
        assertTrue(dto.getEmails().contains("<EMAIL>"));

        assertTrue(dto.getAdditionalInfo().contains("3 failed transactions"));
    }
}
