package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class AdministrativeAreaReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void whenAllFieldsValid_thenNoViolations() {
        AdministrativeAreaReq req = AdministrativeAreaReq.builder()
                .treasuryCode("1234")
                .build();

        Set<ConstraintViolation<AdministrativeAreaReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void whenTreasuryCodeExceedsMaxLength_thenViolation() {
        AdministrativeAreaReq req = AdministrativeAreaReq.builder()
                .treasuryCode("12345")
                .build();

        Set<ConstraintViolation<AdministrativeAreaReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("size must be between 0 and 4");
    }

    @Test
    void builderAndGetterSetter_ShouldWorkCorrectly() {
        String treasuryCode = "1234";

        AdministrativeAreaReq req = new AdministrativeAreaReq();
        req.setTreasuryCode(treasuryCode);
        assertThat(req.getTreasuryCode()).isEqualTo(treasuryCode);

        AdministrativeAreaReq builderReq = AdministrativeAreaReq.builder()
                .treasuryCode(treasuryCode)
                .build();
        assertThat(builderReq.getTreasuryCode()).isEqualTo(treasuryCode);
    }

    @Test
    void equalsAndHashCode_ShouldWorkCorrectly() {
        AdministrativeAreaReq req1 = AdministrativeAreaReq.builder()
                .treasuryCode("1234")
                .build();

        AdministrativeAreaReq req2 = AdministrativeAreaReq.builder()
                .treasuryCode("1234")
                .build();

        AdministrativeAreaReq req3 = AdministrativeAreaReq.builder()
                .treasuryCode("4321")
                .build();

        assertThat(req1).isEqualTo(req2);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isNotEqualTo(req3.hashCode());
    }
}