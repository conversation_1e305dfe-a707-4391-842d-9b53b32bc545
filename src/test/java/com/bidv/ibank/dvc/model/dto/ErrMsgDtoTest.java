package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class ErrMsgDtoTest {

    @Test
    void testDefaultConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn(null);

            // When
            ErrMsgDto dto = new ErrMsgDto();

            // Then
            assertNotNull(dto);
            assertNull(dto.getCode());
            assertNull(dto.getField());
            assertNull(dto.getMessage());
        }
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String code = "ERR001";
        String field = "taxCode";
        String message = "Invalid tax code";

        // When
        ErrMsgDto dto = new ErrMsgDto(code, field, message);

        // Then
        assertEquals(code, dto.getCode());
        assertEquals(field, dto.getField());
        assertEquals(message, dto.getMessage());
    }

    @Test
    void testBuilder() {
        // Given
        String code = "ERR002";
        String field = "amount";
        String message = "Amount must be positive";

        // When
        ErrMsgDto dto = ErrMsgDto.builder()
                .code(code)
                .field(field)
                .message(message)
                .build();

        // Then
        assertEquals(code, dto.getCode());
        assertEquals(field, dto.getField());
        assertEquals(message, dto.getMessage());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        ErrMsgDto dto = new ErrMsgDto();
        String code = "ERR003";
        String field = "debitAccNo";
        String message = "Account number is required";

        // When
        dto.setCode(code);
        dto.setField(field);
        dto.setMessage(message);

        // Then
        assertEquals(code, dto.getCode());
        assertEquals(field, dto.getField());
        assertEquals(message, dto.getMessage());
    }

    @Test
    void testGetMessageWithExistingMessage() {
        // Given
        String code = "ERR004";
        String existingMessage = "Custom error message";
        ErrMsgDto dto = new ErrMsgDto(code, "field", existingMessage);

        // When
        String result = dto.getMessage();

        // Then
        assertEquals(existingMessage, result);
    }

    @Test
    void testGetMessageWithNullMessageUsesTranslator() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String code = "ERR005";
            String translatedMessage = "Translated error message";
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn(translatedMessage);

            ErrMsgDto dto = new ErrMsgDto(code, "field", null);

            // When
            String result = dto.getMessage();

            // Then
            assertEquals(translatedMessage, result);
            mockedTranslator.verify(() -> Translator.toLocale("response.code." + code, code));
        }
    }

    @Test
    void testGetMessageWithEmptyMessageUsesTranslator() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String code = "ERR006";
            String translatedMessage = "Another translated message";
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn(translatedMessage);

            ErrMsgDto dto = ErrMsgDto.builder()
                    .code(code)
                    .field("testField")
                    .message(null)
                    .build();

            // When
            String result = dto.getMessage();

            // Then
            assertEquals(translatedMessage, result);
        }
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String code = "ERR007";
        String field = "testField";
        String message = "Test message";

        ErrMsgDto dto1 = new ErrMsgDto(code, field, message);
        ErrMsgDto dto2 = new ErrMsgDto(code, field, message);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String code = "ERR008";
        String field = "testField";
        String message = "Test message";
        ErrMsgDto dto = new ErrMsgDto(code, field, message);

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertEquals("ErrMsgDto(code=ERR008, field=testField, message=Test message)", result);
    }
}
