package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmLoaitienhqaCodeNameEntityDtoTest {

    private TccDmLoaitienhqaCodeNameEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmLoaitienhqaCodeNameEntityDto();
        dto.setMaLoaitienhqa("LT001");
        dto.setTenLoaitienhqa("Test Loaitienhqa");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("LT001", dto.getMaLoaitienhqa());
        assertEquals("Test Loaitienhqa", dto.getTenLoaitienhqa());
    }

    @Test
    void testGetMaTenLoaitienhqa() {
        assertEquals("LT001 - Test Loaitienhqa", dto.getMaTenLoaitienhqa());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmLoaitienhqaCodeNameEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmLoaitienhqaCodeNameEntityDto dto1 = new TccDmLoaitienhqaCodeNameEntityDto("LT001", "Test Loaitienhqa");
        TccDmLoaitienhqaCodeNameEntityDto dto2 = new TccDmLoaitienhqaCodeNameEntityDto("LT001", "Test Loaitienhqa");
        TccDmLoaitienhqaCodeNameEntityDto dto3 = new TccDmLoaitienhqaCodeNameEntityDto("LT002", "Other Loaitienhqa");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmLoaitienhqaCodeNameEntityDto dto = new TccDmLoaitienhqaCodeNameEntityDto("LT001", "Test Loaitienhqa");
        assertEquals("LT001", dto.getMaLoaitienhqa());
        assertEquals("Test Loaitienhqa", dto.getTenLoaitienhqa());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmLoaitienhqaCodeNameEntityDto dto = new TccDmLoaitienhqaCodeNameEntityDto();
        assertNull(dto.getMaLoaitienhqa());
        assertNull(dto.getTenLoaitienhqa());
    }
}