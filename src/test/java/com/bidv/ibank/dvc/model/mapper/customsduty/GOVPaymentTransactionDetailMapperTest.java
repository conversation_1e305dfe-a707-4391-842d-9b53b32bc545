package com.bidv.ibank.dvc.model.mapper.customsduty;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.util.Translator;

class GOVPaymentTransactionDetailMapperTest {

    private GOVPaymentTransactionMapper govPaymentTransactionMapper;
    private GOVPaymentTransactionEntity transactionEntity;
    private GOVPaymentItemEntity paymentItemEntity;
    private MockedStatic<Translator> translatorMock;
    private LocalDateTime now;

    private TccDmKhobacEntity treasuryEntity;
    private TccDmDbhcEntity admAreaEntity;
    private TccDmCqthuEntity revAuthEntity;
    private TccDmTkNsnnEntity revAccEntity;
    private TccDmChuongEntity chapterEntity;
    private TccDmNdktEntity ecEntity;
    private TccDmLhxnkEntity eiTypeEntity;
    private TccDmSthueHqaEntity taxTypeEntity;

    @BeforeEach
    void setUp() {
        govPaymentTransactionMapper = new GOVPaymentTransactionMapper();
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenAnswer(i -> i.getArgument(1));
        translatorMock.when(() -> Translator.toLocale(AppConstants.LANGUAGE.TAX_PAYER_TYPE))
                .thenReturn("Business");

        now = LocalDateTime.now();
        setupMockEntities();
        setupTransactionEntity();
        setupPaymentItemEntity();
    }

    private void setupMockEntities() {
        treasuryEntity = new TccDmKhobacEntity();
        treasuryEntity.setShkb("TR001");
        treasuryEntity.setTen("Treasury Name 1");

        admAreaEntity = new TccDmDbhcEntity();
        admAreaEntity.setMaDbhc("ADM001");
        admAreaEntity.setTen("Admin Area Name 1");

        revAuthEntity = new TccDmCqthuEntity();
        revAuthEntity.setMaCqthu("REV001");
        revAuthEntity.setTen("Revenue Authority Name 1");

        revAccEntity = new TccDmTkNsnnEntity();
        revAccEntity.setMaTk("AUTH001");
        revAccEntity.setTen("Revenue Account Name 1");

        chapterEntity = new TccDmChuongEntity();
        chapterEntity.setMaChuong("CH001");
        chapterEntity.setTen("Chapter 1");

        ecEntity = new TccDmNdktEntity();
        ecEntity.setMaNdkt("EC001");
        ecEntity.setTen("Economic Code 1");

        eiTypeEntity = new TccDmLhxnkEntity();
        eiTypeEntity.setMaLh("EI001");
        eiTypeEntity.setTen("Export/Import Type 1");

        taxTypeEntity = new TccDmSthueHqaEntity();
        taxTypeEntity.setMaSthue("TAX001");
        taxTypeEntity.setTenSthue("Tax Type 1");
    }

    private void setupTransactionEntity() {
        transactionEntity = mock(GOVPaymentTransactionEntity.class);
        when(transactionEntity.getId()).thenReturn("TXN001");
        when(transactionEntity.getDebitAccNo()).thenReturn("**********");
        when(transactionEntity.getDebitAccName()).thenReturn("Account Name");
        when(transactionEntity.getTaxCode()).thenReturn("TAX001");
        when(transactionEntity.getAltTaxCode()).thenReturn("ALT_TAX001");
        when(transactionEntity.getPayerName()).thenReturn("Payer Name");
        when(transactionEntity.getAltPayerName()).thenReturn("Alt Payer Name");
        when(transactionEntity.getPayerAddr()).thenReturn("Payer Address");
        when(transactionEntity.getAltPayerAddr()).thenReturn("Alt Payer Address");
        when(transactionEntity.getAmount()).thenReturn(BigDecimal.valueOf(1000));
        when(transactionEntity.getCcy()).thenReturn("VND");
        when(transactionEntity.getBatchNo()).thenReturn("BATCH001");
        when(transactionEntity.getStatus()).thenReturn("REJECTED");
        when(transactionEntity.getCreatedDate()).thenReturn(now);
        when(transactionEntity.getCreatedBy()).thenReturn("creator");
        when(transactionEntity.getApprovalUsers()).thenReturn("approver");
        when(transactionEntity.getShkb()).thenReturn("TR001");
        when(transactionEntity.getMaDbhc()).thenReturn("ADM001");
        when(transactionEntity.getMaTk()).thenReturn("REV001");
        when(transactionEntity.getMaCqthu()).thenReturn("AUTH001");
        when(transactionEntity.getMaNh()).thenReturn("BB001");
        when(transactionEntity.getFeeTotal()).thenReturn(BigDecimal.valueOf(10));
        when(transactionEntity.getFeeOpt()).thenReturn(null);
        when(transactionEntity.getRaNote()).thenReturn("RA Note");
        when(transactionEntity.getApprovalNote()).thenReturn("Approval Note");
        when(transactionEntity.getPayerType()).thenReturn(PayerTypeEnum.BUSINESS);

        when(transactionEntity.getTccDmKhobacEntity()).thenReturn(treasuryEntity);
        when(transactionEntity.getTccDmDbhcEntity()).thenReturn(admAreaEntity);
        when(transactionEntity.getTccDmCqthuEntity()).thenReturn(revAuthEntity);
        when(transactionEntity.getTccDmTkNsnnEntity()).thenReturn(revAccEntity);
    }

    private void setupPaymentItemEntity() {
        paymentItemEntity = mock(GOVPaymentItemEntity.class);
        when(paymentItemEntity.getDeclarationNo()).thenReturn("DEC001");
        when(paymentItemEntity.getDeclarationDate()).thenReturn(LocalDate.now());
        when(paymentItemEntity.getMaLthq()).thenReturn("CC001");
        when(paymentItemEntity.getCcy()).thenReturn("VND");
        when(paymentItemEntity.getMaChuong()).thenReturn("CH001");
        when(paymentItemEntity.getMaNdkt()).thenReturn("EC001");
        when(paymentItemEntity.getMaLh()).thenReturn("EI001");
        when(paymentItemEntity.getMaSthue()).thenReturn("TAX001");
        when(paymentItemEntity.getAmount()).thenReturn(BigDecimal.valueOf(500));

        when(paymentItemEntity.getTccDmChuongEntity()).thenReturn(chapterEntity);
        when(paymentItemEntity.getTccDmNdktEntity()).thenReturn(ecEntity);
        when(paymentItemEntity.getTccDmLhxnkEntity()).thenReturn(eiTypeEntity);
        when(paymentItemEntity.getTccDmSthueHqaEntity()).thenReturn(taxTypeEntity);

        when(transactionEntity.getGovPaymentItemList()).thenReturn(Set.of(paymentItemEntity));
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void toDetailDto_WhenAllFieldsPresent_ShouldMapCorrectly() {
        TxnDetailRes result = govPaymentTransactionMapper.toDetailDto(transactionEntity);

        assertThat(result).isNotNull()
                .satisfies(res -> {
                    assertThat(res.getTxnId()).isEqualTo("TXN001");
                    assertThat(res.getDebitAccNo()).isEqualTo("**********");
                    assertThat(res.getDebitAccName()).isEqualTo("Account Name");
                    assertThat(res.getTaxCode()).isEqualTo("TAX001");
                    assertThat(res.getAltTaxCode()).isEqualTo("ALT_TAX001");
                    assertThat(res.getPayerName()).isEqualTo("Payer Name");
                    assertThat(res.getAltPayerName()).isEqualTo("Alt Payer Name");
                    assertThat(res.getPayerAddr()).isEqualTo("Payer Address");
                    assertThat(res.getAltPayerAddr()).isEqualTo("Alt Payer Address");
                    assertThat(res.getPayerType()).isEqualTo(PayerTypeEnum.BUSINESS.getValue());
                    assertThat(res.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
                    assertThat(res.getCcy()).isEqualTo("VND");
                    assertThat(res.getBatchNo()).isEqualTo("BATCH001");
                    assertThat(res.getStatus()).isEqualTo("REJECTED");
                    assertThat(res.getCreatedDate()).isEqualTo(now);
                    assertThat(res.getCreatedBy()).isEqualTo("creator");
                    assertThat(res.getApprovalUsers()).isEqualTo("approver");
                    assertThat(res.getTreasuryCode()).isEqualTo("TR001");
                    assertThat(res.getTreasuryName()).isEqualTo("Treasury Name 1");
                    assertThat(res.getAdmAreaCode()).isEqualTo("ADM001");
                    assertThat(res.getAdmAreaName()).isEqualTo("Admin Area Name 1");
                    assertThat(res.getRevAccCode()).isEqualTo("REV001");
                    assertThat(res.getRevAccName()).isEqualTo("Revenue Account Name 1");
                    assertThat(res.getRevAuthCode()).isEqualTo("AUTH001");
                    assertThat(res.getRevAuthName()).isEqualTo("Revenue Authority Name 1");
                    assertThat(res.getBenBankCode()).isEqualTo("BB001");
                    assertThat(res.getFeeTotal()).isEqualTo(BigDecimal.valueOf(10));
                    assertThat(res.getFeeOpt()).isNull();
                    assertThat(res.getRaNote()).isEqualTo("RA Note");
                    assertThat(res.getApprovalNote()).isEqualTo("Approval Note");
                });
    }

    @Test
    void toDetailDto_WhenTaxItemsPresent_ShouldMapTaxItemsCorrectly() {
        TxnDetailRes result = govPaymentTransactionMapper.toDetailDto(transactionEntity);

        assertThat(result.getTaxItems())
                .hasSize(1)
                .first()
                .satisfies(item -> {
                    assertThat(item.getDeclarationNo()).isEqualTo("DEC001");
                    assertThat(item.getDeclarationDate()).isEqualTo(LocalDate.now().toString());
                    assertThat(item.getCcCode()).isEqualTo("CC001");
                    assertThat(item.getCcy()).isEqualTo("VND");
                    assertThat(item.getChapterCode()).isEqualTo("CH001");
                    assertThat(item.getChapterName()).isEqualTo("Chapter 1");
                    assertThat(item.getEcCode()).isEqualTo("EC001");
                    assertThat(item.getEcName()).isEqualTo("Economic Code 1");
                    assertThat(item.getEiTypeCode()).isEqualTo("EI001");
                    assertThat(item.getEiTypeName()).isEqualTo("Export/Import Type 1");
                    assertThat(item.getTaxTypeCode()).isEqualTo("TAX001");
                    assertThat(item.getTaxTypeName()).isEqualTo("Tax Type 1");
                    assertThat(item.getAmount()).isEqualTo("500");
                });
    }

    @Test
    void toDetailDto_WhenReferenceEntitiesNull_ShouldHandleGracefully() {
        when(transactionEntity.getTccDmKhobacEntity()).thenReturn(null);
        when(transactionEntity.getTccDmDbhcEntity()).thenReturn(null);
        when(transactionEntity.getTccDmCqthuEntity()).thenReturn(null);
        when(transactionEntity.getTccDmTkNsnnEntity()).thenReturn(null);

        when(paymentItemEntity.getTccDmChuongEntity()).thenReturn(null);
        when(paymentItemEntity.getTccDmNdktEntity()).thenReturn(null);
        when(paymentItemEntity.getTccDmLhxnkEntity()).thenReturn(null);
        when(paymentItemEntity.getTccDmSthueHqaEntity()).thenReturn(null);

        TxnDetailRes result = govPaymentTransactionMapper.toDetailDto(transactionEntity);

        assertThat(result).isNotNull()
                .satisfies(res -> {
                    assertThat(res.getTreasuryName()).isNull();
                    assertThat(res.getAdmAreaName()).isNull();
                    assertThat(res.getRevAccName()).isNull();
                    assertThat(res.getRevAuthName()).isNull();
                });

        assertThat(result.getTaxItems())
                .hasSize(1)
                .first()
                .satisfies(item -> {
                    assertThat(item.getChapterName()).isNull();
                    assertThat(item.getEcName()).isNull();
                    assertThat(item.getEiTypeName()).isNull();
                    assertThat(item.getTaxTypeName()).isNull();
                });
    }

    @Test
    void toDetailDto_WhenNoTaxItems_ShouldReturnEmptyTaxItemsList() {
        when(transactionEntity.getGovPaymentItemList()).thenReturn(Set.of());

        TxnDetailRes result = govPaymentTransactionMapper.toDetailDto(transactionEntity);

        assertThat(result).isNotNull();
        assertThat(result.getTaxItems()).isEmpty();
    }

    @Test
    void toDetailDto_WhenOptionalFieldsNull_ShouldHandleGracefully() {
        when(transactionEntity.getAltTaxCode()).thenReturn(null);
        when(transactionEntity.getAltPayerName()).thenReturn(null);
        when(transactionEntity.getAltPayerAddr()).thenReturn(null);
        when(transactionEntity.getFeeTotal()).thenReturn(null);
        when(transactionEntity.getRaNote()).thenReturn(null);
        when(transactionEntity.getApprovalNote()).thenReturn(null);

        TxnDetailRes result = govPaymentTransactionMapper.toDetailDto(transactionEntity);

        assertThat(result).isNotNull()
                .satisfies(res -> {
                    assertThat(res.getAltTaxCode()).isNull();
                    assertThat(res.getAltPayerName()).isNull();
                    assertThat(res.getAltPayerAddr()).isNull();
                    assertThat(res.getFeeTotal()).isNull();
                    assertThat(res.getRaNote()).isNull();
                    assertThat(res.getApprovalNote()).isNull();
                });
    }
}