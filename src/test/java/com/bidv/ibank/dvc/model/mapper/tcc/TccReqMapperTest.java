package com.bidv.ibank.dvc.model.mapper.tcc;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.dto.TxnTaxItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.NumberUtils;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingReq;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationReq;
import com.bidv.ibank.integrate.entity.tcc.TccMessageReq;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoReq;
import com.bidv.ibank.integrate.entity.tcc.TccResendDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfo;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfoDetail;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfoDetailRow;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfoHdr;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocReq;
import com.bidv.ibank.integrate.entity.account.CoreAccount;

@ExtendWith(MockitoExtension.class)
class TccReqMapperTest {

    @InjectMocks
    private TccReqMapper tccReqMapper;

    private InquiryCustomsDutyReq mockInquiryReq;
    private ValidateCustomsDutyReq mockValidateReq;
    private GOVPaymentTransactionEntity mockTransaction;
    private MappingTreasuryBenBankCodeDto mockTreasuryInfo;
    private BatchImportItemDto mockBatchItem;
    private CoreAccount mockAccountInfo;

    @BeforeEach
    void setUp() {
        setupMockInquiryRequest();
        setupMockValidateRequest();
        setupMockTransaction();
        setupMockTreasuryInfo();
        setupMockBatchItem();
        setupMockAccountInfo();
    }

    private void setupMockInquiryRequest() {
        mockInquiryReq = InquiryCustomsDutyReq.builder()
                .taxCode("0*********")
                .declarationNo("DECL001")
                .declarationYear("2024")
                .build();
    }

    private void setupMockValidateRequest() {
        TxnTaxItemDto taxItem1 = TxnTaxItemDto.builder()
                .declarationNo("DECL001")
                .declarationDate(LocalDate.now()) // Use LocalDate instead of Date
                .ecCode("EC001")
                .chapterCode("CH001")
                .ccCode("CC001")
                .eiTypeCode("EI001")
                .taxTypeCode("TAX001")
                .amount("500000") // Use String instead of BigDecimal
                .transDesc("Tax item 1 description")
                .build();

        TxnTaxItemDto taxItem2 = TxnTaxItemDto.builder()
                .declarationNo("DECL002")
                .declarationDate(LocalDate.now()) // Use LocalDate instead of Date
                .ecCode("EC002")
                .chapterCode("CH002")
                .ccCode("CC002")
                .eiTypeCode("EI002")
                .taxTypeCode("TAX002")
                .amount("300000") // Use String instead of BigDecimal
                .transDesc("Tax item 2 description")
                .build();

        mockValidateReq = ValidateCustomsDutyReq.builder()
                .debitAccNo("*********")
                .taxCode("0*********")
                .altTaxCode("ALT123")
                .payerName("Test Payer")
                .altPayerName("Alt Payer")
                .payerAddr("123 Test Street")
                .altPayerAddr("456 Alt Street")
                .treasuryCode("TREA001")
                .revAccCode("REV001")
                .revAuthCode("AUTH001")
                .admAreaCode("ADM001")
                .ccy("VND")
                .amount("800000")
                .taxItems(Arrays.asList(taxItem1, taxItem2))
                .build();
    }

    private void setupMockTransaction() {
        GOVPaymentItemEntity item1 = GOVPaymentItemEntity.builder()
                .declarationNo("DECL001")
                .declarationDate(LocalDate.now())
                .maNdkt("NDKT001")
                .maChuong("CHUONG001")
                .maSthue("STHUE001")
                .maLthq("LTHQ001")
                .maLh("LH001")
                .amount(new BigDecimal("500000"))
                .transDesc("Payment item 1")
                .build();

        GOVPaymentItemEntity item2 = GOVPaymentItemEntity.builder()
                .declarationNo("DECL002")
                .declarationDate(LocalDate.now())
                .maNdkt("NDKT002")
                .maChuong("CHUONG002")
                .maSthue("STHUE002")
                .maLthq("LTHQ002")
                .maLh("LH002")
                .amount(new BigDecimal("300000"))
                .transDesc("Payment item 2")
                .build();

        mockTransaction = GOVPaymentTransactionEntity.builder()
                .taxCode("0*********")
                .altTaxCode("ALT123")
                .payerName("Test Payer")
                .altPayerName("Alt Payer")
                .payerAddr("123 Test Street")
                .altPayerAddr("456 Alt Street")
                .shkb("SHKB001")
                .maTk("TK001")
                .maCqthu("CQTHU001")
                .maDbhc("DBHC001")
                .govPaymentItemList(Set.of(item1, item2))
                .build();

        // Set fields that are not available in builder but are needed by the mapper
        mockTransaction.setAmount(new BigDecimal("800000"));
        mockTransaction.setDebitAccNo("*********");
        mockTransaction.setDebitCcy("VND");
        mockTransaction.setTxnType("1");
        mockTransaction.setFeeOpt("INST");
        mockTransaction.setFeeAmount(new BigDecimal("50000"));
        mockTransaction.setFeeVAT(new BigDecimal("5000"));
        mockTransaction.setFeeTotal(new BigDecimal("55000"));
    }

    private void setupMockTreasuryInfo() {
        mockTreasuryInfo = MappingTreasuryBenBankCodeDto.builder()
                .debtAccCode("DEBT001")
                .benBankCode("BANK001")
                .build();
    }

    private void setupMockBatchItem() {
        mockAccountInfo = new CoreAccount();
        mockAccountInfo.setBrnCode("001");

        mockBatchItem = BatchImportItemDto.builder()
                .debitAccNo("*********")
                .taxCode("0*********")
                .altTaxCode("ALT123")
                .payerName("Batch Payer")
                .altPayerName("Alt Batch Payer")
                .payerAddr("123 Batch Street")
                .altPayerAddr("456 Alt Batch Street")
                .treasuryCode("TREA001")
                .revAccCode("REV001")
                .revAuthCode("AUTH001")
                .revAuthName("Revenue Authority")
                .admAreaCode("ADM001")
                .ccy("VND")
                .amount("1000000")
                .ecCode("EC001")
                .chapterCode("CH001")
                .ccCode("CC001")
                .eiTypeCode("EI001")
                .taxTypeCode("TAX001")
                .declarationNo("DECL001")
                .declarationDate("01/01/2024")
                .transDesc("Batch transaction description")
                .payerType("1")
                .treasuryInfo(mockTreasuryInfo)
                .accountInfo(mockAccountInfo)
                .build();
    }

    private void setupMockAccountInfo() {
        mockAccountInfo = new CoreAccount();
        mockAccountInfo.setBrnCode("001");
    }

    @Test
    void testToInquiryDeclarationReq_WithValidRequest_ShouldMapAllFields() {
        // When
        TccMessageReq<TccInquiryDeclarationReq> result = tccReqMapper.toInquiryDeclarationReq(mockInquiryReq);

        // Then
        assertNotNull(result);
        assertNotNull(result.getMessageRequest());
        
        TccInquiryDeclarationReq messageRequest = result.getMessageRequest();
        assertEquals(mockInquiryReq.getTaxCode(), messageRequest.getMaDv());
        assertEquals(mockInquiryReq.getDeclarationNo(), messageRequest.getSoTk());
        assertEquals(mockInquiryReq.getDeclarationYear(), messageRequest.getNamDk());
    }

    @Test
    void testToInquiryDeclarationReq_WithNullFields_ShouldHandleGracefully() {
        // Given
        InquiryCustomsDutyReq requestWithNulls = InquiryCustomsDutyReq.builder().build();

        // When
        TccMessageReq<TccInquiryDeclarationReq> result = tccReqMapper.toInquiryDeclarationReq(requestWithNulls);

        // Then
        assertNotNull(result);
        assertNotNull(result.getMessageRequest());
        
        TccInquiryDeclarationReq messageRequest = result.getMessageRequest();
        assertNull(messageRequest.getMaDv());
        assertNull(messageRequest.getSoTk());
        assertNull(messageRequest.getNamDk());
    }

    @Test
    void testToValidateDocReq_WithValidRequestAndTransaction_ShouldMapAllFields() {
        try (MockedStatic<NumberUtils> numberUtils = mockStatic(NumberUtils.class);
             MockedStatic<DateUtils> dateUtils = mockStatic(DateUtils.class)) {
            
            // Setup mocks
            numberUtils.when(() -> NumberUtils.toDouble(any(BigDecimal.class), anyDouble()))
                      .thenAnswer(invocation -> {
                          BigDecimal value = invocation.getArgument(0);
                          return value != null ? value.doubleValue() : invocation.getArgument(1);
                      });
            
            dateUtils.when(() -> DateUtils.convertDateToString(any(LocalDate.class)))
                    .thenReturn("01/01/2024");

            String revAuthName = "Revenue Authority Name";

            // When
            TccValidateDocReq result = tccReqMapper.toValidateDocReq(mockValidateReq, mockTransaction, mockTreasuryInfo, revAuthName);

            // Then
            assertNotNull(result);
            assertNotNull(result.getChungTuLst());
            assertNotNull(result.getChungTuLst().getChungTu());
            
            TccValidateDocInfo chungTu = result.getChungTuLst().getChungTu();
            assertEquals("1", chungTu.getSttCtu());
            
            // Verify header
            TccValidateDocInfoHdr header = chungTu.getCtuHdr();
            assertNotNull(header);
            assertEquals(AppConstants.TCC_VALIDATE_DOC.BDR, header.getSrData());
            assertEquals(mockValidateReq.getTreasuryCode(), header.getShkb());
            assertEquals(mockTreasuryInfo.getDebtAccCode(), header.getTkNo());
            assertEquals(mockValidateReq.getRevAccCode(), header.getTkCo());
            assertEquals(mockValidateReq.getTaxCode(), header.getMaNnthue());
            assertEquals(mockValidateReq.getPayerName(), header.getTenNnthue());
            assertEquals(mockValidateReq.getPayerAddr(), header.getDcNnthue());
            assertEquals(mockValidateReq.getRevAuthCode(), header.getMaCqthu());
            assertEquals(mockValidateReq.getAdmAreaCode(), header.getMaDbhc());
            assertEquals(mockTreasuryInfo.getBenBankCode(), header.getMaNhB());
            assertEquals(mockValidateReq.getCcy(), header.getMaNt());
            assertEquals(Double.valueOf(mockValidateReq.getAmount()), header.getTtien());
            assertEquals(mockValidateReq.getDebitAccNo(), header.getStkNo());
            assertEquals(mockValidateReq.getAltTaxCode(), header.getMaNntien());
            assertEquals(mockValidateReq.getAltPayerName(), header.getTenNntien());
            assertEquals(mockValidateReq.getAltPayerAddr(), header.getDcNntien());
            assertEquals(revAuthName, header.getTenCqt());
            
            // Verify details
            TccValidateDocInfoDetail detail = chungTu.getCtuDtl();
            assertNotNull(detail);
            assertNotNull(detail.getRowDtl());
            assertEquals(2, detail.getRowDtl().size());
            
            // Verify first detail row
            TccValidateDocInfoDetailRow row1 = detail.getRowDtl().get(0);
            assertEquals(mockValidateReq.getTreasuryCode(), row1.getShkb());
            assertEquals(mockValidateReq.getTaxItems().get(0).getEcCode(), row1.getMaNdkt());
            assertEquals(mockValidateReq.getTaxItems().get(0).getChapterCode(), row1.getMaChuong());
            assertEquals(mockValidateReq.getTaxItems().get(0).getAmount(), row1.getSoTienNt());
            assertEquals(mockValidateReq.getTaxItems().get(0).getDeclarationNo(), row1.getSoTk());
            assertEquals(mockValidateReq.getTaxItems().get(0).getTaxTypeCode(), row1.getSacThue());
            assertEquals(1, row1.getSttTk());
        }
    }

    @Test
    void testToValidateDocReq_WithBatchItem_ShouldMapAllFields() {
        // When
        TccValidateDocReq result = tccReqMapper.toValidateDocReq(mockBatchItem);

        // Then
        assertNotNull(result);
        assertNotNull(result.getChungTuLst());
        assertNotNull(result.getChungTuLst().getChungTu());
        
        TccValidateDocInfo chungTu = result.getChungTuLst().getChungTu();
        assertEquals("1", chungTu.getSttCtu());
        
        // Verify header
        TccValidateDocInfoHdr header = chungTu.getCtuHdr();
        assertNotNull(header);
        assertEquals(AppConstants.TCC_VALIDATE_DOC.BDR, header.getSrData());
        assertEquals(mockBatchItem.getTreasuryCode(), header.getShkb());
        assertEquals(mockBatchItem.getTreasuryInfo().getDebtAccCode(), header.getTkNo());
        assertEquals(mockBatchItem.getRevAccCode(), header.getTkCo());
        assertEquals(mockBatchItem.getTaxCode(), header.getMaNnthue());
        assertEquals(mockBatchItem.getPayerName(), header.getTenNnthue());
        assertEquals(mockBatchItem.getPayerAddr(), header.getDcNnthue());
        assertEquals(mockBatchItem.getRevAuthCode(), header.getMaCqthu());
        assertEquals(mockBatchItem.getAdmAreaCode(), header.getMaDbhc());
        assertEquals(mockBatchItem.getTreasuryInfo().getBenBankCode(), header.getMaNhB());
        assertEquals(mockBatchItem.getCcy(), header.getMaNt());
        assertEquals(Double.valueOf(mockBatchItem.getAmount()), header.getTtien());
        assertEquals(mockBatchItem.getDebitAccNo(), header.getStkNo());
        assertEquals(mockBatchItem.getAltTaxCode(), header.getMaNntien());
        assertEquals(mockBatchItem.getAltPayerName(), header.getTenNntien());
        assertEquals(mockBatchItem.getRevAuthName(), header.getTenCqt());
        
        // Verify details
        TccValidateDocInfoDetail detail = chungTu.getCtuDtl();
        assertNotNull(detail);
        assertNotNull(detail.getRowDtl());
        assertEquals(1, detail.getRowDtl().size());
        
        TccValidateDocInfoDetailRow row = detail.getRowDtl().get(0);
        assertEquals(mockBatchItem.getTreasuryCode(), row.getShkb());
        assertEquals(mockBatchItem.getEcCode(), row.getMaNdkt());
        assertEquals(mockBatchItem.getChapterCode(), row.getMaChuong());
        assertEquals(mockBatchItem.getAmount(), row.getSoTienNt());
        assertEquals(mockBatchItem.getDeclarationNo(), row.getSoTk());
        assertEquals(mockBatchItem.getTaxTypeCode(), row.getSacThue());
        assertEquals(AppConstants.NUMBER.ONE, row.getSttTk());
    }

    @Test
    void testToCreateDocReq_WithValidTransaction_ShouldMapAllFields() {
        try (MockedStatic<NumberUtils> numberUtils = mockStatic(NumberUtils.class);
             MockedStatic<DateUtils> dateUtils = mockStatic(DateUtils.class)) {
            
            // Setup mocks
            numberUtils.when(() -> NumberUtils.toDouble(any(BigDecimal.class), anyDouble()))
                      .thenAnswer(invocation -> {
                          BigDecimal value = invocation.getArgument(0);
                          return value != null ? value.doubleValue() : invocation.getArgument(1);
                      });
            
            dateUtils.when(() -> DateUtils.convertDateToString(any(LocalDate.class)))
                    .thenReturn("01/01/2024");

            String revAuthName = "Revenue Authority Name";

            // When
            TccCreateDocReq result = tccReqMapper.toCreateDocReq(mockTransaction, mockTreasuryInfo, revAuthName);

            // Then
            assertNotNull(result);
            assertNotNull(result.getChungTuLst());
            assertNotNull(result.getChungTuLst().getChungTu());
            
            TccValidateDocInfo chungTu = result.getChungTuLst().getChungTu();
            assertEquals("1", chungTu.getSttCtu());
            
            // Verify header
            TccValidateDocInfoHdr header = chungTu.getCtuHdr();
            assertNotNull(header);
            assertEquals(AppConstants.TCC_VALIDATE_DOC.BDR, header.getSrData());
            assertEquals(mockTransaction.getShkb(), header.getShkb());
            assertEquals(mockTreasuryInfo.getDebtAccCode(), header.getTkNo());
            assertEquals(mockTransaction.getMaTk(), header.getTkCo());
            assertEquals(mockTransaction.getTaxCode(), header.getMaNnthue());
            assertEquals(mockTransaction.getPayerName(), header.getTenNnthue());
            assertEquals(mockTransaction.getPayerAddr(), header.getDcNnthue());
            assertEquals(mockTransaction.getMaCqthu(), header.getMaCqthu());
            assertEquals(mockTransaction.getMaDbhc(), header.getMaDbhc());
            assertEquals(mockTreasuryInfo.getBenBankCode(), header.getMaNhB());
            assertEquals(mockTransaction.getDebitCcy(), header.getMaNt());
            assertEquals(mockTransaction.getAmount().doubleValue(), header.getTtien());
            assertEquals(mockTransaction.getDebitAccNo(), header.getStkNo());
            assertEquals(mockTransaction.getAltTaxCode(), header.getMaNntien());
            assertEquals(mockTransaction.getAltPayerName(), header.getTenNntien());
            assertEquals(revAuthName, header.getTenCqt());
            
            // Verify details
            TccValidateDocInfoDetail detail = chungTu.getCtuDtl();
            assertNotNull(detail);
            assertNotNull(detail.getRowDtl());
            assertEquals(2, detail.getRowDtl().size());
        }
    }

    @Test
    void testToCreateDocReq_WithFeeOptNotInst_ShouldSetZeroFees() {
        try (MockedStatic<NumberUtils> numberUtils = mockStatic(NumberUtils.class);
             MockedStatic<DateUtils> dateUtils = mockStatic(DateUtils.class)) {
            
            // Setup mocks
            numberUtils.when(() -> NumberUtils.toDouble(any(BigDecimal.class), anyDouble()))
                      .thenAnswer(invocation -> {
                          BigDecimal value = invocation.getArgument(0);
                          return value != null ? value.doubleValue() : invocation.getArgument(1);
                      });
            
            dateUtils.when(() -> DateUtils.convertDateToString(any(LocalDate.class)))
                    .thenReturn("01/01/2024");

            // Given - transaction with different fee option
            mockTransaction.setFeeOpt("OUTR"); // Use a different fee option code instead of ChargeFeeOpt.OUTR
            String revAuthName = "Revenue Authority Name";

            // When
            TccCreateDocReq result = tccReqMapper.toCreateDocReq(mockTransaction, mockTreasuryInfo, revAuthName);

            // Then
            assertNotNull(result);
            TccValidateDocInfoHdr header = result.getChungTuLst().getChungTu().getCtuHdr();
            assertEquals(Double.valueOf(0), header.getPhiDichvu());
            assertEquals(Double.valueOf(0), header.getVat());
            assertEquals(Double.valueOf(0), header.getTtienDichvu());
        }
    }

    @Test
    void testToAccountingReq_WithValidTccDocId_ShouldMapAllFields() {
        // Given
        String tccDocId = "TCC_DOC_123";

        // When
        TccAccountingReq result = tccReqMapper.toAccountingReq(tccDocId);

        // Then
        assertNotNull(result);
        assertEquals(AppConstants.TCC_VALIDATE_DOC._990BDR, result.getMaNv());
        assertEquals(AppConstants.TCC_ACCOUNTING.BRCD, result.getBds());
        assertEquals(AppConstants.TCC_ACCOUNTING.INQ_CODE_ACCOUNTING, result.getInqCode());
        assertEquals(AppConstants.TCC_ACCOUNTING.INQ_NAME_ACCOUNTING, result.getInqName());
        assertEquals(AppConstants.TCC_ACCOUNTING.DATA_TYPE, result.getDataType());
        assertEquals(tccDocId, result.getDataId());
        assertEquals(AppConstants.TCC_VALIDATE_DOC._990BDR, result.getTellerId());
    }

    @Test
    void testToAccountingReq_WithNullTccDocId_ShouldHandleGracefully() {
        // When
        TccAccountingReq result = tccReqMapper.toAccountingReq(null);

        // Then
        assertNotNull(result);
        assertNull(result.getDataId());
        assertEquals(AppConstants.TCC_VALIDATE_DOC._990BDR, result.getMaNv());
        assertEquals(AppConstants.TCC_ACCOUNTING.BRCD, result.getBds());
    }

    @Test
    void testToResendDocReq_WithValidTccDocId_ShouldMapAllFields() {
        // Given
        String tccDocId = "TCC_DOC_456";

        // When
        TccResendDocReq result = tccReqMapper.toResendDocReq(tccDocId);

        // Then
        assertNotNull(result);
        assertEquals(AppConstants.TCC_ACCOUNTING._990SMB, result.getMaNv());
        assertEquals(AppConstants.TCC_ACCOUNTING.INQ_CODE_RESEND, result.getInqCode());
        assertEquals(AppConstants.TCC_ACCOUNTING.INQ_NAME_RESEND, result.getInqName());
        assertEquals(AppConstants.TCC_ACCOUNTING.DATA_TYPE, result.getDataType());
        assertEquals(tccDocId, result.getDataId());
        assertEquals(AppConstants.TCC_VALIDATE_DOC.NO, result.getResendKba());
        assertEquals(AppConstants.TCC_VALIDATE_DOC.NO, result.getResendThue());
        assertEquals(AppConstants.TCC_VALIDATE_DOC.YES, result.getResendHqa());
    }

    @Test
    void testToResendDocReq_WithEmptyTccDocId_ShouldHandleGracefully() {
        // When
        TccResendDocReq result = tccReqMapper.toResendDocReq("");

        // Then
        assertNotNull(result);
        assertEquals("", result.getDataId());
        assertEquals(AppConstants.TCC_ACCOUNTING._990SMB, result.getMaNv());
    }

    @Test
    void testToQueryTxnInfoReq_WithValidTccDocId_ShouldMapAllFields() {
        // Given
        String tccDocId = "TCC_DOC_789";

        // When
        TccQueryTxnInfoReq result = tccReqMapper.toQueryTxnInfoReq(tccDocId);

        // Then
        assertNotNull(result);
        assertEquals(AppConstants.TCC_ACCOUNTING.DATA_TYPE, result.getDataType());
        assertEquals(tccDocId, result.getDataId());
    }

    @Test
    void testToQueryTxnInfoReq_WithNullTccDocId_ShouldHandleGracefully() {
        // When
        TccQueryTxnInfoReq result = tccReqMapper.toQueryTxnInfoReq(null);

        // Then
        assertNotNull(result);
        assertNull(result.getDataId());
        assertEquals(AppConstants.TCC_ACCOUNTING.DATA_TYPE, result.getDataType());
    }

    @Test
    void testToValidateDocReq_WithEmptyTaxItems_ShouldHandleGracefully() {
        try (MockedStatic<NumberUtils> numberUtils = mockStatic(NumberUtils.class)) {
            
            // Setup mocks
            numberUtils.when(() -> NumberUtils.toDouble(any(BigDecimal.class), anyDouble()))
                      .thenAnswer(invocation -> {
                          BigDecimal value = invocation.getArgument(0);
                          return value != null ? value.doubleValue() : invocation.getArgument(1);
                      });

            // Given - request with empty tax items
            mockValidateReq.setTaxItems(Collections.emptyList());
            String revAuthName = "Revenue Authority Name";

            // When
            TccValidateDocReq result = tccReqMapper.toValidateDocReq(mockValidateReq, mockTransaction, mockTreasuryInfo, revAuthName);

            // Then
            assertNotNull(result);
            TccValidateDocInfoDetail detail = result.getChungTuLst().getChungTu().getCtuDtl();
            assertNotNull(detail);
            assertNotNull(detail.getRowDtl());
            assertTrue(detail.getRowDtl().isEmpty());
        }
    }

    @Test
    void testToCreateDocReq_WithEmptyPaymentItems_ShouldHandleGracefully() {
        try (MockedStatic<NumberUtils> numberUtils = mockStatic(NumberUtils.class)) {
            
            // Setup mocks
            numberUtils.when(() -> NumberUtils.toDouble(any(BigDecimal.class), anyDouble()))
                      .thenAnswer(invocation -> {
                          BigDecimal value = invocation.getArgument(0);
                          return value != null ? value.doubleValue() : invocation.getArgument(1);
                      });

            // Given - transaction with empty payment items
            mockTransaction.setGovPaymentItemList(Collections.emptySet());
            String revAuthName = "Revenue Authority Name";

            // When
            TccCreateDocReq result = tccReqMapper.toCreateDocReq(mockTransaction, mockTreasuryInfo, revAuthName);

            // Then
            assertNotNull(result);
            TccValidateDocInfoDetail detail = result.getChungTuLst().getChungTu().getCtuDtl();
            assertNotNull(detail);
            assertNotNull(detail.getRowDtl());
            assertTrue(detail.getRowDtl().isEmpty());
        }
    }

    @Test
    void testToValidateDocReq_WithBlankAltTaxCode_ShouldSetNopThayToNo() {
        try (MockedStatic<NumberUtils> numberUtils = mockStatic(NumberUtils.class)) {
            
            // Setup mocks
            numberUtils.when(() -> NumberUtils.toDouble(any(BigDecimal.class), anyDouble()))
                      .thenAnswer(invocation -> {
                          BigDecimal value = invocation.getArgument(0);
                          return value != null ? value.doubleValue() : invocation.getArgument(1);
                      });

            // Given - request with blank alt tax code
            mockValidateReq.setAltTaxCode("");
            String revAuthName = "Revenue Authority Name";

            // When
            TccValidateDocReq result = tccReqMapper.toValidateDocReq(mockValidateReq, mockTransaction, mockTreasuryInfo, revAuthName);

            // Then
            assertNotNull(result);
            TccValidateDocInfoHdr header = result.getChungTuLst().getChungTu().getCtuHdr();
            assertEquals(AppConstants.TCC_VALIDATE_DOC.NO, header.getNopThay());
        }
    }

    @Test
    void testToValidateDocReq_WithNonBlankAltTaxCode_ShouldSetNopThayToYes() {
        try (MockedStatic<NumberUtils> numberUtils = mockStatic(NumberUtils.class)) {
            
            // Setup mocks
            numberUtils.when(() -> NumberUtils.toDouble(any(BigDecimal.class), anyDouble()))
                      .thenAnswer(invocation -> {
                          BigDecimal value = invocation.getArgument(0);
                          return value != null ? value.doubleValue() : invocation.getArgument(1);
                      });

            // Given - request with non-blank alt tax code
            mockValidateReq.setAltTaxCode("ALT123");
            String revAuthName = "Revenue Authority Name";

            // When
            TccValidateDocReq result = tccReqMapper.toValidateDocReq(mockValidateReq, mockTransaction, mockTreasuryInfo, revAuthName);

            // Then
            assertNotNull(result);
            TccValidateDocInfoHdr header = result.getChungTuLst().getChungTu().getCtuHdr();
            assertEquals(AppConstants.TCC_VALIDATE_DOC.YES, header.getNopThay());
        }
    }

    @Test
    void testToDto_ShouldThrowUnsupportedOperationException() {
        // When & Then
        assertThrows(UnsupportedOperationException.class, () -> tccReqMapper.toDto(new Object()));
    }

    @Test
    void testToEntity_ShouldThrowUnsupportedOperationException() {
        // When & Then
        assertThrows(UnsupportedOperationException.class, () -> tccReqMapper.toEntity(new Object()));
    }
}
