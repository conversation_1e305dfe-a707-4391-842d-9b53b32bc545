package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

class TemplateListReqTest {

    @Test
    void noArgsConstructor_ShouldCreateDefaultObject() {
        TemplateListReq req = new TemplateListReq();
        assertThat(req.getSearch()).isNull();
    }

    @Test
    void allArgsConstructor_ShouldSetFields() {
        String search = "Test search";
        String txnType = "04";
        TemplateListReq req = new TemplateListReq(search, txnType);
        assertThat(req.getSearch()).isEqualTo(search);
        assertThat(req.getTxnType()).isEqualTo(txnType);
    }

    @Test
    void builder_ShouldBuildObjectCorrectly() {
        String search = "Builder search";
        TemplateListReq req = TemplateListReq.builder().search(search).build();
        assertThat(req.getSearch()).isEqualTo(search);
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        TemplateListReq req = new TemplateListReq();
        String search = "Setter search";
        req.setSearch(search);
        assertThat(req.getSearch()).isEqualTo(search);
    }

    @Test
    void toString_ShouldContainFieldValues() {
        String search = "ToString search";
        String txnType = "04";
        TemplateListReq req = new TemplateListReq(search, txnType);
        assertThat(req.toString()).contains(search);
    }
}