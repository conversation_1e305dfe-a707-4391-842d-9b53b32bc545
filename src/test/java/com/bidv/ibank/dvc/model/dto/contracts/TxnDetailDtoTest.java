package com.bidv.ibank.dvc.model.dto.contracts;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnDetailDtoTest {

    private static class TestTxnDetailDto extends TxnDetailDto {
        // No additional implementation needed for testing
    }

    private TestTxnDetailDto txnDetailDto;

    @BeforeEach
    void setUp() {
        txnDetailDto = new TestTxnDetailDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TestTxnDetailDto dto = new TestTxnDetailDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getTaxCode());
        assertNull(dto.getPayerName());
        assertNull(dto.getDebitAccNo());
        assertNull(dto.getTreasuryCode());
        assertNull(dto.getPayerType());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String taxCode = "**********";
        String payerName = "Test Company";
        String debitAccNo = "**********";
        Integer payerType = 1;
        BigDecimal amount = new BigDecimal("100000");
        String ccy = "VND";

        // When
        TestTxnDetailDto dto = new TestTxnDetailDto();
        dto.setTaxCode(taxCode);
        dto.setPayerName(payerName);
        dto.setDebitAccNo(debitAccNo);
        dto.setPayerType(payerType);
        dto.setAmount(amount);
        dto.setCcy(ccy);

        // Then
        assertEquals(taxCode, dto.getTaxCode());
        assertEquals(payerName, dto.getPayerName());
        assertEquals(debitAccNo, dto.getDebitAccNo());
        assertEquals(payerType, dto.getPayerType());
        assertEquals(amount, dto.getAmount());
        assertEquals(ccy, dto.getCcy());
    }

    @Test
    void testSettersAndGetters() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1));

            // Given
            String taxCode = "**********";
            String altTaxCode = "ALT**********";
            String payerName = "Test Company";
            String altPayerName = "Alternative Test Company";
            String payerAddr = "123 Test Street";
            String altPayerAddr = "456 Alt Street";
            String treasuryCode = "T001";
            String treasuryName = "Treasury Name";
            String admAreaCode = "A001";
            String admAreaName = "Admin Area Name";
            String revAccCode = "R001";
            String revAccName = "Revenue Account Name";
            String revAuthCode = "RA001";
            String revAuthName = "Revenue Authority Name";
            String benBankCode = "BB001";
            String benBankName = "Ben Bank Name";
            Integer payerType = 1;
            String debitAccNo = "**********";
            String txnId = "TXN001";
            BigDecimal amount = new BigDecimal("100000");
            String ccy = "VND";

            // When
            txnDetailDto.setTaxCode(taxCode);
            txnDetailDto.setAltTaxCode(altTaxCode);
            txnDetailDto.setPayerName(payerName);
            txnDetailDto.setAltPayerName(altPayerName);
            txnDetailDto.setPayerAddr(payerAddr);
            txnDetailDto.setAltPayerAddr(altPayerAddr);
            txnDetailDto.setTreasuryCode(treasuryCode);
            txnDetailDto.setTreasuryName(treasuryName);
            txnDetailDto.setAdmAreaCode(admAreaCode);
            txnDetailDto.setAdmAreaName(admAreaName);
            txnDetailDto.setRevAccCode(revAccCode);
            txnDetailDto.setRevAccName(revAccName);
            txnDetailDto.setRevAuthCode(revAuthCode);
            txnDetailDto.setRevAuthName(revAuthName);
            txnDetailDto.setBenBankCode(benBankCode);
            txnDetailDto.setBenBankName(benBankName);
            txnDetailDto.setPayerType(payerType);
            txnDetailDto.setDebitAccNo(debitAccNo);
            txnDetailDto.setTxnId(txnId);
            txnDetailDto.setAmount(amount);
            txnDetailDto.setCcy(ccy);

            // Then
            assertEquals(taxCode, txnDetailDto.getTaxCode());
            assertEquals(altTaxCode, txnDetailDto.getAltTaxCode());
            assertEquals(payerName, txnDetailDto.getPayerName());
            assertEquals(altPayerName, txnDetailDto.getAltPayerName());
            assertEquals(payerAddr, txnDetailDto.getPayerAddr());
            assertEquals(altPayerAddr, txnDetailDto.getAltPayerAddr());
            assertEquals(treasuryCode, txnDetailDto.getTreasuryCode());
            assertEquals(treasuryName, txnDetailDto.getTreasuryName());
            assertEquals(admAreaCode, txnDetailDto.getAdmAreaCode());
            assertEquals(admAreaName, txnDetailDto.getAdmAreaName());
            assertEquals(revAccCode, txnDetailDto.getRevAccCode());
            assertEquals(revAccName, txnDetailDto.getRevAccName());
            assertEquals(revAuthCode, txnDetailDto.getRevAuthCode());
            assertEquals(revAuthName, txnDetailDto.getRevAuthName());
            assertEquals(benBankCode, txnDetailDto.getBenBankCode());
            assertEquals(benBankName, txnDetailDto.getBenBankName());
            assertEquals(payerType, txnDetailDto.getPayerType());
            assertEquals(debitAccNo, txnDetailDto.getDebitAccNo());
            assertEquals(txnId, txnDetailDto.getTxnId());
            assertEquals(amount, txnDetailDto.getAmount());
            assertEquals(ccy, txnDetailDto.getCcy());
        }
    }

    @Test
    void testPayerTypeNameTranslation() {
        // Given
        Integer payerType = 1;
        String expectedTranslatedName = "Doanh nghiệp";
        String expectedKey = AppConstants.LANGUAGE.TAX_PAYER_TYPE + "." + payerType;

        txnDetailDto.setPayerType(payerType);

        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(expectedKey, payerType.toString()))
                    .thenReturn(expectedTranslatedName);

            // When
            String result = txnDetailDto.getPayerTypeName();

            // Then
            assertEquals(expectedTranslatedName, result);
            mockedTranslator.verify(() -> Translator.toLocale(expectedKey, payerType.toString()));
        }
    }

    @Test
    void testPayerTypeNameWithNullPayerType() {
        // Given
        txnDetailDto.setPayerType(null);

        // When
        String result = txnDetailDto.getPayerTypeName();

        // Then
        assertNull(result);
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String taxCode = "**********";
        String payerName = "Test Company";
        BigDecimal amount = new BigDecimal("100000");

        TestTxnDetailDto dto1 = new TestTxnDetailDto();
        dto1.setTaxCode(taxCode);
        dto1.setPayerName(payerName);
        dto1.setAmount(amount);

        TestTxnDetailDto dto2 = new TestTxnDetailDto();
        dto2.setTaxCode(taxCode);
        dto2.setPayerName(payerName);
        dto2.setAmount(amount);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String taxCode = "**********";
        String payerName = "Test Company";
        BigDecimal amount = new BigDecimal("100000");

        txnDetailDto.setTaxCode(taxCode);
        txnDetailDto.setPayerName(payerName);
        txnDetailDto.setAmount(amount);

        // When
        String result = txnDetailDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("taxCode"));
        assertTrue(result.contains("payerName"));
        assertTrue(result.contains("amount"));
    }

    @Test
    void testTaxItemsHandling() {
        // Given
        TxnTaxFullItemDto taxItem1 = new TxnTaxFullItemDto();
        TxnTaxFullItemDto taxItem2 = new TxnTaxFullItemDto();
        List<TxnTaxFullItemDto> taxItems = Arrays.asList(taxItem1, taxItem2);

        // When
        txnDetailDto.setTaxItems(taxItems);

        // Then
        assertEquals(taxItems, txnDetailDto.getTaxItems());
        assertEquals(2, txnDetailDto.getTaxItems().size());
    }

    @Test
    void testDateTimeFields() {
        // Given
        LocalDateTime createdDate = LocalDateTime.now();
        LocalDateTime updatedDate = LocalDateTime.now().plusHours(1);

        // When
        txnDetailDto.setCreatedDate(createdDate);
        txnDetailDto.setUpdatedDate(updatedDate);

        // Then
        assertEquals(createdDate, txnDetailDto.getCreatedDate());
        assertEquals(updatedDate, txnDetailDto.getUpdatedDate());
    }

    @Test
    void testMonetaryFields() {
        // Given
        BigDecimal amount = new BigDecimal("100000.50");
        BigDecimal feeTotal = new BigDecimal("10000.25");
        String ccy = "VND";
        String feeCcy = "USD";

        // When
        txnDetailDto.setAmount(amount);
        txnDetailDto.setFeeTotal(feeTotal);
        txnDetailDto.setCcy(ccy);
        txnDetailDto.setFeeCcy(feeCcy);

        // Then
        assertEquals(amount, txnDetailDto.getAmount());
        assertEquals(feeTotal, txnDetailDto.getFeeTotal());
        assertEquals(ccy, txnDetailDto.getCcy());
        assertEquals(feeCcy, txnDetailDto.getFeeCcy());
    }
}
