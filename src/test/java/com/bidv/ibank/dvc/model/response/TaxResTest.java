package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class TaxResTest {

    private MockedStatic<Translator> translatorMock;
    private TaxTypeRes taxRes;
    private static final String TAX_CODE = "VAT";
    private static final String TAX_NAME = "Value Added Tax";
    private static final String TRANSLATED_NAME = "Thuế giá trị gia tăng";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        taxRes = TaxTypeRes.builder()
                .taxTypeCode(TAX_CODE)
                .taxTypeName(TAX_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TAX_NAME);

        assertThat(taxRes).isNotNull();
        assertThat(taxRes.getTaxTypeCode()).isEqualTo(TAX_CODE);
        assertThat(taxRes.getTaxTypeName()).isEqualTo(TAX_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        TaxTypeRes emptyTax = TaxTypeRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyTax).isNotNull();
        assertThat(emptyTax.getTaxTypeCode()).isNull();
        assertThat(emptyTax.getTaxTypeName()).isNull();
    }

    @Test
    void getTaxName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = taxRes.getTaxTypeName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getTaxName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TAX_NAME);

        String result = taxRes.getTaxTypeName();

        assertThat(result).isEqualTo(TAX_NAME);
    }

    @Test
    void getTaxName_WhenTaxCodeIsNull_ShouldHandleNullGracefully() {
        TaxTypeRes nullCodeTax = TaxTypeRes.builder()
                .taxTypeName(TAX_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeTax.getTaxTypeName();

        assertThat(result).isNull();
    }

    @Test
    void getTaxName_WhenTaxNameIsNull_ShouldHandleNullGracefully() {
        TaxTypeRes nullNameTax = TaxTypeRes.builder()
                .taxTypeCode(TAX_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameTax.getTaxTypeName();

        assertThat(result).isNull();
    }

    @Test
    void noArgsConstructor_ShouldCreateEmptyInstance() {
        TaxTypeRes tax = new TaxTypeRes();

        assertThat(tax).isNotNull();
        assertThat(tax.getTaxTypeCode()).isNull();
        assertThat(tax.getTaxTypeName()).isNull();
    }

    @Test
    void allArgsConstructor_ShouldCreatePopulatedInstance() {
        TaxTypeRes tax = new TaxTypeRes(TAX_CODE, TAX_NAME);

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TAX_NAME);

        assertThat(tax.getTaxTypeCode()).isEqualTo(TAX_CODE);
        assertThat(tax.getTaxTypeName()).isEqualTo(TAX_NAME);
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        TaxTypeRes tax1 = TaxTypeRes.builder()
                .taxTypeCode(TAX_CODE)
                .taxTypeName(TAX_NAME)
                .build();

        TaxTypeRes tax2 = TaxTypeRes.builder()
                .taxTypeCode(TAX_CODE)
                .taxTypeName(TAX_NAME)
                .build();

        assertThat(tax1).isEqualTo(tax2);
        assertThat(tax1.hashCode()).isEqualTo(tax2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        TaxTypeRes tax1 = TaxTypeRes.builder()
                .taxTypeCode(TAX_CODE)
                .taxTypeName(TAX_NAME)
                .build();

        TaxTypeRes tax2 = TaxTypeRes.builder()
                .taxTypeCode("GST")
                .taxTypeName("Goods and Services Tax")
                .build();

        assertThat(tax1).isNotEqualTo(tax2);
        assertThat(tax1.hashCode()).isNotEqualTo(tax2.hashCode());
    }
}