package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TreasuryDetailReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testBuilder() {
        TreasuryDetailReq req = TreasuryDetailReq.builder()
                .treasuryCode("1234") // Changed from "1234567" to comply with 4-char limit
                .build();

        assertThat(req.getTreasuryCode()).isEqualTo("1234");
    }

    @Test
    void testDefaultConstructor() {
        TreasuryDetailReq req = new TreasuryDetailReq();
        assertThat(req).isNotNull();
        assertThat(req.getTreasuryCode()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        TreasuryDetailReq req = new TreasuryDetailReq("1234"); // Changed from "1234567"
        assertThat(req.getTreasuryCode()).isEqualTo("1234");
    }

    @Test
    void whenTreasuryCodeIsNull_thenValidationFails() {
        TreasuryDetailReq req = TreasuryDetailReq.builder()
                .treasuryCode(null)
                .build();

        Set<ConstraintViolation<TreasuryDetailReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTreasuryCodeViolation = violations.stream()
                .anyMatch(v -> "treasuryCode".equals(v.getPropertyPath().toString()));
        assertThat(hasTreasuryCodeViolation).isTrue();
    }

    @Test
    void whenTreasuryCodeIsBlank_thenValidationFails() {
        TreasuryDetailReq req = TreasuryDetailReq.builder()
                .treasuryCode("")
                .build();

        Set<ConstraintViolation<TreasuryDetailReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTreasuryCodeViolation = violations.stream()
                .anyMatch(v -> "treasuryCode".equals(v.getPropertyPath().toString()));
        assertThat(hasTreasuryCodeViolation).isTrue();
    }

    @Test
    void whenTreasuryCodeIsWhitespace_thenValidationFails() {
        TreasuryDetailReq req = TreasuryDetailReq.builder()
                .treasuryCode("   ")
                .build();

        Set<ConstraintViolation<TreasuryDetailReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTreasuryCodeViolation = violations.stream()
                .anyMatch(v -> "treasuryCode".equals(v.getPropertyPath().toString()));
        assertThat(hasTreasuryCodeViolation).isTrue();
    }

    @Test
    void whenTreasuryCodeExceedsMaxLength_thenValidationFails() {
        String longTreasuryCode = "1234567890".repeat(10); // Exceeds max length
        TreasuryDetailReq req = TreasuryDetailReq.builder()
                .treasuryCode(longTreasuryCode)
                .build();

        Set<ConstraintViolation<TreasuryDetailReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
        
        boolean hasTreasuryCodeViolation = violations.stream()
                .anyMatch(v -> "treasuryCode".equals(v.getPropertyPath().toString()));
        assertThat(hasTreasuryCodeViolation).isTrue();
    }

    @Test
    void whenTreasuryCodeIsValid_thenValidationPasses() {
        TreasuryDetailReq req = TreasuryDetailReq.builder()
                .treasuryCode("1234") // Changed from "1234567" to comply with 4-char limit
                .build();

        Set<ConstraintViolation<TreasuryDetailReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void testEqualsAndHashCode() {
        TreasuryDetailReq req1 = TreasuryDetailReq.builder()
                .treasuryCode("1234") // Changed from "1234567"
                .build();

        TreasuryDetailReq req2 = TreasuryDetailReq.builder()
                .treasuryCode("1234") // Changed from "1234567"
                .build();

        TreasuryDetailReq req3 = TreasuryDetailReq.builder()
                .treasuryCode("5678") // Changed from "7654321" to comply with 4-char limit
                .build();

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testSettersAndGetters() {
        TreasuryDetailReq req = new TreasuryDetailReq();
        
        req.setTreasuryCode("1234"); // Changed from "1234567"
        assertThat(req.getTreasuryCode()).isEqualTo("1234");
    }

    @Test
    void testToString() {
        TreasuryDetailReq req = TreasuryDetailReq.builder()
                .treasuryCode("1234") // Changed from "1234567"
                .build();
        
        String toString = req.toString();
        assertThat(toString).contains("TreasuryDetailReq");
        assertThat(toString).contains("treasuryCode");
        assertThat(toString).contains("1234"); // Changed from "1234567"
    }

    @Test
    void testValidTreasuryCodeVariations() {
        // Test various valid treasury code formats (max 4 characters)
        String[] validCodes = {"1234", "TR01", "KB12", "T001"};

        for (String code : validCodes) {
            TreasuryDetailReq req = TreasuryDetailReq.builder()
                    .treasuryCode(code)
                    .build();
            
            Set<ConstraintViolation<TreasuryDetailReq>> violations = validator.validate(req);
            assertThat(violations).isEmpty();
        }
    }
}
