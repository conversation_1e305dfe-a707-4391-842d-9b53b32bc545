package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnDeleteReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        // Arrange & Act
        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_001", "TXN_002"))
                .build();

        // Assert
        assertThat(request.getTxnIds())
                .hasSize(2)
                .containsExactly("TXN_001", "TXN_002");
    }

    @Test
    void noArgsConstructor_ShouldCreateEmptyInstance() {
        // Act
        TxnDeleteReq request = new TxnDeleteReq();

        // Assert
        assertThat(request.getTxnIds()).isNull();
    }

    @Test
    void allArgsConstructor_ShouldCreateInstanceWithAllFields() {
        // Arrange
        List<String> txnIds = List.of("TXN_001", "TXN_002");

        // Act
        TxnDeleteReq request = new TxnDeleteReq(txnIds);

        // Assert
        assertThat(request.getTxnIds())
                .hasSize(2)
                .containsExactly("TXN_001", "TXN_002");
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        // Arrange
        TxnDeleteReq request = new TxnDeleteReq();
        List<String> txnIds = List.of("TXN_001", "TXN_002");

        // Act
        request.setTxnIds(txnIds);

        // Assert
        assertThat(request.getTxnIds())
                .hasSize(2)
                .containsExactly("TXN_001", "TXN_002");
    }

    @Test
    void equals_ShouldReturnTrue_WhenObjectsAreEqual() {
        // Arrange
        TxnDeleteReq request1 = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_001", "TXN_002"))
                .build();

        TxnDeleteReq request2 = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_001", "TXN_002"))
                .build();

        // Assert
        assertThat(request1)
                .isEqualTo(request2)
                .hasSameHashCodeAs(request2);
    }

    @Test
    void equals_ShouldReturnFalse_WhenObjectsAreDifferent() {
        // Arrange
        TxnDeleteReq request1 = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_001", "TXN_002"))
                .build();

        TxnDeleteReq request2 = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_003", "TXN_004"))
                .build();

        // Assert
        assertThat(request1)
                .isNotEqualTo(request2)
                .doesNotHaveSameHashCodeAs(request2);
    }

    @Test
    void toString_ShouldContainAllFields() {
        // Arrange
        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_001", "TXN_002"))
                .build();

        // Act
        String toString = request.toString();

        // Assert
        assertThat(toString)
                .contains("TxnDeleteReq")
                .contains("txnIds=[TXN_001, TXN_002]");
    }

    @Test
    void validate_ShouldPass_WhenTxnIdsIsValid() {
        // Arrange
        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(List.of("TXN001", "TXN002"))
                .build();

        // Act
        Set<ConstraintViolation<TxnDeleteReq>> violations = validator.validate(request);

        // Assert
        assertThat(violations).isEmpty();
    }

    @Test
    void validate_ShouldEmpty_WhenTxnIdsIsNull() {
        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(null)
                .build();
        // Act
        Set<ConstraintViolation<TxnDeleteReq>> violations = validator.validate(request);
        // Assert
        assertThat(violations).anySatisfy(v -> {
            assertThat(v.getPropertyPath().toString()).isEqualTo("txnIds");
            assertThat(v.getMessage()).isEqualTo("must not be empty");
        });
    }

    @Test
    void shouldFail_WhenTxnIdExceedsMaxLength() {
        // Arrange: 51 characters
        String longTxnId = "TXN_" + "1".repeat(47); // Total = 4 + 47 = 51
        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_001", longTxnId))
                .build();

        // Act
        Set<ConstraintViolation<TxnDeleteReq>> violations = validator.validate(request);

        // Debug
        violations.forEach(v -> System.out.println(v.getPropertyPath() + ": " + v.getMessage()));

        // Assert
        assertThat(violations).anySatisfy(v -> {
            assertThat(v.getPropertyPath().toString()).contains("txnIds[1]");
            assertThat(v.getMessage()).isEqualTo("size must be between 0 and 50");
        });

    }

    @Test
    void shouldFail_WhenTxnIdIsBlank() {
        // Arrange
        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(List.of("TXN_001", ""))
                .build();

        // Act
        Set<ConstraintViolation<TxnDeleteReq>> violations = validator.validate(request);

        // Debug
        violations.forEach(v -> System.out.println(v.getPropertyPath() + ": " + v.getMessage()));

        // Assert
        assertThat(violations).anySatisfy(v -> {
            assertThat(v.getPropertyPath().toString()).contains("txnIds[1]");
            assertThat(v.getMessage()).isEqualTo("must not be blank");
        });

    }

    @Test
    void validate_ShouldFail_WhenTxnIdExceedsMaxLength() {
        // Arrange
        String validTxnId = "TXN_001";
        String longTxnId = "TXN_" + "1".repeat(47); // 51 characters

        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(List.of(validTxnId, longTxnId))
                .build();

        // Act
        Set<ConstraintViolation<TxnDeleteReq>> violations = validator.validate(request);

        // Debug (optional)
        violations.forEach(v -> System.out.println(v.getPropertyPath() + ": " + v.getMessage()));

        // Assert
        assertThat(violations).anySatisfy(v -> {
            assertThat(v.getPropertyPath().toString()).contains("txnIds[1]");
            assertThat(v.getMessage()).isEqualTo("size must be between 0 and 50");
        });
    }

    @Test
    void validate_ShouldFail_WhenMultipleTxnIdsExceedMaxLength() {
        // Arrange
        String longTxnId1 = "TXN_" + "1".repeat(47); // 51 characters
        String longTxnId2 = "TXN_" + "2".repeat(47); // 51 characters
        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(List.of(longTxnId1, longTxnId2))
                .build();

        // Act
        Set<ConstraintViolation<TxnDeleteReq>> violations = validator.validate(request);

        // Debug
        violations.forEach(v -> System.out.println(v.getPropertyPath() + ": " + v.getMessage()));

        // Assert
        assertThat(violations).anySatisfy(v -> {
            assertThat(v.getPropertyPath().toString()).contains("txnIds[0]");
            assertThat(v.getMessage()).isEqualTo("size must be between 0 and 50");
        });
    }

    @Test
    void validate_ShouldFail_WhenTxnIdContainsEmptyString() {
        // Arrange
        List<String> txnIds = Arrays.asList("TXN_001", "");

        TxnDeleteReq request = TxnDeleteReq.builder()
                .txnIds(txnIds)
                .build();

        // Act
        Set<ConstraintViolation<TxnDeleteReq>> violations = validator.validate(request);

        // Debug
        violations.forEach(v -> System.out.println(v.getPropertyPath() + ": " + v.getMessage()));

        // Assert
        assertThat(violations).anySatisfy(v -> {
            assertThat(v.getPropertyPath().toString()).contains("txnIds[1]");
            assertThat(v.getMessage()).isEqualTo("must not be blank");
        });
    }
}