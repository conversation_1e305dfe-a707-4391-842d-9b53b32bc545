package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.model.dto.TxnTaxItemDto;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class ValidateCustomsDutyReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testBuilder() {
        ValidateCustomsDutyReq req = ValidateCustomsDutyReq.builder()
                .amount("1000000")
                .ccy("VND")
                .treasuryCode("TC01")
                .admAreaCode("AA001")
                .revAuthCode("RA001")
                .revAccCode("RAC001")
                .debitAccNo("**********")
                .taxCode("**********")
                .payerName("Test Payer")
                .payerAddr("Test Address")
                .altTaxCode("**********")
                .altPayerName("Alt Payer")
                .altPayerAddr("Alt Address")
                .payerType(PayerTypeEnum.BUSINESS)
                .raNote("Test Note")
                .taxItems(Arrays.asList(TxnTaxItemDto.builder().build()))
                .build();

        assertThat(req.getAmount()).isEqualTo("1000000");
        assertThat(req.getCcy()).isEqualTo("VND");
        assertThat(req.getTreasuryCode()).isEqualTo("TC01");
        assertThat(req.getAdmAreaCode()).isEqualTo("AA001");
        assertThat(req.getRevAuthCode()).isEqualTo("RA001");
        assertThat(req.getRevAccCode()).isEqualTo("RAC001");
        assertThat(req.getDebitAccNo()).isEqualTo("**********");
        assertThat(req.getTaxCode()).isEqualTo("**********");
        assertThat(req.getPayerName()).isEqualTo("Test Payer");
        assertThat(req.getPayerAddr()).isEqualTo("Test Address");
        assertThat(req.getAltTaxCode()).isEqualTo("**********");
        assertThat(req.getAltPayerName()).isEqualTo("Alt Payer");
        assertThat(req.getAltPayerAddr()).isEqualTo("Alt Address");
        assertThat(req.getPayerType()).isEqualTo(PayerTypeEnum.BUSINESS);
        assertThat(req.getRaNote()).isEqualTo("Test Note");
        assertThat(req.getTaxItems()).hasSize(1);
    }

    @Test
    void whenRequiredFieldsAreMissing_thenValidationFails() {
        ValidateCustomsDutyReq req = ValidateCustomsDutyReq.builder().build();

        Set<ConstraintViolation<ValidateCustomsDutyReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(12); // All @NotBlank fields
    }

    @Test
    void whenRaNoteExceeds210Characters_thenValidationFails() {
        // Create a minimally valid TxnTaxItemDto to satisfy @Valid on taxItems
        TxnTaxItemDto validTaxItem = TxnTaxItemDto.builder()
                .eiTypeCode("EIT")
                .taxTypeCode("TT")
                .ccCode("CC")
                .chapterCode("CH")
                .ecCode("EC")
                .amount("100")
                .ccy("VND")
                .declarationDate(LocalDate.now())
                .declarationNo("DN123")
                .transDesc("Valid item")
                .build();

        ValidateCustomsDutyReq req = ValidateCustomsDutyReq.builder()
                .amount("1000000")
                .ccy("VND")
                .treasuryCode("TC01")
                .admAreaCode("AA001")
                .revAuthCode("RA001")
                .revAccCode("RAC001")
                .debitAccNo("**********")
                .taxCode("**********")
                .payerName("Test Payer")
                .payerAddr("Test Address")
                .payerType(PayerTypeEnum.BUSINESS)
                .raNote("x".repeat(101)) // This is the field we want to test for failure
                .taxItems(Arrays.asList(validTaxItem)) // Provide valid taxItems
                .build();

        Set<ConstraintViolation<ValidateCustomsDutyReq>> violations = validator.validate(req);

        // Now we expect only one violation, specifically for raNote
        assertThat(violations).hasSize(1);
        ConstraintViolation<ValidateCustomsDutyReq> violation = violations.iterator().next();
        assertThat(violation.getPropertyPath().toString()).isEqualTo("raNote");
        assertThat(violation.getMessage()).contains("size must be between 0 and 100"); // Or the actual message
    }

    @Test
    void whenAllFieldsAreValid_thenValidationSucceeds() {
        TxnTaxItemDto taxItem = TxnTaxItemDto.builder().build();
        taxItem.setEiTypeCode("EI001");
        taxItem.setTaxTypeCode("TT001");
        taxItem.setCcCode("CC001");
        taxItem.setChapterCode("CH1");
        taxItem.setEcCode("EC01");
        taxItem.setAmount("1000000");
        taxItem.setCcy("VND");
        taxItem.setDeclarationDate(LocalDate.now());
        taxItem.setDeclarationNo("DN001");
        taxItem.setTransDesc("Test Transaction");

        ValidateCustomsDutyReq req = ValidateCustomsDutyReq.builder()
                .amount("1000000")
                .ccy("VND")
                .treasuryCode("TC01")
                .admAreaCode("AA001")
                .revAuthCode("RA001")
                .revAccCode("RAC001")
                .debitAccNo("**********")
                .taxCode("**********")
                .payerName("Test Payer")
                .payerAddr("Test Address")
                .payerType(PayerTypeEnum.BUSINESS)
                .raNote("Test Note")
                .taxItems(Arrays.asList(taxItem))
                .build();

        Set<ConstraintViolation<ValidateCustomsDutyReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void testNoArgsConstructor() {
        ValidateCustomsDutyReq req = new ValidateCustomsDutyReq();
        assertThat(req).isNotNull();
        assertThat(req.getAmount()).isNull();
        assertThat(req.getCcy()).isNull();
        assertThat(req.getTaxItems()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        String amount = "1000000";
        String ccy = "VND";
        String treasuryCode = "TC01";
        String admAreaCode = "AA001";
        String revAuthCode = "RA001";
        String revAccCode = "RAC001";
        String debitAccNo = "**********";
        String taxCode = "**********";
        String payerName = "Test Payer";
        String payerAddr = "Test Address";
        String altTaxCode = "**********";
        String altPayerName = "Alt Payer";
        String altPayerAddr = "Alt Address";
        PayerTypeEnum payerType = PayerTypeEnum.BUSINESS;
        String raNote = "Test Note";
        List<TxnTaxItemDto> taxItems = Arrays.asList(TxnTaxItemDto.builder().build());

        ValidateCustomsDutyReq req = ValidateCustomsDutyReq.builder()
                .amount(amount)
                .ccy(ccy)
                .treasuryCode(treasuryCode)
                .admAreaCode(admAreaCode)
                .revAuthCode(revAuthCode)
                .revAccCode(revAccCode)
                .debitAccNo(debitAccNo)
                .taxCode(taxCode)
                .payerName(payerName)
                .payerAddr(payerAddr)
                .altTaxCode(altTaxCode)
                .altPayerName(altPayerName)
                .altPayerAddr(altPayerAddr)
                .payerType(payerType)
                .raNote(raNote)
                .taxItems(taxItems)
                .build();

        assertThat(req.getAmount()).isEqualTo(amount);
        assertThat(req.getCcy()).isEqualTo(ccy);
        assertThat(req.getTreasuryCode()).isEqualTo(treasuryCode);
        assertThat(req.getAdmAreaCode()).isEqualTo(admAreaCode);
        assertThat(req.getRevAuthCode()).isEqualTo(revAuthCode);
        assertThat(req.getRevAccCode()).isEqualTo(revAccCode);
        assertThat(req.getDebitAccNo()).isEqualTo(debitAccNo);
        assertThat(req.getTaxCode()).isEqualTo(taxCode);
        assertThat(req.getPayerName()).isEqualTo(payerName);
        assertThat(req.getPayerAddr()).isEqualTo(payerAddr);
        assertThat(req.getAltTaxCode()).isEqualTo(altTaxCode);
        assertThat(req.getAltPayerName()).isEqualTo(altPayerName);
        assertThat(req.getAltPayerAddr()).isEqualTo(altPayerAddr);
        assertThat(req.getPayerType()).isEqualTo(payerType);
        assertThat(req.getRaNote()).isEqualTo(raNote);
        assertThat(req.getTaxItems()).isEqualTo(taxItems);
    }
}