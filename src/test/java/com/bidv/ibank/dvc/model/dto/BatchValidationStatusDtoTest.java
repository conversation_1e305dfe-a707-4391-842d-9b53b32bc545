package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;

class BatchValidationStatusDtoTest {

    @Test
    void testBuilder() {
        BatchValidationStatusDto dto = BatchValidationStatusDto.builder()
                .debitAccNoValid(true)
                .taxCodeValid(true)
                .payerNameValid(true)
                .payerAddrValid(true)
                .altTaxCodeValid(true)
                .altPayerNameValid(true)
                .altPayerAddrValid(true)
                .declarationNoValid(true)
                .declarationDateValid(true)
                .treasuryCodeValid(true)
                .revAccCodeValid(true)
                .revAuthCodeValid(true)
                .admAreaCodeValid(true)
                .chapterCodeValid(true)
                .ecCodeValid(true)
                .amountValid(true)
                .ccyValid(true)
                .transDescValid(true)
                .taxTypeCodeValid(true)
                .ccCodeValid(true)
                .eiTypeCodeValid(true)
                .payerTypeValid(true)
                .build();

        assertTrue(dto.isDebitAccNoValid());
        assertTrue(dto.isTaxCodeValid());
        assertTrue(dto.isPayerNameValid());
        assertTrue(dto.isPayerAddrValid());
        assertTrue(dto.isAltTaxCodeValid());
        assertTrue(dto.isAltPayerNameValid());
        assertTrue(dto.isAltPayerAddrValid());
        assertTrue(dto.isDeclarationNoValid());
        assertTrue(dto.isDeclarationDateValid());
        assertTrue(dto.isTreasuryCodeValid());
        assertTrue(dto.isRevAccCodeValid());
        assertTrue(dto.isRevAuthCodeValid());
        assertTrue(dto.isAdmAreaCodeValid());
        assertTrue(dto.isChapterCodeValid());
        assertTrue(dto.isEcCodeValid());
        assertTrue(dto.isAmountValid());
        assertTrue(dto.isCcyValid());
        assertTrue(dto.isTransDescValid());
        assertTrue(dto.isTaxTypeCodeValid());
        assertTrue(dto.isCcCodeValid());
        assertTrue(dto.isEiTypeCodeValid());
        assertTrue(dto.isPayerTypeValid());
    }

    @Test
    void testBuilderWithFalseValues() {
        BatchValidationStatusDto dto = BatchValidationStatusDto.builder()
                .debitAccNoValid(false)
                .taxCodeValid(false)
                .payerNameValid(false)
                .payerAddrValid(false)
                .altTaxCodeValid(false)
                .altPayerNameValid(false)
                .altPayerAddrValid(false)
                .declarationNoValid(false)
                .declarationDateValid(false)
                .treasuryCodeValid(false)
                .revAccCodeValid(false)
                .revAuthCodeValid(false)
                .admAreaCodeValid(false)
                .chapterCodeValid(false)
                .ecCodeValid(false)
                .amountValid(false)
                .ccyValid(false)
                .transDescValid(false)
                .taxTypeCodeValid(false)
                .ccCodeValid(false)
                .eiTypeCodeValid(false)
                .payerTypeValid(false)
                .build();

        assertFalse(dto.isDebitAccNoValid());
        assertFalse(dto.isTaxCodeValid());
        assertFalse(dto.isPayerNameValid());
        assertFalse(dto.isPayerAddrValid());
        assertFalse(dto.isAltTaxCodeValid());
        assertFalse(dto.isAltPayerNameValid());
        assertFalse(dto.isAltPayerAddrValid());
        assertFalse(dto.isDeclarationNoValid());
        assertFalse(dto.isDeclarationDateValid());
        assertFalse(dto.isTreasuryCodeValid());
        assertFalse(dto.isRevAccCodeValid());
        assertFalse(dto.isRevAuthCodeValid());
        assertFalse(dto.isAdmAreaCodeValid());
        assertFalse(dto.isChapterCodeValid());
        assertFalse(dto.isEcCodeValid());
        assertFalse(dto.isAmountValid());
        assertFalse(dto.isCcyValid());
        assertFalse(dto.isTransDescValid());
        assertFalse(dto.isTaxTypeCodeValid());
        assertFalse(dto.isCcCodeValid());
        assertFalse(dto.isEiTypeCodeValid());
        assertFalse(dto.isPayerTypeValid());
    }

    @Test
    void testBuilderWithMixedValues() {
        BatchValidationStatusDto dto = BatchValidationStatusDto.builder()
                .debitAccNoValid(true)
                .taxCodeValid(false)
                .payerNameValid(true)
                .payerAddrValid(false)
                .altTaxCodeValid(true)
                .altPayerNameValid(false)
                .altPayerAddrValid(true)
                .declarationNoValid(false)
                .declarationDateValid(true)
                .treasuryCodeValid(false)
                .revAccCodeValid(true)
                .revAuthCodeValid(false)
                .admAreaCodeValid(true)
                .chapterCodeValid(false)
                .ecCodeValid(true)
                .amountValid(false)
                .ccyValid(true)
                .transDescValid(false)
                .taxTypeCodeValid(true)
                .ccCodeValid(false)
                .eiTypeCodeValid(true)
                .payerTypeValid(false)
                .build();

        assertTrue(dto.isDebitAccNoValid());
        assertFalse(dto.isTaxCodeValid());
        assertTrue(dto.isPayerNameValid());
        assertFalse(dto.isPayerAddrValid());
        assertTrue(dto.isAltTaxCodeValid());
        assertFalse(dto.isAltPayerNameValid());
        assertTrue(dto.isAltPayerAddrValid());
        assertFalse(dto.isDeclarationNoValid());
        assertTrue(dto.isDeclarationDateValid());
        assertFalse(dto.isTreasuryCodeValid());
        assertTrue(dto.isRevAccCodeValid());
        assertFalse(dto.isRevAuthCodeValid());
        assertTrue(dto.isAdmAreaCodeValid());
        assertFalse(dto.isChapterCodeValid());
        assertTrue(dto.isEcCodeValid());
        assertFalse(dto.isAmountValid());
        assertTrue(dto.isCcyValid());
        assertFalse(dto.isTransDescValid());
        assertTrue(dto.isTaxTypeCodeValid());
        assertFalse(dto.isCcCodeValid());
        assertTrue(dto.isEiTypeCodeValid());
        assertFalse(dto.isPayerTypeValid());
    }
}