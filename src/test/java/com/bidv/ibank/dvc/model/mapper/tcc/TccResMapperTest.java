package com.bidv.ibank.dvc.model.mapper.tcc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationItem;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationItemDebtDetail;

@ExtendWith(MockitoExtension.class)
class TccResMapperTest {

    @InjectMocks
    private TccResMapper mapper;

    private TccInquiryDeclarationItem item;
    private TccInquiryDeclarationItemDebtDetail itemDetail;
    private MockedStatic<Translator> translatorMock;

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenAnswer(inv -> inv.getArgument(1));

        itemDetail = new TccInquiryDeclarationItemDebtDetail();
        itemDetail.setDuNo("1000");
        itemDetail.setTieuMuc("EC123");
        itemDetail.setLoaiThue("TAX1");

        item = new TccInquiryDeclarationItem();
        item.setSoTk("DEC123");
        item.setNgayDk("2024-03-21");
        item.setMaChuong("CH1");
        item.setMaHqCqt("AUTH123");
        item.setTenHqPh("Test Authority");
        item.setTkkb("7111");
        item.setMaLh("EI1");
        item.setTenLh("EI Type 1");
        item.setMaLt("CC1");
        item.setMaKb("KB1");
        item.setTenKb("Treasury 1");
        item.setCtNo(Arrays.asList(itemDetail));
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void testToInquiryCustomsDutyRes() {
        List<InquiryCustomsDutyRes> result = mapper.toInquiryCustomsDutyRes(item);

        assertNotNull(result);
        assertEquals(1, result.size());

        InquiryCustomsDutyRes res = result.get(0);
        assertEquals(item.getSoTk(), res.getDeclarationNo());
        assertEquals(LocalDate.parse("2024-03-21"), res.getDeclarationDate());
        assertEquals(item.getMaChuong(), res.getChapterCode());
        assertEquals(itemDetail.getTieuMuc(), res.getEcCode());
        assertEquals(item.getMaHqCqt(), res.getRevAuthCode());
        assertEquals(item.getTenHqPh(), res.getRevAuthName());
        assertEquals("7111", res.getRevAccCode());
        assertEquals(itemDetail.getLoaiThue(), res.getTaxTypeCode());
        assertEquals(item.getMaLh(), res.getEiTypeCode());
        assertEquals(item.getTenLh(), res.getEiTypeName());
        assertEquals(item.getMaLt(), res.getCcCode());
        assertEquals(item.getMaKb(), res.getTreasuryCode());
        assertEquals(item.getTenKb(), res.getTreasuryName());
        assertEquals("1000", res.getAmount());
        assertEquals(PayerTypeEnum.BUSINESS.getValue(), res.getPayerType());
        assertEquals("VND", res.getCcy());
    }

    @Test
    void testToInquiryCustomsDutyResWithZeroAmount() {
        itemDetail.setDuNo("0");
        List<InquiryCustomsDutyRes> result = mapper.toInquiryCustomsDutyRes(item);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToInquiryCustomsDutyResWithNegativeAmount() {
        itemDetail.setDuNo("-1000");
        List<InquiryCustomsDutyRes> result = mapper.toInquiryCustomsDutyRes(item);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToInquiryCustomsDutyResWithNullAmount() {
        itemDetail.setDuNo(null);
        List<InquiryCustomsDutyRes> result = mapper.toInquiryCustomsDutyRes(item);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToInquiryCustomsDutyResWithNullTkkb() {
        item.setTkkb(null);
        List<InquiryCustomsDutyRes> result = mapper.toInquiryCustomsDutyRes(item);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getRevAccCode());
    }

    @Test
    void testUnsupportedOperations() {
        assertThrows(UnsupportedOperationException.class, () -> mapper.toDto(new Object()));
        assertThrows(UnsupportedOperationException.class, () -> mapper.toEntity(new Object()));
    }
}