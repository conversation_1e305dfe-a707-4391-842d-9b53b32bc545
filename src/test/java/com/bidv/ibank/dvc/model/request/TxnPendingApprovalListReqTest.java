package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDate;
import java.util.Set;

import com.bidv.ibank.framework.domain.request.PagingQueryRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;

class TxnPendingApprovalListReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (var factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        assertThat(req).isNotNull();
        assertThat(req).isInstanceOf(PagingQueryRequest.class);
    }

    @Test
    void testInheritanceFromPagingQueryRequest() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        assertThat(req).isInstanceOf(PagingQueryRequest.class);
    }

    @Test
    void testSettersAndGetters() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        
        // Test properties from TxnPendingApprovalListReq
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");
        
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req.getTaxCode()).isEqualTo("1234567890");
    }

    @Test
    void testEqualsAndHashCode() {
        // Test basic functionality rather than strict equals/hashCode contract
        // due to inheritance complexity from PagingQueryRequest
        TxnPendingApprovalListReq req1 = new TxnPendingApprovalListReq();
        req1.setStartDate(LocalDate.of(2024, 1, 1));
        req1.setEndDate(LocalDate.of(2024, 12, 31));
        req1.setTaxCode("1234567890");

        TxnPendingApprovalListReq req2 = new TxnPendingApprovalListReq();
        req2.setStartDate(LocalDate.of(2024, 6, 1));
        req2.setEndDate(LocalDate.of(2024, 6, 30));
        req2.setTaxCode("0987654321");

        // Test that objects with different content are not equal
        assertThat(req1).isNotEqualTo(req2);

        // Test basic equals contract
        assertThat(req1).isEqualTo(req1); // reflexive
        assertThat(req1).isNotEqualTo(null); // null check
        assertThat(req1).isNotEqualTo("different type"); // different type

        // Test that fields are set correctly (main functionality test)
        assertThat(req1.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req1.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req1.getTaxCode()).isEqualTo("1234567890");
    }

    @Test
    void testToString() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");
        
        String toString = req.toString();
        assertThat(toString).contains("TxnPendingApprovalListReq");
    }

    @Test
    void testValidationInheritsFromParent() {
        // Test validation constraints from parent class
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        // Set invalid data that should fail parent validation
        req.setStartDate(null);

        Set<ConstraintViolation<TxnPendingApprovalListReq>> violations = validator.validate(req);
        // Should have violations from parent class validation
        assertThat(violations).isNotNull();
    }

    @Test
    void testValidRequestPassesValidation() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");
        
        Set<ConstraintViolation<TxnPendingApprovalListReq>> violations = validator.validate(req);
        
        // Filter out violations not related to the basic required fields
        boolean hasRequiredFieldViolation = violations.stream()
                .anyMatch(v -> ("startDate".equals(v.getPropertyPath().toString()) ||
                              "endDate".equals(v.getPropertyPath().toString()) ||
                              "taxCode".equals(v.getPropertyPath().toString())) &&
                         v.getMessage().contains("must not be blank"));
        assertThat(hasRequiredFieldViolation).isFalse();
    }

    @Test
    void testPendingApprovalSpecificBehavior() {
        // Test that TxnPendingApprovalListReq behaves correctly for pending approval requests
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");
        
        // Verify it inherits from PagingQueryRequest (not TxnPendingListReq)
        assertThat(req).isInstanceOf(PagingQueryRequest.class);
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req.getTaxCode()).isEqualTo("1234567890");
    }

    @Test
    void testApprovalWorkflowFiltering() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 1, 31));

        // Test that the request is properly configured for approval workflow
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 1, 31));
    }

    @Test
    void testApprovalListWithMinimalData() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        req.setStartDate(LocalDate.of(2024, 7, 23));
        req.setEndDate(LocalDate.of(2024, 7, 23)); // Same day

        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 7, 23));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 7, 23));
    }

    @Test
    void testApprovalListWithOptionalFilters() {
        TxnPendingApprovalListReq req = new TxnPendingApprovalListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));

        // Test that the object can be created without all optional fields
        assertThat(req).isNotNull();
        assertThat(req.getStartDate()).isNotNull();
        assertThat(req.getEndDate()).isNotNull();
    }
}
