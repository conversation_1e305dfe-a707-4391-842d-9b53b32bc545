package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;

import java.time.LocalDate;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.util.Translator;

class InquiryCustomsDutyResTest {

    @Test
    void testBuilder() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Set up mock translations
            mockedTranslator.when(() -> Translator.toLocale("TC001", "Treasury Name"))
                    .thenReturn("Treasury Name");
            mockedTranslator.when(() -> Translator.toLocale("AA001", "Admin Area Name"))
                    .thenReturn("Admin Area Name");
            mockedTranslator.when(() -> Translator.toLocale("RA001", "Revenue Account Name"))
                    .thenReturn("Revenue Account Name");
            mockedTranslator.when(() -> Translator.toLocale("RAC001", "Revenue Authority Name"))
                    .thenReturn("Revenue Authority Name");

            LocalDate declarationDate = LocalDate.now();
            InquiryCustomsDutyRes res = InquiryCustomsDutyRes.builder()
                    .treasuryCode("TC001")
                    .treasuryName("Treasury Name")
                    .admAreaCode("AA001")
                    .admAreaName("Admin Area Name")
                    .revAccCode("RA001")
                    .revAccName("Revenue Account Name")
                    .amount("1000000")
                    .ccy("VND")
                    .declarationDate(declarationDate)
                    .declarationNo("DN001")
                    .transDesc("Test Transaction")
                    .build();

            assertThat(res.getTreasuryCode()).isEqualTo("TC001");
            assertThat(res.getTreasuryName()).isEqualTo("Treasury Name");
            assertThat(res.getAdmAreaCode()).isEqualTo("AA001");
            assertThat(res.getAdmAreaName()).isEqualTo("Admin Area Name");
            assertThat(res.getRevAccCode()).isEqualTo("RA001");
            assertThat(res.getRevAccName()).isEqualTo("Revenue Account Name");
            assertThat(res.getAmount()).isEqualTo("1000000");
            assertThat(res.getCcy()).isEqualTo("VND");
            assertThat(res.getDeclarationDate()).isEqualTo(declarationDate);
            assertThat(res.getDeclarationNo()).isEqualTo("DN001");
            assertThat(res.getTransDesc()).isEqualTo("Test Transaction");
        }
    }

    @Test
    void testTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            InquiryCustomsDutyRes res = InquiryCustomsDutyRes.builder()
                    .treasuryCode("TC001")
                    .treasuryName("Treasury Name")
                    .admAreaCode("AA001")
                    .admAreaName("Admin Area Name")
                    .build();

            mockedTranslator.when(() -> Translator.toLocale("TC001", "Treasury Name"))
                    .thenReturn("Translated Treasury Name");
            mockedTranslator.when(() -> Translator.toLocale("AA001", "Admin Area Name"))
                    .thenReturn("Translated Admin Area Name");

            assertThat(res.getTreasuryName()).isEqualTo("Translated Treasury Name");
            assertThat(res.getAdmAreaName()).isEqualTo("Translated Admin Area Name");
        }
    }

    @Test
    void testInheritance() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            InquiryCustomsDutyRes res = InquiryCustomsDutyRes.builder()
                    .treasuryCode("TC001")
                    .treasuryName("Treasury Name")
                    .amount("1000000")
                    .ccy("VND")
                    .payerType(PayerTypeEnum.BUSINESS.getValue())
                    .build();

            mockedTranslator.when(() -> Translator.toLocale("TC001", "Treasury Name"))
                    .thenReturn("Translated Treasury Name");

            assertThat(res.getTreasuryCode()).isEqualTo("TC001");
            assertThat(res.getTreasuryName()).isEqualTo("Translated Treasury Name");
            assertThat(res.getAmount()).isEqualTo("1000000");
            assertThat(res.getCcy()).isEqualTo("VND");
            assertThat(res.getPayerType()).isEqualTo(PayerTypeEnum.BUSINESS.getValue());
        }
    }

    @Test
    void testNoArgsConstructor() {
        InquiryCustomsDutyRes res = new InquiryCustomsDutyRes();
        assertThat(res).isNotNull();
        assertThat(res.getTreasuryName()).isEmpty();
        assertThat(res.getAdmAreaName()).isEmpty();
        assertThat(res.getAmount()).isNull();
    }
}