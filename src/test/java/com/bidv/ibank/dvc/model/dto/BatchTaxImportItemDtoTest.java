package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class BatchTaxImportItemDtoTest {

    private BatchTaxImportItemDto dto;
    private static final String DECLARATION_NO = "DEC123";
    private static final String DECLARATION_YEAR = "2024";
    private static final String TCC_ERR_CODE = "TCC001";
    private static final String TCC_ERR_MSG = "TCC Error";
    private static final Integer ROW_NUM = 1;

    @BeforeEach
    void setUp() {
        dto = BatchTaxImportItemDto.builder()
                .declarationNo(DECLARATION_NO)
                .declarationYear(DECLARATION_YEAR)
                .tccErrCode(TCC_ERR_CODE)
                .tccErrMsg(TCC_ERR_MSG)
                .build();
        dto.setRownum(ROW_NUM);
    }

    @Test
    void testBuilder() {
        assertNotNull(dto);
        assertEquals(DECLARATION_NO, dto.getDeclarationNo());
        assertEquals(DECLARATION_YEAR, dto.getDeclarationYear());
        assertEquals(TCC_ERR_CODE, dto.getTccErrCode());
        assertEquals(TCC_ERR_MSG, dto.getTccErrMsg());
        assertEquals(ROW_NUM, dto.getRownum());
    }

    @Test
    void testIsValidRow() {
        // When there are errors, the row should be invalid
        assertFalse(dto.isValidRow());

        // When there are no errors, the row should be valid
        BatchTaxImportItemDto validDto = BatchTaxImportItemDto.builder()
                .declarationNo(DECLARATION_NO)
                .declarationYear(DECLARATION_YEAR)
                .build();
        assertTrue(validDto.isValidRow());

        // When errList is empty but tccErrCode exists, the row should be invalid
        BatchTaxImportItemDto invalidDto1 = BatchTaxImportItemDto.builder()
                .declarationNo(DECLARATION_NO)
                .declarationYear(DECLARATION_YEAR)
                .tccErrCode(TCC_ERR_CODE)
                .build();
        assertFalse(invalidDto1.isValidRow());

        // When errList is empty but tccErrMsg exists, the row should be invalid
        BatchTaxImportItemDto invalidDto2 = BatchTaxImportItemDto.builder()
                .declarationNo(DECLARATION_NO)
                .declarationYear(DECLARATION_YEAR)
                .tccErrMsg(TCC_ERR_MSG)
                .build();
        assertFalse(invalidDto2.isValidRow());

        Map<String, Set<String>> fieldErrors = new HashMap<>();
        fieldErrors.put(DECLARATION_NO, Set.of("ERR1"));
        // When errList has items but tccErrCode and tccErrMsg are blank, the row should be invalid
        BatchTaxImportItemDto invalidDto3 = BatchTaxImportItemDto.builder()
                .declarationNo(DECLARATION_NO)
                .declarationYear(DECLARATION_YEAR)
                .fieldErrors(fieldErrors)
                .build();
        assertFalse(invalidDto3.isValidRow());
    }

    @Test
    void testSetRownum() {
        Integer newRowNum = 2;
        dto.setRownum(newRowNum);
        assertEquals(newRowNum, dto.getRownum());
    }

    @Test
    void testToString() {
        String toString = dto.toString();
        assertNotNull(toString);
        assertTrue(toString.contains(DECLARATION_NO));
        assertTrue(toString.contains(DECLARATION_YEAR));
        assertTrue(toString.contains(TCC_ERR_CODE));
        assertTrue(toString.contains(TCC_ERR_MSG));
        assertTrue(toString.contains(ROW_NUM.toString()));
    }

    @Test
    void testEqualsAndHashCode() {
        BatchTaxImportItemDto sameDto = BatchTaxImportItemDto.builder()
                .declarationNo(DECLARATION_NO)
                .declarationYear(DECLARATION_YEAR)
                .tccErrCode(TCC_ERR_CODE)
                .tccErrMsg(TCC_ERR_MSG)
                .build();
        sameDto.setRownum(ROW_NUM);

        BatchTaxImportItemDto differentDto = BatchTaxImportItemDto.builder()
                .declarationNo("DEC456")
                .declarationYear("2025")
                .tccErrCode("TCC002")
                .tccErrMsg("Different Error")
                .build();
        differentDto.setRownum(2);

        // Test equals
        assertEquals(dto, dto); // Same object
        assertEquals(dto.hashCode(), sameDto.hashCode()); // Different object with same values
        assertNotEquals(dto, differentDto); // Different values
        assertNotEquals(dto, null); // Null comparison
        assertNotEquals(dto, new Object()); // Different type
    }
}