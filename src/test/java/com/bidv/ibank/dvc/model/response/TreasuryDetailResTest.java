package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TreasuryDetailResTest {

    private TreasuryDetailRes treasuryDetailRes;

    @BeforeEach
    void setUp() {
        treasuryDetailRes = new TreasuryDetailRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TreasuryDetailRes res = new TreasuryDetailRes();

        // Then
        assertNotNull(res);
        assertNull(res.getTreasuryCode());
        assertNull(res.getBenBankCode());
        assertNull(res.getBenBankName());
        assertFalse(res.isInBidv());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String treasuryCode = "T001";
        String benBankCode = "BIDV";
        String benBankName = "Bank for Investment and Development of Vietnam";
        boolean isInBidv = true;

        // When
        TreasuryDetailRes res = new TreasuryDetailRes(treasuryCode, benBankCode, benBankName, isInBidv);

        // Then
        assertEquals(treasuryCode, res.getTreasuryCode());
        assertEquals(benBankCode, res.getBenBankCode());
        assertEquals(benBankName, res.getBenBankName());
        assertTrue(res.isInBidv());
    }

    @Test
    void testBuilderPattern() {
        // Given
        String treasuryCode = "T002";
        String benBankCode = "VCB";
        String benBankName = "Vietcombank";
        boolean isInBidv = false;

        // When
        TreasuryDetailRes res = TreasuryDetailRes.builder()
                .treasuryCode(treasuryCode)
                .benBankCode(benBankCode)
                .benBankName(benBankName)
                .isInBidv(isInBidv)
                .build();

        // Then
        assertEquals(treasuryCode, res.getTreasuryCode());
        assertEquals(benBankCode, res.getBenBankCode());
        assertEquals(benBankName, res.getBenBankName());
        assertFalse(res.isInBidv());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String treasuryCode = "T003";
        String benBankCode = "TCB";
        String benBankName = "Techcombank";
        boolean isInBidv = true;

        // When
        treasuryDetailRes.setTreasuryCode(treasuryCode);
        treasuryDetailRes.setBenBankCode(benBankCode);
        treasuryDetailRes.setBenBankName(benBankName);
        treasuryDetailRes.setInBidv(isInBidv);

        // Then
        assertEquals(treasuryCode, treasuryDetailRes.getTreasuryCode());
        assertEquals(benBankCode, treasuryDetailRes.getBenBankCode());
        assertEquals(benBankName, treasuryDetailRes.getBenBankName());
        assertTrue(treasuryDetailRes.isInBidv());
    }

    @Test
    void testJsonPropertyAnnotation() {
        // Given
        TreasuryDetailRes res = TreasuryDetailRes.builder()
                .treasuryCode("T001")
                .isInBidv(true)
                .build();

        // Then
        assertNotNull(res);
        assertEquals("T001", res.getTreasuryCode());
        assertTrue(res.isInBidv());
    }

    @Test
    void testBooleanFieldFalseCase() {
        // Given
        treasuryDetailRes.setInBidv(false);

        // Then
        assertFalse(treasuryDetailRes.isInBidv());
    }

    @Test
    void testBooleanFieldTrueCase() {
        // Given
        treasuryDetailRes.setInBidv(true);

        // Then
        assertTrue(treasuryDetailRes.isInBidv());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String treasuryCode = "T001";
        String benBankCode = "BIDV";
        boolean isInBidv = true;

        TreasuryDetailRes res1 = TreasuryDetailRes.builder()
                .treasuryCode(treasuryCode)
                .benBankCode(benBankCode)
                .isInBidv(isInBidv)
                .build();

        TreasuryDetailRes res2 = TreasuryDetailRes.builder()
                .treasuryCode(treasuryCode)
                .benBankCode(benBankCode)
                .isInBidv(isInBidv)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String treasuryCode = "T001";
        String benBankName = "BIDV";

        treasuryDetailRes.setTreasuryCode(treasuryCode);
        treasuryDetailRes.setBenBankName(benBankName);
        treasuryDetailRes.setInBidv(true);

        // When
        String result = treasuryDetailRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
