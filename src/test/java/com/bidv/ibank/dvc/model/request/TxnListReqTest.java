package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDate;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;

class TxnListReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (var factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        TxnListReq req = new TxnListReq();
        assertThat(req).isNotNull();
        assertThat(req).isInstanceOf(TxnPendingListReq.class);
    }

    @Test
    void testInheritanceFromTxnPendingListReq() {
        TxnListReq req = new TxnListReq();
        assertThat(req).isInstanceOf(TxnPendingListReq.class);
    }

    @Test
    void testSettersAndGetters() {
        TxnListReq req = new TxnListReq();

        // Test inherited properties from TxnPendingListReq
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");

        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req.getTaxCode()).isEqualTo("1234567890");
    }

    @Test
    void testEqualsAndHashCode() {
        // Test basic functionality rather than strict equals/hashCode contract
        // due to inheritance complexity from TxnPendingListReq
        TxnListReq req1 = new TxnListReq();
        req1.setStartDate(LocalDate.of(2024, 1, 1));
        req1.setEndDate(LocalDate.of(2024, 12, 31));
        req1.setTaxCode("1234567890");

        TxnListReq req2 = new TxnListReq();
        req2.setStartDate(LocalDate.of(2024, 6, 1));
        req2.setEndDate(LocalDate.of(2024, 6, 30));
        req2.setTaxCode("0987654321");

        // Test that objects with different content are not equal
        assertThat(req1).isNotEqualTo(req2);

        // Test basic equals contract
        assertThat(req1).isEqualTo(req1); // reflexive
        assertThat(req1).isNotEqualTo(null); // null check
        assertThat(req1).isNotEqualTo("different type"); // different type

        // Test that fields are set correctly (main functionality test)
        assertThat(req1.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req1.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req1.getTaxCode()).isEqualTo("1234567890");
    }

    @Test
    void testToString() {
        TxnListReq req = new TxnListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");

        String toString = req.toString();
        assertThat(toString).contains("TxnListReq");
    }

    @Test
    void testValidationInheritsFromParent() {
        // Test validation constraints from parent class
        TxnListReq req = new TxnListReq();
        // Set invalid data that should fail parent validation
        req.setStartDate(null);

        Set<ConstraintViolation<TxnListReq>> violations = validator.validate(req);
        // Should have violations from parent class validation
        assertThat(violations).isNotNull();
    }

    @Test
    void testValidRequestPassesValidation() {
        TxnListReq req = new TxnListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");

        Set<ConstraintViolation<TxnListReq>> violations = validator.validate(req);

        // Filter out violations not related to the basic required fields
        boolean hasRequiredFieldViolation = violations.stream()
                .anyMatch(v -> ("startDate".equals(v.getPropertyPath().toString()) ||
                              "endDate".equals(v.getPropertyPath().toString()) ||
                              "taxCode".equals(v.getPropertyPath().toString())) &&
                         v.getMessage().contains("must not be blank"));
        assertThat(hasRequiredFieldViolation).isFalse();
    }

    @Test
    void testListSpecificBehavior() {
        // Test that TxnListReq behaves correctly as a list request
        TxnListReq req = new TxnListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");

        // Verify it inherits all TxnPendingListReq behavior
        assertThat(req).isInstanceOf(TxnPendingListReq.class);
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req.getTaxCode()).isEqualTo("1234567890");
    }

    @Test
    void testListWithDateRange() {
        TxnListReq req = new TxnListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 1, 31)); // One month range

        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 1, 31));
    }

    @Test
    void testListWithOptionalFilters() {
        TxnListReq req = new TxnListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));

        // Test that the object can be created without all optional fields
        assertThat(req).isNotNull();
        assertThat(req.getStartDate()).isNotNull();
        assertThat(req.getEndDate()).isNotNull();
    }

    @Test
    void testListWithAllParameters() {
        TxnListReq req = new TxnListReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");

        // Verify all parameters are set correctly
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req.getTaxCode()).isEqualTo("1234567890");
    }
}
