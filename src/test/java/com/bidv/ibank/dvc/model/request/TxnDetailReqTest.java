package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnDetailReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void whenAllFieldsValid_thenNoViolations() {
        TxnDetailReq req = TxnDetailReq.builder()
                .txnId("DVC01704202411252339")
                .build();

        Set<ConstraintViolation<TxnDetailReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void whenTxnIdIsNull_thenViolation() {
        TxnDetailReq req = TxnDetailReq.builder()
                .txnId(null)
                .build();

        Set<ConstraintViolation<TxnDetailReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("must not be blank");
    }

    @Test
    void whenTxnIdIsEmpty_thenViolation() {
        TxnDetailReq req = TxnDetailReq.builder()
                .txnId("")
                .build();

        Set<ConstraintViolation<TxnDetailReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("must not be blank");
    }

    @Test
    void whenTxnIdExceedsMaxLength_thenViolation() {
        String longTxnId = "DVC01704202411252339".repeat(3); // Creates a string longer than 50 characters
        TxnDetailReq req = TxnDetailReq.builder()
                .txnId(longTxnId)
                .build();

        Set<ConstraintViolation<TxnDetailReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("size must be between 0 and 50");
    }

    @Test
    void whenCheckPermIsNull_thenNoViolation() {
        TxnDetailReq req = TxnDetailReq.builder()
                .txnId("DVC01704202411252339")
                .build();

        Set<ConstraintViolation<TxnDetailReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void builderAndGetterSetter_ShouldWorkCorrectly() {
        String txnId = "DVC01704202411252339";

        // Test no-args constructor and setters
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(txnId);
        assertThat(req.getTxnId()).isEqualTo(txnId);

        // Test builder
        TxnDetailReq builderReq = TxnDetailReq.builder()
                .txnId(txnId)
                .build();
        assertThat(builderReq.getTxnId()).isEqualTo(txnId);
    }

    @Test
    void equalsAndHashCode_ShouldWorkCorrectly() {
        TxnDetailReq req1 = TxnDetailReq.builder()
                .txnId("DVC01704202411252339")
                .build();

        TxnDetailReq req2 = TxnDetailReq.builder()
                .txnId("DVC01704202411252339")
                .build();

        TxnDetailReq req3 = TxnDetailReq.builder()
                .txnId("DVC01704202411252340")
                .build();

        assertThat(req1).isEqualTo(req2);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isNotEqualTo(req3.hashCode());
    }

    @Test
    void allArgsConstructor_ShouldWorkCorrectly() {
        String txnId = "DVC01704202411252339";

        TxnDetailReq req = new TxnDetailReq(txnId);

        assertThat(req.getTxnId()).isEqualTo(txnId);
    }
}