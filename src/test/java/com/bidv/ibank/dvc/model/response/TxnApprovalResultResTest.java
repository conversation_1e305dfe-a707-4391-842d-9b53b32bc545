package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mockStatic;

import java.time.LocalDateTime;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnApprovalResultResTest {

    private TxnApprovalResultRes txnApprovalResultRes;

    @BeforeEach
    void setUp() {
        txnApprovalResultRes = new TxnApprovalResultRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnApprovalResultRes res = new TxnApprovalResultRes();

        // Then
        assertNotNull(res);
        assertNull(res.getApprovedDate());
        assertNull(res.getIsFinal());
        assertNull(res.getUnderBalanceFlag());
        assertNull(res.getTotal());
        assertNull(res.getTotalSuccess());
        assertNull(res.getFailTxns());
    }

    @Test
    void testSuperBuilderPattern() {
        // Given
        LocalDateTime approvedDate = LocalDateTime.of(2025, 5, 29, 14, 30, 0);
        Boolean isFinal = true;
        String underBalanceFlag = AppConstants.UNDER_BALANCE_FLAG.NO;
        Long total = 5L;
        Long totalSuccess = 4L;

        // When
        TxnApprovalResultRes res = TxnApprovalResultRes.builder()
                .approvedDate(approvedDate)
                .isFinal(isFinal)
                .underBalanceFlag(underBalanceFlag)
                .total(total)
                .totalSuccess(totalSuccess)
                .build();

        // Then
        assertEquals(approvedDate, res.getApprovedDate());
        assertEquals(isFinal, res.getIsFinal());
        assertEquals(underBalanceFlag, res.getUnderBalanceFlag());
        assertEquals(total, res.getTotal());
        assertEquals(totalSuccess, res.getTotalSuccess());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        LocalDateTime approvedDate = LocalDateTime.now();
        Boolean isFinal = false;
        String underBalanceFlag = AppConstants.UNDER_BALANCE_FLAG.YES;

        // When
        txnApprovalResultRes.setApprovedDate(approvedDate);
        txnApprovalResultRes.setIsFinal(isFinal);
        txnApprovalResultRes.setUnderBalanceFlag(underBalanceFlag);

        // Then
        assertEquals(approvedDate, txnApprovalResultRes.getApprovedDate());
        assertEquals(isFinal, txnApprovalResultRes.getIsFinal());
        assertEquals(underBalanceFlag, txnApprovalResultRes.getUnderBalanceFlag());
    }

    @Test
    void testGetStatusNameWithTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String expectedStatus = TransactionStatusEnum.SUCCESS.name();
            String translatedStatusName = "Thành công";
            String expectedKey = AppConstants.LANGUAGE.TXN_STATUS + "." + expectedStatus;

            txnApprovalResultRes.setTotal(1L);
            txnApprovalResultRes.setTotalSuccess(1L);
            txnApprovalResultRes.setUnderBalanceFlag(AppConstants.UNDER_BALANCE_FLAG.NO);

            mockedTranslator.when(() -> Translator.toLocale(expectedKey))
                    .thenReturn(translatedStatusName);

            // When
            String result = txnApprovalResultRes.getStatusName();

            // Then
            assertEquals(translatedStatusName, result);
        }
    }

    @Test
    void testGetStatusWhenAllFailed() {
        // Given
        txnApprovalResultRes.setTotal(2L);
        txnApprovalResultRes.setTotalSuccess(0L);

        // When
        String status = txnApprovalResultRes.getStatus();

        // Then
        assertEquals(TransactionStatusEnum.FAILED.name(), status);
    }

    @Test
    void testGetStatusWhenSingleTransactionFailedWithUndefinedError() {
        // Given
        TransactionResDetail failedTxn = new TransactionResDetail();
        failedTxn.setCode(ResponseCode.UNDEFINED.code());

        txnApprovalResultRes.setTotal(1L);
        txnApprovalResultRes.setTotalSuccess(0L);
        txnApprovalResultRes.setFailTxns(Collections.singletonList(failedTxn));

        // When
        String status = txnApprovalResultRes.getStatus();

        // Then
        assertEquals(TransactionStatusEnum.UNDEFINED.name(), status);
    }

    @Test
    void testGetStatusWhenSingleTransactionFailedWithDefinedError() {
        // Given
        TransactionResDetail failedTxn = new TransactionResDetail();
        failedTxn.setCode("VALIDATION_ERROR");

        txnApprovalResultRes.setTotal(1L);
        txnApprovalResultRes.setTotalSuccess(0L);
        txnApprovalResultRes.setFailTxns(Collections.singletonList(failedTxn));

        // When
        String status = txnApprovalResultRes.getStatus();

        // Then
        assertEquals(TransactionStatusEnum.FAILED.name(), status);
    }

    @Test
    void testGetStatusWhenSuccessWithNoUnderBalance() {
        // Given
        txnApprovalResultRes.setTotal(1L);
        txnApprovalResultRes.setTotalSuccess(1L);
        txnApprovalResultRes.setUnderBalanceFlag(AppConstants.UNDER_BALANCE_FLAG.NO);

        // When
        String status = txnApprovalResultRes.getStatus();

        // Then
        assertEquals(TransactionStatusEnum.SUCCESS.name(), status);
    }

    @Test
    void testGetStatusWhenSuccessWithUnderBalance() {
        // Given
        txnApprovalResultRes.setTotal(1L);
        txnApprovalResultRes.setTotalSuccess(1L);
        txnApprovalResultRes.setUnderBalanceFlag(AppConstants.UNDER_BALANCE_FLAG.YES);

        // When
        String status = txnApprovalResultRes.getStatus();

        // Then
        assertEquals(TransactionStatusEnum.BANK_PROCESSING.name(), status);
    }

    @Test
    void testInheritanceFromTxnProcessResultRes() {
        // Given
        TxnApprovalResultRes res = new TxnApprovalResultRes();

        // Then
        assertNotNull(res);
        res.setTotal(10L);
        assertEquals(10L, res.getTotal());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        LocalDateTime approvedDate = LocalDateTime.of(2025, 5, 29, 14, 30, 0);
        Boolean isFinal = true;

        TxnApprovalResultRes res1 = TxnApprovalResultRes.builder()
                .approvedDate(approvedDate)
                .isFinal(isFinal)
                .total(5L)
                .build();

        TxnApprovalResultRes res2 = TxnApprovalResultRes.builder()
                .approvedDate(approvedDate)
                .isFinal(isFinal)
                .total(5L)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        LocalDateTime approvedDate = LocalDateTime.now();
        txnApprovalResultRes.setApprovedDate(approvedDate);
        txnApprovalResultRes.setIsFinal(true);
        txnApprovalResultRes.setTotal(5L);

        // When
        String result = txnApprovalResultRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
