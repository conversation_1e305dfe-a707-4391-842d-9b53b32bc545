package com.bidv.ibank.dvc.model.mapper.customsduty;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.client.common.dto.workflow.WfTxnInfoResponse;
import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentDto;
import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentItemDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.util.JasperReportUtils;

import net.sf.jasperreports.engine.JREmptyDataSource;

@ExtendWith(MockitoExtension.class)
class DocumentMapperTest {

    @InjectMocks
    private DocumentMapper documentMapper;

    private TxnPrintDocumentItemDto mockItemDto1;
    private TxnPrintDocumentItemDto mockItemDto2;
    private TxnPrintDocumentItemDto mockItemDto3;

    @BeforeEach
    void setUp() {
        setupMockDocumentItems();
    }

    private TxnPrintDocumentDto createMockTxnPrintDocument(String txnId, String admAreaCode,
            Map<String, Object> firstPageParams,
            Map<String, Object> secondPageParams) {
        TxnPrintDocumentDto mockDto = mock(TxnPrintDocumentDto.class);
        lenient().when(mockDto.getTxnId()).thenReturn(txnId);
        lenient().when(mockDto.getAdmAreaCode()).thenReturn(admAreaCode);
        lenient().when(mockDto.getParamFirstPage()).thenReturn(firstPageParams);
        lenient().when(mockDto.getParamSecondPage(any(WfTxnInfoResponse.class))).thenReturn(secondPageParams);
        lenient().when(mockDto.getApprovalWfId()).thenReturn("WF_" + txnId);
        return mockDto;
    }

    private WfTxnInfoResponse createMockWfTxnInfoResponse(String id) {
        WfTxnInfoResponse mockResponse = mock(WfTxnInfoResponse.class);
        lenient().when(mockResponse.getId()).thenReturn(id);
        return mockResponse;
    }

    private void setupMockDocumentItems() {
        mockItemDto1 = new TxnPrintDocumentItemDto();
        mockItemDto1.setTxnId("TXN001");
        mockItemDto1.setDeclarationNo("DECL001");
        mockItemDto1.setAmount("1000");

        mockItemDto2 = new TxnPrintDocumentItemDto();
        mockItemDto2.setTxnId("TXN001");
        mockItemDto2.setDeclarationNo("DECL002");
        mockItemDto2.setAmount("2000");

        mockItemDto3 = new TxnPrintDocumentItemDto();
        mockItemDto3.setTxnId("TXN002");
        mockItemDto3.setDeclarationNo("DECL003");
        mockItemDto3.setAmount("3000");
    }

    @Test
    void testToBatchExportParams_Success() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        TxnPrintDocumentDto mockTxnPrintDto2 = createMockTxnPrintDocument("TXN002", "ADM002",
                Map.of("key3", "value3"), Map.of("key4", "value4"));

        List<TxnPrintDocumentDto> txnPrintDtos = Arrays.asList(mockTxnPrintDto1, mockTxnPrintDto2);
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = Arrays.asList(mockItemDto1, mockItemDto2, mockItemDto3);
        List<WfTxnInfoResponse> wfTxnInfoResponses = Arrays.asList(
                createMockWfTxnInfoResponse("WF_TXN001"),
                createMockWfTxnInfoResponse("WF_TXN002")
        );

        // When
        List<JasperReportUtils.BatchExportParam> result = documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size()); // 2 txns * 2 pages each

        // Verify first transaction pages
        JasperReportUtils.BatchExportParam firstPageTxn1 = result.get(0);
        assertEquals(AppConstants.DOCUMENT_JASPER_FIRST_PAGE_PATH, firstPageTxn1.getTemplateFile());
        assertInstanceOf(JREmptyDataSource.class, firstPageTxn1.getDataSource());
        assertEquals(Map.of("key1", "value1"), firstPageTxn1.getInputParams());

        JasperReportUtils.BatchExportParam secondPageTxn1 = result.get(1);
        assertEquals(AppConstants.DOCUMENT_JASPER_SECOND_PAGE_PATH, secondPageTxn1.getTemplateFile());
        assertInstanceOf(JREmptyDataSource.class, secondPageTxn1.getDataSource());
        assertEquals(Map.of("key2", "value2"), secondPageTxn1.getInputParams());

        // Verify second transaction pages
        JasperReportUtils.BatchExportParam firstPageTxn2 = result.get(2);
        assertEquals(AppConstants.DOCUMENT_JASPER_FIRST_PAGE_PATH, firstPageTxn2.getTemplateFile());
        assertInstanceOf(JREmptyDataSource.class, firstPageTxn2.getDataSource());
        assertEquals(Map.of("key3", "value3"), firstPageTxn2.getInputParams());

        JasperReportUtils.BatchExportParam secondPageTxn2 = result.get(3);
        assertEquals(AppConstants.DOCUMENT_JASPER_SECOND_PAGE_PATH, secondPageTxn2.getTemplateFile());
        assertInstanceOf(JREmptyDataSource.class, secondPageTxn2.getDataSource());
        assertEquals(Map.of("key4", "value4"), secondPageTxn2.getInputParams());
    }

    @Test
    void testToBatchExportParams_DocumentItemsProcessing() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = Arrays.asList(mockItemDto1, mockItemDto2);
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When
        documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then - Verify that setTxnPrintDocumentItems was called with filtered and processed items
        verify(mockTxnPrintDto1).setTxnPrintDocumentItems(argThat(items -> {
            if (items.size() != 2)
                return false;

            // Check that orders are set correctly
            TxnPrintDocumentItemDto item1 = items.get(0);
            TxnPrintDocumentItemDto item2 = items.get(1);

            return "1".equals(item1.getOrder()) && "2".equals(item2.getOrder()) &&
                    "TXN001".equals(item1.getTxnId()) && "TXN001".equals(item2.getTxnId());
        }));
    }

    @Test
    void testToBatchExportParams_EmptyTxnPrintDtos() {
        // Given
        List<TxnPrintDocumentDto> txnPrintDtos = Collections.emptyList();
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = Arrays.asList(mockItemDto1, mockItemDto2);
        List<WfTxnInfoResponse> wfTxnInfoResponses = Collections.emptyList();

        // When
        List<JasperReportUtils.BatchExportParam> result = documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testToBatchExportParams_EmptyDocumentItems() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = Collections.emptyList();
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When
        List<JasperReportUtils.BatchExportParam> result = documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Still creates 2 pages for the transaction

        // Verify that setTxnPrintDocumentItems was called with empty list
        verify(mockTxnPrintDto1).setTxnPrintDocumentItems(argThat(List::isEmpty));
    }

    @Test
    void testToBatchExportParams_NoMatchingDocumentItems() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = List.of(mockItemDto3); // Different txnId
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When
        List<JasperReportUtils.BatchExportParam> result = documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify that setTxnPrintDocumentItems was called with empty list (no matching items)
        verify(mockTxnPrintDto1).setTxnPrintDocumentItems(argThat(List::isEmpty));
    }

    @Test
    void testToBatchExportParams_SingleTransaction() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = List.of(mockItemDto1);
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When
        List<JasperReportUtils.BatchExportParam> result = documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify first page
        JasperReportUtils.BatchExportParam firstPage = result.get(0);
        assertEquals(AppConstants.DOCUMENT_JASPER_FIRST_PAGE_PATH, firstPage.getTemplateFile());
        assertInstanceOf(JREmptyDataSource.class, firstPage.getDataSource());
        assertEquals(Map.of("key1", "value1"), firstPage.getInputParams());

        // Verify second page
        JasperReportUtils.BatchExportParam secondPage = result.get(1);
        assertEquals(AppConstants.DOCUMENT_JASPER_SECOND_PAGE_PATH, secondPage.getTemplateFile());
        assertInstanceOf(JREmptyDataSource.class, secondPage.getDataSource());
        assertEquals(Map.of("key2", "value2"), secondPage.getInputParams());
    }

    @Test
    void testToBatchExportParams_OrderAssignment() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);

        // Create multiple items for the same transaction
        TxnPrintDocumentItemDto item1 = new TxnPrintDocumentItemDto();
        item1.setTxnId("TXN001");

        TxnPrintDocumentItemDto item2 = new TxnPrintDocumentItemDto();
        item2.setTxnId("TXN001");

        TxnPrintDocumentItemDto item3 = new TxnPrintDocumentItemDto();
        item3.setTxnId("TXN001");

        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = Arrays.asList(item1, item2, item3);
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When
        documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then - Verify that orders are assigned sequentially
        verify(mockTxnPrintDto1).setTxnPrintDocumentItems(argThat(items -> {
            if (items.size() != 3)
                return false;

            return "1".equals(items.get(0).getOrder()) &&
                    "2".equals(items.get(1).getOrder()) &&
                    "3".equals(items.get(2).getOrder());
        }));
    }

    @Test
    void testToBatchExportParams_AdmAreaCodeAssignment() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);

        TxnPrintDocumentItemDto item = new TxnPrintDocumentItemDto();
        item.setTxnId("TXN001");
        item.setChapterCode(null); // Initially null

        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = List.of(item);
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When
        documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then - Verify that admAreaCode is set from the parent transaction
        verify(mockTxnPrintDto1).setTxnPrintDocumentItems(argThat(items -> {
            if (items.size() != 1)
                return false;

            return "TXN001".equals(items.get(0).getTxnId());
        }));
    }

    @Test
    void testToBatchExportParams_NullInputLists() {
        // Given
        List<TxnPrintDocumentDto> txnPrintDtos = null;
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = null;
        List<WfTxnInfoResponse> wfTxnInfoResponses = null;

        // When & Then
        assertThrows(NullPointerException.class, () ->
            documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses));
    }

    @Test
    void testToBatchExportParams_NullTxnPrintDtos() {
        // Given
        List<TxnPrintDocumentDto> txnPrintDtos = null;
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = List.of(mockItemDto1);
        List<WfTxnInfoResponse> wfTxnInfoResponses = Collections.emptyList();

        // When & Then
        assertThrows(NullPointerException.class, () ->
            documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses));
    }

    @Test
    void testToBatchExportParams_NullDocumentItemsList() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = null;
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When & Then
        assertThrows(NullPointerException.class, () ->
            documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses));
    }

    @Test
    void testToBatchExportParams_MultipleTransactionsWithDifferentItems() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        TxnPrintDocumentDto mockTxnPrintDto2 = createMockTxnPrintDocument("TXN002", "ADM002",
                Map.of("key3", "value3"), Map.of("key4", "value4"));
        List<TxnPrintDocumentDto> txnPrintDtos = Arrays.asList(mockTxnPrintDto1, mockTxnPrintDto2);

        // Create items for different transactions
        TxnPrintDocumentItemDto item1 = new TxnPrintDocumentItemDto();
        item1.setTxnId("TXN001");

        TxnPrintDocumentItemDto item2 = new TxnPrintDocumentItemDto();
        item2.setTxnId("TXN001");

        TxnPrintDocumentItemDto item3 = new TxnPrintDocumentItemDto();
        item3.setTxnId("TXN002");

        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = Arrays.asList(item1, item2, item3);
        List<WfTxnInfoResponse> wfTxnInfoResponses = Arrays.asList(
                createMockWfTxnInfoResponse("WF_TXN001"),
                createMockWfTxnInfoResponse("WF_TXN002")
        );

        // When
        List<JasperReportUtils.BatchExportParam> result = documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size()); // 2 transactions * 2 pages each

        // Verify TXN001 gets 2 items
        verify(mockTxnPrintDto1).setTxnPrintDocumentItems(argThat(items -> items.size() == 2));

        // Verify TXN002 gets 1 item
        verify(mockTxnPrintDto2).setTxnPrintDocumentItems(argThat(items -> items.size() == 1));
    }

    @Test
    void testToBatchExportParams_VerifyParameterStructure() {
        // Given
        TxnPrintDocumentDto mockTxnPrintDto1 = createMockTxnPrintDocument("TXN001", "ADM001",
                Map.of("key1", "value1"), Map.of("key2", "value2"));
        List<TxnPrintDocumentDto> txnPrintDtos = List.of(mockTxnPrintDto1);
        List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = Collections.emptyList();
        List<WfTxnInfoResponse> wfTxnInfoResponses = List.of(createMockWfTxnInfoResponse("WF_TXN001"));

        // When
        List<JasperReportUtils.BatchExportParam> result = documentMapper.toBatchExportParams(txnPrintDtos, txnPrintDocumentItemList, wfTxnInfoResponses);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // Verify first page structure
        JasperReportUtils.BatchExportParam firstPage = result.get(0);
        assertNotNull(firstPage.getTemplateFile());
        assertNotNull(firstPage.getDataSource());
        assertNotNull(firstPage.getInputParams());
        assertEquals(AppConstants.DOCUMENT_JASPER_FIRST_PAGE_PATH, firstPage.getTemplateFile());

        // Verify second page structure
        JasperReportUtils.BatchExportParam secondPage = result.get(1);
        assertNotNull(secondPage.getTemplateFile());
        assertNotNull(secondPage.getDataSource());
        assertNotNull(secondPage.getInputParams());
        assertEquals(AppConstants.DOCUMENT_JASPER_SECOND_PAGE_PATH, secondPage.getTemplateFile());
    }
}
