package com.bidv.ibank.dvc.model.response;

import org.junit.jupiter.api.Test;
import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

class BatchListResTest {

    @Test
    void builder_ShouldCreateBatchListResWithAllFields() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        String status = "PROCESSING";
        String batchName = "Test Batch";
        String batchNo = "BDR20240321";

        // Act
        BatchListRes res = BatchListRes.builder()
                .createdDate(now)
                .status(status)
                .batchName(batchName)
                .batchNo(batchNo)
                .build();

        // Assert
        assertThat(res)
                .satisfies(r -> {
                    assertThat(r.getCreatedDate()).isEqualTo(now);
                    assertThat(r.getStatus()).isEqualTo(status);
                    assertThat(r.getBatchName()).isEqualTo(batchName);
                    assertThat(r.getBatchNo()).isEqualTo(batchNo);
                });
    }

    @Test
    void builder_WithNullValues_ShouldCreateBatchListRes() {
        // Act
        BatchListRes res = BatchListRes.builder().build();

        // Assert
        assertThat(res)
                .satisfies(r -> {
                    assertThat(r.getCreatedDate()).isNull();
                    assertThat(r.getStatus()).isNull();
                    assertThat(r.getBatchName()).isNull();
                    assertThat(r.getBatchNo()).isNull();
                });
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        // Arrange
        BatchListRes res = new BatchListRes();
        LocalDateTime now = LocalDateTime.now();
        String status = "PROCESSING";
        String batchName = "Test Batch";
        String batchNo = "BDR20240321";

        // Act
        res.setCreatedDate(now);
        res.setStatus(status);
        res.setBatchName(batchName);
        res.setBatchNo(batchNo);

        // Assert
        assertThat(res)
                .satisfies(r -> {
                    assertThat(r.getCreatedDate()).isEqualTo(now);
                    assertThat(r.getStatus()).isEqualTo(status);
                    assertThat(r.getBatchName()).isEqualTo(batchName);
                    assertThat(r.getBatchNo()).isEqualTo(batchNo);
                });
    }

    @Test
    void equals_WithSameValues_ShouldReturnTrue() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        BatchListRes res1 = BatchListRes.builder()
                .createdDate(now)
                .status("PROCESSING")
                .batchName("Test")
                .batchNo("BDR123")
                .build();

        BatchListRes res2 = BatchListRes.builder()
                .createdDate(now)
                .status("PROCESSING")
                .batchName("Test")
                .batchNo("BDR123")
                .build();

        // Act & Assert
        assertThat(res1)
                .isEqualTo(res2)
                .hasSameHashCodeAs(res2);
    }

    @Test
    void equals_WithDifferentValues_ShouldReturnFalse() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        BatchListRes res1 = BatchListRes.builder()
                .createdDate(now)
                .status("PROCESSING")
                .batchName("Test1")
                .batchNo("BDR123")
                .build();

        BatchListRes res2 = BatchListRes.builder()
                .createdDate(now)
                .status("PROCESSING")
                .batchName("Test2")
                .batchNo("BDR123")
                .build();

        // Act & Assert
        assertThat(res1)
                .isNotEqualTo(res2)
                .doesNotHaveSameHashCodeAs(res2);
    }

    @Test
    void toString_ShouldContainAllFields() {
        // Arrange
        LocalDateTime now = LocalDateTime.now();
        BatchListRes res = BatchListRes.builder()
                .createdDate(now)
                .status("PROCESSING")
                .batchName("Test")
                .batchNo("BDR123")
                .build();

        // Act
        String toString = res.toString();

        // Assert
        assertThat(toString)
                .contains("createdDate=" + now)
                .contains("status=PROCESSING")
                .contains("batchName=Test")
                .contains("batchNo=BDR123");
    }

    @Test
    void noArgsConstructor_ShouldCreateEmptyBatchListRes() {
        // Act
        BatchListRes res = new BatchListRes();

        // Assert
        assertThat(res)
                .satisfies(r -> {
                    assertThat(r.getCreatedDate()).isNull();
                    assertThat(r.getStatus()).isNull();
                    assertThat(r.getBatchName()).isNull();
                    assertThat(r.getBatchNo()).isNull();
                });
    }
}