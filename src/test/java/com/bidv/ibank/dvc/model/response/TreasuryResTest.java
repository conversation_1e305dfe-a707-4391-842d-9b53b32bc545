package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class TreasuryResTest {

    private MockedStatic<Translator> translatorMock;
    private TreasuryRes treasuryRes;
    private static final String TREASURY_CODE = "TR001";
    private static final String TREASURY_NAME = "Treasury 1";
    private static final String TRANSLATED_NAME = "Translated Treasury 1";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        treasuryRes = TreasuryRes.builder()
                .treasuryCode(TREASURY_CODE)
                .treasuryName(TREASURY_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TREASURY_NAME);

        assertThat(treasuryRes).isNotNull();
        assertThat(treasuryRes.getTreasuryCode()).isEqualTo(TREASURY_CODE);
        assertThat(treasuryRes.getTreasuryName()).isEqualTo(TREASURY_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        TreasuryRes emptyTreasury = TreasuryRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyTreasury).isNotNull();
        assertThat(emptyTreasury.getTreasuryCode()).isNull();
        assertThat(emptyTreasury.getTreasuryName()).isNull();
    }

    @Test
    void getTreasuryName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = treasuryRes.getTreasuryName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getTreasuryName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TREASURY_NAME);

        String result = treasuryRes.getTreasuryName();

        assertThat(result).isEqualTo(TREASURY_NAME);
    }

    @Test
    void getTreasuryName_WhenTreasuryCodeIsNull_ShouldHandleNullGracefully() {
        TreasuryRes nullCodeTreasury = TreasuryRes.builder()
                .treasuryName(TREASURY_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeTreasury.getTreasuryName();

        assertThat(result).isNull();
    }

    @Test
    void getTreasuryName_WhenTreasuryNameIsNull_ShouldHandleNullGracefully() {
        TreasuryRes nullNameTreasury = TreasuryRes.builder()
                .treasuryCode(TREASURY_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameTreasury.getTreasuryName();

        assertThat(result).isNull();
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        TreasuryRes treasury1 = TreasuryRes.builder()
                .treasuryCode(TREASURY_CODE)
                .treasuryName(TREASURY_NAME)
                .build();

        TreasuryRes treasury2 = TreasuryRes.builder()
                .treasuryCode(TREASURY_CODE)
                .treasuryName(TREASURY_NAME)
                .build();

        assertThat(treasury1).isEqualTo(treasury2);
        assertThat(treasury1.hashCode()).isEqualTo(treasury2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        TreasuryRes treasury1 = TreasuryRes.builder()
                .treasuryCode(TREASURY_CODE)
                .treasuryName(TREASURY_NAME)
                .build();

        TreasuryRes treasury2 = TreasuryRes.builder()
                .treasuryCode("TR002")
                .treasuryName("Treasury 2")
                .build();

        assertThat(treasury1).isNotEqualTo(treasury2);
        assertThat(treasury1.hashCode()).isNotEqualTo(treasury2.hashCode());
    }
}