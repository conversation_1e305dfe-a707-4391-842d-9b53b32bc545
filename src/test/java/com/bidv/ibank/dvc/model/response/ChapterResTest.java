package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class ChapterResTest {

    private MockedStatic<Translator> translatorMock;
    private ChapterRes chapterRes;
    private static final String CHAPTER_CODE = "CH001";
    private static final String CHAPTER_NAME = "Chapter 1";
    private static final String TRANSLATED_NAME = "Translated Chapter 1";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        chapterRes = ChapterRes.builder()
                .chapterCode(CHAPTER_CODE)
                .chapterName(CHAPTER_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(CHAPTER_NAME);

        assertThat(chapterRes).isNotNull();
        assertThat(chapterRes.getChapterCode()).isEqualTo(CHAPTER_CODE);
        assertThat(chapterRes.getChapterName()).isEqualTo(CHAPTER_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        ChapterRes emptyChapter = ChapterRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyChapter).isNotNull();
        assertThat(emptyChapter.getChapterCode()).isNull();
        assertThat(emptyChapter.getChapterName()).isNull();
    }

    @Test
    void getChapterName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = chapterRes.getChapterName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getChapterName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(CHAPTER_NAME);

        String result = chapterRes.getChapterName();

        assertThat(result).isEqualTo(CHAPTER_NAME);
    }

    @Test
    void getChapterName_WhenChapterCodeIsNull_ShouldHandleNullGracefully() {
        ChapterRes nullCodeChapter = ChapterRes.builder()
                .chapterName(CHAPTER_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeChapter.getChapterName();

        assertThat(result).isNull();
    }

    @Test
    void getChapterName_WhenChapterNameIsNull_ShouldHandleNullGracefully() {
        ChapterRes nullNameChapter = ChapterRes.builder()
                .chapterCode(CHAPTER_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameChapter.getChapterName();

        assertThat(result).isNull();
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        ChapterRes chapter1 = ChapterRes.builder()
                .chapterCode(CHAPTER_CODE)
                .chapterName(CHAPTER_NAME)
                .build();

        ChapterRes chapter2 = ChapterRes.builder()
                .chapterCode(CHAPTER_CODE)
                .chapterName(CHAPTER_NAME)
                .build();

        assertThat(chapter1).isEqualTo(chapter2);
        assertThat(chapter1.hashCode()).isEqualTo(chapter2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        ChapterRes chapter1 = ChapterRes.builder()
                .chapterCode(CHAPTER_CODE)
                .chapterName(CHAPTER_NAME)
                .build();

        ChapterRes chapter2 = ChapterRes.builder()
                .chapterCode("CH002")
                .chapterName("Chapter 2")
                .build();

        assertThat(chapter1).isNotEqualTo(chapter2);
        assertThat(chapter1.hashCode()).isNotEqualTo(chapter2.hashCode());
    }
}