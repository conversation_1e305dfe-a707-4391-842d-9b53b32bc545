package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;

class TxnPrintDocumentReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (var factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        assertThat(req).isNotNull();
    }

    @Test
    void testSettersAndGetters() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();

        // Test the txnIds property
        List<String> txnIds = Arrays.asList("TXN123456789", "TXN987654321");
        req.setTxnIds(txnIds);

        assertThat(req.getTxnIds()).isEqualTo(txnIds);
        assertThat(req.getTxnIds()).hasSize(2);
        assertThat(req.getTxnIds()).contains("TXN123456789", "TXN987654321");
    }

    @Test
    void testEqualsAndHashCode() {
        TxnPrintDocumentReq req1 = new TxnPrintDocumentReq();
        req1.setTxnIds(Arrays.asList("TXN123456789"));

        TxnPrintDocumentReq req2 = new TxnPrintDocumentReq();
        req2.setTxnIds(Arrays.asList("TXN123456789"));

        TxnPrintDocumentReq req3 = new TxnPrintDocumentReq();
        req3.setTxnIds(Arrays.asList("TXN987654321"));

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testToString() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        req.setTxnIds(Arrays.asList("TXN123456789"));

        String toString = req.toString();
        assertThat(toString).contains("TxnPrintDocumentReq");
    }

    @Test
    void testValidationWithEmptyList() {
        // Test validation with empty list
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        req.setTxnIds(Arrays.asList());

        Set<ConstraintViolation<TxnPrintDocumentReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasTxnIdsViolation = violations.stream()
                .anyMatch(v -> "txnIds".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdsViolation).isTrue();
    }

    @Test
    void testValidRequestPassesValidation() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339"));

        Set<ConstraintViolation<TxnPrintDocumentReq>> violations = validator.validate(req);

        // Filter out violations not related to TxnPrintDocumentReq specific fields
        boolean hasTxnIdsViolation = violations.stream()
                .anyMatch(v -> "txnIds".equals(v.getPropertyPath().toString()) &&
                         v.getMessage().contains("must not be empty"));
        assertThat(hasTxnIdsViolation).isFalse();
    }

    @Test
    void testPrintDocumentSpecificBehavior() {
        // Test that TxnPrintDocumentReq behaves correctly as a print document request
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339"));

        assertThat(req.getTxnIds()).hasSize(1);
        assertThat(req.getTxnIds().get(0)).isEqualTo("DVC01704202411252339");
    }

    @Test
    void testPrintDocumentWithValidTxnIds() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        List<String> txnIds = Arrays.asList("DVC01704202411252339", "DVC01704202411252340");
        req.setTxnIds(txnIds);

        // Test that valid transaction IDs are properly set
        assertThat(req.getTxnIds()).hasSize(2);
        assertThat(req.getTxnIds()).containsExactly("DVC01704202411252339", "DVC01704202411252340");
    }

    @Test
    void testPrintDocumentWorkflow() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339"));

        // Verify the request is suitable for print document workflow
        assertThat(req.getTxnIds()).isNotNull();
        assertThat(req.getTxnIds()).isNotEmpty();
        assertThat(req.getTxnIds()).hasSize(1);
    }

    @Test
    void testNullTxnIdsHandling() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        req.setTxnIds(null);

        Set<ConstraintViolation<TxnPrintDocumentReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasTxnIdsViolation = violations.stream()
                .anyMatch(v -> "txnIds".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdsViolation).isTrue();
    }

    @Test
    void testSingleTxnIdPrint() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339"));

        assertThat(req.getTxnIds()).hasSize(1);
        assertThat(req.getTxnIds().get(0)).isEqualTo("DVC01704202411252339");
    }

    @Test
    void testMultipleTxnIdsPrint() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        List<String> txnIds = Arrays.asList("DVC01704202411252339", "DVC01704202411252340", "DVC01704202411252341");
        req.setTxnIds(txnIds);

        assertThat(req.getTxnIds()).hasSize(3);
        assertThat(req.getTxnIds()).containsExactlyElementsOf(txnIds);
    }
}
