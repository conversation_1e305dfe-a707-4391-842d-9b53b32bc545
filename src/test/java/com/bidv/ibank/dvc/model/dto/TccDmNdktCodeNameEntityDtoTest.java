package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmNdktCodeNameEntityDtoTest {

    private TccDmNdktCodeNameEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmNdktCodeNameEntityDto();
        dto.setMaNdkt("ND001");
        dto.setTenNdkt("Test NDKT");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("ND001", dto.getMaNdkt());
        assertEquals("Test NDKT", dto.getTenNdkt());
    }

    @Test
    void testGetMaTenNdkt() {
        assertEquals("ND001 - Test NDKT", dto.getMaTenNdkt());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmNdktCodeNameEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmNdktCodeNameEntityDto dto1 = new TccDmNdktCodeNameEntityDto("ND001", "Test NDKT");
        TccDmNdktCodeNameEntityDto dto2 = new TccDmNdktCodeNameEntityDto("ND001", "Test NDKT");
        TccDmNdktCodeNameEntityDto dto3 = new TccDmNdktCodeNameEntityDto("ND002", "Other NDKT");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmNdktCodeNameEntityDto dto = new TccDmNdktCodeNameEntityDto("ND001", "Test NDKT");
        assertEquals("ND001", dto.getMaNdkt());
        assertEquals("Test NDKT", dto.getTenNdkt());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmNdktCodeNameEntityDto dto = new TccDmNdktCodeNameEntityDto();
        assertNull(dto.getMaNdkt());
        assertNull(dto.getTenNdkt());
    }
}