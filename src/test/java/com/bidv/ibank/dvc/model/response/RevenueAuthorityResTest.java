package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class RevenueAuthorityResTest {

    private MockedStatic<Translator> translatorMock;
    private RevenueAuthorityRes revenueAuthorityRes;
    private static final String REV_AUTH_CODE = "AUTH001";
    private static final String REV_AUTH_NAME = "Revenue Authority 1";
    private static final String TRANSLATED_NAME = "Translated Revenue Authority 1";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        revenueAuthorityRes = RevenueAuthorityRes.builder()
                .revAuthCode(REV_AUTH_CODE)
                .revAuthName(REV_AUTH_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(REV_AUTH_NAME);

        assertThat(revenueAuthorityRes).isNotNull();
        assertThat(revenueAuthorityRes.getRevAuthCode()).isEqualTo(REV_AUTH_CODE);
        assertThat(revenueAuthorityRes.getRevAuthName()).isEqualTo(REV_AUTH_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        RevenueAuthorityRes emptyAuthority = RevenueAuthorityRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyAuthority).isNotNull();
        assertThat(emptyAuthority.getRevAuthCode()).isNull();
        assertThat(emptyAuthority.getRevAuthName()).isNull();
    }

    @Test
    void getRevAuthName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = revenueAuthorityRes.getRevAuthName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getRevAuthName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(REV_AUTH_NAME);

        String result = revenueAuthorityRes.getRevAuthName();

        assertThat(result).isEqualTo(REV_AUTH_NAME);
    }

    @Test
    void getRevAuthName_WhenRevAuthCodeIsNull_ShouldHandleNullGracefully() {
        RevenueAuthorityRes nullCodeAuthority = RevenueAuthorityRes.builder()
                .revAuthName(REV_AUTH_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeAuthority.getRevAuthName();

        assertThat(result).isNull();
    }

    @Test
    void getRevAuthName_WhenRevAuthNameIsNull_ShouldHandleNullGracefully() {
        RevenueAuthorityRes nullNameAuthority = RevenueAuthorityRes.builder()
                .revAuthCode(REV_AUTH_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameAuthority.getRevAuthName();

        assertThat(result).isNull();
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        RevenueAuthorityRes authority1 = RevenueAuthorityRes.builder()
                .revAuthCode(REV_AUTH_CODE)
                .revAuthName(REV_AUTH_NAME)
                .build();

        RevenueAuthorityRes authority2 = RevenueAuthorityRes.builder()
                .revAuthCode(REV_AUTH_CODE)
                .revAuthName(REV_AUTH_NAME)
                .build();

        assertThat(authority1).isEqualTo(authority2);
        assertThat(authority1.hashCode()).isEqualTo(authority2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        RevenueAuthorityRes authority1 = RevenueAuthorityRes.builder()
                .revAuthCode(REV_AUTH_CODE)
                .revAuthName(REV_AUTH_NAME)
                .build();

        RevenueAuthorityRes authority2 = RevenueAuthorityRes.builder()
                .revAuthCode("AUTH002")
                .revAuthName("Revenue Authority 2")
                .build();

        assertThat(authority1).isNotEqualTo(authority2);
        assertThat(authority1.hashCode()).isNotEqualTo(authority2.hashCode());
    }
}