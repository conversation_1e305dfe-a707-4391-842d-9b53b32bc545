package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ExportFileResTest {

    private ExportFileRes exportFileRes;

    @BeforeEach
    void setUp() {
        exportFileRes = new ExportFileRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        ExportFileRes res = new ExportFileRes();

        // Then
        assertNotNull(res);
        assertNull(res.getUrl());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String url = "https://example.com/export/file.xlsx";

        // When
        ExportFileRes res = new ExportFileRes(url);

        // Then
        assertEquals(url, res.getUrl());
    }

    @Test
    void testBuilderPattern() {
        // Given
        String url = "https://api.example.com/download/report.pdf";

        // When
        ExportFileRes res = ExportFileRes.builder()
                .url(url)
                .build();

        // Then
        assertEquals(url, res.getUrl());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String url = "https://storage.example.com/files/batch_export.csv";

        // When
        exportFileRes.setUrl(url);

        // Then
        assertEquals(url, exportFileRes.getUrl());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String url = "https://example.com/file.xlsx";

        ExportFileRes res1 = ExportFileRes.builder()
                .url(url)
                .build();

        ExportFileRes res2 = ExportFileRes.builder()
                .url(url)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String url = "https://example.com/test.xlsx";
        exportFileRes.setUrl(url);

        // When
        String result = exportFileRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
