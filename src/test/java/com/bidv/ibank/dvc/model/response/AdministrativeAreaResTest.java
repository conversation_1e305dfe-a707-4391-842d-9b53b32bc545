package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class AdministrativeAreaResTest {

    private MockedStatic<Translator> translatorMock;
    private AdministrativeAreaRes administrativeAreaRes;
    private static final String ADM_AREA_CODE = "ADM001";
    private static final String ADM_AREA_NAME = "Administrative Area 1";
    private static final String TRANSLATED_NAME = "Translated Administrative Area 1";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        administrativeAreaRes = AdministrativeAreaRes.builder()
                .admAreaCode(ADM_AREA_CODE)
                .admAreaName(ADM_AREA_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(ADM_AREA_NAME);

        assertThat(administrativeAreaRes).isNotNull();
        assertThat(administrativeAreaRes.getAdmAreaCode()).isEqualTo(ADM_AREA_CODE);
        assertThat(administrativeAreaRes.getAdmAreaName()).isEqualTo(ADM_AREA_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        AdministrativeAreaRes emptyArea = AdministrativeAreaRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyArea).isNotNull();
        assertThat(emptyArea.getAdmAreaCode()).isNull();
        assertThat(emptyArea.getAdmAreaName()).isNull();
    }

    @Test
    void getAdmAreaName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = administrativeAreaRes.getAdmAreaName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getAdmAreaName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(ADM_AREA_NAME);

        String result = administrativeAreaRes.getAdmAreaName();

        assertThat(result).isEqualTo(ADM_AREA_NAME);
    }

    @Test
    void getAdmAreaName_WhenAdmAreaCodeIsNull_ShouldHandleNullGracefully() {
        AdministrativeAreaRes nullCodeArea = AdministrativeAreaRes.builder()
                .admAreaName(ADM_AREA_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeArea.getAdmAreaName();

        assertThat(result).isNull();
    }

    @Test
    void getAdmAreaName_WhenAdmAreaNameIsNull_ShouldHandleNullGracefully() {
        AdministrativeAreaRes nullNameArea = AdministrativeAreaRes.builder()
                .admAreaCode(ADM_AREA_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameArea.getAdmAreaName();

        assertThat(result).isNull();
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        AdministrativeAreaRes area1 = AdministrativeAreaRes.builder()
                .admAreaCode(ADM_AREA_CODE)
                .admAreaName(ADM_AREA_NAME)
                .build();

        AdministrativeAreaRes area2 = AdministrativeAreaRes.builder()
                .admAreaCode(ADM_AREA_CODE)
                .admAreaName(ADM_AREA_NAME)
                .build();

        assertThat(area1).isEqualTo(area2);
        assertThat(area1.hashCode()).isEqualTo(area2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        AdministrativeAreaRes area1 = AdministrativeAreaRes.builder()
                .admAreaCode(ADM_AREA_CODE)
                .admAreaName(ADM_AREA_NAME)
                .build();

        AdministrativeAreaRes area2 = AdministrativeAreaRes.builder()
                .admAreaCode("ADM002")
                .admAreaName("Administrative Area 2")
                .build();

        assertThat(area1).isNotEqualTo(area2);
        assertThat(area1.hashCode()).isNotEqualTo(area2.hashCode());
    }
}