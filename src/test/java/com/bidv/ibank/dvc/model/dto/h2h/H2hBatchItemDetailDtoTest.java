package com.bidv.ibank.dvc.model.dto.h2h;

import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.framework.util.Translator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class H2hBatchItemDetailDtoTest {

    @BeforeEach
    void setUp() {
        // No setup needed for now
    }

    @Test
    void testGetStatusName() {
        H2hBatchItemDetailDto dto = new H2hBatchItemDetailDto();
        dto.setStatus(TransactionStatusEnum.SUCCESS.name());
        try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class)) {
            translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Thành công");
            String result = dto.getStatusName();
            assertEquals("Thành công", result);
        }
    }

    @Test
    void testGetAccountingStatus() {
        H2hBatchItemDetailDto dto = new H2hBatchItemDetailDto();
        dto.setStatus(TransactionStatusEnum.SUCCESS.name());
        assertEquals(TransactionStatusEnum.SUCCESS.name(), dto.getAccountingStatus());
        dto.setStatus("SOME_OTHER_STATUS");
        assertNull(dto.getAccountingStatus());
    }

    @Test
    void testGetCustomsConnStatus() {
        H2hBatchItemDetailDto dto = new H2hBatchItemDetailDto();
        dto.setState(TransactionStateEnum.SUCCESS.name());
        assertEquals(TransactionStateEnum.SUCCESS.name(), dto.getCustomsConnStatus());
        dto.setState(TransactionStateEnum.FAIL_TRANS_TCC.name());
        assertEquals(TransactionStateEnum.FAILED.name(), dto.getCustomsConnStatus());
        dto.setState("SOME_OTHER_STATE");
        assertNull(dto.getCustomsConnStatus());
    }

    @Test
    void testGetAccountingStatusName() {
        H2hBatchItemDetailDto dto = new H2hBatchItemDetailDto();
        dto.setStatus(TransactionStatusEnum.SUCCESS.name());
        try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class)) {
            translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Hạch toán thành công");
            String result = dto.getAccountingStatusName();
            assertEquals("Hạch toán thành công", result);
        }
        dto.setStatus("SOME_OTHER_STATUS");
        assertNull(dto.getAccountingStatusName());
    }

    @Test
    void testGetCustomsConnStatusName() {
        H2hBatchItemDetailDto dto = new H2hBatchItemDetailDto();
        dto.setState(TransactionStateEnum.SUCCESS.name());
        try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class)) {
            translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Hải quan thành công");
            String result = dto.getCustomsConnStatusName();
            assertEquals("Hải quan thành công", result);
        }
        dto.setState("SOME_OTHER_STATE");
        assertNull(dto.getCustomsConnStatusName());
    }
}