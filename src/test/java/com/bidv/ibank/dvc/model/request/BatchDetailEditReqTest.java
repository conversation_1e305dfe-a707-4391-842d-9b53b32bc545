package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class BatchDetailEditReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testBuilder() {
        BatchDetailEditReq req = BatchDetailEditReq.builder()
                .batchItemId("a614d77b-9ee6-481f-b321-838ed39ddba8")
                .build();

        assertThat(req.getBatchItemId()).isEqualTo("a614d77b-9ee6-481f-b321-838ed39ddba8");
        assertThat(req).isInstanceOf(TxnSaveReq.class);
    }

    @Test
    void testDefaultConstructor() {
        BatchDetailEditReq req = new BatchDetailEditReq();
        assertThat(req).isNotNull();
        assertThat(req.getBatchItemId()).isNull();
    }

    @Test
    void whenBatchItemIdIsNull_thenValidationFails() {
        BatchDetailEditReq req = BatchDetailEditReq.builder()
                .batchItemId(null)
                .build();

        Set<ConstraintViolation<BatchDetailEditReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasBatchItemIdViolation = violations.stream()
                .anyMatch(v -> "batchItemId".equals(v.getPropertyPath().toString()));
        assertThat(hasBatchItemIdViolation).isTrue();
    }

    @Test
    void whenBatchItemIdIsBlank_thenValidationFails() {
        BatchDetailEditReq req = BatchDetailEditReq.builder()
                .batchItemId("")
                .build();

        Set<ConstraintViolation<BatchDetailEditReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasBatchItemIdViolation = violations.stream()
                .anyMatch(v -> "batchItemId".equals(v.getPropertyPath().toString()));
        assertThat(hasBatchItemIdViolation).isTrue();
    }

    @Test
    void whenBatchItemIdExceedsMaxLength_thenValidationFails() {
        String longBatchItemId = "a".repeat(100); // Exceeds max UUID length
        BatchDetailEditReq req = BatchDetailEditReq.builder()
                .batchItemId(longBatchItemId)
                .build();

        Set<ConstraintViolation<BatchDetailEditReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasBatchItemIdViolation = violations.stream()
                .anyMatch(v -> "batchItemId".equals(v.getPropertyPath().toString()));
        assertThat(hasBatchItemIdViolation).isTrue();
    }

    @Test
    void whenAllFieldsAreValid_thenValidationPasses() {
        BatchDetailEditReq req = BatchDetailEditReq.builder()
                .batchItemId("a614d77b-9ee6-481f-b321-838ed39ddba8")
                .build();

        Set<ConstraintViolation<BatchDetailEditReq>> violations = validator.validate(req);

        // Filter out violations from parent class that might require additional setup
        boolean hasBatchItemIdViolation = violations.stream()
                .anyMatch(v -> "batchItemId".equals(v.getPropertyPath().toString()));
        assertThat(hasBatchItemIdViolation).isFalse();
    }

    @Test
    void testEqualsAndHashCode() {
        BatchDetailEditReq req1 = BatchDetailEditReq.builder()
                .batchItemId("a614d77b-9ee6-481f-b321-838ed39ddba8")
                .build();

        BatchDetailEditReq req2 = BatchDetailEditReq.builder()
                .batchItemId("a614d77b-9ee6-481f-b321-838ed39ddba8")
                .build();

        BatchDetailEditReq req3 = BatchDetailEditReq.builder()
                .batchItemId("different-id-12345")
                .build();

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testSettersAndGetters() {
        BatchDetailEditReq req = new BatchDetailEditReq();
        String batchItemId = "a614d77b-9ee6-481f-b321-838ed39ddba8";

        req.setBatchItemId(batchItemId);
        assertThat(req.getBatchItemId()).isEqualTo(batchItemId);
    }

    @Test
    void testToString() {
        BatchDetailEditReq req = BatchDetailEditReq.builder()
                .batchItemId("a614d77b-9ee6-481f-b321-838ed39ddba8")
                .build();

        String toString = req.toString();
        assertThat(toString).contains("BatchDetailEditReq");
        assertThat(toString).contains("batchItemId");
    }

    @Test
    void testInheritanceFromTxnSaveReq() {
        BatchDetailEditReq req = new BatchDetailEditReq();
        assertThat(req).isInstanceOf(TxnSaveReq.class);
    }
}
