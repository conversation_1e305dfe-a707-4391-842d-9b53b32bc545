package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class ExportImportTypeTest {

    private ExportImportType exportImportType;

    @BeforeEach
    void setUp() {
        exportImportType = new ExportImportType();
    }

    @Test
    void testDefaultConstructor() {
        // When
        ExportImportType type = new ExportImportType();

        // Then
        assertNotNull(type);
        assertNull(type.getEiTypeCode());
    }

    @Test
    void testAllArgsConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String eiTypeCode = "IMP";
            String eiTypeName = "Import";

            mockedTranslator.when(() -> Translator.toLocale(eiTypeCode, eiTypeName))
                    .thenReturn(eiTypeName);

            // When
            ExportImportType type = new ExportImportType(eiTypeCode, eiTypeName);

            // Then
            assertEquals(eiTypeCode, type.getEiTypeCode());
            assertEquals(eiTypeName, type.getEiTypeName());
        }
    }

    @Test
    void testBuilderPattern() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String eiTypeCode = "EXP";
            String eiTypeName = "Export";

            mockedTranslator.when(() -> Translator.toLocale(eiTypeCode, eiTypeName))
                    .thenReturn(eiTypeName);

            // When
            ExportImportType type = ExportImportType.builder()
                    .eiTypeCode(eiTypeCode)
                    .eiTypeName(eiTypeName)
                    .build();

            // Then
            assertEquals(eiTypeCode, type.getEiTypeCode());
            assertEquals(eiTypeName, type.getEiTypeName());
        }
    }

    @Test
    void testSettersAndGetters() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String eiTypeCode = "BOTH";
            String eiTypeName = "Import Export";

            mockedTranslator.when(() -> Translator.toLocale(eiTypeCode, eiTypeName))
                    .thenReturn(eiTypeName);

            // When
            exportImportType.setEiTypeCode(eiTypeCode);
            exportImportType.setEiTypeName(eiTypeName);

            // Then
            assertEquals(eiTypeCode, exportImportType.getEiTypeCode());
            assertEquals(eiTypeName, exportImportType.getEiTypeName());
        }
    }

    @Test
    void testGetEiTypeNameWithTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String eiTypeCode = "IMP";
            String eiTypeName = "Import";
            String translatedName = "Nhập khẩu";

            exportImportType.setEiTypeCode(eiTypeCode);
            exportImportType.setEiTypeName(eiTypeName);

            mockedTranslator.when(() -> Translator.toLocale(eiTypeCode, eiTypeName))
                    .thenReturn(translatedName);

            // When
            String result = exportImportType.getEiTypeName();

            // Then
            assertEquals(translatedName, result);
            mockedTranslator.verify(() -> Translator.toLocale(eiTypeCode, eiTypeName));
        }
    }

    @Test
    void testGetEiTypeNameWithNullValues() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            exportImportType.setEiTypeCode(null);
            exportImportType.setEiTypeName(null);

            // Cast to String to avoid ambiguity between toLocale(String, String) and toLocale(String, Object[])
            mockedTranslator.when(() -> Translator.toLocale((String) null, (String) null))
                    .thenReturn("Default");

            // When
            String result = exportImportType.getEiTypeName();

            // Then
            assertEquals("Default", result);
        }
    }

    @Test
    void testEqualsAndHashCode() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String eiTypeCode = "EXP";
            String eiTypeName = "Export";

            mockedTranslator.when(() -> Translator.toLocale(eiTypeCode, eiTypeName))
                    .thenReturn(eiTypeName);

            ExportImportType type1 = ExportImportType.builder()
                    .eiTypeCode(eiTypeCode)
                    .eiTypeName(eiTypeName)
                    .build();

            ExportImportType type2 = ExportImportType.builder()
                    .eiTypeCode(eiTypeCode)
                    .eiTypeName(eiTypeName)
                    .build();

            // Then
            assertEquals(type1, type2);
            assertEquals(type1.hashCode(), type2.hashCode());
        }
    }

    @Test
    void testToString() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String eiTypeCode = "IMP";
            String eiTypeName = "Import";

            mockedTranslator.when(() -> Translator.toLocale(eiTypeCode, eiTypeName))
                    .thenReturn(eiTypeName);

            exportImportType.setEiTypeCode(eiTypeCode);
            exportImportType.setEiTypeName(eiTypeName);

            // When
            String result = exportImportType.toString();

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    @Test
    void testGetEiTypeCodeOnly() {
        // Given
        String eiTypeCode = "TRANSIT";

        // When
        exportImportType.setEiTypeCode(eiTypeCode);

        // Then
        assertEquals(eiTypeCode, exportImportType.getEiTypeCode());
        // Note: Not calling getEiTypeName() here to avoid Translator dependency
    }
}
