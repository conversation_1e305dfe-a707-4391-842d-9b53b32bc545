package com.bidv.ibank.dvc.model.request;

import org.junit.jupiter.api.Test;
import java.time.LocalDate;
import java.util.List;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

import static org.assertj.core.api.Assertions.assertThat;

class BatchListReqTest {
    private final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
    private final Validator validator = factory.getValidator();

    @Test
    void builder_ShouldCreateBatchListReqWithAllFields() {
        // Arrange
        LocalDate now = LocalDate.now();
        String search = "test search";
        String batchName = "Test Batch";
        String batchNo = "BDR20240321";
        List<String> statuses = List.of("PROCESSING", "PROCESSED");

        // Act
        BatchListReq req = BatchListReq.builder()
                .search(search)
                .startDate(now.minusDays(7))
                .endDate(now)
                .batchName(batchName)
                .statuses(statuses)
                .batchNo(batchNo)
                .build();

        // Assert
        assertThat(req)
                .satisfies(r -> {
                    assertThat(r.getSearch()).isEqualTo(search);
                    assertThat(r.getStartDate()).isEqualTo(now.minusDays(7));
                    assertThat(r.getEndDate()).isEqualTo(now);
                    assertThat(r.getBatchName()).isEqualTo(batchName);
                    assertThat(r.getStatuses()).containsExactlyElementsOf(statuses);
                    assertThat(r.getBatchNo()).isEqualTo(batchNo);
                });
    }

    @Test
    void builder_WithNullValues_ShouldCreateBatchListReq() {
        // Act
        BatchListReq req = BatchListReq.builder().build();

        // Assert
        assertThat(req)
                .satisfies(r -> {
                    assertThat(r.getSearch()).isNull();
                    assertThat(r.getStartDate()).isNull();
                    assertThat(r.getEndDate()).isNull();
                    assertThat(r.getBatchName()).isNull();
                    assertThat(r.getStatuses()).isNull();
                    assertThat(r.getBatchNo()).isNull();
                });
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        // Arrange
        BatchListReq req = new BatchListReq();
        LocalDate now = LocalDate.now();
        String search = "test search";
        String batchName = "Test Batch";
        String batchNo = "BDR20240321";
        List<String> statuses = List.of("PROCESSING", "PROCESSED");

        // Act
        req.setSearch(search);
        req.setStartDate(now.minusDays(7));
        req.setEndDate(now);
        req.setBatchName(batchName);
        req.setStatuses(statuses);
        req.setBatchNo(batchNo);

        // Assert
        assertThat(req)
                .satisfies(r -> {
                    assertThat(r.getSearch()).isEqualTo(search);
                    assertThat(r.getStartDate()).isEqualTo(now.minusDays(7));
                    assertThat(r.getEndDate()).isEqualTo(now);
                    assertThat(r.getBatchName()).isEqualTo(batchName);
                    assertThat(r.getStatuses()).containsExactlyElementsOf(statuses);
                    assertThat(r.getBatchNo()).isEqualTo(batchNo);
                });
    }

    @Test
    void toString_ShouldContainAllFields() {
        // Arrange
        LocalDate now = LocalDate.now();
        BatchListReq req = BatchListReq.builder()
                .search("test")
                .startDate(now)
                .endDate(now)
                .batchName("Test")
                .statuses(List.of("PROCESSING"))
                .batchNo("BDR123")
                .build();

        // Act
        String toString = req.toString();

        // Assert
        assertThat(toString)
                .contains("search=test")
                .contains("startDate=" + now)
                .contains("endDate=" + now)
                .contains("batchName=Test")
                .contains("statuses=[PROCESSING]")
                .contains("batchNo=BDR123");
    }

    @Test
    void validate_WithValidBatchNo_ShouldPass() {
        // Arrange
        BatchListReq req = BatchListReq.builder()
                .batchNo("12345678901234567890") // 20 characters
                .build();

        // Act
        var violations = validator.validate(req);

        // Assert
        assertThat(violations).isEmpty();
    }

    @Test
    void validate_WithTooLongBatchNo_ShouldFail() {
        // Arrange
        BatchListReq req = BatchListReq.builder()
                .batchNo("1234567890123456789012345678901234567890123456789020250529172533579")
                .build();

        // Act
        var violations = validator.validate(req);

        // Assert
        assertThat(violations)
                .hasSize(1)
                .first()
                .satisfies(violation -> {
                    assertThat(violation.getPropertyPath().toString()).isEqualTo("batchNo");
                    assertThat(violation.getMessage()).contains("size must be");
                });
    }

    @Test
    void validate_WithNullBatchNo_ShouldPass() {
        // Arrange
        BatchListReq req = BatchListReq.builder()
                .batchNo(null)
                .build();

        // Act
        var violations = validator.validate(req);

        // Assert
        assertThat(violations).isEmpty();
    }

    @Test
    void searchable_ShouldImplementSearchableInterface() {
        // Arrange
        String searchTerm = "test search";
        BatchListReq req = new BatchListReq();

        // Act
        req.setSearch(searchTerm);

        // Assert
        assertThat(req.getSearch()).isEqualTo(searchTerm);
    }

    @Test
    void dateRangeFilter_ShouldImplementDateRangeFilterInterface() {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        BatchListReq req = new BatchListReq();

        // Act
        req.setStartDate(startDate);
        req.setEndDate(endDate);

        // Assert
        assertThat(req.getStartDate()).isEqualTo(startDate);
        assertThat(req.getEndDate()).isEqualTo(endDate);
    }

    @Test
    void statusesFilter_ShouldImplementStatusesFilterInterface() {
        // Arrange
        List<String> statuses = List.of("PROCESSING", "PROCESSED");
        BatchListReq req = new BatchListReq();

        // Act
        req.setStatuses(statuses);

        // Assert
        assertThat(req.getStatuses()).containsExactlyElementsOf(statuses);
    }
}