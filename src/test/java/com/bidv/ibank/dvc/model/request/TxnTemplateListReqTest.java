package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.model.filter.Searchable;
import com.bidv.ibank.framework.domain.request.PagingQueryRequest;

@DisplayName("TxnTemplateListReq Tests")
class TxnTemplateListReqTest {

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {

        @Test
        @DisplayName("Should create instance with no-args constructor")
        void shouldCreateInstanceWithNoArgsConstructor() {
            TemplateListReq req = new TemplateListReq();

            assertThat(req).isNotNull();
            assertThat(req.getSearch()).isNull();
            assertThat(req).isInstanceOf(PagingQueryRequest.class);
            assertThat(req).isInstanceOf(Searchable.class);
        }

        @Test
        @DisplayName("Should create instance with all-args constructor")
        void shouldCreateInstanceWithAllArgsConstructor() {
            TemplateListReq req = new TemplateListReq(
                    "Test Search",
                    "04");

            assertThat(req).isNotNull();
            assertThat(req.getSearch()).isEqualTo("Test Search");
        }
    }

    @Nested
    @DisplayName("Builder Tests")
    class BuilderTests {

        @Test
        @DisplayName("Should create instance with builder")
        void shouldCreateInstanceWithBuilder() {
            TemplateListReq req = TemplateListReq.builder()
                    .search("Test Search")
                    .build();

            assertThat(req).isNotNull();
            assertThat(req.getSearch()).isEqualTo("Test Search");
        }

        @Test
        @DisplayName("Should create instance with builder and null values")
        void shouldCreateInstanceWithBuilderAndNullValues() {
            TemplateListReq req = TemplateListReq.builder().build();

            assertThat(req).isNotNull();
            assertThat(req.getSearch()).isNull();
        }
    }

    @Nested
    @DisplayName("Getter/Setter Tests")
    class GetterSetterTests {

        @Test
        @DisplayName("Should get and set search value")
        void shouldGetAndSetSearchValue() {
            TemplateListReq req = new TemplateListReq();

            req.setSearch("Test Search");
            assertThat(req.getSearch()).isEqualTo("Test Search");

            req.setSearch(null);
            assertThat(req.getSearch()).isNull();
        }
    }

    @Nested
    @DisplayName("ToString Tests")
    class ToStringTests {

        @Test
        @DisplayName("Should include all fields in toString")
        void shouldIncludeAllFieldsInToString() {
            TemplateListReq req = TemplateListReq.builder()
                    .search("Test Search")
                    .build();

            String toString = req.toString();

            assertThat(toString)
                    .contains("TemplateListReq")
                    .contains("search=Test Search");
        }

        @Test
        @DisplayName("Should handle null values in toString")
        void shouldHandleNullValuesInToString() {
            TemplateListReq req = new TemplateListReq();

            String toString = req.toString();

            assertThat(toString)
                    .contains("TemplateListReq")
                    .contains("search=null");
        }
    }
}