package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDate;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;

class TxnExportReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (var factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        TxnExportReq req = new TxnExportReq();
        assertThat(req).isNotNull();
    }

    @Test
    void testSettersAndGetters() {
        TxnExportReq req = new TxnExportReq();
        
        // Test properties
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("**********");
        
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req.getTaxCode()).isEqualTo("**********");
    }

    @Test
    void testEqualsAndHashCode() {
        TxnExportReq req1 = new TxnExportReq();
        req1.setStartDate(LocalDate.of(2024, 1, 1));
        req1.setEndDate(LocalDate.of(2024, 12, 31));
        req1.setTaxCode("**********");

        TxnExportReq req2 = new TxnExportReq();
        req2.setStartDate(LocalDate.of(2024, 1, 1));
        req2.setEndDate(LocalDate.of(2024, 12, 31));
        req2.setTaxCode("**********");

        TxnExportReq req3 = new TxnExportReq();
        req3.setStartDate(LocalDate.of(2024, 6, 1));
        req3.setEndDate(LocalDate.of(2024, 6, 30));
        req3.setTaxCode("0987654321");

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testToString() {
        TxnExportReq req = new TxnExportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("**********");
        
        String toString = req.toString();
        assertThat(toString).contains("TxnExportReq");
    }

    @Test
    void testValidation() {
        TxnExportReq req = new TxnExportReq();
        req.setTaxCode(""); // Test validation

        Set<ConstraintViolation<TxnExportReq>> violations = validator.validate(req);
        assertThat(violations).isNotNull();
    }

    @Test
    void testValidRequestPassesValidation() {
        TxnExportReq req = new TxnExportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("**********");
        
        Set<ConstraintViolation<TxnExportReq>> violations = validator.validate(req);
        
        // Filter out violations not related to the basic required fields
        boolean hasRequiredFieldViolation = violations.stream()
                .anyMatch(v -> ("startDate".equals(v.getPropertyPath().toString()) ||
                              "endDate".equals(v.getPropertyPath().toString()) ||
                              "taxCode".equals(v.getPropertyPath().toString())) &&
                         v.getMessage().contains("must not be blank"));
        assertThat(hasRequiredFieldViolation).isFalse();
    }

    @Test
    void testExportSpecificBehavior() {
        // Test that TxnExportReq behaves correctly as an export request
        TxnExportReq req = new TxnExportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("**********");
        
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
        assertThat(req.getTaxCode()).isEqualTo("**********");
    }

    @Test
    void testExportWithMinimalData() {
        TxnExportReq req = new TxnExportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 1, 1)); // Same day export

        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 1, 1));
    }

    @Test
    void testExportWithOptionalFields() {
        TxnExportReq req = new TxnExportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("**********");
        req.setSearch("test search");

        // Test that the object can be created with optional fields
        assertThat(req).isNotNull();
        assertThat(req.getStartDate()).isNotNull();
        assertThat(req.getEndDate()).isNotNull();
        assertThat(req.getSearch()).isEqualTo("test search");
    }
}
