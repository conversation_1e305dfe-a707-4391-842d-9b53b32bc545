package com.bidv.ibank.dvc.model.mapper.param;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.framework.util.Translator;

class ParamMapperTest {

    private ParamMapper paramMapper;
    private MockedStatic<Translator> translatorMock;

    private TccDmChuongEntity chapterEntity;
    private TccDmNdktEntity ecEntity;
    private TccDmSthueHqaEntity taxEntity;
    private TccDmLoaitienhqaEntity ccEntity;
    private TccDmLhxnkEntity ieEntity;
    private TccDmKhobacEntity treasuryEntity;
    private TccDmKbnnNhtmEntity treasuryNhtmEntity;
    private TccDmTkNsnnEntity revenueAccountEntity;
    private TccDmCqthuEntity revenueAuthorityEntity;
    private TccDmDbhcEntity administrativeAreaEntity;

    @BeforeEach
    void setUp() {
        paramMapper = new ParamMapper();
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class))).thenAnswer(i -> i.getArgument(1));

        chapterEntity = TccDmChuongEntity.builder()
                .maChuong("CH001")
                .ten("Chapter 1")
                .build();

        ecEntity = TccDmNdktEntity.builder()
                .maNdkt("EC001")
                .ten("Economic Content 1")
                .build();

        taxEntity = TccDmSthueHqaEntity.builder()
                .maSthue("TX001")
                .tenSthue("Tax 1")
                .build();

        ccEntity = TccDmLoaitienhqaEntity.builder()
                .maLthq("CC001")
                .tenLthq("Customs Currency 1")
                .build();

        ieEntity = TccDmLhxnkEntity.builder()
                .maLh("IE001")
                .ten("Import Export 1")
                .build();

        treasuryEntity = TccDmKhobacEntity.builder()
                .shkb("TR001")
                .ten("Treasury 1")
                .build();

        treasuryNhtmEntity = TccDmKbnnNhtmEntity.builder()
                .shkb("TR002")
                .tenKb("Treasury 2")
                .build();

        revenueAccountEntity = TccDmTkNsnnEntity.builder()
                .maTk("RA001")
                .ten("Revenue Account 1")
                .build();

        revenueAuthorityEntity = TccDmCqthuEntity.builder()
                .maCqthu("AUTH001")
                .ten("Revenue Authority 1")
                .build();

        administrativeAreaEntity = TccDmDbhcEntity.builder()
                .maDbhc("ADM001")
                .ten("Administrative Area 1")
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void toDto_ShouldThrowUnsupportedOperationException() {
        assertThatThrownBy(() -> paramMapper.toDto(new Object()))
                .isInstanceOf(UnsupportedOperationException.class)
                .hasMessage("Unimplemented method 'toDto'");
    }

    @Test
    void toEntity_ShouldThrowUnsupportedOperationException() {
        assertThatThrownBy(() -> paramMapper.toEntity(new Object()))
                .isInstanceOf(UnsupportedOperationException.class)
                .hasMessage("Unimplemented method 'toEntity'");
    }

    @Test
    void toChapterRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("chapter.code." + chapterEntity.getMaChuong()), eq(chapterEntity.getTen())))
                .thenReturn("Chapter 1");

        ChapterRes result = paramMapper.toChapterRes(chapterEntity);

        assertThat(result).isNotNull();
        assertThat(result.getChapterCode()).isEqualTo(chapterEntity.getMaChuong());
        assertThat(result.getChapterName()).isEqualTo("Chapter 1");
    }

    @Test
    void toEconomicContentRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("ec.code." + ecEntity.getMaNdkt()), eq(ecEntity.getTen())))
                .thenReturn("Economic Content 1");

        EconomicContentRes result = paramMapper.toEconomicContentRes(ecEntity);

        assertThat(result).isNotNull();
        assertThat(result.getEcCode()).isEqualTo(ecEntity.getMaNdkt());
        assertThat(result.getEcName()).isEqualTo("Economic Content 1");
    }

    @Test
    void toTaxRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("tax.code." + taxEntity.getMaSthue()), eq(taxEntity.getTenSthue())))
                .thenReturn("Tax 1");

        TaxTypeRes result = paramMapper.toTaxTypeRes(taxEntity);

        assertThat(result).isNotNull();
        assertThat(result.getTaxTypeCode()).isEqualTo(taxEntity.getMaSthue());
        assertThat(result.getTaxTypeName()).isEqualTo("Tax 1");
    }

    @Test
    void toCustomsCurrencyRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("cc.code." + ccEntity.getMaLthq()), eq(ccEntity.getTenLthq())))
                .thenReturn("Customs Currency 1");

        CustomsCurrencyRes result = paramMapper.toCustomsCurrencyRes(ccEntity);

        assertThat(result).isNotNull();
        assertThat(result.getCcCode()).isEqualTo(ccEntity.getMaLthq());
        assertThat(result.getCcName()).isEqualTo("Customs Currency 1");
    }

    @Test
    void toImportExportRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("ie.code." + ieEntity.getMaLh()), eq(ieEntity.getTen())))
                .thenReturn("Import Export 1");

        ExportImportType result = paramMapper.toExportImportType(ieEntity);

        assertThat(result).isNotNull();
        assertThat(result.getEiTypeCode()).isEqualTo(ieEntity.getMaLh());
        assertThat(result.getEiTypeName()).isEqualTo("Import Export 1");
    }

    @Test
    void toTreasuryRes_WhenValidKhobacInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("treasury.code." + treasuryEntity.getShkb()), eq(treasuryEntity.getTen())))
                .thenReturn("Treasury 1");

        TreasuryRes result = paramMapper.toTreasuryRes(treasuryEntity);

        assertThat(result).isNotNull();
        assertThat(result.getTreasuryCode()).isEqualTo(treasuryEntity.getShkb());
        assertThat(result.getTreasuryName()).isEqualTo("Treasury 1");
    }

    @Test
    void toTreasuryRes_WhenValidKbnnNhtmInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("treasury.code." + treasuryNhtmEntity.getShkb()), eq(treasuryNhtmEntity.getTenKb())))
                .thenReturn("Treasury 2");

        TreasuryRes result = paramMapper.toTreasuryRes(treasuryNhtmEntity);

        assertThat(result).isNotNull();
        assertThat(result.getTreasuryCode()).isEqualTo(treasuryNhtmEntity.getShkb());
        assertThat(result.getTreasuryName()).isEqualTo("Treasury 2");
    }

    @Test
    void toRevenueAccountRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("revenue.account.code." + revenueAccountEntity.getMaTk()), eq(revenueAccountEntity.getTen())))
                .thenReturn("Revenue Account 1");

        RevenueAccountRes result = paramMapper.toRevenueAccountRes(revenueAccountEntity);

        assertThat(result).isNotNull();
        assertThat(result.getRevAccCode()).isEqualTo(revenueAccountEntity.getMaTk());
        assertThat(result.getRevAccName()).isEqualTo("Revenue Account 1");
    }

    @Test
    void toRevenueAuthorityRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("revenue.authority.code." + revenueAuthorityEntity.getMaCqthu()), eq(revenueAuthorityEntity.getTen())))
                .thenReturn("Revenue Authority 1");

        RevenueAuthorityRes result = paramMapper.toRevenueAuthorityRes(revenueAuthorityEntity);

        assertThat(result).isNotNull();
        assertThat(result.getRevAuthCode()).isEqualTo(revenueAuthorityEntity.getMaCqthu());
        assertThat(result.getRevAuthName()).isEqualTo("Revenue Authority 1");
    }

    @Test
    void toAdministrativeAreaRes_WhenValidInput_ShouldMapCorrectly() {
        translatorMock.when(() -> Translator.toLocale(eq("administrative.area.code." + administrativeAreaEntity.getMaDbhc()), eq(administrativeAreaEntity
                .getTen())))
                .thenReturn("Administrative Area 1");

        AdministrativeAreaRes result = paramMapper.toAdministrativeAreaRes(administrativeAreaEntity);

        assertThat(result).isNotNull();
        assertThat(result.getAdmAreaCode()).isEqualTo(administrativeAreaEntity.getMaDbhc());
        assertThat(result.getAdmAreaName()).isEqualTo("Administrative Area 1");
    }
}