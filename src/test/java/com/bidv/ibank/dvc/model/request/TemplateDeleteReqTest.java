package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;

import org.junit.jupiter.api.Test;

class TemplateDeleteReqTest {

    @Test
    void noArgsConstructor_ShouldCreateDefaultObject() {
        TemplateDeleteReq req = new TemplateDeleteReq();
        assertThat(req.getTemplateIds()).isNull();
    }

    @Test
    void allArgsConstructor_ShouldSetFields() {
        List<String> ids = List.of("id1", "id2");
        TemplateDeleteReq req = new TemplateDeleteReq(ids);
        assertThat(req.getTemplateIds()).isEqualTo(ids);
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        TemplateDeleteReq req = new TemplateDeleteReq();
        List<String> ids = List.of("id3", "id4");
        req.setTemplateIds(ids);
        assertThat(req.getTemplateIds()).isEqualTo(ids);
    }

    @Test
    void toString_ShouldContainFieldValues() {
        List<String> ids = List.of("id7", "id8");
        TemplateDeleteReq req = new TemplateDeleteReq(ids);
        assertThat(req.toString()).contains("id7").contains("id8");
    }
}