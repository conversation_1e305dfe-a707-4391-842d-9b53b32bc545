package com.bidv.ibank.dvc.model.dto.contracts;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnBaseListDtoTest {

    private static class TestTxnBaseListDto extends TxnBaseListDto {
        public TestTxnBaseListDto() {
            super();
        }

        public TestTxnBaseListDto(String txnId, String debitAccNo, String taxCode, String declarationNo,
                BigDecimal amount, String ccy, String batchNo, String status, LocalDateTime createdDate,
                String orgId, String treasuryCode, String treasuryName, String admAreaCode,
                String admAreaName, String revAccCode, String revAccName, String revAuthCode,
                String revAuthName) {
            super(txnId, debitAccNo, taxCode, declarationNo, amount, ccy, batchNo, status,
                  createdDate, orgId, treasuryCode, treasuryName, admAreaCode, admAreaName,
                  revAccCode, revAccName, revAuthCode, revAuthName);
        }
    }

    private TestTxnBaseListDto txnBaseListDto;

    @BeforeEach
    void setUp() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Mocked Translation");
            txnBaseListDto = new TestTxnBaseListDto();
        }
    }

    @Test
    void testDefaultConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // When
            TestTxnBaseListDto dto = new TestTxnBaseListDto();

            // Then
            assertNotNull(dto);
            assertNull(dto.getTxnId());
            assertNull(dto.getDebitAccNo());
            assertNull(dto.getTaxCode());
            assertNull(dto.getDeclarationNo());
            assertNull(dto.getAmount());
            assertNull(dto.getCcy());
            assertNull(dto.getBatchNo());
            assertNull(dto.getStatus());
            assertNull(dto.getCreatedDate());
            assertNull(dto.getOrgId());
            assertNull(dto.getTreasuryCode());
            assertNull(dto.getTreasuryName());
            assertNull(dto.getAdmAreaCode());
            assertNull(dto.getAdmAreaName());
            assertNull(dto.getRevAccCode());
            assertNull(dto.getRevAccName());
            assertNull(dto.getRevAuthCode());
            assertNull(dto.getRevAuthName());
        }
    }

    @Test
    void testAllArgsConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback directly

            // Given
            String txnId = "DVC01704202411252339";
            String debitAccNo = "1200046752";
            String taxCode = "3500948285";
            String declarationNo = "30644771632";
            BigDecimal amount = new BigDecimal("100000");
            String ccy = "VND";
            String batchNo = "BDR20250607215402732";
            String status = "INIT";
            LocalDateTime createdDate = LocalDateTime.now();
            String orgId = "********";
            String treasuryCode = "T001";
            String treasuryName = "Treasury Name";
            String admAreaCode = "A001";
            String admAreaName = "Admin Area Name";
            String revAccCode = "R001";
            String revAccName = "Revenue Account Name";
            String revAuthCode = "RA001";
            String revAuthName = "Revenue Authority Name";

            // When
            TestTxnBaseListDto dto = new TestTxnBaseListDto(txnId, debitAccNo, taxCode, declarationNo,
                    amount, ccy, batchNo, status, createdDate, orgId, treasuryCode, treasuryName,
                    admAreaCode, admAreaName, revAccCode, revAccName, revAuthCode, revAuthName);

            // Then
            assertEquals(txnId, dto.getTxnId());
            assertEquals(debitAccNo, dto.getDebitAccNo());
            assertEquals(taxCode, dto.getTaxCode());
            assertEquals(declarationNo, dto.getDeclarationNo());
            assertEquals(amount, dto.getAmount());
            assertEquals(ccy, dto.getCcy());
            assertEquals(batchNo, dto.getBatchNo());
            assertEquals(status, dto.getStatus());
            assertEquals(createdDate, dto.getCreatedDate());
            assertEquals(orgId, dto.getOrgId());
            assertEquals(treasuryCode, dto.getTreasuryCode());
            assertEquals(treasuryName, dto.getTreasuryName());
            assertEquals(admAreaCode, dto.getAdmAreaCode());
            assertEquals(admAreaName, dto.getAdmAreaName());
            assertEquals(revAccCode, dto.getRevAccCode());
            assertEquals(revAccName, dto.getRevAccName());
            assertEquals(revAuthCode, dto.getRevAuthCode());
            assertEquals(revAuthName, dto.getRevAuthName());
        }
    }

    @Test
    void testSettersAndGetters() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Mocked Translation");

            // Given
            String txnId = "DVC01704202411252339";
            String debitAccNo = "1200046752";
            String taxCode = "3500948285";
            String declarationNo = "30644771632";
            BigDecimal amount = new BigDecimal("100000");
            String ccy = "VND";
            String batchNo = "BDR20250607215402732";
            String status = "INIT";
            LocalDateTime createdDate = LocalDateTime.now();
            String orgId = "********";

            // When
            txnBaseListDto.setTxnId(txnId);
            txnBaseListDto.setDebitAccNo(debitAccNo);
            txnBaseListDto.setTaxCode(taxCode);
            txnBaseListDto.setDeclarationNo(declarationNo);
            txnBaseListDto.setAmount(amount);
            txnBaseListDto.setCcy(ccy);
            txnBaseListDto.setBatchNo(batchNo);
            txnBaseListDto.setStatus(status);
            txnBaseListDto.setCreatedDate(createdDate);
            txnBaseListDto.setOrgId(orgId);

            // Then
            assertEquals(txnId, txnBaseListDto.getTxnId());
            assertEquals(debitAccNo, txnBaseListDto.getDebitAccNo());
            assertEquals(taxCode, txnBaseListDto.getTaxCode());
            assertEquals(declarationNo, txnBaseListDto.getDeclarationNo());
            assertEquals(amount, txnBaseListDto.getAmount());
            assertEquals(ccy, txnBaseListDto.getCcy());
            assertEquals(batchNo, txnBaseListDto.getBatchNo());
            assertEquals(status, txnBaseListDto.getStatus());
            assertEquals(createdDate, txnBaseListDto.getCreatedDate());
            assertEquals(orgId, txnBaseListDto.getOrgId());
        }
    }

    @Test
    void testTreasuryFields() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1));

            // Given
            String treasuryCode = "T001";
            String treasuryName = "Treasury Name";
            String admAreaCode = "A001";
            String admAreaName = "Admin Area Name";
            String revAccCode = "R001";
            String revAccName = "Revenue Account Name";
            String revAuthCode = "RA001";
            String revAuthName = "Revenue Authority Name";

            // When
            txnBaseListDto.setTreasuryCode(treasuryCode);
            txnBaseListDto.setTreasuryName(treasuryName);
            txnBaseListDto.setAdmAreaCode(admAreaCode);
            txnBaseListDto.setAdmAreaName(admAreaName);
            txnBaseListDto.setRevAccCode(revAccCode);
            txnBaseListDto.setRevAccName(revAccName);
            txnBaseListDto.setRevAuthCode(revAuthCode);
            txnBaseListDto.setRevAuthName(revAuthName);

            // Then
            assertEquals(treasuryCode, txnBaseListDto.getTreasuryCode());
            assertEquals(treasuryName, txnBaseListDto.getTreasuryName());
            assertEquals(admAreaCode, txnBaseListDto.getAdmAreaCode());
            assertEquals(admAreaName, txnBaseListDto.getAdmAreaName());
            assertEquals(revAccCode, txnBaseListDto.getRevAccCode());
            assertEquals(revAccName, txnBaseListDto.getRevAccName());
            assertEquals(revAuthCode, txnBaseListDto.getRevAuthCode());
            assertEquals(revAuthName, txnBaseListDto.getRevAuthName());
        }
    }

    @Test
    void testGetTreasuryNameWithTranslation() {
        // Given
        String treasuryCode = "T001";
        String treasuryName = "Treasury Name";
        String expectedTranslatedName = "Translated Treasury Name";

        txnBaseListDto.setTreasuryCode(treasuryCode);
        txnBaseListDto.setTreasuryName(treasuryName);

        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(treasuryCode, treasuryName))
                    .thenReturn(expectedTranslatedName);

            // When
            String result = txnBaseListDto.getTreasuryName();

            // Then
            assertEquals(expectedTranslatedName, result);
            mockedTranslator.verify(() -> Translator.toLocale(treasuryCode, treasuryName));
        }
    }

    @Test
    void testGetTreasuryNameWithNullValues() {
        // Given
        txnBaseListDto.setTreasuryCode(null);
        txnBaseListDto.setTreasuryName(null);

        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale((String) null, (String) null))
                    .thenReturn(null);

            // When
            String result = txnBaseListDto.getTreasuryName();

            // Then
            assertNull(result);
            mockedTranslator.verify(() -> Translator.toLocale((String) null, (String) null));
        }
    }

    @Test
    void testEqualsAndHashCode() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Mocked Translation");

            // Given
            String txnId = "DVC01704202411252339";
            BigDecimal amount = new BigDecimal("100000");

            TestTxnBaseListDto dto1 = new TestTxnBaseListDto();
            dto1.setTxnId(txnId);
            dto1.setAmount(amount);

            TestTxnBaseListDto dto2 = new TestTxnBaseListDto();
            dto2.setTxnId(txnId);
            dto2.setAmount(amount);

            // Then
            assertEquals(dto1, dto2);
            assertEquals(dto1.hashCode(), dto2.hashCode());
        }
    }

    @Test
    void testToString() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Mocked Translation");

            // Given
            String txnId = "DVC01704202411252339";
            BigDecimal amount = new BigDecimal("100000");

            txnBaseListDto.setTxnId(txnId);
            txnBaseListDto.setAmount(amount);

            // When
            String result = txnBaseListDto.toString();

            // Then
            assertNotNull(result);
            assertTrue(result.contains("txnId"));
            assertTrue(result.contains("amount"));
        }
    }

    @Test
    void testBigDecimalAmountPrecision() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenReturn("Mocked Translation");

            // Given
            BigDecimal amount = new BigDecimal("100000.50");

            // When
            txnBaseListDto.setAmount(amount);

            // Then
            assertEquals(amount, txnBaseListDto.getAmount());
            assertEquals(0, amount.compareTo(txnBaseListDto.getAmount()));
        }
    }
}
