package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.framework.util.Translator;

class EconomicContentResTest {

    private MockedStatic<Translator> translatorMock;
    private EconomicContentRes economicContentRes;
    private static final String EC_CODE = "EC001";
    private static final String EC_NAME = "Economic Content 1";
    private static final String TRANSLATED_NAME = "Nội dung kinh tế 1";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        economicContentRes = EconomicContentRes.builder()
                .ecCode(EC_CODE)
                .ecName(EC_NAME)
                .build();
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(EC_NAME);

        assertThat(economicContentRes).isNotNull();
        assertThat(economicContentRes.getEcCode()).isEqualTo(EC_CODE);
        assertThat(economicContentRes.getEcName()).isEqualTo(EC_NAME);
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        EconomicContentRes emptyContent = EconomicContentRes.builder().build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        assertThat(emptyContent).isNotNull();
        assertThat(emptyContent.getEcCode()).isNull();
        assertThat(emptyContent.getEcName()).isNull();
    }

    @Test
    void getEcName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(TRANSLATED_NAME);

        String result = economicContentRes.getEcName();

        assertThat(result).isEqualTo(TRANSLATED_NAME);
    }

    @Test
    void getEcName_WhenTranslationNotFound_ShouldReturnOriginalName() {
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(EC_NAME);

        String result = economicContentRes.getEcName();

        assertThat(result).isEqualTo(EC_NAME);
    }

    @Test
    void getEcName_WhenEcCodeIsNull_ShouldHandleNullGracefully() {
        EconomicContentRes nullCodeContent = EconomicContentRes.builder()
                .ecName(EC_NAME)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullCodeContent.getEcName();

        assertThat(result).isNull();
    }

    @Test
    void getEcName_WhenEcNameIsNull_ShouldHandleNullGracefully() {
        EconomicContentRes nullNameContent = EconomicContentRes.builder()
                .ecCode(EC_CODE)
                .build();

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(null);

        String result = nullNameContent.getEcName();

        assertThat(result).isNull();
    }

    @Test
    void noArgsConstructor_ShouldCreateEmptyInstance() {
        EconomicContentRes content = new EconomicContentRes();

        assertThat(content).isNotNull();
        assertThat(content.getEcCode()).isNull();
        assertThat(content.getEcName()).isNull();
    }

    @Test
    void allArgsConstructor_ShouldCreatePopulatedInstance() {
        EconomicContentRes content = new EconomicContentRes(EC_CODE, EC_NAME);

        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenReturn(EC_NAME);

        assertThat(content.getEcCode()).isEqualTo(EC_CODE);
        assertThat(content.getEcName()).isEqualTo(EC_NAME);
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        EconomicContentRes content1 = EconomicContentRes.builder()
                .ecCode(EC_CODE)
                .ecName(EC_NAME)
                .build();

        EconomicContentRes content2 = EconomicContentRes.builder()
                .ecCode(EC_CODE)
                .ecName(EC_NAME)
                .build();

        assertThat(content1).isEqualTo(content2);
        assertThat(content1.hashCode()).isEqualTo(content2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        EconomicContentRes content1 = EconomicContentRes.builder()
                .ecCode(EC_CODE)
                .ecName(EC_NAME)
                .build();

        EconomicContentRes content2 = EconomicContentRes.builder()
                .ecCode("EC002")
                .ecName("Economic Content 2")
                .build();

        assertThat(content1).isNotEqualTo(content2);
        assertThat(content1.hashCode()).isNotEqualTo(content2.hashCode());
    }
}