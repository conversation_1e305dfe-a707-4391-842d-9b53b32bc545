package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TxnInitApproveResTest {

    private TxnInitApproveRes txnInitApproveRes;

    @BeforeEach
    void setUp() {
        txnInitApproveRes = new TxnInitApproveRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnInitApproveRes res = new TxnInitApproveRes();

        // Then
        assertNotNull(res);
        assertNull(res.getTransKey());
        assertNull(res.getTransAuth());
        assertFalse(res.isRequireAuth());
    }

    @Test
    void testSuperBuilderPattern() {
        // Given
        String transKey = "TEST_TRANS_KEY";
        boolean requireAuth = true;

        // When
        TxnInitApproveRes res = TxnInitApproveRes.builder()
                .transKey(transKey)
                .requireAuth(requireAuth)
                .build();

        // Then
        assertEquals(transKey, res.getTransKey());
        assertEquals(requireAuth, res.isRequireAuth());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String transKey = "APPROVAL_KEY";
        boolean requireAuth = false;

        // When
        txnInitApproveRes.setTransKey(transKey);
        txnInitApproveRes.setRequireAuth(requireAuth);

        // Then
        assertEquals(transKey, txnInitApproveRes.getTransKey());
        assertEquals(requireAuth, txnInitApproveRes.isRequireAuth());
    }

    @Test
    void testInheritanceFromTxnInitPushRes() {
        // Given
        TxnInitApproveRes res = new TxnInitApproveRes();

        // Then
        assertNotNull(res);
        res.setTransKey("TEST");
        assertEquals("TEST", res.getTransKey());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String transKey = "KEY123";
        boolean requireAuth = true;

        TxnInitApproveRes res1 = TxnInitApproveRes.builder()
                .transKey(transKey)
                .requireAuth(requireAuth)
                .build();

        TxnInitApproveRes res2 = TxnInitApproveRes.builder()
                .transKey(transKey)
                .requireAuth(requireAuth)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        txnInitApproveRes.setTransKey("KEY123");
        txnInitApproveRes.setRequireAuth(true);

        // When
        String result = txnInitApproveRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
