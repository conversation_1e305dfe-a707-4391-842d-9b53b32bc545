package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;

@ExtendWith(MockitoExtension.class)
class BatchProcessResultResTest {

    private BatchProcessResultRes batchProcessResultRes;

    @BeforeEach
    void setUp() {
        batchProcessResultRes = new BatchProcessResultRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        BatchProcessResultRes res = new BatchProcessResultRes();

        // Then
        assertNotNull(res);
        assertNull(res.getFailTxns());
        // BaseTotalDto inherited fields
        assertNull(res.getTotal());
        assertNull(res.getTotalSuccess());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        List<TransactionResDetail> failTxns = Collections.singletonList(new TransactionResDetail());

        // When
        BatchProcessResultRes res = new BatchProcessResultRes(failTxns);

        // Then
        assertEquals(failTxns, res.getFailTxns());
    }

    @Test
    void testSuperBuilderPattern() {
        // Given
        List<TransactionResDetail> failTxns = Arrays.asList(new TransactionResDetail(), new TransactionResDetail());
        Long total = 10L;
        Long totalSuccess = 8L;

        // When
        BatchProcessResultRes res = BatchProcessResultRes.builder()
                .failTxns(failTxns)
                .total(total)
                .totalSuccess(totalSuccess)
                .build();

        // Then
        assertEquals(failTxns, res.getFailTxns());
        assertEquals(total, res.getTotal());
        assertEquals(totalSuccess, res.getTotalSuccess());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        List<TransactionResDetail> failTxns = Collections.singletonList(new TransactionResDetail());
        Long total = 5L;
        Long totalSuccess = 4L;

        // When
        batchProcessResultRes.setFailTxns(failTxns);
        batchProcessResultRes.setTotal(total);
        batchProcessResultRes.setTotalSuccess(totalSuccess);

        // Then
        assertEquals(failTxns, batchProcessResultRes.getFailTxns());
        assertEquals(total, batchProcessResultRes.getTotal());
        assertEquals(totalSuccess, batchProcessResultRes.getTotalSuccess());
    }

    @Test
    void testInheritanceFromBaseTotalDto() {
        // Given
        BatchProcessResultRes res = new BatchProcessResultRes();

        // Then - Verify that BatchProcessResultRes extends BaseTotalDto
        assertNotNull(res);
        // Should have access to BaseTotalDto methods
        res.setTotal(10L);
        assertEquals(10L, res.getTotal());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        List<TransactionResDetail> failTxns = Collections.singletonList(new TransactionResDetail());
        Long total = 10L;

        BatchProcessResultRes res1 = BatchProcessResultRes.builder()
                .failTxns(failTxns)
                .total(total)
                .build();

        BatchProcessResultRes res2 = BatchProcessResultRes.builder()
                .failTxns(failTxns)
                .total(total)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        List<TransactionResDetail> failTxns = Collections.singletonList(new TransactionResDetail());
        batchProcessResultRes.setFailTxns(failTxns);
        batchProcessResultRes.setTotal(5L);

        // When
        String result = batchProcessResultRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
