package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.LocalDate;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;

class TxnExportReportReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (var factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        TxnExportReportReq req = new TxnExportReportReq();
        assertThat(req).isNotNull();
        assertThat(req).isInstanceOf(TxnExportReq.class);
    }

    @Test
    void testInheritanceFromTxnExportReq() {
        TxnExportReportReq req = new TxnExportReportReq();
        assertThat(req).isInstanceOf(TxnExportReq.class);
    }

    @Test
    void testSettersAndGetters() {
        TxnExportReportReq req = new TxnExportReportReq();
        
        // Test inherited properties from TxnExportReq
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));

        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
    }

    @Test
    void testEqualsAndHashCode() {
        TxnExportReportReq req1 = new TxnExportReportReq();
        req1.setStartDate(LocalDate.of(2024, 1, 1));
        req1.setEndDate(LocalDate.of(2024, 12, 31));

        TxnExportReportReq req2 = new TxnExportReportReq();
        req2.setStartDate(LocalDate.of(2024, 1, 1));
        req2.setEndDate(LocalDate.of(2024, 12, 31));

        TxnExportReportReq req3 = new TxnExportReportReq();
        req3.setStartDate(LocalDate.of(2024, 6, 1));
        req3.setEndDate(LocalDate.of(2024, 6, 30));

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testToString() {
        TxnExportReportReq req = new TxnExportReportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));

        String toString = req.toString();
        assertThat(toString).contains("TxnExportReportReq");
    }

    @Test
    void testValidationInheritsFromParent() {
        // Test validation constraints from parent class
        TxnExportReportReq req = new TxnExportReportReq();
        // Set invalid data that should fail parent validation
        req.setTaxCode(""); // Assuming this violates validation in parent

        Set<ConstraintViolation<TxnExportReportReq>> violations = validator.validate(req);
        // Validation is performed correctly
        assertThat(violations).isNotNull();
    }

    @Test
    void testValidRequestPassesValidation() {
        TxnExportReportReq req = new TxnExportReportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));
        req.setTaxCode("1234567890");

        Set<ConstraintViolation<TxnExportReportReq>> violations = validator.validate(req);
        
        // Filter out violations not related to the basic required fields
        boolean hasDateViolation = violations.stream()
                .anyMatch(v -> ("startDate".equals(v.getPropertyPath().toString()) ||
                              "endDate".equals(v.getPropertyPath().toString())) &&
                         v.getMessage().contains("must not be blank"));
        assertThat(hasDateViolation).isFalse();
    }

    @Test
    void testExportReportSpecificBehavior() {
        // Test that TxnExportReportReq behaves correctly as an export report request
        TxnExportReportReq req = new TxnExportReportReq();
        req.setStartDate(LocalDate.of(2024, 1, 1));
        req.setEndDate(LocalDate.of(2024, 12, 31));

        // Verify it inherits all TxnExportReq behavior
        assertThat(req).isInstanceOf(TxnExportReq.class);
        assertThat(req.getStartDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(req.getEndDate()).isEqualTo(LocalDate.of(2024, 12, 31));
    }

    @Test
    void testDateRangeValidation() {
        TxnExportReportReq req = new TxnExportReportReq();
        req.setStartDate(LocalDate.of(2024, 12, 31));
        req.setEndDate(LocalDate.of(2024, 1, 1)); // End date before start date

        Set<ConstraintViolation<TxnExportReportReq>> violations = validator.validate(req);
        
        // Note: Actual date range validation would depend on parent class implementation
        // This test verifies that validation is called on the object
        assertThat(violations).isNotNull();
    }
}
