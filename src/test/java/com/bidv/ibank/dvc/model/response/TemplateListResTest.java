package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.framework.i18n.CompositeMessageSource;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TemplateListResTest {

    @Mock
    private CompositeMessageSource messageSource;

    private TemplateListRes templateListRes;
    private final String templateId = UUID.randomUUID().toString();
    private final LocalDateTime createdDate = LocalDateTime.now();
    private static final BigDecimal AMOUNT = new BigDecimal("*********.00");
    private static final BigDecimal FEE_TOTAL = new BigDecimal("10000.00");

    @BeforeEach
    void setUp() {
        templateListRes = TemplateListRes.builder()
                .templateId(templateId)
                .templateName("Test Template")
                .taxCode("**********")
                .altTaxCode("**********")
                .payerName("Test Payer")
                .altPayerName("Alt Payer")
                .payerAddr("Test Address")
                .altPayerAddr("Alt Address")
                .treasuryCode("TREASURY")
                .treasuryName("Treasury Name")
                .admAreaCode("AREA")
                .admAreaName("Area Name")
                .revAccCode("ACC")
                .revAccName("Account Name")
                .revAuthCode("AUTH")
                .revAuthName("Auth Name")
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .createdDate(createdDate)
                .taxItems(Arrays.asList(
                        TxnTaxFullItemDto.builder()
                                .amount("1000")
                                .ccy("VND")
                                .payerType(PayerTypeEnum.BUSINESS.getValue())
                                .build()))
                .build();
    }

    @Nested
    @DisplayName("Constructor and Builder Tests")
    class ConstructorAndBuilderTests {

        @Test
        @DisplayName("Should create object using builder")
        void shouldCreateObjectUsingBuilder() {
            assertNotNull(templateListRes);
            assertEquals(templateId, templateListRes.getTemplateId());
            assertEquals("Test Template", templateListRes.getTemplateName());
            assertEquals("**********", templateListRes.getTaxCode());
            assertEquals("**********", templateListRes.getAltTaxCode());
            assertEquals("Test Payer", templateListRes.getPayerName());
            assertEquals("Alt Payer", templateListRes.getAltPayerName());
            assertEquals("Test Address", templateListRes.getPayerAddr());
            assertEquals("Alt Address", templateListRes.getAltPayerAddr());
            assertEquals(PayerTypeEnum.BUSINESS.getValue(), templateListRes.getPayerType());
            assertEquals("TREASURY", templateListRes.getTreasuryCode());
            assertEquals("AREA", templateListRes.getAdmAreaCode());
            assertEquals("ACC", templateListRes.getRevAccCode());
            assertEquals("AUTH", templateListRes.getRevAuthCode());
            assertEquals(createdDate, templateListRes.getCreatedDate());
            assertNotNull(templateListRes.getTaxItems());
            assertEquals(1, templateListRes.getTaxItems().size());
            assertEquals("1000", templateListRes.getTaxItems().get(0).getAmount());
            assertEquals("VND", templateListRes.getTaxItems().get(0).getCcy());
        }

        @Test
        @DisplayName("Should create object using no-args constructor")
        void shouldCreateObjectUsingNoArgsConstructor() {
            TemplateListRes emptyRes = new TemplateListRes();
            assertNotNull(emptyRes);
        }
    }

    @Nested
    @DisplayName("Getter and Setter Tests")
    class GetterSetterTests {

        @Test
        @DisplayName("Should get and set all fields correctly")
        void shouldGetAndSetAllFieldsCorrectly() {
            String newTemplateId = UUID.randomUUID().toString();
            templateListRes.setTemplateId(newTemplateId);
            assertEquals(newTemplateId, templateListRes.getTemplateId());

            templateListRes.setTemplateName("New Template");
            assertEquals("New Template", templateListRes.getTemplateName());

            templateListRes.setPayerType(PayerTypeEnum.INDIVIDUAL.getValue());
            assertEquals(PayerTypeEnum.INDIVIDUAL.getValue(), templateListRes.getPayerType());
        }
    }

    @Nested
    @DisplayName("Translation Tests")
    class TranslationTests {

        @BeforeEach
        void setupTranslator() {
            ReflectionTestUtils.setField(Translator.class, "messageSource", messageSource);
            when(messageSource.getMessage(any(), any(), any(), any())).thenAnswer(invocation -> {
                Object[] args = invocation.getArguments();
                String defaultMessage = (String) args[2];
                return defaultMessage;
            });
        }

        @Test
        @DisplayName("Should translate treasury name correctly")
        void shouldTranslateTreasuryNameCorrectly() {
            assertEquals("Treasury Name", templateListRes.getTreasuryName());
        }

        @Test
        @DisplayName("Should translate admin area name correctly")
        void shouldTranslateAdminAreaNameCorrectly() {
            assertEquals("Area Name", templateListRes.getAdmAreaName());
        }

        @Test
        @DisplayName("Should translate revenue account name correctly")
        void shouldTranslateRevAccNameCorrectly() {
            assertEquals("Account Name", templateListRes.getRevAccName());
        }

        @Test
        @DisplayName("Should translate revenue authority name correctly")
        void shouldTranslateRevAuthNameCorrectly() {
            assertEquals("Auth Name", templateListRes.getRevAuthName());
        }
    }

    @Test
    void noArgsConstructor_shouldCreateEmptyInstance() {
        TemplateListRes emptyRes = new TemplateListRes();
        assertNotNull(emptyRes);
        assertNull(emptyRes.getTemplateId());
        assertNull(emptyRes.getTemplateName());
        assertNull(emptyRes.getTaxCode());
        assertNull(emptyRes.getAltTaxCode());
        assertNull(emptyRes.getPayerName());
        assertNull(emptyRes.getAltPayerName());
        assertNull(emptyRes.getPayerAddr());
        assertNull(emptyRes.getAltPayerAddr());
        assertNull(emptyRes.getPayerType());
        assertNull(emptyRes.getTreasuryCode());
        assertNull(emptyRes.getAdmAreaCode());
        assertNull(emptyRes.getRevAccCode());
        assertNull(emptyRes.getRevAuthCode());
        assertNull(emptyRes.getCreatedDate());
        assertNull(emptyRes.getTaxItems());
    }

    @Test
    void allArgsConstructor_shouldCreateInstanceWithAllFields() {
        ReflectionTestUtils.setField(Translator.class, "messageSource", messageSource);
        when(messageSource.getMessage(any(), any(), any(), any())).thenAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            String defaultMessage = (String) args[2];
            return defaultMessage;
        });

        TemplateListRes res = new TemplateListRes(
                "**********",
                "**********",
                "Test Payer",
                "Alt Payer",
                "Test Address",
                "Alt Address",
                "TREASURY",
                "Treasury Name",
                "AREA",
                "Area Name",
                "ACC",
                "Account Name",
                "AUTH",
                "Auth Name",
                "BBC",
                "Ben Bank Name",
                PayerTypeEnum.BUSINESS.getValue(),
                "**********",
                "Công ty TNHH A",
                templateId,
                "Test Template",
                createdDate,
                Arrays.asList(TxnTaxFullItemDto.builder().build()),
                "04",
                AMOUNT,
                "VND",
                FEE_TOTAL,
                "Phí khoán",
                "VND",
                "APP",
                "************",
                true);

        assertNotNull(res);
        assertEquals("**********", res.getTaxCode());
        assertEquals("**********", res.getAltTaxCode());
        assertEquals("Test Payer", res.getPayerName());
        assertEquals("Alt Payer", res.getAltPayerName());
        assertEquals("Test Address", res.getPayerAddr());
        assertEquals("Alt Address", res.getAltPayerAddr());
        assertEquals("TREASURY", res.getTreasuryCode());
        assertEquals("Treasury Name", res.getTreasuryName());
        assertEquals("AREA", res.getAdmAreaCode());
        assertEquals("Area Name", res.getAdmAreaName());
        assertEquals("ACC", res.getRevAccCode());
        assertEquals("Account Name", res.getRevAccName());
        assertEquals("AUTH", res.getRevAuthCode());
        assertEquals("Auth Name", res.getRevAuthName());
        assertEquals("BBC", res.getBenBankCode());
        assertEquals("Ben Bank Name", res.getBenBankName());
        assertEquals(templateId, res.getTemplateId());
        assertEquals("Test Template", res.getTemplateName());
        assertEquals(PayerTypeEnum.BUSINESS.getValue(), res.getPayerType());
        assertEquals(createdDate, res.getCreatedDate());
        assertNotNull(res.getTaxItems());
        assertEquals(1, res.getTaxItems().size());
    }

    @Test
    void equalsAndHashCode_shouldWorkCorrectly() {
        TemplateListRes res1 = TemplateListRes.builder()
                .templateId(templateId)
                .templateName("Test")
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .build();

        TemplateListRes res2 = TemplateListRes.builder()
                .templateId(templateId)
                .templateName("Test")
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .build();

        TemplateListRes res3 = TemplateListRes.builder()
                .templateId(UUID.randomUUID().toString())
                .templateName("Different")
                .payerType(PayerTypeEnum.INDIVIDUAL.getValue())
                .build();

        // Test equality with same values
        assertEquals(res1, res2);
        assertNotEquals(res1, res3);
        assertEquals(res1.hashCode(), res2.hashCode());
        assertNotEquals(res1.hashCode(), res3.hashCode());

        // Test with null PayerType
        TemplateListRes res4 = TemplateListRes.builder()
                .templateId(templateId)
                .templateName("Test")
                .build();

        TemplateListRes res5 = TemplateListRes.builder()
                .templateId(templateId)
                .templateName("Test")
                .build();

        // Objects with null PayerType should still be equal if other fields match
        assertEquals(res4, res5);
        assertEquals(res4.hashCode(), res5.hashCode());
        assertNotEquals(res1, res4); // Object with PayerType should not equal object without PayerType
    }

    @Test
    void toString_shouldIncludeAllFields() {
        ReflectionTestUtils.setField(Translator.class, "messageSource", messageSource);
        when(messageSource.getMessage(any(), any(), any(), any())).thenAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            String defaultMessage = (String) args[2];
            return defaultMessage;
        });

        String toString = templateListRes.toString();

        // Basic fields
        assertTrue(toString.contains("templateId=" + templateId.toString()), "Should contain templateId");
        assertTrue(toString.contains("templateName=Test Template"), "Should contain templateName");
        assertTrue(toString.contains("taxCode=**********"), "Should contain taxCode");
        assertTrue(toString.contains("payerName=Test Payer"), "Should contain payerName");
        assertTrue(toString.contains("payerAddr=Test Address"), "Should contain payerAddr");
        assertTrue(toString.contains("treasuryCode=TREASURY"), "Should contain treasuryCode");
        assertTrue(toString.contains("admAreaCode=AREA"), "Should contain admAreaCode");
        assertTrue(toString.contains("revAccCode=ACC"), "Should contain revAccCode");
        assertTrue(toString.contains("revAuthCode=AUTH"), "Should contain revAuthCode");

        // Tax items verification - using TxnTaxFullItemDto's actual format
        TxnTaxFullItemDto taxItem = templateListRes.getTaxItems().get(0);
        String taxItemString = taxItem.toString();

        assertTrue(toString.contains(taxItemString), "Should contain complete tax item string");
    }
}