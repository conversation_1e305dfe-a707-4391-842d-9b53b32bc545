package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.util.constant.TxnPushTypeEnum;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnInitPushReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void whenTransKeyExceeds150Characters_thenValidationFails() {
        String longTransKey = "TXNKEY_REQUEST_APPROVAL_" + "x".repeat(150);
        TxnInitPushReq req = TxnInitPushReq.builder()
                .transKey(longTransKey)
                .type(TxnPushTypeEnum.PUSH_SAVE)
                .build();

        Set<ConstraintViolation<TxnInitPushReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("transKey");
    }

    @Test
    void whenTypeIsNull_thenValidationFails() {
        TxnInitPushReq req = TxnInitPushReq.builder()
                .transKey("TEST_KEY")
                .build();

        Set<ConstraintViolation<TxnInitPushReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("type");
    }

    @Test
    void whenPushTypeWithoutTxnIds_thenValidationFails() {
        TxnInitPushReq req = TxnInitPushReq.builder()
                .type(TxnPushTypeEnum.PUSH)
                .build();

        Set<ConstraintViolation<TxnInitPushReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
    }

    @Test
    void whenPushSaveTypeWithoutTransKey_thenValidationFails() {
        TxnInitPushReq req = TxnInitPushReq.builder()
                .type(TxnPushTypeEnum.PUSH_SAVE)
                .build();

        Set<ConstraintViolation<TxnInitPushReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
    }

    @Test
    void whenValidRequest_thenValidationSucceeds() {
        TxnInitPushReq req = TxnInitPushReq.builder()
                .transKey("TEST_KEY")
                .type(TxnPushTypeEnum.PUSH_SAVE)
                .build();

        Set<ConstraintViolation<TxnInitPushReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void whenValidPushRequest_thenValidationSucceeds() {
        TxnInitPushReq req = TxnInitPushReq.builder()
                .type(TxnPushTypeEnum.PUSH)
                .txnIds(List.of("TXN1", "TXN2"))
                .build();

        Set<ConstraintViolation<TxnInitPushReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }
}