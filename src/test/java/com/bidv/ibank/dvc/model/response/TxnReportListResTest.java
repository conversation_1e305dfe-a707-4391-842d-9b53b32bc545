package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnReportListResTest {

    private TxnReportListRes txnReportListRes;

    @BeforeEach
    void setUp() {
        txnReportListRes = new TxnReportListRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnReportListRes res = new TxnReportListRes();

        // Then
        assertNotNull(res);
        assertNull(res.getCreatedBy());
        assertNull(res.getApprovalUsers());
        assertNull(res.getTxnType());
        assertNull(res.getTxnItemId());
        assertNull(res.getTxnId());
        assertNull(res.getAmount());
        assertNull(res.getStatus());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String createdBy = "user123";
        String approvalUsers = "approver1,approver2";
        String txnType = "01";
        String txnItemId = "**********";

        // When
        TxnReportListRes res = new TxnReportListRes(createdBy, approvalUsers, txnType, txnItemId);

        // Then
        assertEquals(createdBy, res.getCreatedBy());
        assertEquals(approvalUsers, res.getApprovalUsers());
        assertEquals(txnType, res.getTxnType());
        assertEquals(txnItemId, res.getTxnItemId());
    }

    @Test
    void testSuperBuilderPattern() {
        // Given
        String createdBy = "creator456";
        String approvalUsers = "admin,manager";
        String txnType = "04";
        String txnItemId = "9876543210";
        String txnId = "TXN001";
        BigDecimal amount = new BigDecimal("1000000");
        String status = "SUCCESS";
        LocalDateTime createdDate = LocalDateTime.of(2025, 1, 1, 12, 0, 0);

        // When
        TxnReportListRes res = TxnReportListRes.builder()
                .createdBy(createdBy)
                .approvalUsers(approvalUsers)
                .txnType(txnType)
                .txnItemId(txnItemId)
                .txnId(txnId)
                .amount(amount)
                .status(status)
                .createdDate(createdDate)
                .build();

        // Then
        assertEquals(createdBy, res.getCreatedBy());
        assertEquals(approvalUsers, res.getApprovalUsers());
        assertEquals(txnType, res.getTxnType());
        assertEquals(txnItemId, res.getTxnItemId());
        assertEquals(txnId, res.getTxnId());
        assertEquals(amount, res.getAmount());
        assertEquals(status, res.getStatus());
        assertEquals(createdDate, res.getCreatedDate());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String createdBy = "employee789";
        String approvalUsers = "supervisor,director";
        String txnType = "03";
        String txnItemId = "5555555555";

        // When
        txnReportListRes.setCreatedBy(createdBy);
        txnReportListRes.setApprovalUsers(approvalUsers);
        txnReportListRes.setTxnType(txnType);
        txnReportListRes.setTxnItemId(txnItemId);

        // Then
        assertEquals(createdBy, txnReportListRes.getCreatedBy());
        assertEquals(approvalUsers, txnReportListRes.getApprovalUsers());
        assertEquals(txnType, txnReportListRes.getTxnType());
        assertEquals(txnItemId, txnReportListRes.getTxnItemId());
    }

    @Test
    void testGetTxnTypeNameWithTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String txnType = "01";
            String translatedName = "Thuế nội địa";
            String expectedKey = AppConstants.LANGUAGE.TXN_TYPE + "." + txnType;

            txnReportListRes.setTxnType(txnType);

            mockedTranslator.when(() -> Translator.toLocale(expectedKey, txnType))
                    .thenReturn(translatedName);

            // When
            String result = txnReportListRes.getTxnTypeName();

            // Then
            assertEquals(translatedName, result);
            mockedTranslator.verify(() -> Translator.toLocale(expectedKey, txnType));
        }
    }

    @Test
    void testGetTxnTypeNameWithNullTxnType() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            txnReportListRes.setTxnType(null);
            mockedTranslator.when(() -> Translator.toLocale(anyString(), (String) isNull()))
                    .thenReturn("Unknown Type");

            // When
            String result = txnReportListRes.getTxnTypeName();

            // Then
            assertEquals("Unknown Type", result);
        }
    }

    @Test
    void testInheritanceFromTxnPendingListRes() {
        // Given
        TxnReportListRes res = new TxnReportListRes();

        // Then
        assertNotNull(res);
        res.setTxnId("REPORT_TXN123");
        assertEquals("REPORT_TXN123", res.getTxnId());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String createdBy = "user123";
        String txnType = "01";
        String txnId = "TXN001";

        TxnReportListRes res1 = TxnReportListRes.builder()
                .createdBy(createdBy)
                .txnType(txnType)
                .txnId(txnId)
                .build();

        TxnReportListRes res2 = TxnReportListRes.builder()
                .createdBy(createdBy)
                .txnType(txnType)
                .txnId(txnId)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String createdBy = "reporter";
        String txnType = "04";

        txnReportListRes.setCreatedBy(createdBy);
        txnReportListRes.setTxnType(txnType);
        txnReportListRes.setTxnId("REPORT001");

        // When
        String result = txnReportListRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
