package com.bidv.ibank.dvc.model.mapper.customsduty;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.framework.util.Translator;

class GOVPaymentTransactionMapperTest {

    private GOVPaymentTransactionMapper govPaymentTransactionMapper;
    private GOVPaymentTransactionEntity transactionEntity;
    private TccDmKhobacEntity treasuryEntity;
    private GOVPaymentItemEntity paymentItemEntity;
    private MockedStatic<Translator> translatorMock;
    private LocalDateTime now;

    @BeforeEach
    void setUp() {
        govPaymentTransactionMapper = new GOVPaymentTransactionMapper();
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class)))
                .thenAnswer(i -> i.getArgument(1));

        treasuryEntity = new TccDmKhobacEntity();
        treasuryEntity.setShkb("TR001");
        treasuryEntity.setTen("Treasury 1");

        paymentItemEntity = mock(GOVPaymentItemEntity.class);
        when(paymentItemEntity.getDeclarationNo()).thenReturn("DEC001");

        now = LocalDateTime.now();
        transactionEntity = mock(GOVPaymentTransactionEntity.class);
        when(transactionEntity.getId()).thenReturn("TXN001");
        when(transactionEntity.getDebitAccNo()).thenReturn("1234567890");
        when(transactionEntity.getTaxCode()).thenReturn("TAX001");
        when(transactionEntity.getAmount()).thenReturn(BigDecimal.valueOf(1000));
        when(transactionEntity.getCcy()).thenReturn("VND");
        when(transactionEntity.getTccDmKhobacEntity()).thenReturn(treasuryEntity);
        when(transactionEntity.getShkb()).thenReturn("TR001");
        when(transactionEntity.getBatchNo()).thenReturn("BATCH001");
        when(transactionEntity.getStatus()).thenReturn("PENDING");
        when(transactionEntity.getCreatedDate()).thenReturn(now);
        when(transactionEntity.getGovPaymentItemList()).thenReturn(Set.of(paymentItemEntity));
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void toDto_ShouldThrowUnsupportedOperationException() {
        assertThatThrownBy(() -> govPaymentTransactionMapper.toDto(transactionEntity))
                .isInstanceOf(UnsupportedOperationException.class)
                .hasMessage("Unimplemented method 'toDto'");
    }

    @Test
    void toEntity_ShouldThrowUnsupportedOperationException() {
        TxnPendingListRes dto = TxnPendingListRes.builder().build();
        assertThatThrownBy(() -> govPaymentTransactionMapper.toEntity(dto))
                .isInstanceOf(UnsupportedOperationException.class)
                .hasMessage("Unimplemented method 'toEntity'");
    }

    @Test
    void toPendingDto_WhenValidInput_ShouldMapCorrectly() {
        TxnPendingListRes result = govPaymentTransactionMapper.toPendingDto(transactionEntity);

        assertThat(result).isNotNull();
        assertThat(result.getTxnId()).isEqualTo("TXN001");
        assertThat(result.getDebitAccNo()).isEqualTo("1234567890");
        assertThat(result.getTaxCode()).isEqualTo("TAX001");
        assertThat(result.getDeclarationNo()).isEqualTo("DEC001");
        assertThat(result.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
        assertThat(result.getCcy()).isEqualTo("VND");
        assertThat(result.getTreasuryCode()).isEqualTo("TR001");
        assertThat(result.getTreasuryName()).isEqualTo("Treasury 1");
        assertThat(result.getBatchNo()).isEqualTo("BATCH001");
        assertThat(result.getStatus()).isEqualTo("PENDING");
        assertThat(result.getCreatedDate()).isEqualTo(now);
    }

    @Test
    void toPendingDto_WhenNullTreasury_ShouldMapCorrectly() {
        transactionEntity = mock(GOVPaymentTransactionEntity.class);
        paymentItemEntity = mock(GOVPaymentItemEntity.class);
        when(paymentItemEntity.getDeclarationNo()).thenReturn("DEC001");

        when(transactionEntity.getId()).thenReturn("TXN001");
        when(transactionEntity.getDebitAccNo()).thenReturn("1234567890");
        when(transactionEntity.getTaxCode()).thenReturn("TAX001");
        when(transactionEntity.getAmount()).thenReturn(BigDecimal.valueOf(1000));
        when(transactionEntity.getCcy()).thenReturn("VND");
        when(transactionEntity.getBatchNo()).thenReturn("BATCH001");
        when(transactionEntity.getStatus()).thenReturn("PENDING");
        when(transactionEntity.getCreatedDate()).thenReturn(now);
        when(transactionEntity.getTccDmKhobacEntity()).thenReturn(null);
        when(transactionEntity.getGovPaymentItemList()).thenReturn(Set.of(paymentItemEntity));

        TxnPendingListRes result = govPaymentTransactionMapper.toPendingDto(transactionEntity);

        assertThat(result).isNotNull();
        assertThat(result.getTxnId()).isEqualTo("TXN001");
        assertThat(result.getDebitAccNo()).isEqualTo("1234567890");
        assertThat(result.getTaxCode()).isEqualTo("TAX001");
        assertThat(result.getDeclarationNo()).isEqualTo("DEC001");
        assertThat(result.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
        assertThat(result.getCcy()).isEqualTo("VND");
        assertThat(result.getTreasuryCode()).isNull();
        assertThat(result.getTreasuryName()).isNull();
        assertThat(result.getBatchNo()).isEqualTo("BATCH001");
        assertThat(result.getStatus()).isEqualTo("PENDING");
        assertThat(result.getCreatedDate()).isEqualTo(now);
    }
}