package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmLhxnkCodeNameEntityDtoTest {

    private TccDmLhxnkCodeNameEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmLhxnkCodeNameEntityDto();
        dto.setMaLhxnk("LH001");
        dto.setTenLhxnk("Test LHXNK");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("LH001", dto.getMaLhxnk());
        assertEquals("Test LHXNK", dto.getTenLhxnk());
    }

    @Test
    void testGetMaTenLhxnk() {
        assertEquals("LH001 - Test LHXNK", dto.getMaTenLhxnk());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmLhxnkCodeNameEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmLhxnkCodeNameEntityDto dto1 = new TccDmLhxnkCodeNameEntityDto("LH001", "Test LHXNK");
        TccDmLhxnkCodeNameEntityDto dto2 = new TccDmLhxnkCodeNameEntityDto("LH001", "Test LHXNK");
        TccDmLhxnkCodeNameEntityDto dto3 = new TccDmLhxnkCodeNameEntityDto("LH002", "Other LHXNK");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmLhxnkCodeNameEntityDto dto = new TccDmLhxnkCodeNameEntityDto("LH001", "Test LHXNK");
        assertEquals("LH001", dto.getMaLhxnk());
        assertEquals("Test LHXNK", dto.getTenLhxnk());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmLhxnkCodeNameEntityDto dto = new TccDmLhxnkCodeNameEntityDto();
        assertNull(dto.getMaLhxnk());
        assertNull(dto.getTenLhxnk());
    }
}