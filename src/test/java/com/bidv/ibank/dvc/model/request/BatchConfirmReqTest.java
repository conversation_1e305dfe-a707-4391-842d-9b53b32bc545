package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class BatchConfirmReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        BatchConfirmReq req = new BatchConfirmReq();
        assertThat(req).isNotNull();
        assertThat(req).isInstanceOf(TxnConfirmReq.class);
    }

    @Test
    void testInheritanceFromTxnConfirmReq() {
        BatchConfirmReq req = new BatchConfirmReq();
        assertThat(req).isInstanceOf(TxnConfirmReq.class);
    }

    @Test
    void testSettersAndGetters() {
        BatchConfirmReq req = new BatchConfirmReq();
        
        // Test inherited properties from TxnConfirmReq
        req.setTransKey("transKey123");
        req.setConfirmValue("test-confirm-value");

        assertThat(req.getTransKey()).isEqualTo("transKey123");
        assertThat(req.getConfirmValue()).isEqualTo("test-confirm-value");
    }

    @Test
    void testEqualsAndHashCode() {
        BatchConfirmReq req1 = new BatchConfirmReq();
        req1.setTransKey("transKey123");
        req1.setConfirmValue("test-confirm-value");

        BatchConfirmReq req2 = new BatchConfirmReq();
        req2.setTransKey("transKey123");
        req2.setConfirmValue("test-confirm-value");

        BatchConfirmReq req3 = new BatchConfirmReq();
        req3.setTransKey("transKey456");
        req3.setConfirmValue("different-confirm-value");

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testToString() {
        BatchConfirmReq req = new BatchConfirmReq();
        req.setTransKey("transKey123");
        req.setConfirmValue("test-confirm-value");

        String toString = req.toString();
        assertThat(toString).contains("BatchConfirmReq");
    }

    @Test
    void testValidationInheritsFromParent() {
        // Test validation constraints from parent class
        BatchConfirmReq req = new BatchConfirmReq();
        // Set invalid data that should fail parent validation
        req.setTransKey(""); // This violates NotBlank constraint in parent

        Set<ConstraintViolation<BatchConfirmReq>> violations = validator.validate(req);
        // Should have violations from parent class validation
        assertThat(violations).isNotEmpty();
    }

    @Test
    void testValidRequestPassesValidation() {
        BatchConfirmReq req = new BatchConfirmReq();
        req.setTransKey("validTransKey123");
        req.setConfirmValue("valid-confirm-value");

        Set<ConstraintViolation<BatchConfirmReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void whenTransKeyIsNull_thenValidationFails() {
        BatchConfirmReq req = new BatchConfirmReq();
        req.setTransKey(null);
        req.setConfirmValue("valid-confirm-value");

        Set<ConstraintViolation<BatchConfirmReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasTransKeyViolation = violations.stream()
                .anyMatch(v -> "transKey".equals(v.getPropertyPath().toString()));
        assertThat(hasTransKeyViolation).isTrue();
    }

    @Test
    void whenConfirmValueIsNull_thenValidationFails() {
        BatchConfirmReq req = new BatchConfirmReq();
        req.setTransKey("validTransKey123");
        req.setConfirmValue(null);

        Set<ConstraintViolation<BatchConfirmReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasConfirmValueViolation = violations.stream()
                .anyMatch(v -> "confirmValue".equals(v.getPropertyPath().toString()));
        assertThat(hasConfirmValueViolation).isTrue();
    }

    @Test
    void whenTransKeyExceedsMaxLength_thenValidationFails() {
        BatchConfirmReq req = new BatchConfirmReq();
        req.setTransKey("a".repeat(200)); // Exceeds max trans key length
        req.setConfirmValue("valid-confirm-value");

        Set<ConstraintViolation<BatchConfirmReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasTransKeyViolation = violations.stream()
                .anyMatch(v -> "transKey".equals(v.getPropertyPath().toString()));
        assertThat(hasTransKeyViolation).isTrue();
    }
}
