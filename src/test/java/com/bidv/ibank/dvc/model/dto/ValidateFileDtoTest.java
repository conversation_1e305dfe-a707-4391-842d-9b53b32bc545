package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.util.constant.FileTypeEnum;
import com.bidv.ibank.util.excel.Importable;

class ValidateFileDtoTest {

    // Test implementation of Importable interface for testing
    private static class TestImportable implements Importable {
        private int rownum = 1;
        private int index = 0;

        @Override
        public int getRownum() {
            return rownum;
        }

        @Override
        public void setRownum(int rownum) {
            this.rownum = rownum;
        }

        @Override
        public int getIndex() {
            return index;
        }

        @Override
        public void setIndex(int index) {
            this.index = index;
        }
    }

    @Test
    @SuppressWarnings("unchecked")
    void testBuilderPatternWithAllFields() {
        // Given
        Pattern pattern = Pattern.compile("^[A-Za-z0-9]+$");
        long startRow = 2L;
        long maxItem = 1000L;
        long maxFileSize = 5242880L; // 5MB
        List<FileTypeEnum> fileTypes = List.of(FileTypeEnum.XLSX, FileTypeEnum.XLS);

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .pattern(pattern)
                .startRow(startRow)
                .maxItem(maxItem)
                .maxFileSize(maxFileSize)
                .fileTypes(fileTypes)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(pattern, dto.getPattern());
        assertEquals(startRow, dto.getStartRow());
        assertEquals(maxItem, dto.getMaxItem());
        assertEquals(maxFileSize, dto.getMaxFileSize());
        assertEquals(fileTypes, dto.getFileTypes());
        assertEquals(TestImportable.class, dto.getClazz());
    }

    @Test
    @SuppressWarnings("unchecked")
    void testBuilderPatternWithMinimalFields() {
        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertNotNull(dto);
        assertNull(dto.getPattern());
        assertEquals(0L, dto.getStartRow());
        assertEquals(0L, dto.getMaxItem());
        assertEquals(0L, dto.getMaxFileSize());
        assertNull(dto.getFileTypes());
        assertEquals(TestImportable.class, dto.getClazz());
    }

    @Test
    @SuppressWarnings("unchecked")
    void testPatternValidation() {
        // Given
        Pattern emailPattern = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .pattern(emailPattern)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(emailPattern, dto.getPattern());
        assertTrue(dto.getPattern().matcher("<EMAIL>").matches());
        assertFalse(dto.getPattern().matcher("invalid-email").matches());
    }

    @Test
    @SuppressWarnings("unchecked")
    void testFileTypesValidation() {
        // Given
        List<FileTypeEnum> excelTypes = List.of(FileTypeEnum.XLSX, FileTypeEnum.XLS);

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .fileTypes(excelTypes)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(excelTypes, dto.getFileTypes());
        assertEquals(2, dto.getFileTypes().size());
        assertTrue(dto.getFileTypes().contains(FileTypeEnum.XLSX));
        assertTrue(dto.getFileTypes().contains(FileTypeEnum.XLS));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testLargeNumbers() {
        // Given
        long maxStartRow = Long.MAX_VALUE;
        long maxItem = Long.MAX_VALUE;
        long maxFileSize = Long.MAX_VALUE;

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .startRow(maxStartRow)
                .maxItem(maxItem)
                .maxFileSize(maxFileSize)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(maxStartRow, dto.getStartRow());
        assertEquals(maxItem, dto.getMaxItem());
        assertEquals(maxFileSize, dto.getMaxFileSize());
    }

    @Test
    @SuppressWarnings("unchecked")
    void testZeroValues() {
        // Given
        long startRow = 0L;
        long maxItem = 0L;
        long maxFileSize = 0L;

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .startRow(startRow)
                .maxItem(maxItem)
                .maxFileSize(maxFileSize)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(0L, dto.getStartRow());
        assertEquals(0L, dto.getMaxItem());
        assertEquals(0L, dto.getMaxFileSize());
    }

    @Test
    @SuppressWarnings("unchecked")
    void testEmptyFileTypesList() {
        // Given
        List<FileTypeEnum> emptyFileTypes = Collections.emptyList();

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .fileTypes(emptyFileTypes)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(emptyFileTypes, dto.getFileTypes());
        assertEquals(0, dto.getFileTypes().size());
    }

    @Test
    @SuppressWarnings("unchecked")
    void testSingleFileType() {
        // Given
        List<FileTypeEnum> singleFileType = List.of(FileTypeEnum.XLSX);

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .fileTypes(singleFileType)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(singleFileType, dto.getFileTypes());
        assertEquals(1, dto.getFileTypes().size());
        assertEquals(FileTypeEnum.XLSX, dto.getFileTypes().get(0));
    }

    @Test
    @SuppressWarnings("unchecked")
    void testComplexPattern() {
        // Given
        Pattern complexPattern = Pattern.compile("^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$");

        // When
        ValidateFileDto<TestImportable> dto = ValidateFileDto.<TestImportable>builder()
                .pattern(complexPattern)
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(complexPattern, dto.getPattern());
        assertTrue(dto.getPattern().matcher("Password123!").matches());
        assertFalse(dto.getPattern().matcher("weakpassword").matches());
    }

    @Test
    @SuppressWarnings("unchecked")
    void testGenericTypeHandling() {
        // When
        ValidateFileDto<TestImportable> dto1 = ValidateFileDto.<TestImportable>builder()
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        ValidateFileDto<TestImportable> dto2 = ValidateFileDto.<TestImportable>builder()
                .clazz((Class<TestImportable>) TestImportable.class)
                .build();

        // Then
        assertEquals(TestImportable.class, dto1.getClazz());
        assertEquals(TestImportable.class, dto2.getClazz());
    }
}
