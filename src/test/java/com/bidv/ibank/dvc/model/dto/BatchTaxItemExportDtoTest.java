package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class BatchTaxItemExportDtoTest {

    private BatchTaxItemExportDto batchTaxItemExportDto;

    @BeforeEach
    void setUp() {
        batchTaxItemExportDto = new BatchTaxItemExportDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        BatchTaxItemExportDto dto = new BatchTaxItemExportDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getBatchOrder());
        assertNull(dto.getTaxCode());
        assertNull(dto.getPayerName());
        assertNull(dto.getDeclarationNo());
        assertNull(dto.getDeclarationDate());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String batchOrder = "1";
        String taxCode = "**********";
        String payerName = "Test Company";
        String declarationNo = "***********";
        String declarationDate = "24/05/2024";
        String revAccCode = "R001";
        String amount = "*********";
        String ccy = "VND";
        String transDesc = "Tờ khai thuế GTGT";

        // When
        batchTaxItemExportDto.setBatchOrder(batchOrder);
        batchTaxItemExportDto.setTaxCode(taxCode);
        batchTaxItemExportDto.setPayerName(payerName);
        batchTaxItemExportDto.setDeclarationNo(declarationNo);
        batchTaxItemExportDto.setDeclarationDate(declarationDate);
        batchTaxItemExportDto.setRevAccCode(revAccCode);
        batchTaxItemExportDto.setAmount(amount);
        batchTaxItemExportDto.setCcy(ccy);
        batchTaxItemExportDto.setTransDesc(transDesc);

        // Then
        assertEquals(batchOrder, batchTaxItemExportDto.getBatchOrder());
        assertEquals(taxCode, batchTaxItemExportDto.getTaxCode());
        assertEquals(payerName, batchTaxItemExportDto.getPayerName());
        assertEquals(declarationNo, batchTaxItemExportDto.getDeclarationNo());
        assertEquals(declarationDate, batchTaxItemExportDto.getDeclarationDate());
        assertEquals(revAccCode, batchTaxItemExportDto.getRevAccCode());
        assertEquals(amount, batchTaxItemExportDto.getAmount());
        assertEquals(ccy, batchTaxItemExportDto.getCcy());
        assertEquals(transDesc, batchTaxItemExportDto.getTransDesc());
    }

    @Test
    void testBuilder() {
        // Given
        String batchOrder = "2";
        String taxCode = "3500948286";
        String payerName = "Test Company 2";
        String declarationNo = "30644771633";
        String amount = "*********";

        // When
        BatchTaxItemExportDto dto = BatchTaxItemExportDto.builder()
                .batchOrder(batchOrder)
                .taxCode(taxCode)
                .payerName(payerName)
                .declarationNo(declarationNo)
                .amount(amount)
                .build();

        // Then
        assertEquals(batchOrder, dto.getBatchOrder());
        assertEquals(taxCode, dto.getTaxCode());
        assertEquals(payerName, dto.getPayerName());
        assertEquals(declarationNo, dto.getDeclarationNo());
        assertEquals(amount, dto.getAmount());
    }

    @Test
    void testGetTreasuryCodeName() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return the fallback value (second parameter)
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            batchTaxItemExportDto.setTreasuryCode("T001");
            batchTaxItemExportDto.setTreasuryName("Treasury Name");

            // When
            String result = batchTaxItemExportDto.getTreasuryCodeName();

            // Then
            assertEquals("T001 - Treasury Name", result);
        }
    }

    @Test
    void testGetRevAuthCodeName() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return the fallback value (second parameter)
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            batchTaxItemExportDto.setRevAuthCode("RA001");
            batchTaxItemExportDto.setRevAuthName("Revenue Authority Name");

            // When
            String result = batchTaxItemExportDto.getRevAuthCodeName();

            // Then
            assertEquals("RA001 - Revenue Authority Name", result);
        }
    }

    @Test
    void testGetChapterCodeName() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return the fallback value (second parameter)
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            batchTaxItemExportDto.setChapterCode("755");
            batchTaxItemExportDto.setChapterName("Chapter Name");

            // When
            String result = batchTaxItemExportDto.getChapterCodeName();

            // Then
            assertEquals("755 - Chapter Name", result);
        }
    }

    @Test
    void testGetEcCodeName() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return the fallback value (second parameter)
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            batchTaxItemExportDto.setEcCode("3063");
            batchTaxItemExportDto.setEcName("Economic Code Name");

            // When
            String result = batchTaxItemExportDto.getEcCodeName();

            // Then
            assertEquals("3063 - Economic Code Name", result);
        }
    }

    @Test
    void testGetTaxTypeCodeName() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return the fallback value (second parameter)
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            batchTaxItemExportDto.setTaxTypeCode("VA");
            batchTaxItemExportDto.setTaxTypeName("Tax Type Name");

            // When
            String result = batchTaxItemExportDto.getTaxTypeCodeName();

            // Then
            assertEquals("VA - Tax Type Name", result);
        }
    }

    @Test
    void testGetCcCodeName() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return the fallback value (second parameter)
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            batchTaxItemExportDto.setCcCode("11");
            batchTaxItemExportDto.setCcName("CC Code Name");

            // When
            String result = batchTaxItemExportDto.getCcCodeName();

            // Then
            assertEquals("11 - CC Code Name", result);
        }
    }

    @Test
    void testGetEiTypeCodeName() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Mock the Translator to return the fallback value (second parameter)
            mockedTranslator.when(() -> Translator.toLocale(anyString(), anyString()))
                    .thenAnswer(invocation -> invocation.getArgument(1)); // Return fallback value

            // Given
            batchTaxItemExportDto.setEiTypeCode("A11");
            batchTaxItemExportDto.setEiTypeName("Export Import Type Name");

            // When
            String result = batchTaxItemExportDto.getEiTypeCodeName();

            // Then
            assertEquals("A11 - Export Import Type Name", result);
        }
    }

    @Test
    void testGetSeverity() {
        // When
        String severity = batchTaxItemExportDto.getSeverity();

        // Then
        assertEquals("I", severity);
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String batchOrder = "1";
        String taxCode = "**********";
        String payerName = "Test Company";

        BatchTaxItemExportDto dto1 = BatchTaxItemExportDto.builder()
                .batchOrder(batchOrder)
                .taxCode(taxCode)
                .payerName(payerName)
                .build();

        BatchTaxItemExportDto dto2 = BatchTaxItemExportDto.builder()
                .batchOrder(batchOrder)
                .taxCode(taxCode)
                .payerName(payerName)
                .build();

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String batchOrder = "1";
        String taxCode = "**********";
        batchTaxItemExportDto.setBatchOrder(batchOrder);
        batchTaxItemExportDto.setTaxCode(taxCode);

        // When
        String result = batchTaxItemExportDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("batchOrder"));
        assertTrue(result.contains("taxCode"));
    }
}
