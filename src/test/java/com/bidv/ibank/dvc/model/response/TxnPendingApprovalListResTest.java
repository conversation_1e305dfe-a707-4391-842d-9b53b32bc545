package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnPendingApprovalListResTest {

    private TxnPendingApprovalListRes txnPendingApprovalListRes;

    @BeforeEach
    void setUp() {
        txnPendingApprovalListRes = new TxnPendingApprovalListRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnPendingApprovalListRes res = new TxnPendingApprovalListRes();

        // Then
        assertNotNull(res);
        assertNull(res.getRaNote());
        assertNull(res.getApprovalUsers());
        assertNull(res.getTxnType());
        assertNull(res.getTxnItemId());
        assertNull(res.getUpdatedDate());
        assertNull(res.getTxnId());
        assertNull(res.getAmount());
        assertNull(res.getStatus());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String raNote = "Please approve this transaction";
        String approvalUsers = "[\"user1\", \"user2\"]";
        String txnType = "04";
        String txnItemId = "1234567890";
        LocalDateTime updatedDate = LocalDateTime.of(2025, 1, 1, 12, 0, 0);

        // When
        TxnPendingApprovalListRes res = new TxnPendingApprovalListRes(raNote, approvalUsers, txnType, txnItemId, updatedDate);

        // Then
        assertEquals(raNote, res.getRaNote());
        assertEquals(approvalUsers, res.getApprovalUsers());
        assertEquals(txnType, res.getTxnType());
        assertEquals(txnItemId, res.getTxnItemId());
        assertEquals(updatedDate, res.getUpdatedDate());
    }

    @Test
    void testSuperBuilderPattern() {
        // Given
        String raNote = "Approval note";
        String approvalUsers = "[\"admin\", \"manager\"]";
        String txnType = "01";
        String txnItemId = "9876543210";
        LocalDateTime updatedDate = LocalDateTime.now();
        String txnId = "TXN001";
        BigDecimal amount = new BigDecimal("1000000");
        String status = "PENDING_APPROVAL";

        // When
        TxnPendingApprovalListRes res = TxnPendingApprovalListRes.builder()
                .raNote(raNote)
                .approvalUsers(approvalUsers)
                .txnType(txnType)
                .txnItemId(txnItemId)
                .updatedDate(updatedDate)
                .txnId(txnId)
                .amount(amount)
                .status(status)
                .build();

        // Then
        assertEquals(raNote, res.getRaNote());
        assertEquals(approvalUsers, res.getApprovalUsers());
        assertEquals(txnType, res.getTxnType());
        assertEquals(txnItemId, res.getTxnItemId());
        assertEquals(updatedDate, res.getUpdatedDate());
        assertEquals(txnId, res.getTxnId());
        assertEquals(amount, res.getAmount());
        assertEquals(status, res.getStatus());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String raNote = "Transaction approval required";
        String approvalUsers = "[\"supervisor\", \"cfo\"]";
        String txnType = "03";
        String txnItemId = "5555555555";
        LocalDateTime updatedDate = LocalDateTime.of(2025, 5, 29, 14, 30, 0);

        // When
        txnPendingApprovalListRes.setRaNote(raNote);
        txnPendingApprovalListRes.setApprovalUsers(approvalUsers);
        txnPendingApprovalListRes.setTxnType(txnType);
        txnPendingApprovalListRes.setTxnItemId(txnItemId);
        txnPendingApprovalListRes.setUpdatedDate(updatedDate);

        // Then
        assertEquals(raNote, txnPendingApprovalListRes.getRaNote());
        assertEquals(approvalUsers, txnPendingApprovalListRes.getApprovalUsers());
        assertEquals(txnType, txnPendingApprovalListRes.getTxnType());
        assertEquals(txnItemId, txnPendingApprovalListRes.getTxnItemId());
        assertEquals(updatedDate, txnPendingApprovalListRes.getUpdatedDate());
    }

    @Test
    void testGetTxnTypeNameWithTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String txnType = "04";
            String translatedName = "Thuế hải quan";
            String expectedKey = AppConstants.LANGUAGE.TXN_TYPE + "." + txnType;

            txnPendingApprovalListRes.setTxnType(txnType);

            mockedTranslator.when(() -> Translator.toLocale(expectedKey, txnType))
                    .thenReturn(translatedName);

            // When
            String result = txnPendingApprovalListRes.getTxnTypeName();

            // Then
            assertEquals(translatedName, result);
            mockedTranslator.verify(() -> Translator.toLocale(expectedKey, txnType));
        }
    }

    @Test
    void testGetTxnTypeNameWithNullTxnType() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            txnPendingApprovalListRes.setTxnType(null);
            mockedTranslator.when(() -> Translator.toLocale(anyString(), (String) isNull()))
                    .thenReturn("Default Type");

            // When
            String result = txnPendingApprovalListRes.getTxnTypeName();

            // Then
            assertEquals("Default Type", result);
        }
    }

    @Test
    void testInheritanceFromTxnPendingListRes() {
        // Given
        TxnPendingApprovalListRes res = new TxnPendingApprovalListRes();

        // Then
        assertNotNull(res);
        res.setTxnId("TXN123");
        assertEquals("TXN123", res.getTxnId());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String raNote = "Test note";
        String txnType = "01";
        String txnId = "TXN001";

        TxnPendingApprovalListRes res1 = TxnPendingApprovalListRes.builder()
                .raNote(raNote)
                .txnType(txnType)
                .txnId(txnId)
                .build();

        TxnPendingApprovalListRes res2 = TxnPendingApprovalListRes.builder()
                .raNote(raNote)
                .txnType(txnType)
                .txnId(txnId)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String raNote = "Test approval note";
        String txnType = "04";

        txnPendingApprovalListRes.setRaNote(raNote);
        txnPendingApprovalListRes.setTxnType(txnType);
        txnPendingApprovalListRes.setTxnId("TXN001");

        // When
        String result = txnPendingApprovalListRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
