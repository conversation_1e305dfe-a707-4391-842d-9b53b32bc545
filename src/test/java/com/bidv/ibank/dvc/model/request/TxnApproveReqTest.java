package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnApproveReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        TxnApproveReq req = new TxnApproveReq();
        assertThat(req).isNotNull();
        assertThat(req).isInstanceOf(TxnDeleteReq.class);
    }

    @Test
    void testInheritanceFromTxnDeleteReq() {
        TxnApproveReq req = new TxnApproveReq();
        assertThat(req).isInstanceOf(TxnDeleteReq.class);
    }

    @Test
    void testSettersAndGetters() {
        TxnApproveReq req = new TxnApproveReq();
        
        // Test inherited properties from TxnDeleteReq
        List<String> txnIds = Arrays.asList("TXN123456789", "TXN987654321");
        req.setTxnIds(txnIds);

        assertThat(req.getTxnIds()).isEqualTo(txnIds);
        assertThat(req.getTxnIds()).hasSize(2);
        assertThat(req.getTxnIds()).contains("TXN123456789", "TXN987654321");
    }

    @Test
    void testEqualsAndHashCode() {
        List<String> txnIds1 = Arrays.asList("TXN123456789", "TXN987654321");
        List<String> txnIds2 = Arrays.asList("TXN123456789", "TXN987654321");
        List<String> txnIds3 = Arrays.asList("TXN111111111", "TXN222222222");

        TxnApproveReq req1 = new TxnApproveReq();
        req1.setTxnIds(txnIds1);

        TxnApproveReq req2 = new TxnApproveReq();
        req2.setTxnIds(txnIds2);

        TxnApproveReq req3 = new TxnApproveReq();
        req3.setTxnIds(txnIds3);

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testToString() {
        TxnApproveReq req = new TxnApproveReq();
        req.setTxnIds(Arrays.asList("TXN123456789"));

        String toString = req.toString();
        assertThat(toString).contains("TxnApproveReq");
    }

    @Test
    void testValidationInheritsFromParent() {
        // Test validation constraints from parent class
        TxnApproveReq req = new TxnApproveReq();
        // Set invalid data that should fail parent validation
        req.setTxnIds(Collections.emptyList()); // This violates NotEmpty constraint in parent

        Set<ConstraintViolation<TxnApproveReq>> violations = validator.validate(req);
        // Should have violations from parent class validation
        assertThat(violations).isNotEmpty();
    }

    @Test
    void testValidRequestPassesValidation() {
        TxnApproveReq req = new TxnApproveReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339"));

        Set<ConstraintViolation<TxnApproveReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void testApprovalSpecificBehavior() {
        // Test that TxnApproveReq behaves correctly as an approval request
        TxnApproveReq req = new TxnApproveReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339"));

        // Verify it inherits all TxnDeleteReq behavior but is semantically different
        assertThat(req).isInstanceOf(TxnDeleteReq.class);
        assertThat(req.getTxnIds()).contains("DVC01704202411252339");
    }

    @Test
    void whenTxnIdsIsNull_thenValidationFails() {
        TxnApproveReq req = new TxnApproveReq();
        req.setTxnIds(null);

        Set<ConstraintViolation<TxnApproveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasTxnIdsViolation = violations.stream()
                .anyMatch(v -> "txnIds".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdsViolation).isTrue();
    }

    @Test
    void whenTxnIdsContainsBlankString_thenValidationFails() {
        TxnApproveReq req = new TxnApproveReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339", "")); // Contains blank string

        Set<ConstraintViolation<TxnApproveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
    }

    @Test
    void whenTxnIdsContainsInvalidPattern_thenValidationFails() {
        TxnApproveReq req = new TxnApproveReq();
        req.setTxnIds(Arrays.asList("INVALID@#$%")); // Contains invalid characters

        Set<ConstraintViolation<TxnApproveReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
    }

    @Test
    void testMultipleTxnIdsApproval() {
        TxnApproveReq req = new TxnApproveReq();
        List<String> txnIds = Arrays.asList(
            "DVC01704202411252339",
            "DVC01704202411252340",
            "DVC01704202411252341"
        );
        req.setTxnIds(txnIds);

        assertThat(req.getTxnIds()).hasSize(3);
        assertThat(req.getTxnIds()).containsExactlyElementsOf(txnIds);
    }
}
