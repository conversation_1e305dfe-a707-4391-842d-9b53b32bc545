package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnPendingListReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void whenAllFieldsValid_thenNoViolations() {
        TxnPendingListReq req = TxnPendingListReq.builder()
                .search("test search")
                .startDate(LocalDate.now())
                .endDate(LocalDate.now())
                .minAmount(BigDecimal.ONE)
                .maxAmount(BigDecimal.TEN)
                .ccys(Arrays.asList("VND"))
                .statuses(Arrays.asList("PENDING", "APPROVED"))
                .debitAccNo("1200046752")
                .taxCode("9581856")
                .declarationNo("8481929515")
                .batchNo("123456")
                .build();

        Set<ConstraintViolation<TxnPendingListReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void whenDebitAccNoExceedsMaxLength_thenViolation() {
        TxnPendingListReq req = TxnPendingListReq.builder()
                .debitAccNo("123456789012345") // 15 characters, max is 14
                .build();

        Set<ConstraintViolation<TxnPendingListReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("size must be between 0 and 14");
    }

    @Test
    void whenTaxCodeExceedsMaxLength_thenViolation() {
        TxnPendingListReq req = TxnPendingListReq.builder()
                .taxCode("123456789012345678901") // 21 characters, max is 20
                .build();

        Set<ConstraintViolation<TxnPendingListReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("size must be between 0 and 20");
    }

    @Test
    void whenDeclarationNoExceedsMaxLength_thenViolation() {
        TxnPendingListReq req = TxnPendingListReq.builder()
                .declarationNo("1234567890123456789012345678901") // 31 characters, max is 30
                .build();

        Set<ConstraintViolation<TxnPendingListReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("size must be between 0 and 30");
    }

    @Test
    void whenBatchNoExceedsMaxLength_thenViolation() {
        TxnPendingListReq req = TxnPendingListReq.builder()
                .batchNo("123456789012345678901234567890123456789012345678902025052917253357920250529172533579")
                .build();

        Set<ConstraintViolation<TxnPendingListReq>> violations = validator.validate(req);
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getMessage()).isEqualTo("size must be between 0 and 50");
    }

    @Test
    void builderAndGetterSetter_ShouldWorkCorrectly() {
        String search = "test search";
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = LocalDate.now();
        BigDecimal minAmount = BigDecimal.ONE;
        BigDecimal maxAmount = BigDecimal.TEN;
        String ccy = "VND";
        List<String> statuses = Arrays.asList("PENDING", "APPROVED");
        String debitAccNo = "1200046752";
        String taxCode = "9581856";
        String declarationNo = "8481929515";
        String batchNo = "123456";

        // Test setter/getter
        TxnPendingListReq req = new TxnPendingListReq();
        req.setSearch(search);
        req.setStartDate(startDate);
        req.setEndDate(endDate);
        req.setMinAmount(minAmount);
        req.setMaxAmount(maxAmount);
        req.setCcys(Arrays.asList(ccy));
        req.setStatuses(statuses);
        req.setDebitAccNo(debitAccNo);
        req.setTaxCode(taxCode);
        req.setDeclarationNo(declarationNo);
        req.setBatchNo(batchNo);

        assertThat(req.getSearch()).isEqualTo(search);
        assertThat(req.getStartDate()).isEqualTo(startDate);
        assertThat(req.getEndDate()).isEqualTo(endDate);
        assertThat(req.getMinAmount()).isEqualTo(minAmount);
        assertThat(req.getMaxAmount()).isEqualTo(maxAmount);
        assertThat(req.getCcys()).isEqualTo(Arrays.asList(ccy));
        assertThat(req.getStatuses()).isEqualTo(statuses);
        assertThat(req.getDebitAccNo()).isEqualTo(debitAccNo);
        assertThat(req.getTaxCode()).isEqualTo(taxCode);
        assertThat(req.getDeclarationNo()).isEqualTo(declarationNo);
        assertThat(req.getBatchNo()).isEqualTo(batchNo);

        // Test builder
        TxnPendingListReq builderReq = TxnPendingListReq.builder()
                .search(search)
                .startDate(startDate)
                .endDate(endDate)
                .minAmount(minAmount)
                .maxAmount(maxAmount)
                .ccys(Arrays.asList(ccy))
                .statuses(statuses)
                .debitAccNo(debitAccNo)
                .taxCode(taxCode)
                .declarationNo(declarationNo)
                .batchNo(batchNo)
                .build();

        assertThat(builderReq.getSearch()).isEqualTo(search);
        assertThat(builderReq.getStartDate()).isEqualTo(startDate);
        assertThat(builderReq.getEndDate()).isEqualTo(endDate);
        assertThat(builderReq.getMinAmount()).isEqualTo(minAmount);
        assertThat(builderReq.getMaxAmount()).isEqualTo(maxAmount);
        assertThat(builderReq.getCcys()).isEqualTo(Arrays.asList(ccy));
        assertThat(builderReq.getStatuses()).isEqualTo(statuses);
        assertThat(builderReq.getDebitAccNo()).isEqualTo(debitAccNo);
        assertThat(builderReq.getTaxCode()).isEqualTo(taxCode);
        assertThat(builderReq.getDeclarationNo()).isEqualTo(declarationNo);
        assertThat(builderReq.getBatchNo()).isEqualTo(batchNo);
    }

    @Test
    void equalsAndHashCode_ShouldWorkCorrectly() {
        TxnPendingListReq req1 = TxnPendingListReq.builder()
                .search("test")
                .debitAccNo("1200046752")
                .taxCode("9581856")
                .build();

        TxnPendingListReq req2 = TxnPendingListReq.builder()
                .search("test")
                .debitAccNo("1200046752")
                .taxCode("9581856")
                .build();

        TxnPendingListReq req3 = TxnPendingListReq.builder()
                .search("different")
                .debitAccNo("9999999999")
                .taxCode("1111111")
                .build();

        assertThat(req1).isNotEqualTo(req2);
        assertThat(req1.hashCode()).isNotEqualTo(req2.hashCode());
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isNotEqualTo(req3.hashCode());
    }

    @Test
    void whenDateRangeIsValid_thenNoViolations() {
        TxnPendingListReq req = TxnPendingListReq.builder()
                .startDate(LocalDate.now().plusDays(-1))
                .endDate(LocalDate.now())
                .build();

        Set<ConstraintViolation<TxnPendingListReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }

    @Test
    void whenAmountRangeIsValid_thenNoViolations() {
        TxnPendingListReq req = TxnPendingListReq.builder()
                .minAmount(BigDecimal.ONE)
                .maxAmount(BigDecimal.TEN)
                .build();

        Set<ConstraintViolation<TxnPendingListReq>> violations = validator.validate(req);
        assertThat(violations).isEmpty();
    }
}