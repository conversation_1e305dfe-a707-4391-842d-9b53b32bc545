package com.bidv.ibank.dvc.model.response.h2h;

import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;
import com.bidv.ibank.dvc.model.dto.h2h.H2hBatchItemDetailDto;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class H2hBatchDetailResTest {

    @Test
    void testGetTotalValidRecords_NullList() {
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().validItems(null).build();
        assertEquals(0, res.getTotalValidRecords());
    }

    @Test
    void testGetTotalValidRecords_EmptyList() {
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().validItems(new ArrayList<>()).build();
        assertEquals(0, res.getTotalValidRecords());
    }

    @Test
    void testGetTotalValidRecords_WithItems() {
        List<H2hBatchItemDetailDto> validItems = new ArrayList<>();
        validItems.add(new H2hBatchItemDetailDto());
        validItems.add(new H2hBatchItemDetailDto());
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().validItems(validItems).build();
        assertEquals(2, res.getTotalValidRecords());
    }

    @Test
    void testGetTotalInvalidRecords_NullList() {
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().invalidItems(null).build();
        assertEquals(0, res.getTotalInvalidRecords());
    }

    @Test
    void testGetTotalInvalidRecords_EmptyList() {
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().invalidItems(new ArrayList<>()).build();
        assertEquals(0, res.getTotalInvalidRecords());
    }

    @Test
    void testGetTotalInvalidRecords_WithItems() {
        List<BatchItemDetailDto> invalidItems = new ArrayList<>();
        invalidItems.add(new BatchItemDetailDto());
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().invalidItems(invalidItems).build();
        assertEquals(1, res.getTotalInvalidRecords());
    }

    @Test
    void testGetTotalRecords_BothNull() {
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().validItems(null).invalidItems(null).build();
        assertEquals(0, res.getTotalRecords());
    }

    @Test
    void testGetTotalRecords_ValidAndInvalid() {
        List<H2hBatchItemDetailDto> validItems = new ArrayList<>();
        validItems.add(new H2hBatchItemDetailDto());
        List<BatchItemDetailDto> invalidItems = new ArrayList<>();
        invalidItems.add(new BatchItemDetailDto());
        invalidItems.add(new BatchItemDetailDto());
        H2hBatchDetailRes res = H2hBatchDetailRes.builder().validItems(validItems).invalidItems(invalidItems).build();
        assertEquals(3, res.getTotalRecords());
    }
}