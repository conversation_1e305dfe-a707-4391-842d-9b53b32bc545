package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;

import org.junit.jupiter.api.Test;

class ValidateCustomsDutyResTest {

    @Test
    void testBuilder() {
        ValidateCustomsDutyRes res = ValidateCustomsDutyRes.builder()
                .transKey("TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86")
                .feeTotal(BigDecimal.valueOf(10000))
                .feeCcy("VND")
                .feeOpt("Phí khoán")
                .build();

        assertThat(res.getTransKey()).isEqualTo("TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86");
        assertThat(res.getFeeTotal()).isEqualTo(BigDecimal.valueOf(10000));
        assertThat(res.getFeeCcy()).isEqualTo("VND");
        assertThat(res.getFeeOpt()).isEqualTo("Phí khoán");
    }

    @Test
    void testNoArgsConstructor() {
        ValidateCustomsDutyRes res = new ValidateCustomsDutyRes();
        assertThat(res).isNotNull();
        assertThat(res.getTransKey()).isNull();
        assertThat(res.getFeeTotal()).isNull();
        assertThat(res.getFeeCcy()).isNull();
        assertThat(res.getFeeOpt()).isNull();
    }

    @Test
    void testAllArgsConstructor() {
        String transKey = "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86";
        BigDecimal feeTotal = BigDecimal.valueOf(10000);
        String feeCcy = "VND";
        String feeOpt = "Phí khoán";
        BigDecimal amount = BigDecimal.valueOf(1000000);
        String ccy = "VND";

        ValidateCustomsDutyRes res = new ValidateCustomsDutyRes(transKey, feeTotal, feeCcy, feeOpt, amount, ccy);

        assertThat(res.getTransKey()).isEqualTo(transKey);
        assertThat(res.getFeeTotal()).isEqualTo(feeTotal);
        assertThat(res.getFeeCcy()).isEqualTo(feeCcy);
        assertThat(res.getFeeOpt()).isEqualTo(feeOpt);
        assertThat(res.getAmount()).isEqualTo(amount);
        assertThat(res.getCcy()).isEqualTo(ccy);
    }

    @Test
    void testBigDecimalPrecision() {
        BigDecimal feeTotal = new BigDecimal("10000.50");
        ValidateCustomsDutyRes res = ValidateCustomsDutyRes.builder()
                .feeTotal(feeTotal)
                .build();

        assertThat(res.getFeeTotal()).isEqualTo(feeTotal);
        assertThat(res.getFeeTotal().scale()).isEqualTo(2);
    }

    @Test
    void testSettersAndGetters() {
        ValidateCustomsDutyRes res = new ValidateCustomsDutyRes();

        res.setTransKey("TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86");
        res.setFeeTotal(BigDecimal.valueOf(10000));
        res.setFeeCcy("VND");
        res.setFeeOpt("Phí khoán");

        assertThat(res.getTransKey()).isEqualTo("TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86");
        assertThat(res.getFeeTotal()).isEqualTo(BigDecimal.valueOf(10000));
        assertThat(res.getFeeCcy()).isEqualTo("VND");
        assertThat(res.getFeeOpt()).isEqualTo("Phí khoán");
    }
}