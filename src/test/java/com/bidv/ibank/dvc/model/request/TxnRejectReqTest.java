package com.bidv.ibank.dvc.model.request;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;

class TxnRejectReqTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    void testCreateInstance() {
        TxnRejectReq req = new TxnRejectReq();
        assertThat(req).isNotNull();
        assertThat(req).isInstanceOf(TxnRejectReq.class);
    }

    @Test
    void testSettersAndGetters() {
        TxnRejectReq req = new TxnRejectReq();

        // Test txnIds field
        req.setTxnIds(Arrays.asList("TXN123456789", "TXN987654321"));
        req.setApprovalNote("Rejection reason");

        assertThat(req.getTxnIds()).containsExactly("TXN123456789", "TXN987654321");
        assertThat(req.getApprovalNote()).isEqualTo("Rejection reason");
    }

    @Test
    void testEqualsAndHashCode() {
        TxnRejectReq req1 = new TxnRejectReq();
        req1.setTxnIds(Collections.singletonList("TXN123456789"));
        req1.setApprovalNote("Rejection reason");

        TxnRejectReq req2 = new TxnRejectReq();
        req2.setTxnIds(Collections.singletonList("TXN123456789"));
        req2.setApprovalNote("Rejection reason");

        TxnRejectReq req3 = new TxnRejectReq();
        req3.setTxnIds(Collections.singletonList("TXN987654321"));
        req3.setApprovalNote("Different reason");

        assertThat(req1).isEqualTo(req2);
        assertThat(req1).isNotEqualTo(req3);
        assertThat(req1.hashCode()).isEqualTo(req2.hashCode());
    }

    @Test
    void testToString() {
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(Collections.singletonList("TXN123456789"));
        req.setApprovalNote("Rejection reason");

        String toString = req.toString();
        assertThat(toString).contains("TxnRejectReq");
    }

    @Test
    void testValidationConstraints() {
        // Test validation constraints
        TxnRejectReq req = new TxnRejectReq();
        // Set invalid data that should fail validation
        req.setTxnIds(Collections.emptyList()); // Empty list should fail @NotEmpty
        req.setApprovalNote(""); // Empty string should fail @NotBlank

        Set<ConstraintViolation<TxnRejectReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();
    }

    @Test
    void testValidRequestPassesValidation() {
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(Collections.singletonList("DVC01704202411252339"));
        req.setApprovalNote("Valid rejection reason");

        Set<ConstraintViolation<TxnRejectReq>> violations = validator.validate(req);

        // Filter out violations related to txnIds and approvalNote
        boolean hasTxnIdsViolation = violations.stream()
                .anyMatch(v -> "txnIds".equals(v.getPropertyPath().toString()));
        boolean hasApprovalNoteViolation = violations.stream()
                .anyMatch(v -> "approvalNote".equals(v.getPropertyPath().toString()));

        assertThat(hasTxnIdsViolation).isFalse();
        assertThat(hasApprovalNoteViolation).isFalse();
    }

    @Test
    void testRejectSpecificBehavior() {
        // Test that TxnRejectReq behaves correctly as a rejection request
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(Collections.singletonList("DVC01704202411252339"));
        req.setApprovalNote("Transaction rejected due to invalid data");

        assertThat(req.getTxnIds()).containsExactly("DVC01704202411252339");
        assertThat(req.getApprovalNote()).isEqualTo("Transaction rejected due to invalid data");
    }

    @Test
    void testRejectWorkflow() {
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(Collections.singletonList("DVC01704202411252339"));
        req.setApprovalNote("Workflow rejection");

        // Test that the request is properly configured for rejection workflow
        assertThat(req.getTxnIds()).isNotNull();
        assertThat(req.getTxnIds()).isNotEmpty();
        assertThat(req.getApprovalNote()).isNotNull();
        assertThat(req.getApprovalNote()).isNotEmpty();
    }

    @Test
    void testRejectWithValidTxnIds() {
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(Arrays.asList("DVC01704202411252339", "DVC01704202411252340"));
        req.setApprovalNote("Multiple transactions rejected");

        // Test that valid transaction IDs are properly set for rejection
        assertThat(req.getTxnIds()).hasSize(2);
        assertThat(req.getTxnIds()).containsExactly("DVC01704202411252339", "DVC01704202411252340");
        assertThat(req.getApprovalNote()).isNotBlank();
    }

    @Test
    void testNullTxnIdsHandling() {
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(null);
        req.setApprovalNote("Valid reason");

        Set<ConstraintViolation<TxnRejectReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasTxnIdsViolation = violations.stream()
                .anyMatch(v -> "txnIds".equals(v.getPropertyPath().toString()));
        assertThat(hasTxnIdsViolation).isTrue();
    }

    @Test
    void testNullApprovalNoteHandling() {
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(Collections.singletonList("DVC01704202411252339"));
        req.setApprovalNote(null);

        Set<ConstraintViolation<TxnRejectReq>> violations = validator.validate(req);
        assertThat(violations).isNotEmpty();

        boolean hasApprovalNoteViolation = violations.stream()
                .anyMatch(v -> "approvalNote".equals(v.getPropertyPath().toString()));
        assertThat(hasApprovalNoteViolation).isTrue();
    }

    @Test
    void testRejectWithMultipleTransactions() {
        TxnRejectReq req = new TxnRejectReq();
        req.setTxnIds(Arrays.asList("GOV231952", "GOV931164"));
        req.setApprovalNote("Sai thong tin giao dich");

        // Test handling multiple transaction rejections
        assertThat(req.getTxnIds()).hasSize(2);
        assertThat(req.getTxnIds()).containsExactly("GOV231952", "GOV931164");
        assertThat(req.getApprovalNote()).isEqualTo("Sai thong tin giao dich");
    }
}
