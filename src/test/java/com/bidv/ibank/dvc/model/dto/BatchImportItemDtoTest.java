package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Map;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;

class BatchImportItemDtoTest {

    private BatchImportItemDto batchImportItemDto;

    @BeforeEach
    void setUp() {
        batchImportItemDto = BatchImportItemDto.builder()
                .order("1")
                .debitAccNo("********")
                .taxCode("**********")
                .payerName("Test Payer")
                .payerAddr("Test Address")
                .declarationNo("DEC123")
                .declarationDate("08/09/2024")
                .treasuryCode("TR01")
                .revAccCode("REV001")
                .revAuthCode("AUTH001")
                .admAreaCode("ADM1")
                .chapterCode("CH1")
                .ecCode("EC01")
                .amount("1000000")
                .ccy("VND")
                .transDesc("Test Transaction")
                .taxTypeCode("TAX001")
                .ccCode("CC001")
                .eiTypeCode("EI001")
                .payerType("1")
                .build();
    }

    @Test
    void testCompareTo() {
        BatchImportItemDto item1 = BatchImportItemDto.builder().build();
        BatchImportItemDto item2 = BatchImportItemDto.builder().build();

        item1.setRownum(1);
        item2.setRownum(2);

        assertTrue(item1.compareTo(item2) < 0);
        assertTrue(item2.compareTo(item1) > 0);
        assertEquals(0, item1.compareTo(item1));
    }

    @Test
    void testValidate_ValidData() {
        batchImportItemDto.validate();
        assertTrue(batchImportItemDto.isValidRow());
        assertTrue(batchImportItemDto.getFieldErrors().isEmpty());
    }

    @Test
    void testValidate_InvalidData() {
        batchImportItemDto.setDebitAccNo("");
        batchImportItemDto.setAmount("invalid");
        batchImportItemDto.validate();

        assertFalse(batchImportItemDto.isValidRow());
        assertFalse(batchImportItemDto.getFieldErrors().isEmpty());
        assertTrue(batchImportItemDto.getFieldErrors().containsKey(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO));
        assertTrue(batchImportItemDto.getFieldErrors().containsKey(AppConstants.BATCH_FIELD_CODE.AMOUNT));
    }

    @Test
    void testValidate_AltTaxCodeValidation() {
        batchImportItemDto.setAltTaxCode("ALT123");
        batchImportItemDto.validate();

        Map<String, Set<String>> fieldErrors = batchImportItemDto.getFieldErrors();
        assertTrue(fieldErrors.containsKey(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_NAME));
        assertTrue(fieldErrors.containsKey(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_ADDR));
    }

    @Test
    void testValidate_CompleteAltTaxCodeInfo() {
        batchImportItemDto.setAltTaxCode("ALT123");
        batchImportItemDto.setAltPayerName("Alt Payer");
        batchImportItemDto.setAltPayerAddr("Alt Address");
        batchImportItemDto.validate();

        assertTrue(batchImportItemDto.isValidRow());
        assertTrue(batchImportItemDto.getFieldErrors().isEmpty());
    }

    @Test
    void testIsValidDebitAccNoAmountCcyTreasuryRow() {
        assertTrue(batchImportItemDto.isValidDebitAccNoAmountCcyTreasuryRow());

        batchImportItemDto.setDebitAccNo("");
        batchImportItemDto.validate();
        assertFalse(batchImportItemDto.isValidDebitAccNoAmountCcyTreasuryRow());
    }

    @Test
    void testGetFieldValidationStatusAfterValidate() {
        batchImportItemDto.validate();
        BatchValidationStatusDto status = batchImportItemDto.getFieldValidationStatusAfterValidate();

        assertTrue(status.isDebitAccNoValid());
        assertTrue(status.isTaxCodeValid());
        assertTrue(status.isPayerNameValid());
        assertTrue(status.isAmountValid());
        assertTrue(status.isCcyValid());
    }

    @Test
    void testAddFieldError() {
        batchImportItemDto.addFieldError("testField", ResponseCode.TIMEOUT_01.code());

        Map<String, Set<String>> fieldErrors = batchImportItemDto.getFieldErrors();
        assertTrue(fieldErrors.containsKey("testField"));
        assertEquals(ResponseCode.TIMEOUT_01.code(), fieldErrors.get("testField").iterator().next());
    }

    @Test
    void testTccErrorHandling() {
        assertTrue(batchImportItemDto.isValidRow());

        batchImportItemDto.setTccErrCode("ERR001");
        assertFalse(batchImportItemDto.isValidRow());

        batchImportItemDto.setTccErrMsg("Error message");
        assertFalse(batchImportItemDto.isValidRow());
    }

    @Test
    void testRowAndIndexHandling() {
        batchImportItemDto.setRownum(5);
        assertEquals(5, batchImportItemDto.getRownum());

        batchImportItemDto.setIndex(10);
        assertEquals(10, batchImportItemDto.getIndex());
    }
}