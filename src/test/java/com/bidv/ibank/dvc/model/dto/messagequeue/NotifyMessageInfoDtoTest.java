package com.bidv.ibank.dvc.model.dto.messagequeue;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NotifyMessageInfoDtoTest {

    private NotifyMessageInfoDto notifyMessageInfoDto;

    @BeforeEach
    void setUp() {
        notifyMessageInfoDto = new NotifyMessageInfoDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        NotifyMessageInfoDto dto = new NotifyMessageInfoDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getProductCode());
        assertNull(dto.getSubProductCode());
        assertNull(dto.getEventType());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String productCode = "DVC";
        String subProductCode = "TAX_PAYMENT";
        String eventType = "PAYMENT_SUCCESS";

        // When
        NotifyMessageInfoDto dto = new NotifyMessageInfoDto(productCode, subProductCode, eventType);

        // Then
        assertNotNull(dto);
        assertEquals(productCode, dto.getProductCode());
        assertEquals(subProductCode, dto.getSubProductCode());
        assertEquals(eventType, dto.getEventType());
    }

    @Test
    void testBuilder() {
        // Given
        String productCode = "DVC";
        String subProductCode = "CUSTOMS_DUTY";
        String eventType = "BATCH_PROCESSED";

        // When
        NotifyMessageInfoDto dto = NotifyMessageInfoDto.builder()
                .productCode(productCode)
                .subProductCode(subProductCode)
                .eventType(eventType)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(productCode, dto.getProductCode());
        assertEquals(subProductCode, dto.getSubProductCode());
        assertEquals(eventType, dto.getEventType());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String productCode = "GOV";
        String subProductCode = "DECLARATION";
        String eventType = "SUBMISSION_FAILED";

        // When
        notifyMessageInfoDto.setProductCode(productCode);
        notifyMessageInfoDto.setSubProductCode(subProductCode);
        notifyMessageInfoDto.setEventType(eventType);

        // Then
        assertEquals(productCode, notifyMessageInfoDto.getProductCode());
        assertEquals(subProductCode, notifyMessageInfoDto.getSubProductCode());
        assertEquals(eventType, notifyMessageInfoDto.getEventType());
    }

    @Test
    void testSettersAndGettersWithNullValues() {
        // When
        notifyMessageInfoDto.setProductCode(null);
        notifyMessageInfoDto.setSubProductCode(null);
        notifyMessageInfoDto.setEventType(null);

        // Then
        assertNull(notifyMessageInfoDto.getProductCode());
        assertNull(notifyMessageInfoDto.getSubProductCode());
        assertNull(notifyMessageInfoDto.getEventType());
    }

    @Test
    void testSettersAndGettersWithEmptyStrings() {
        // When
        notifyMessageInfoDto.setProductCode("");
        notifyMessageInfoDto.setSubProductCode("");
        notifyMessageInfoDto.setEventType("");

        // Then
        assertEquals("", notifyMessageInfoDto.getProductCode());
        assertEquals("", notifyMessageInfoDto.getSubProductCode());
        assertEquals("", notifyMessageInfoDto.getEventType());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String productCode = "DVC";
        String subProductCode = "TAX_PAYMENT";
        String eventType = "PAYMENT_SUCCESS";

        NotifyMessageInfoDto dto1 = NotifyMessageInfoDto.builder()
                .productCode(productCode)
                .subProductCode(subProductCode)
                .eventType(eventType)
                .build();

        NotifyMessageInfoDto dto2 = NotifyMessageInfoDto.builder()
                .productCode(productCode)
                .subProductCode(subProductCode)
                .eventType(eventType)
                .build();

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        notifyMessageInfoDto.setProductCode("DVC");
        notifyMessageInfoDto.setSubProductCode("BATCH");
        notifyMessageInfoDto.setEventType("COMPLETED");

        // When
        String result = notifyMessageInfoDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("NotifyMessageInfoDto"));
        assertTrue(result.contains("productCode"));
        assertTrue(result.contains("subProductCode"));
        assertTrue(result.contains("eventType"));
    }

    @Test
    void testSerializable() throws Exception {
        // Given
        NotifyMessageInfoDto originalDto = NotifyMessageInfoDto.builder()
                .productCode("DVC")
                .subProductCode("TAX_PAYMENT")
                .eventType("PAYMENT_COMPLETED")
                .build();

        // When - Serialize
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(originalDto);
        oos.close();

        // When - Deserialize
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        NotifyMessageInfoDto deserializedDto = (NotifyMessageInfoDto) ois.readObject();
        ois.close();

        // Then
        assertNotNull(deserializedDto);
        assertEquals(originalDto.getProductCode(), deserializedDto.getProductCode());
        assertEquals(originalDto.getSubProductCode(), deserializedDto.getSubProductCode());
        assertEquals(originalDto.getEventType(), deserializedDto.getEventType());
    }

    @Test
    void testBuilderWithPartialData() {
        // When
        NotifyMessageInfoDto dto = NotifyMessageInfoDto.builder()
                .productCode("DVC")
                .eventType("NOTIFICATION")
                .build();

        // Then
        assertNotNull(dto);
        assertEquals("DVC", dto.getProductCode());
        assertNull(dto.getSubProductCode());
        assertEquals("NOTIFICATION", dto.getEventType());
    }

    @Test
    void testBuilderWithAllNullValues() {
        // When
        NotifyMessageInfoDto dto = NotifyMessageInfoDto.builder()
                .productCode(null)
                .subProductCode(null)
                .eventType(null)
                .build();

        // Then
        assertNotNull(dto);
        assertNull(dto.getProductCode());
        assertNull(dto.getSubProductCode());
        assertNull(dto.getEventType());
    }

    @Test
    void testProductCodeTypes() {
        // Test different product codes
        String[] productCodes = {"DVC", "GOV", "H2H", "BATCH"};
        
        for (String productCode : productCodes) {
            // When
            notifyMessageInfoDto.setProductCode(productCode);
            
            // Then
            assertEquals(productCode, notifyMessageInfoDto.getProductCode());
        }
    }

    @Test
    void testEventTypes() {
        // Test different event types
        String[] eventTypes = {
            "PAYMENT_SUCCESS", 
            "PAYMENT_FAILED", 
            "BATCH_PROCESSED", 
            "VALIDATION_ERROR",
            "SUBMISSION_COMPLETED"
        };
        
        for (String eventType : eventTypes) {
            // When
            notifyMessageInfoDto.setEventType(eventType);
            
            // Then
            assertEquals(eventType, notifyMessageInfoDto.getEventType());
        }
    }
}
