package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDate;

import org.junit.jupiter.api.Test;

class TxnTaxItemDtoTest {

    @Test
    void testBuilder() {
        LocalDate declarationDate = LocalDate.of(2024, 3, 21);
        TxnTaxItemDto dto = TxnTaxItemDto.builder()
                .eiTypeCode("EI001")
                .taxTypeCode("TAX001")
                .ccCode("CC001")
                .chapterCode("CH001")
                .ecCode("EC001")
                .amount("1000000")
                .ccy("VND")
                .declarationDate(declarationDate)
                .declarationNo("DEC123")
                .transDesc("Test Transaction")
                .build();

        assertEquals("EI001", dto.getEiTypeCode());
        assertEquals("TAX001", dto.getTaxTypeCode());
        assertEquals("CC001", dto.getCcCode());
        assertEquals("CH001", dto.getChapterCode());
        assertEquals("EC001", dto.getEcCode());
        assertEquals("1000000", dto.getAmount());
        assertEquals("VND", dto.getCcy());
        assertEquals(declarationDate, dto.getDeclarationDate());
        assertEquals("DEC123", dto.getDeclarationNo());
        assertEquals("Test Transaction", dto.getTransDesc());
    }

    @Test
    void testSettersAndGetters() {
        LocalDate declarationDate = LocalDate.of(2024, 3, 21);
        TxnTaxItemDto dto = new TxnTaxItemDto();

        dto.setEiTypeCode("EI001");
        dto.setTaxTypeCode("TAX001");
        dto.setCcCode("CC001");
        dto.setChapterCode("CH001");
        dto.setEcCode("EC001");
        dto.setAmount("1000000");
        dto.setCcy("VND");
        dto.setDeclarationDate(declarationDate);
        dto.setDeclarationNo("DEC123");
        dto.setTransDesc("Test Transaction");

        assertEquals("EI001", dto.getEiTypeCode());
        assertEquals("TAX001", dto.getTaxTypeCode());
        assertEquals("CC001", dto.getCcCode());
        assertEquals("CH001", dto.getChapterCode());
        assertEquals("EC001", dto.getEcCode());
        assertEquals("1000000", dto.getAmount());
        assertEquals("VND", dto.getCcy());
        assertEquals(declarationDate, dto.getDeclarationDate());
        assertEquals("DEC123", dto.getDeclarationNo());
        assertEquals("Test Transaction", dto.getTransDesc());
    }

    @Test
    void testAllArgsConstructor() {
        LocalDate declarationDate = LocalDate.of(2024, 3, 21);
        TxnTaxItemDto dto = new TxnTaxItemDto(
                "EI001", "TAX001", "CC001", "CH001", "EC001",
                "1000000", "VND", declarationDate, "DEC123", "Test Transaction");

        assertEquals("EI001", dto.getEiTypeCode());
        assertEquals("TAX001", dto.getTaxTypeCode());
        assertEquals("CC001", dto.getCcCode());
        assertEquals("CH001", dto.getChapterCode());
        assertEquals("EC001", dto.getEcCode());
        assertEquals("1000000", dto.getAmount());
        assertEquals("VND", dto.getCcy());
        assertEquals(declarationDate, dto.getDeclarationDate());
        assertEquals("DEC123", dto.getDeclarationNo());
        assertEquals("Test Transaction", dto.getTransDesc());
    }

    @Test
    void testNoArgsConstructor() {
        TxnTaxItemDto dto = new TxnTaxItemDto();

        assertNull(dto.getEiTypeCode());
        assertNull(dto.getTaxTypeCode());
        assertNull(dto.getCcCode());
        assertNull(dto.getChapterCode());
        assertNull(dto.getEcCode());
        assertNull(dto.getAmount());
        assertNull(dto.getCcy());
        assertNull(dto.getDeclarationDate());
        assertNull(dto.getDeclarationNo());
        assertNull(dto.getTransDesc());
    }
}