package com.bidv.ibank.dvc.model.validation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.util.constant.ResponseCode;

class FieldValidatorTest {

    @Test
    void testValidateRequiredField() {
        FieldValidator validator = FieldValidator.builder()
                .fieldName("testField")
                .isRequired(true)
                .requiredErrorCode(ResponseCode.DEBIT_ACCNO_REQUIRED)
                .build();

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_REQUIRED, errors.get(0));

        errors = validator.validate("");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_REQUIRED, errors.get(0));

        errors = validator.validate("   ");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_REQUIRED, errors.get(0));

        errors = validator.validate("value");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testValidateMaxLength() {
        FieldValidator validator = FieldValidator.builder()
                .fieldName("testField")
                .maxLength(5)
                .maxLengthErrorCode(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH)
                .build();

        List<ResponseCode> errors = validator.validate("123456");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH, errors.get(0));

        errors = validator.validate("12345");
        assertTrue(errors.isEmpty());

        errors = validator.validate("1234");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testValidatePattern() {
        FieldValidator validator = FieldValidator.builder()
                .fieldName("testField")
                .pattern("^[0-9]+$")
                .patternErrorCode(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT)
                .build();

        List<ResponseCode> errors = validator.validate("abc");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT, errors.get(0));

        errors = validator.validate("123");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testValidateCustomRules() {
        ValidationRule rule1 = (value, errors) -> {
            if (value.contains("invalid")) {
                errors.add(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT);
            }
        };

        ValidationRule rule2 = (value, errors) -> {
            if (value.length() < 3) {
                errors.add(ResponseCode.DEBIT_ACCNO_REQUIRED);
            }
        };

        FieldValidator validator = FieldValidator.builder()
                .fieldName("testField")
                .rules(List.of(rule1, rule2))
                .build();

        List<ResponseCode> errors = validator.validate("invalid text");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT, errors.get(0));

        errors = validator.validate("ab");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_REQUIRED, errors.get(0));

        errors = validator.validate("valid text");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testValidateMultipleRules() {
        FieldValidator validator = FieldValidator.builder()
                .fieldName("testField")
                .isRequired(true)
                .requiredErrorCode(ResponseCode.DEBIT_ACCNO_REQUIRED)
                .maxLength(5)
                .maxLengthErrorCode(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH)
                .pattern("^[0-9]+$")
                .patternErrorCode(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT)
                .build();

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_REQUIRED, errors.get(0));

        errors = validator.validate("abcdef");
        assertEquals(2, errors.size());
        assertTrue(errors.contains(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH));
        assertTrue(errors.contains(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT));

        errors = validator.validate("12345");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testValidateOptionalField() {
        FieldValidator validator = FieldValidator.builder()
                .fieldName("testField")
                .isRequired(false)
                .maxLength(5)
                .maxLengthErrorCode(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH)
                .pattern("^[0-9]+$")
                .patternErrorCode(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT)
                .build();

        List<ResponseCode> errors = validator.validate(null);
        assertTrue(errors.isEmpty());

        errors = validator.validate("");
        assertTrue(errors.isEmpty());

        errors = validator.validate("   ");
        assertTrue(errors.isEmpty());

        errors = validator.validate("abcdef");
        assertEquals(2, errors.size());
        assertTrue(errors.contains(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH));
        assertTrue(errors.contains(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT));

        errors = validator.validate("12345");
        assertTrue(errors.isEmpty());
    }
}