package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.common.txn.model.dto.TransAuth;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

class TxnInitPushResTest {

    @Nested
    @DisplayName("Builder Tests")
    class BuilderTests {
        @Test
        @DisplayName("builder - Should Create Object With All Fields")
        void builder_ShouldCreateObjectWithAllFields() {
            // Given
            String transKey = "TEST_KEY";
            boolean requireAuth = true;
            TransAuth transAuth = mock(TransAuth.class);

            // When
            TxnInitPushRes response = TxnInitPushRes.builder()
                    .transKey(transKey)
                    .requireAuth(requireAuth)
                    .transAuth(transAuth)
                    .build();

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getTransKey()).isEqualTo(transKey);
            assertThat(response.isRequireAuth()).isTrue();
            assertThat(response.getTransAuth()).isEqualTo(transAuth);
        }

        @Test
        @DisplayName("builder - Should Create Object With Null Fields")
        void builder_ShouldCreateObjectWithNullFields() {
            // When
            TxnInitPushRes response = TxnInitPushRes.builder().build();

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getTransKey()).isNull();
            assertThat(response.isRequireAuth()).isFalse();
            assertThat(response.getTransAuth()).isNull();
        }
    }

    @Nested
    @DisplayName("Constructor Tests")
    class ConstructorTests {
        @Test
        @DisplayName("noArgsConstructor - Should Create Empty Object")
        void noArgsConstructor_ShouldCreateEmptyObject() {
            // When
            TxnInitPushRes response = new TxnInitPushRes();

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getTransKey()).isNull();
            assertThat(response.isRequireAuth()).isFalse();
            assertThat(response.getTransAuth()).isNull();
        }

        @Test
        @DisplayName("allArgsConstructor - Should Create Object With All Fields")
        void allArgsConstructor_ShouldCreateObjectWithAllFields() {
            // Given
            String transKey = "TEST_KEY";
            boolean requireAuth = true;
            TransAuth transAuth = mock(TransAuth.class);

            // When
            TxnInitPushRes response = new TxnInitPushRes(transKey, requireAuth, transAuth);

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getTransKey()).isEqualTo(transKey);
            assertThat(response.isRequireAuth()).isTrue();
            assertThat(response.getTransAuth()).isEqualTo(transAuth);
        }
    }

    @Nested
    @DisplayName("Getter and Setter Tests")
    class GetterAndSetterTests {
        @Test
        @DisplayName("gettersAndSetters - Should Work Correctly")
        void gettersAndSetters_ShouldWorkCorrectly() {
            // Given
            TxnInitPushRes response = new TxnInitPushRes();
            String transKey = "TEST_KEY";
            boolean requireAuth = true;
            TransAuth transAuth = mock(TransAuth.class);

            // When
            response.setTransKey(transKey);
            response.setRequireAuth(requireAuth);
            response.setTransAuth(transAuth);

            // Then
            assertThat(response.getTransKey()).isEqualTo(transKey);
            assertThat(response.isRequireAuth()).isTrue();
            assertThat(response.getTransAuth()).isEqualTo(transAuth);
        }
    }

    @Nested
    @DisplayName("Equals and HashCode Tests")
    class EqualsAndHashCodeTests {
        @Test
        @DisplayName("equals - Should Return True For Equal Objects")
        void equals_ShouldReturnTrueForEqualObjects() {
            // Given
            String transKey = "TEST_KEY";
            boolean requireAuth = true;
            TransAuth transAuth = mock(TransAuth.class);

            TxnInitPushRes response1 = TxnInitPushRes.builder()
                    .transKey(transKey)
                    .requireAuth(requireAuth)
                    .transAuth(transAuth)
                    .build();

            TxnInitPushRes response2 = TxnInitPushRes.builder()
                    .transKey(transKey)
                    .requireAuth(requireAuth)
                    .transAuth(transAuth)
                    .build();

            // Then
            assertThat(response1).isEqualTo(response2);
            assertThat(response1.hashCode()).isEqualTo(response2.hashCode());
        }

        @Test
        @DisplayName("equals - Should Return False For Different Objects")
        void equals_ShouldReturnFalseForDifferentObjects() {
            // Given
            TxnInitPushRes response1 = TxnInitPushRes.builder()
                    .transKey("KEY1")
                    .requireAuth(true)
                    .transAuth(mock(TransAuth.class))
                    .build();

            TxnInitPushRes response2 = TxnInitPushRes.builder()
                    .transKey("KEY2")
                    .requireAuth(false)
                    .transAuth(mock(TransAuth.class))
                    .build();

            // Then
            assertThat(response1).isNotEqualTo(response2);
            assertThat(response1.hashCode()).isNotEqualTo(response2.hashCode());
        }
    }

    @Nested
    @DisplayName("ToString Tests")
    class ToStringTests {
        @Test
        @DisplayName("toString - Should Include All Fields")
        void toString_ShouldIncludeAllFields() {
            // Given
            String transKey = "TEST_KEY";
            boolean requireAuth = true;
            TransAuth transAuth = mock(TransAuth.class);

            TxnInitPushRes response = TxnInitPushRes.builder()
                    .transKey(transKey)
                    .requireAuth(requireAuth)
                    .transAuth(transAuth)
                    .build();

            // When
            String toString = response.toString();

            // Then
            assertThat(toString)
                    .contains("transKey=" + transKey)
                    .contains("requireAuth=" + requireAuth)
                    .contains("transAuth=" + transAuth.toString());
        }
    }
}