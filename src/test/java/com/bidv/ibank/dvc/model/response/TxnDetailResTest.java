package com.bidv.ibank.dvc.model.response;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Locale;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;

class TxnDetailResTest {

    private MockedStatic<Translator> translatorMock;
    private MockedStatic<ReadNumber> readNumberMock;
    private TxnDetailRes txnDetailRes;

    private static final String TXN_ID = "TXN001";
    private static final String DEBIT_ACC_NO = "********";
    private static final String DEBIT_ACC_NAME = "Test Account";
    private static final String TAX_CODE = "TAX001";
    private static final BigDecimal AMOUNT = new BigDecimal("1000.00");
    private static final String CCY = "VND";
    private static final String STATUS = "SUCCESS";
    private static final String TREASURY_CODE = "TR001";
    private static final String TREASURY_NAME = "Treasury 1";
    private static final String TRANSLATED_TREASURY_NAME = "Translated Treasury 1";
    private static final String TRANSLATED_STATUS = "Thành công";
    private static final String AMOUNT_TEXT = "Một nghìn đồng";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        readNumberMock = mockStatic(ReadNumber.class);

        txnDetailRes = TxnDetailRes.builder()
                .txnId(TXN_ID)
                .debitAccNo(DEBIT_ACC_NO)
                .debitAccName(DEBIT_ACC_NAME)
                .taxCode(TAX_CODE)
                .amount(AMOUNT)
                .ccy(CCY)
                .status(STATUS)
                .treasuryCode(TREASURY_CODE)
                .treasuryName(TREASURY_NAME)
                .taxItems(new ArrayList<>())
                .build();

        translatorMock.when(() -> Translator.getLocale()).thenReturn(new Locale("vi", "VN"));
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
        if (readNumberMock != null) {
            readNumberMock.close();
        }
    }

    @Test
    void builder_ShouldCreateInstanceWithAllFields() {
        // Setup mock for treasury name translation
        translatorMock.when(() -> Translator.toLocale(TREASURY_CODE, TREASURY_NAME))
                .thenReturn(TREASURY_NAME);

        assertThat(txnDetailRes).isNotNull();
        assertThat(txnDetailRes.getTxnId()).isEqualTo(TXN_ID);
        assertThat(txnDetailRes.getDebitAccNo()).isEqualTo(DEBIT_ACC_NO);
        assertThat(txnDetailRes.getDebitAccName()).isEqualTo(DEBIT_ACC_NAME);
        assertThat(txnDetailRes.getTaxCode()).isEqualTo(TAX_CODE);
        assertThat(txnDetailRes.getAmount()).isEqualTo(AMOUNT);
        assertThat(txnDetailRes.getCcy()).isEqualTo(CCY);
        assertThat(txnDetailRes.getStatus()).isEqualTo(STATUS);
        assertThat(txnDetailRes.getTreasuryCode()).isEqualTo(TREASURY_CODE);
        assertThat(txnDetailRes.getTreasuryName()).isEqualTo(TREASURY_NAME);
        assertThat(txnDetailRes.getTaxItems()).isNotNull().isEmpty();
    }

    @Test
    void builder_WhenNoFieldsSet_ShouldCreateInstanceWithNullFields() {
        TxnDetailRes emptyDetail = TxnDetailRes.builder().build();

        assertThat(emptyDetail).isNotNull();
        assertThat(emptyDetail.getTxnId()).isNull();
        assertThat(emptyDetail.getDebitAccNo()).isNull();
        assertThat(emptyDetail.getDebitAccName()).isNull();
        assertThat(emptyDetail.getTaxCode()).isNull();
        assertThat(emptyDetail.getAmount()).isNull();
        assertThat(emptyDetail.getCcy()).isNull();
        assertThat(emptyDetail.getStatus()).isNull();
        assertThat(emptyDetail.getTreasuryCode()).isNull();
        assertThat(emptyDetail.getTreasuryName()).isNull();
        assertThat(emptyDetail.getTaxItems()).isNull();
    }

    @Test
    void getTreasuryName_WhenTranslationExists_ShouldReturnTranslatedName() {
        translatorMock.when(() -> Translator.toLocale(TREASURY_CODE, TREASURY_NAME))
                .thenReturn(TRANSLATED_TREASURY_NAME);

        String result = txnDetailRes.getTreasuryName();

        assertThat(result).isEqualTo(TRANSLATED_TREASURY_NAME);
        translatorMock.verify(() -> Translator.toLocale(TREASURY_CODE, TREASURY_NAME));
    }

    @Test
    void getStatusName_WhenTranslationExists_ShouldReturnTranslatedStatus() {
        translatorMock.when(() -> Translator.toLocale("txn.status." + STATUS, STATUS))
                .thenReturn(TRANSLATED_STATUS);

        String result = txnDetailRes.getStatusName();

        assertThat(result).isEqualTo(TRANSLATED_STATUS);
        translatorMock.verify(() -> Translator.toLocale("txn.status." + STATUS, STATUS));
    }

    @Test
    void getAmountText_ShouldReturnFormattedAmount() {
        // Setup mock for locale
        Locale viLocale = new Locale("vi", "VN");
        translatorMock.when(() -> Translator.getLocale()).thenReturn(viLocale);

        // Setup mock for amount formatting
        readNumberMock.when(() -> ReadNumber.formatAmountToText(viLocale.toLanguageTag().toLowerCase(),
                AMOUNT.toString(), CCY))
                .thenReturn(AMOUNT_TEXT);

        String result = txnDetailRes.getAmountText();

        assertThat(result).isEqualTo(AMOUNT_TEXT);
        readNumberMock.verify(() -> ReadNumber.formatAmountToText(viLocale.toLanguageTag().toLowerCase(),
                AMOUNT.toString(), CCY));
    }

    @Test
    void equalsAndHashCode_WhenSameContent_ShouldBeEqual() {
        TxnDetailRes detail1 = TxnDetailRes.builder()
                .txnId(TXN_ID)
                .debitAccNo(DEBIT_ACC_NO)
                .amount(AMOUNT)
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .build();

        TxnDetailRes detail2 = TxnDetailRes.builder()
                .txnId(TXN_ID)
                .debitAccNo(DEBIT_ACC_NO)
                .amount(AMOUNT)
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .build();

        assertThat(detail1).isEqualTo(detail2);
        assertThat(detail1.hashCode()).isEqualTo(detail2.hashCode());
    }

    @Test
    void equalsAndHashCode_WhenDifferentContent_ShouldNotBeEqual() {
        TxnDetailRes detail1 = TxnDetailRes.builder()
                .txnId(TXN_ID)
                .debitAccNo(DEBIT_ACC_NO)
                .amount(AMOUNT)
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .build();

        TxnDetailRes detail2 = TxnDetailRes.builder()
                .txnId("TXN002")
                .debitAccNo("87654321")
                .amount(new BigDecimal("2000.00"))
                .payerType(PayerTypeEnum.BUSINESS.getValue())
                .build();

        assertThat(detail1).isNotEqualTo(detail2);
        assertThat(detail1.hashCode()).isNotEqualTo(detail2.hashCode());
    }
}