package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mockStatic;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnRejectResTest {

    private TxnRejectRes txnRejectRes;

    @BeforeEach
    void setUp() {
        txnRejectRes = new TxnRejectRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnRejectRes res = new TxnRejectRes();

        // Then
        assertNotNull(res);
        assertNull(res.getFailTxns());
        assertNull(res.getTxnId());
        assertNull(res.getTotalAmount());
        assertNull(res.getCcy());
        assertNull(res.getTotal());
        assertNull(res.getTotalSuccess());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        List<TransactionResDetail> failTxns = Arrays.asList(new TransactionResDetail(), new TransactionResDetail());
        String txnId = "GOV0125052700000086";
        BigDecimal totalAmount = new BigDecimal("*********");
        String ccy = "VND";

        // When
        TxnRejectRes res = new TxnRejectRes(failTxns, txnId, totalAmount, ccy);

        // Then
        assertEquals(failTxns, res.getFailTxns());
        assertEquals(txnId, res.getTxnId());
        assertEquals(totalAmount, res.getTotalAmount());
        assertEquals(ccy, res.getCcy());
    }

    @Test
    void testSuperBuilderPattern() {
        // Given
        List<TransactionResDetail> failTxns = Arrays.asList(new TransactionResDetail(), new TransactionResDetail());
        String txnId = "TXN001";
        BigDecimal totalAmount = new BigDecimal("5000000");
        String ccy = "USD";
        Long total = 10L;
        Long totalSuccess = 8L;

        // When
        TxnRejectRes res = TxnRejectRes.builder()
                .failTxns(failTxns)
                .txnId(txnId)
                .totalAmount(totalAmount)
                .ccy(ccy)
                .total(total)
                .totalSuccess(totalSuccess)
                .build();

        // Then
        assertEquals(failTxns, res.getFailTxns());
        assertEquals(txnId, res.getTxnId());
        assertEquals(totalAmount, res.getTotalAmount());
        assertEquals(ccy, res.getCcy());
        assertEquals(total, res.getTotal());
        assertEquals(totalSuccess, res.getTotalSuccess());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        List<TransactionResDetail> failTxns = Arrays.asList(new TransactionResDetail(), new TransactionResDetail());
        String txnId = "REJ123456";
        BigDecimal totalAmount = new BigDecimal("2000000");
        String ccy = "EUR";

        // When
        txnRejectRes.setFailTxns(failTxns);
        txnRejectRes.setTxnId(txnId);
        txnRejectRes.setTotalAmount(totalAmount);
        txnRejectRes.setCcy(ccy);

        // Then
        assertEquals(failTxns, txnRejectRes.getFailTxns());
        assertEquals(txnId, txnRejectRes.getTxnId());
        assertEquals(totalAmount, txnRejectRes.getTotalAmount());
        assertEquals(ccy, txnRejectRes.getCcy());
    }

    @Test
    void testGetTotalAmountTextWithNullAmount() {
        // Given
        txnRejectRes.setTotalAmount(null);
        txnRejectRes.setCcy("VND");

        // When
        String amountText = txnRejectRes.getTotalAmountText();

        // Then
        assertNull(amountText);
    }

    @Test
    void testGetTotalAmountTextWithValidAmount() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            BigDecimal totalAmount = new BigDecimal("1234567890");
            String ccy = "VND";
            txnRejectRes.setTotalAmount(totalAmount);
            txnRejectRes.setCcy(ccy);

            mockedTranslator.when(Translator::getLocale).thenReturn(Locale.forLanguageTag("vi-VN"));

            // When
            String amountText = txnRejectRes.getTotalAmountText();

            // Then
            assertNotNull(amountText);
            assertFalse(amountText.isEmpty());
        }
    }

    @Test
    void testInheritanceFromBaseTotalDto() {
        // Given
        TxnRejectRes res = new TxnRejectRes();

        // Then
        assertNotNull(res);
        res.setTotal(5L);
        assertEquals(5L, res.getTotal());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String txnId = "TXN001";
        BigDecimal totalAmount = new BigDecimal("1000000");

        TxnRejectRes res1 = TxnRejectRes.builder()
                .txnId(txnId)
                .totalAmount(totalAmount)
                .build();

        TxnRejectRes res2 = TxnRejectRes.builder()
                .txnId(txnId)
                .totalAmount(totalAmount)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String txnId = "TXN001";
        BigDecimal totalAmount = new BigDecimal("1000000");

        txnRejectRes.setTxnId(txnId);
        txnRejectRes.setTotalAmount(totalAmount);

        // When
        String result = txnRejectRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
