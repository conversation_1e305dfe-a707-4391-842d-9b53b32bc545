package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmCqthuCodeNameKhobacEntityDtoTest {

    private TccDmCqthuCodeNameKhobacEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmCqthuCodeNameKhobacEntityDto();
        dto.setMaKhobac("KB001");
        dto.setTenKhobac("Test Treasury");
        dto.setMaCqthu("CQ001");
        dto.setTenCqthu("Test Authority");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("KB001", dto.getMaKhobac());
        assertEquals("Test Treasury", dto.getTenKhobac());
        assertEquals("CQ001", dto.getMaCqthu());
        assertEquals("Test Authority", dto.getTenCqthu());
    }

    @Test
    void testGetMaTenKhobac() {
        assertEquals("KB001 - Test Treasury", dto.getMaTenKhobac());
    }

    @Test
    void testGetMaTenCqthu() {
        assertEquals("CQ001 - Test Authority", dto.getMaTenCqthu());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmCqthuCodeNameKhobacEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmCqthuCodeNameKhobacEntityDto dto1 = new TccDmCqthuCodeNameKhobacEntityDto("KB001", "Test Treasury", "CQ001", "Test Authority");
        TccDmCqthuCodeNameKhobacEntityDto dto2 = new TccDmCqthuCodeNameKhobacEntityDto("KB001", "Test Treasury", "CQ001", "Test Authority");
        TccDmCqthuCodeNameKhobacEntityDto dto3 = new TccDmCqthuCodeNameKhobacEntityDto("KB002", "Other Treasury", "CQ002", "Other Authority");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmCqthuCodeNameKhobacEntityDto dto = new TccDmCqthuCodeNameKhobacEntityDto("KB001", "Test Treasury", "CQ001", "Test Authority");
        assertEquals("KB001", dto.getMaKhobac());
        assertEquals("Test Treasury", dto.getTenKhobac());
        assertEquals("CQ001", dto.getMaCqthu());
        assertEquals("Test Authority", dto.getTenCqthu());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmCqthuCodeNameKhobacEntityDto dto = new TccDmCqthuCodeNameKhobacEntityDto();
        assertNull(dto.getMaKhobac());
        assertNull(dto.getTenKhobac());
        assertNull(dto.getMaCqthu());
        assertNull(dto.getTenCqthu());
    }
}