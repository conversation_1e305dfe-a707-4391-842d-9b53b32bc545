package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;

class MappingTreasuryBBCodeDtoTest {

    @Test
    void testBuilder() {
        MappingTreasuryBenBankCodeDto dto = MappingTreasuryBenBankCodeDto.builder()
                .treasuryCode("TR001")
                .benBankCode("BB001")
                .isInBidv(true)
                .debtAccCode("ACC001")
                .build();

        assertEquals("TR001", dto.getTreasuryCode());
        assertEquals("BB001", dto.getBenBankCode());
        assertTrue(dto.isInBidv());
        assertEquals("ACC001", dto.getDebtAccCode());
    }

    @Test
    void testSettersAndGetters() {
        MappingTreasuryBenBankCodeDto dto = new MappingTreasuryBenBankCodeDto();
        dto.setTreasuryCode("TR002");
        dto.setBenBankCode("BB002");
        dto.setInBidv(false);
        dto.setDebtAccCode("ACC002");

        assertEquals("TR002", dto.getTreasuryCode());
        assertEquals("BB002", dto.getBenBankCode());
        assertFalse(dto.isInBidv());
        assertEquals("ACC002", dto.getDebtAccCode());
    }

    @Test
    void testEqualsAndHashCode() {
        MappingTreasuryBenBankCodeDto dto1 = MappingTreasuryBenBankCodeDto.builder()
                .treasuryCode("TR001")
                .benBankCode("BB001")
                .isInBidv(true)
                .debtAccCode("ACC001")
                .build();

        MappingTreasuryBenBankCodeDto dto2 = MappingTreasuryBenBankCodeDto.builder()
                .treasuryCode("TR001")
                .benBankCode("BB001")
                .isInBidv(true)
                .debtAccCode("ACC001")
                .build();

        MappingTreasuryBenBankCodeDto dto3 = MappingTreasuryBenBankCodeDto.builder()
                .treasuryCode("TR002")
                .benBankCode("BB002")
                .isInBidv(false)
                .debtAccCode("ACC002")
                .build();

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }
}