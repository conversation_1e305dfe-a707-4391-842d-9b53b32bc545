package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mockStatic;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TaxTypeResTest {

    private TaxTypeRes taxTypeRes;

    @BeforeEach
    void setUp() {
        taxTypeRes = new TaxTypeRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TaxTypeRes res = new TaxTypeRes();

        // Then
        assertNotNull(res);
        assertNull(res.getTaxTypeCode());
    }

    @Test
    void testAllArgsConstructor() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String taxTypeCode = "VAT";
            String taxTypeName = "Value Added Tax";

            mockedTranslator.when(() -> Translator.toLocale(taxTypeCode, taxTypeName))
                    .thenReturn(taxTypeName);

            // When
            TaxTypeRes res = new TaxTypeRes(taxTypeCode, taxTypeName);

            // Then
            assertEquals(taxTypeCode, res.getTaxTypeCode());
            assertEquals(taxTypeName, res.getTaxTypeName());
        }
    }

    @Test
    void testBuilderPattern() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String taxTypeCode = "CIT";
            String taxTypeName = "Corporate Income Tax";

            mockedTranslator.when(() -> Translator.toLocale(taxTypeCode, taxTypeName))
                    .thenReturn(taxTypeName);

            // When
            TaxTypeRes res = TaxTypeRes.builder()
                    .taxTypeCode(taxTypeCode)
                    .taxTypeName(taxTypeName)
                    .build();

            // Then
            assertEquals(taxTypeCode, res.getTaxTypeCode());
            assertEquals(taxTypeName, res.getTaxTypeName());
        }
    }

    @Test
    void testSettersAndGetters() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String taxTypeCode = "PIT";
            String taxTypeName = "Personal Income Tax";

            mockedTranslator.when(() -> Translator.toLocale(taxTypeCode, taxTypeName))
                    .thenReturn(taxTypeName);

            // When
            taxTypeRes.setTaxTypeCode(taxTypeCode);
            taxTypeRes.setTaxTypeName(taxTypeName);

            // Then
            assertEquals(taxTypeCode, taxTypeRes.getTaxTypeCode());
            assertEquals(taxTypeName, taxTypeRes.getTaxTypeName());
        }
    }

    @Test
    void testGetTaxTypeNameWithTranslation() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String taxTypeCode = "VAT";
            String taxTypeName = "Value Added Tax";
            String translatedName = "Thuế giá trị gia tăng";

            taxTypeRes.setTaxTypeCode(taxTypeCode);
            taxTypeRes.setTaxTypeName(taxTypeName);

            mockedTranslator.when(() -> Translator.toLocale(taxTypeCode, taxTypeName))
                    .thenReturn(translatedName);

            // When
            String result = taxTypeRes.getTaxTypeName();

            // Then
            assertEquals(translatedName, result);
            mockedTranslator.verify(() -> Translator.toLocale(taxTypeCode, taxTypeName));
        }
    }

    @Test
    void testGetTaxTypeNameWithNullValues() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            taxTypeRes.setTaxTypeCode(null);
            taxTypeRes.setTaxTypeName(null);

            // Cast to String to avoid ambiguity between toLocale(String, String) and toLocale(String, Object[])
            mockedTranslator.when(() -> Translator.toLocale((String) null, (String) null))
                    .thenReturn("Default Tax Type");

            // When
            String result = taxTypeRes.getTaxTypeName();

            // Then
            assertEquals("Default Tax Type", result);
        }
    }

    @Test
    void testEqualsAndHashCode() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String taxTypeCode = "VAT";
            String taxTypeName = "Value Added Tax";

            mockedTranslator.when(() -> Translator.toLocale(taxTypeCode, taxTypeName))
                    .thenReturn(taxTypeName);

            TaxTypeRes res1 = TaxTypeRes.builder()
                    .taxTypeCode(taxTypeCode)
                    .taxTypeName(taxTypeName)
                    .build();

            TaxTypeRes res2 = TaxTypeRes.builder()
                    .taxTypeCode(taxTypeCode)
                    .taxTypeName(taxTypeName)
                    .build();

            // Then
            assertEquals(res1, res2);
            assertEquals(res1.hashCode(), res2.hashCode());
        }
    }

    @Test
    void testToString() {
        try (MockedStatic<Translator> mockedTranslator = mockStatic(Translator.class)) {
            // Given
            String taxTypeCode = "VAT";
            String taxTypeName = "Value Added Tax";

            mockedTranslator.when(() -> Translator.toLocale(taxTypeCode, taxTypeName))
                    .thenReturn(taxTypeName);

            taxTypeRes.setTaxTypeCode(taxTypeCode);
            taxTypeRes.setTaxTypeName(taxTypeName);

            // When
            String result = taxTypeRes.toString();

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    @Test
    void testGetTaxTypeCodeOnly() {
        // Given
        String taxTypeCode = "CUSTOMS";

        // When
        taxTypeRes.setTaxTypeCode(taxTypeCode);

        // Then
        assertEquals(taxTypeCode, taxTypeRes.getTaxTypeCode());
        // Note: Not calling getTaxTypeName() here to avoid Translator dependency
    }
}
