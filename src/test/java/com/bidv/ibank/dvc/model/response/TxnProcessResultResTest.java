package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class TxnProcessResultResTest {

    @Nested
    @DisplayName("Builder Tests")
    class BuilderTests {
        @Test
        @DisplayName("builder - Should Create Object With All Fields")
        void builder_ShouldCreateObjectWithAllFields() {
            // Given
            String treasuryCode = "TC001";
            String admAreaCode = "AA001";
            String revAccCode = "RA001";
            String revAuthCode = "AUTH001";
            String treasuryName = "Treasury Name";
            String admAreaName = "Admin Area Name";
            String revAccName = "Revenue Account Name";
            String revAuthName = "Revenue Authority Name";
            List<TransactionResDetail> failTxns = Arrays.asList(
                    TransactionResDetail.builder().txnId("TXN1").build(),
                    TransactionResDetail.builder().txnId("TXN2").build());
            String txnId = "TXN001";
            BigDecimal totalAmount = new BigDecimal("1000000");
            String ccy = "VND";
            BigDecimal feeTotal = new BigDecimal("10000");
            String feeCcy = "VND";
            String feeOpt = "Fixed Fee";
            LocalDateTime createdDate = LocalDateTime.now();
            String debitAccNo = "********";
            String debitAccName = "Debit Account Name";
            Long total = 10L;
            Long totalSuccess = 8L;

            // When
            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .treasuryCode(treasuryCode)
                    .admAreaCode(admAreaCode)
                    .revAccCode(revAccCode)
                    .revAuthCode(revAuthCode)
                    .treasuryName(treasuryName)
                    .admAreaName(admAreaName)
                    .revAccName(revAccName)
                    .revAuthName(revAuthName)
                    .failTxns(failTxns)
                    .txnId(txnId)
                    .totalAmount(totalAmount)
                    .ccy(ccy)
                    .feeTotal(feeTotal)
                    .feeCcy(feeCcy)
                    .feeOpt(feeOpt)
                    .createdDate(createdDate)
                    .debitAccNo(debitAccNo)
                    .debitAccName(debitAccName)
                    .total(total)
                    .totalSuccess(totalSuccess)
                    .build();

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getTreasuryCode()).isEqualTo(treasuryCode);
            assertThat(response.getAdmAreaCode()).isEqualTo(admAreaCode);
            assertThat(response.getRevAccCode()).isEqualTo(revAccCode);
            assertThat(response.getRevAuthCode()).isEqualTo(revAuthCode);
            assertThat(response.getFailTxns()).isEqualTo(failTxns);
            assertThat(response.getTxnId()).isEqualTo(txnId);
            assertThat(response.getTotalAmount()).isEqualTo(totalAmount);
            assertThat(response.getCcy()).isEqualTo(ccy);
            assertThat(response.getFeeTotal()).isEqualTo(feeTotal);
            assertThat(response.getFeeCcy()).isEqualTo(feeCcy);
            assertThat(response.getFeeOpt()).isEqualTo(feeOpt);
            assertThat(response.getCreatedDate()).isEqualTo(createdDate);
            assertThat(response.getDebitAccNo()).isEqualTo(debitAccNo);
            assertThat(response.getDebitAccName()).isEqualTo(debitAccName);
            assertThat(response.getTotal()).isEqualTo(total);
            assertThat(response.getTotalSuccess()).isEqualTo(totalSuccess);
        }

        @Test
        @DisplayName("builder - Should Create Object With Null Fields")
        void builder_ShouldCreateObjectWithNullFields() {
            // When
            TxnProcessResultRes response = TxnProcessResultRes.builder().build();

            // Then
            assertThat(response).isNotNull();
            assertThat(response.getTreasuryCode()).isNull();
            assertThat(response.getAdmAreaCode()).isNull();
            assertThat(response.getRevAccCode()).isNull();
            assertThat(response.getRevAuthCode()).isNull();
            assertThat(response.getFailTxns()).isNull();
            assertThat(response.getTxnId()).isNull();
            assertThat(response.getTotalAmount()).isNull();
            assertThat(response.getCcy()).isNull();
            assertThat(response.getFeeTotal()).isNull();
            assertThat(response.getFeeCcy()).isNull();
            assertThat(response.getFeeOpt()).isNull();
            assertThat(response.getCreatedDate()).isNull();
            assertThat(response.getDebitAccNo()).isNull();
            assertThat(response.getDebitAccName()).isNull();
            assertThat(response.getTotal()).isNull();
            assertThat(response.getTotalSuccess()).isNull();
        }
    }

    @Nested
    @DisplayName("Calculated Fields Tests")
    class CalculatedFieldsTests {
        @Test
        @DisplayName("getTotalFail - Should Calculate Correctly")
        void getTotalFail_ShouldCalculateCorrectly() {
            // Given
            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .total(10L)
                    .totalSuccess(7L)
                    .build();

            // When
            Long totalFail = response.getTotalFail();

            // Then
            assertThat(totalFail).isEqualTo(3L);
        }

        @Test
        @DisplayName("getTotalAmountText - Should Return Null When Amount Is Null")
        void getTotalAmountText_ShouldReturnNullWhenAmountIsNull() {
            // Given
            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .totalAmount(null)
                    .ccy("VND")
                    .build();

            // When
            String amountText = response.getTotalAmountText();

            // Then
            assertThat(amountText).isNull();
        }

        @Test
        @DisplayName("getTotalAmountText - Should Format Amount To Text")
        void getTotalAmountText_ShouldFormatAmountToText() {
            // Given
            String expectedText = "một triệu đồng";
            Locale viLocale = new Locale("vi", "VN");
            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .totalAmount(new BigDecimal("1000000"))
                    .ccy("VND")
                    .build();

            try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class);
                    MockedStatic<ReadNumber> readNumberMock = mockStatic(ReadNumber.class)) {

                translatorMock.when(Translator::getLocale).thenReturn(viLocale);
                readNumberMock.when(() -> ReadNumber.formatAmountToText(
                        eq(viLocale.toString().toLowerCase()),
                        eq("1000000"),
                        eq("VND")))
                        .thenReturn(expectedText);

                // When
                String amountText = response.getTotalAmountText();

                // Then
                assertThat(amountText).isEqualTo(expectedText);
            }
        }
    }

    @Nested
    @DisplayName("Translation Tests")
    class TranslationTests {
        @Test
        @DisplayName("getTreasuryName - Should Translate Correctly")
        void getTreasuryName_ShouldTranslateCorrectly() {
            // Given
            String treasuryCode = "TC001";
            String treasuryName = "Treasury Name";
            String translatedName = "Translated Treasury Name";

            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .treasuryCode(treasuryCode)
                    .treasuryName(treasuryName)
                    .build();

            try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class)) {
                translatorMock.when(() -> Translator.toLocale(treasuryCode, treasuryName))
                        .thenReturn(translatedName);

                // When
                String result = response.getTreasuryName();

                // Then
                assertThat(result).isEqualTo(translatedName);
            }
        }

        @Test
        @DisplayName("getAdmAreaName - Should Translate Correctly")
        void getAdmAreaName_ShouldTranslateCorrectly() {
            // Given
            String admAreaCode = "AA001";
            String admAreaName = "Admin Area Name";
            String translatedName = "Translated Admin Area Name";

            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .admAreaCode(admAreaCode)
                    .admAreaName(admAreaName)
                    .build();

            try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class)) {
                translatorMock.when(() -> Translator.toLocale(admAreaCode, admAreaName))
                        .thenReturn(translatedName);

                // When
                String result = response.getAdmAreaName();

                // Then
                assertThat(result).isEqualTo(translatedName);
            }
        }

        @Test
        @DisplayName("getRevAccName - Should Translate Correctly")
        void getRevAccName_ShouldTranslateCorrectly() {
            // Given
            String revAccCode = "RA001";
            String revAccName = "Revenue Account Name";
            String translatedName = "Translated Revenue Account Name";

            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .revAccCode(revAccCode)
                    .revAccName(revAccName)
                    .build();

            try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class)) {
                translatorMock.when(() -> Translator.toLocale(revAccCode, revAccName))
                        .thenReturn(translatedName);

                // When
                String result = response.getRevAccName();

                // Then
                assertThat(result).isEqualTo(translatedName);
            }
        }

        @Test
        @DisplayName("getRevAuthName - Should Translate Correctly")
        void getRevAuthName_ShouldTranslateCorrectly() {
            // Given
            String revAuthCode = "AUTH001";
            String revAuthName = "Revenue Authority Name";
            String translatedName = "Translated Revenue Authority Name";

            TxnProcessResultRes response = TxnProcessResultRes.builder()
                    .revAuthCode(revAuthCode)
                    .revAuthName(revAuthName)
                    .build();

            try (MockedStatic<Translator> translatorMock = mockStatic(Translator.class)) {
                translatorMock.when(() -> Translator.toLocale(revAuthCode, revAuthName))
                        .thenReturn(translatedName);

                // When
                String result = response.getRevAuthName();

                // Then
                assertThat(result).isEqualTo(translatedName);
            }
        }
    }
}