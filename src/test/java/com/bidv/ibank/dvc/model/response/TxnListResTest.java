package com.bidv.ibank.dvc.model.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TxnListResTest {

    private TxnListRes txnListRes;

    @BeforeEach
    void setUp() {
        txnListRes = new TxnListRes();
    }

    @Test
    void testDefaultConstructor() {
        // When
        TxnListRes res = new TxnListRes();

        // Then
        assertNotNull(res);
        assertNull(res.getApprovalUsers());
        assertNull(res.getCreatedBy());
        assertNull(res.getTxnId());
        assertNull(res.getAmount());
        assertNull(res.getStatus());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String approvalUsers = "user1,user2,user3";
        String createdBy = "testUser";

        // When
        TxnListRes res = new TxnListRes(approvalUsers, createdBy);

        // Then
        assertEquals(approvalUsers, res.getApprovalUsers());
        assertEquals(createdBy, res.getCreatedBy());
    }

    @Test
    void testSuperBuilderPattern() {
        // Given
        String approvalUsers = "approver1,approver2";
        String createdBy = "creator123";
        String txnId = "TXN001";
        BigDecimal amount = new BigDecimal("1000000");
        String status = "PENDING";
        String batchNo = "BATCH001";
        LocalDateTime createdDate = LocalDateTime.of(2025, 1, 1, 12, 0, 0);

        // When
        TxnListRes res = TxnListRes.builder()
                .approvalUsers(approvalUsers)
                .createdBy(createdBy)
                .txnId(txnId)
                .amount(amount)
                .status(status)
                .batchNo(batchNo)
                .createdDate(createdDate)
                .build();

        // Then
        assertEquals(approvalUsers, res.getApprovalUsers());
        assertEquals(createdBy, res.getCreatedBy());
        assertEquals(txnId, res.getTxnId());
        assertEquals(amount, res.getAmount());
        assertEquals(status, res.getStatus());
        assertEquals(batchNo, res.getBatchNo());
        assertEquals(createdDate, res.getCreatedDate());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        String approvalUsers = "admin,manager";
        String createdBy = "employee001";

        // When
        txnListRes.setApprovalUsers(approvalUsers);
        txnListRes.setCreatedBy(createdBy);

        // Then
        assertEquals(approvalUsers, txnListRes.getApprovalUsers());
        assertEquals(createdBy, txnListRes.getCreatedBy());
    }

    @Test
    void testInheritanceFromTxnPendingListRes() {
        // Given
        TxnListRes res = new TxnListRes();

        // Then
        assertNotNull(res);
        res.setTxnId("TXN123");
        assertEquals("TXN123", res.getTxnId());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        String approvalUsers = "user1,user2";
        String createdBy = "creator";
        String txnId = "TXN001";

        TxnListRes res1 = TxnListRes.builder()
                .approvalUsers(approvalUsers)
                .createdBy(createdBy)
                .txnId(txnId)
                .build();

        TxnListRes res2 = TxnListRes.builder()
                .approvalUsers(approvalUsers)
                .createdBy(createdBy)
                .txnId(txnId)
                .build();

        // Then
        assertEquals(res1, res2);
        assertEquals(res1.hashCode(), res2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        String approvalUsers = "user1,user2";
        String createdBy = "creator";

        txnListRes.setApprovalUsers(approvalUsers);
        txnListRes.setCreatedBy(createdBy);
        txnListRes.setTxnId("TXN001");

        // When
        String result = txnListRes.toString();

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
