package com.bidv.ibank.dvc.model.dto.messagequeue;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NotifyMessageAdditionalInfoDtoTest {

    private NotifyMessageAdditionalInfoDto notifyMessageAdditionalInfoDto;

    @BeforeEach
    void setUp() {
        notifyMessageAdditionalInfoDto = new NotifyMessageAdditionalInfoDto();
    }

    @Test
    void testDefaultConstructor() {
        // When
        NotifyMessageAdditionalInfoDto dto = new NotifyMessageAdditionalInfoDto();

        // Then
        assertNotNull(dto);
        assertNull(dto.getParams());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("key1", "value1");
        params.put("key2", "value2");

        // When
        NotifyMessageAdditionalInfoDto dto = new NotifyMessageAdditionalInfoDto(params);

        // Then
        assertNotNull(dto);
        assertEquals(params, dto.getParams());
        assertEquals("value1", dto.getParams().get("key1"));
        assertEquals("value2", dto.getParams().get("key2"));
    }

    @Test
    void testBuilder() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("productCode", "DVC");
        params.put("transactionId", "TXN123");

        // When
        NotifyMessageAdditionalInfoDto dto = NotifyMessageAdditionalInfoDto.builder()
                .params(params)
                .build();

        // Then
        assertNotNull(dto);
        assertEquals(params, dto.getParams());
        assertEquals("DVC", dto.getParams().get("productCode"));
        assertEquals("TXN123", dto.getParams().get("transactionId"));
    }

    @Test
    void testSettersAndGetters() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("userId", "user123");
        params.put("amount", "1000000");
        params.put("currency", "VND");

        // When
        notifyMessageAdditionalInfoDto.setParams(params);

        // Then
        assertEquals(params, notifyMessageAdditionalInfoDto.getParams());
        assertEquals("user123", notifyMessageAdditionalInfoDto.getParams().get("userId"));
        assertEquals("1000000", notifyMessageAdditionalInfoDto.getParams().get("amount"));
        assertEquals("VND", notifyMessageAdditionalInfoDto.getParams().get("currency"));
    }

    @Test
    void testSettersAndGettersWithNullValues() {
        // When
        notifyMessageAdditionalInfoDto.setParams(null);

        // Then
        assertNull(notifyMessageAdditionalInfoDto.getParams());
    }

    @Test
    void testSettersAndGettersWithEmptyMap() {
        // Given
        Map<String, String> emptyParams = new HashMap<>();

        // When
        notifyMessageAdditionalInfoDto.setParams(emptyParams);

        // Then
        assertNotNull(notifyMessageAdditionalInfoDto.getParams());
        assertTrue(notifyMessageAdditionalInfoDto.getParams().isEmpty());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        Map<String, String> params1 = new HashMap<>();
        params1.put("key1", "value1");
        params1.put("key2", "value2");

        Map<String, String> params2 = new HashMap<>();
        params2.put("key1", "value1");
        params2.put("key2", "value2");

        NotifyMessageAdditionalInfoDto dto1 = NotifyMessageAdditionalInfoDto.builder()
                .params(params1)
                .build();

        NotifyMessageAdditionalInfoDto dto2 = NotifyMessageAdditionalInfoDto.builder()
                .params(params2)
                .build();

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testToString() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("testKey", "testValue");
        notifyMessageAdditionalInfoDto.setParams(params);

        // When
        String result = notifyMessageAdditionalInfoDto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("NotifyMessageAdditionalInfoDto"));
        assertTrue(result.contains("params"));
    }

    @Test
    void testSerializable() throws Exception {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("serialKey", "serialValue");
        params.put("testData", "testValue");
        
        NotifyMessageAdditionalInfoDto originalDto = NotifyMessageAdditionalInfoDto.builder()
                .params(params)
                .build();

        // When
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(originalDto);
        oos.close();

        // When
        ByteArrayInputStream bais = new ByteArrayInputStream(baos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bais);
        NotifyMessageAdditionalInfoDto deserializedDto = (NotifyMessageAdditionalInfoDto) ois.readObject();
        ois.close();

        // Then
        assertNotNull(deserializedDto);
        assertEquals(originalDto.getParams(), deserializedDto.getParams());
        assertEquals("serialValue", deserializedDto.getParams().get("serialKey"));
        assertEquals("testValue", deserializedDto.getParams().get("testData"));
    }

    @Test
    void testBuilderWithNullParams() {
        // When
        NotifyMessageAdditionalInfoDto dto = NotifyMessageAdditionalInfoDto.builder()
                .params(null)
                .build();

        // Then
        assertNotNull(dto);
        assertNull(dto.getParams());
    }

    @Test
    void testModifyParams() {
        // Given
        Map<String, String> params = new HashMap<>();
        params.put("initialKey", "initialValue");
        notifyMessageAdditionalInfoDto.setParams(params);

        // When
        notifyMessageAdditionalInfoDto.getParams().put("newKey", "newValue");
        notifyMessageAdditionalInfoDto.getParams().put("initialKey", "modifiedValue");

        // Then
        assertEquals("modifiedValue", notifyMessageAdditionalInfoDto.getParams().get("initialKey"));
        assertEquals("newValue", notifyMessageAdditionalInfoDto.getParams().get("newKey"));
        assertEquals(2, notifyMessageAdditionalInfoDto.getParams().size());
    }
}
