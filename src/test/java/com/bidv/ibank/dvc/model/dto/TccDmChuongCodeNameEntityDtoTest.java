package com.bidv.ibank.dvc.model.dto;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TccDmChuongCodeNameEntityDtoTest {

    private TccDmChuongCodeNameEntityDto dto;

    @BeforeEach
    void setUp() {
        dto = new TccDmChuongCodeNameEntityDto();
        dto.setMaChuong("CH001");
        dto.setTenChuong("Test Chapter");
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("CH001", dto.getMaChuong());
        assertEquals("Test Chapter", dto.getTenChuong());
    }

    @Test
    void testGetMaTenChuong() {
        assertEquals("CH001 - Test Chapter", dto.getMaTenChuong());
    }

    @Test
    void testGetSeverity() {
        assertEquals(TccDmChuongCodeNameEntityDto.SEVERITY_INFO, dto.getSeverity());
    }

    @Test
    void testEqualsAndHashCode() {
        TccDmChuongCodeNameEntityDto dto1 = new TccDmChuongCodeNameEntityDto("CH001", "Test Chapter");
        TccDmChuongCodeNameEntityDto dto2 = new TccDmChuongCodeNameEntityDto("CH001", "Test Chapter");
        TccDmChuongCodeNameEntityDto dto3 = new TccDmChuongCodeNameEntityDto("CH002", "Other Chapter");

        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testAllArgsConstructor() {
        TccDmChuongCodeNameEntityDto dto = new TccDmChuongCodeNameEntityDto("CH001", "Test Chapter");
        assertEquals("CH001", dto.getMaChuong());
        assertEquals("Test Chapter", dto.getTenChuong());
    }

    @Test
    void testNoArgsConstructor() {
        TccDmChuongCodeNameEntityDto dto = new TccDmChuongCodeNameEntityDto();
        assertNull(dto.getMaChuong());
        assertNull(dto.getTenChuong());
    }
}