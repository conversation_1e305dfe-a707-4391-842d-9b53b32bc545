package com.bidv.ibank.dvc.specification;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.anyChar;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.junit.jupiter.api.Assertions;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class BatchSpecificationsTest {

    @Mock
    private Root<GOVPaymentBatchEntity> root;

    @Mock
    private CriteriaQuery<GOVPaymentBatchEntity> query;

    @Mock
    private CriteriaBuilder cb;

    @Test
    void searchLike_WithValidSearch() {
        // Given
        String search = "test";
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        Expression<String> expression = mock(Expression.class);
        Predicate likePredicate = mock(Predicate.class);

        when(root.<String>get(anyString())).thenReturn(path);
        when(path.as(String.class)).thenReturn(path);
        when(cb.lower(path)).thenReturn(expression);
        when(cb.like(any(Expression.class), anyString(), anyChar())).thenReturn(likePredicate);

        Predicate orPredicate = mock(Predicate.class);
        doReturn(orPredicate).when(cb).or(any(Predicate[].class));

        // When
        Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.searchLike(search);
        spec.toPredicate(root, query, cb);

        // Then
        verify(root).<String>get("name");
        verify(root).<String>get("batchNo");
        verify(cb, times(2)).like(any(Expression.class), eq("%" + search.toLowerCase() + "%"), eq('\\'));
        verify(cb).or(any(Predicate[].class));
    }

    @Test
    void batchNoLike_WithValidBatchNo() {
        // Given
        String batchNo = "BDR123";
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        Expression<String> expression = mock(Expression.class);
        Predicate likePredicate = mock(Predicate.class);

        when(root.<String>get("batchNo")).thenReturn(path);
        when(path.as(String.class)).thenReturn(path);
        when(cb.lower(path)).thenReturn(expression);
        when(cb.like(any(Expression.class), anyString(), anyChar())).thenReturn(likePredicate);

        // When
        Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.batchNoLike(batchNo);
        Predicate result = spec.toPredicate(root, query, cb);

        // Then
        verify(root).<String>get("batchNo");
        verify(cb).like(any(Expression.class), eq("%" + batchNo.toLowerCase() + "%"), eq('\\'));
        assertThat(result).isEqualTo(likePredicate);
    }

    @Test
    void batchNameLike_WithValidBatchName() {
        // Given
        String batchName = "Test Batch";
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        Expression<String> expression = mock(Expression.class);
        Predicate likePredicate = mock(Predicate.class);

        when(root.<String>get("name")).thenReturn(path);
        when(path.as(String.class)).thenReturn(path);
        when(cb.lower(path)).thenReturn(expression);
        when(cb.like(any(Expression.class), anyString(), anyChar())).thenReturn(likePredicate);

        // When
        Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.batchNameLike(batchName);
        Predicate result = spec.toPredicate(root, query, cb);

        // Then
        verify(root).<String>get("name");
        verify(cb).like(any(Expression.class), eq("%" + batchName.toLowerCase() + "%"), eq('\\'));
        assertThat(result).isEqualTo(likePredicate);
    }

    @Test
    void statusIn_WithValidStatuses() {
        // Given
        List<String> statuses = Arrays.asList("PROCESSING", "PROCESSED");
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        when(root.<String>get("status")).thenReturn(path);
        when(path.in(any(Collection.class))).thenReturn(mock(Predicate.class));

        // When
        Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.statusIn(statuses);
        spec.toPredicate(root, query, cb);

        // Then
        verify(path).in(statuses);
    }

    @Test
    void createBatchListSpec_WithAllFields() {
        // Given
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        BatchListReq request = BatchListReq.builder()
                .search("test")
                .startDate(startDate)
                .endDate(endDate)
                .batchName("Test Batch")
                .statuses(List.of("PROCESSING"))
                .batchNo("BDR123")
                .build();

        // Mock CurrentUser
        try (MockedStatic<AuthenticationUtils> authUtilsMock = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            when(mockUser.getUser()).thenReturn(mockUserInfo);
            authUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

            @SuppressWarnings("unchecked")
            Path<String> stringPath = (Path<String>) mock(Path.class);
            @SuppressWarnings("unchecked")
            Path<LocalDateTime> dateTimePath = (Path<LocalDateTime>) mock(Path.class);
            Expression<String> expression = mock(Expression.class);
            Predicate searchPredicate = mock(Predicate.class);
            Predicate datePredicate = mock(Predicate.class);
            Predicate statusPredicate = mock(Predicate.class);
            Predicate batchNoPredicate = mock(Predicate.class);
            Predicate batchNamePredicate = mock(Predicate.class);
            Predicate finalPredicate = mock(Predicate.class);

            // Mock path and expression setup
            when(root.get(anyString())).thenAnswer(invocation -> {
                String field = invocation.getArgument(0);
                if ("createdDate".equals(field))
                    return dateTimePath;
                if ("name".equals(field) || "batchNo".equals(field) || "status".equals(field))
                    return stringPath;
                // Add more fields as needed for your test
                return mock(Path.class); // fallback for any other field
            });

            when(stringPath.as(String.class)).thenReturn(stringPath);
            when(cb.lower(stringPath)).thenReturn(expression);

            // Mock search criteria (name OR batchNo LIKE search)
            when(cb.like(any(Expression.class), eq("%" + request.getSearch().toLowerCase() + "%"), eq('\\'))).thenReturn(searchPredicate);
            when(cb.or(any(Predicate[].class))).thenReturn(searchPredicate);

            // Mock LIKE criteria for batchName and batchNo
            when(cb.like(any(Expression.class), eq("%" + request.getBatchName().toLowerCase() + "%"), eq('\\'))).thenReturn(batchNamePredicate);
            when(cb.like(any(Expression.class), eq("%" + request.getBatchNo().toLowerCase() + "%"), eq('\\'))).thenReturn(batchNoPredicate);

            // Mock status criteria
            when(stringPath.in(eq(request.getStatuses()))).thenReturn(statusPredicate);

            // Mock date range criteria
            when(cb.greaterThanOrEqualTo(
                    eq(dateTimePath),
                    eq(request.getStartDate().atStartOfDay()))).thenReturn(datePredicate);
            when(cb.lessThanOrEqualTo(
                    eq(dateTimePath),
                    eq(request.getEndDate().atTime(LocalTime.MAX)))).thenReturn(datePredicate);

            // Mock final AND combination
            when(cb.and(any(Predicate.class), any(Predicate.class))).thenReturn(finalPredicate);

            // When
            Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.createBatchListSpec(request, BatchTypeEnum.PAYMENT);
            Predicate result = spec.toPredicate(root, query, cb);

            // Then
            // Verify search criteria (called twice - once for name, once for batchNo)
            verify(cb, times(2)).like(any(Expression.class), eq("%" + request.getSearch().toLowerCase() + "%"), eq('\\'));
            verify(cb).or(any(Predicate[].class));

            // Verify LIKE matches for batchName and batchNo
            verify(cb).like(any(Expression.class), eq("%" + request.getBatchName().toLowerCase() + "%"), eq('\\'));
            verify(cb).like(any(Expression.class), eq("%" + request.getBatchNo().toLowerCase() + "%"), eq('\\'));

            // Verify status criteria
            verify(stringPath).in(eq(request.getStatuses()));

            // Verify date range criteria
            verify(cb).greaterThanOrEqualTo(eq(dateTimePath), eq(request.getStartDate().atStartOfDay()));
            verify(cb).lessThanOrEqualTo(eq(dateTimePath), eq(request.getEndDate().atTime(LocalTime.MAX)));

            // Verify all criteria are combined with AND
            verify(cb, times(5)).and(any(Predicate.class), any(Predicate.class));

            // Verify final result
            assertThat(result).isEqualTo(finalPredicate);
        }
    }

    @Test
    void createBatchListSpec_WithNullValues() {
        // Given
        BatchListReq request = new BatchListReq();
        Predicate conjunctionPredicate = mock(Predicate.class);
        when(cb.conjunction()).thenReturn(conjunctionPredicate);
        when(cb.and(any(Predicate.class), any(Predicate.class))).thenReturn(conjunctionPredicate);

        // Use lenient mocking for this test
        Mockito.lenient().when(cb.equal(any(), any())).thenReturn(conjunctionPredicate);

        // Mock CurrentUser
        try (MockedStatic<AuthenticationUtils> authUtilsMock = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            when(mockUser.getUser()).thenReturn(mockUserInfo);
            authUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

            // Mock paths
            @SuppressWarnings("unchecked")
            Path<String> stringPath = (Path<String>) mock(Path.class);
            @SuppressWarnings("unchecked")
            Path<String> statusPath = (Path<String>) mock(Path.class);
            @SuppressWarnings("unchecked")
            Path<Long> longPath = (Path<Long>) mock(Path.class);

            // Setup basic path mocks
            when(root.<Long>get("cusId")).thenReturn(longPath);
            when(root.<String>get("batchType")).thenReturn(stringPath);
            when(root.<String>get("status")).thenReturn(statusPath);

            // Mock user info
            when(mockUserInfo.getCusId()).thenReturn(0L);

            // Setup path operations
            when(statusPath.in(any(Collection.class))).thenReturn(conjunctionPredicate);
            when(cb.not(any(Predicate.class))).thenReturn(conjunctionPredicate);

            // When
            Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.createBatchListSpec(request, BatchTypeEnum.PAYMENT);
            Predicate result = spec.toPredicate(root, query, cb);

            // Then
            verify(cb, atLeastOnce()).conjunction();
            verify(cb, atLeastOnce()).and(any(Predicate.class), any(Predicate.class));

            // The final result should be the conjunction predicate
            assertThat(result).isEqualTo(conjunctionPredicate);
        }
    }

    @Test
    void createResultSpec_WithValidBatchNo() {
        // Given
        String batchNo = "BDR20250529172533579";
        String createdBy = "testUser";

        // Mock paths
        @SuppressWarnings("unchecked")
        Path<String> stringPath = (Path<String>) mock(Path.class);

        // Mock predicates
        Predicate batchNoPredicate = mock(Predicate.class);
        Predicate statusPredicate = mock(Predicate.class);
        Predicate createdByPredicate = mock(Predicate.class);
        Predicate finalPredicate = mock(Predicate.class);

        // Mock AuthenticationUtils
        try (MockedStatic<AuthenticationUtils> authUtilsMock = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockUser = mock(CurrentUser.class);
            UserInfo userInfo = mock(UserInfo.class);
            when(userInfo.getUsername()).thenReturn(createdBy);
            when(mockUser.getUser()).thenReturn(userInfo);
            authUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

            // Setup mocks
            when(root.<String>get("batchNo")).thenReturn(stringPath);
            when(root.<String>get("status")).thenReturn(stringPath);
            when(root.<String>get("createdBy")).thenReturn(stringPath);

            when(cb.equal(stringPath, batchNo)).thenReturn(batchNoPredicate);
            when(stringPath.in(Arrays.asList(BatchStatusEnum.PROCESSED.name(), BatchStatusEnum.CHECKED.name())))
                    .thenReturn(statusPredicate);
            when(cb.equal(stringPath, createdBy)).thenReturn(createdByPredicate);
            when(cb.and(any(Predicate.class), any(Predicate.class))).thenReturn(finalPredicate);

            // When
            Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.createResultSpec(batchNo, BatchTypeEnum.PAYMENT);
            Predicate result = spec.toPredicate(root, query, cb);

            // Then
            verify(cb).equal(stringPath, batchNo);
            verify(stringPath).in(Arrays.asList(BatchStatusEnum.PROCESSED.name(), BatchStatusEnum.CHECKED.name()));
            verify(cb).equal(stringPath, createdBy);
            verify(cb, times(2)).and(any(Predicate.class), any(Predicate.class));
            assertThat(result).isEqualTo(finalPredicate);
        }
    }

    @Test
    void createResultSpec_WithNullBatchNo() {
        // Given
        String createdBy = "testUser";

        // Mock paths
        @SuppressWarnings("unchecked")
        Path<String> stringPath = (Path<String>) mock(Path.class);

        // Mock predicates
        Predicate statusPredicate = mock(Predicate.class);
        Predicate createdByPredicate = mock(Predicate.class);
        Predicate finalPredicate = mock(Predicate.class);

        // Mock AuthenticationUtils
        try (MockedStatic<AuthenticationUtils> authUtilsMock = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockUser = mock(CurrentUser.class);
            UserInfo userInfo = mock(UserInfo.class);
            when(userInfo.getUsername()).thenReturn(createdBy);
            when(mockUser.getUser()).thenReturn(userInfo);
            authUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

            // Setup mocks
            when(root.<String>get("status")).thenReturn(stringPath);
            when(root.<String>get("createdBy")).thenReturn(stringPath);

            when(stringPath.in(Arrays.asList(BatchStatusEnum.PROCESSED.name(), BatchStatusEnum.CHECKED.name())))
                    .thenReturn(statusPredicate);
            when(cb.equal(stringPath, createdBy)).thenReturn(createdByPredicate);
            when(cb.and(any(Predicate.class), any(Predicate.class))).thenReturn(finalPredicate);

            // When
            Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.createResultSpec(null, BatchTypeEnum.PAYMENT);
            Predicate result = spec.toPredicate(root, query, cb);

            // Then
            verify(stringPath).in(Arrays.asList(BatchStatusEnum.PROCESSED.name(), BatchStatusEnum.CHECKED.name()));
            verify(cb).equal(stringPath, createdBy);
            verify(cb).and(any(Predicate.class), any(Predicate.class));
            assertThat(result).isEqualTo(finalPredicate);
        }
    }

    @Test
    void createBatchTaxListSpec_WithValidRequest() {
        // Given
        BatchListReq request = BatchListReq.builder()
                .search("test")
                .startDate(LocalDate.now().minusDays(7))
                .endDate(LocalDate.now())
                .batchName("Test Batch")
                .statuses(List.of("PROCESSING"))
                .batchNo("BDR123")
                .build();

        String createdBy = "testUser";

        // Mock paths
        @SuppressWarnings("unchecked")
        Path<String> stringPath = (Path<String>) mock(Path.class);
        @SuppressWarnings("unchecked")
        Path<Long> longPath = (Path<Long>) mock(Path.class);
        @SuppressWarnings("unchecked")
        Path<LocalDateTime> dateTimePath = (Path<LocalDateTime>) mock(Path.class);
        Expression<String> expression = mock(Expression.class);

        // Mock predicates
        Predicate searchPredicate = mock(Predicate.class);
        Predicate datePredicate = mock(Predicate.class);
        Predicate statusPredicate = mock(Predicate.class);
        Predicate batchNoPredicate = mock(Predicate.class);
        Predicate batchNamePredicate = mock(Predicate.class);
        Predicate statusNotInPredicate = mock(Predicate.class);
        Predicate finalPredicate = mock(Predicate.class);

        // Use lenient stubbing for this test
        Mockito.lenient().when(cb.equal(any(), any())).thenReturn(finalPredicate);

        // Mock AuthenticationUtils
        try (MockedStatic<AuthenticationUtils> authUtilsMock = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockUser = mock(CurrentUser.class);
            UserInfo userInfo = mock(UserInfo.class);
            when(userInfo.getUsername()).thenReturn(createdBy);
            when(userInfo.getCusId()).thenReturn(0L);
            when(mockUser.getUser()).thenReturn(userInfo);
            authUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

            // Setup mocks for base list spec
            when(root.<Long>get(eq("cusId"))).thenReturn(longPath);
            when(root.<String>get(eq("name"))).thenReturn(stringPath);
            when(root.<String>get(eq("batchNo"))).thenReturn(stringPath);
            when(root.<String>get(eq("status"))).thenReturn(stringPath);
            when(root.<String>get(eq("batchType"))).thenReturn(stringPath);
            when(root.<String>get(eq("createdBy"))).thenReturn(stringPath);
            when(root.<LocalDateTime>get(eq("createdDate"))).thenReturn(dateTimePath);
            when(stringPath.as(String.class)).thenReturn(stringPath);
            when(cb.lower(stringPath)).thenReturn(expression);

            // Mock search criteria
            when(cb.like(any(Expression.class), eq("%" + request.getSearch().toLowerCase() + "%"), eq('\\'))).thenReturn(searchPredicate);
            when(cb.or(any(Predicate[].class))).thenReturn(searchPredicate);

            // Mock date criteria
            when(cb.greaterThanOrEqualTo(
                    eq(dateTimePath),
                    eq(request.getStartDate().atStartOfDay()))).thenReturn(datePredicate);
            when(cb.lessThanOrEqualTo(
                    eq(dateTimePath),
                    eq(request.getEndDate().atTime(LocalTime.MAX)))).thenReturn(datePredicate);

            // Mock status criteria
            when(stringPath.in(eq(request.getStatuses()))).thenReturn(statusPredicate);

            // Mock batch name and number criteria
            when(cb.like(any(Expression.class), eq("%" + request.getBatchName().toLowerCase() + "%"), eq('\\'))).thenReturn(batchNamePredicate);
            when(cb.like(any(Expression.class), eq("%" + request.getBatchNo().toLowerCase() + "%"), eq('\\'))).thenReturn(batchNoPredicate);

            // Mock status not in criteria
            when(stringPath.in(eq(List.of(BatchStatusEnum.DELETED.name())))).thenReturn(statusNotInPredicate);
            when(cb.not(statusNotInPredicate)).thenReturn(statusNotInPredicate);

            // Mock final AND combinations
            when(cb.and(any(Predicate.class), any(Predicate.class))).thenReturn(finalPredicate);

            // When
            Specification<GOVPaymentBatchEntity> spec = BatchSpecifications.createBatchListSpec(request, BatchTypeEnum.INQUIRY);
            Predicate result = spec.toPredicate(root, query, cb);

            // Then
            // Verify base list spec predicates
            verify(cb, times(2)).like(any(Expression.class), eq("%" + request.getSearch().toLowerCase() + "%"), eq('\\'));
            verify(cb).or(any(Predicate[].class));
            verify(cb).greaterThanOrEqualTo(eq(dateTimePath), eq(request.getStartDate().atStartOfDay()));
            verify(cb).lessThanOrEqualTo(eq(dateTimePath), eq(request.getEndDate().atTime(LocalTime.MAX)));
            verify(stringPath).in(eq(request.getStatuses()));
            verify(cb).like(any(Expression.class), eq("%" + request.getBatchName().toLowerCase() + "%"), eq('\\'));
            verify(cb).like(any(Expression.class), eq("%" + request.getBatchNo().toLowerCase() + "%"), eq('\\'));

            // Verify additional tax list spec predicates
            verify(stringPath).in(eq(List.of(BatchStatusEnum.DELETED.name())));
            verify(cb).not(statusNotInPredicate);

            // Verify all predicates are combined with AND
            verify(cb, atLeast(6)).and(any(Predicate.class), any(Predicate.class));

            assertThat(result).isEqualTo(finalPredicate);
        }
    }

    @Test
    void testBatchNoEq() {
        Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.batchNoEq("batchNo");
        assertNotNull(spec);
    }

    @Test
    void testCusIdEq() {
        Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.cusIdEq(1L);
        assertNotNull(spec);
    }

    @Test
    void testStatusNotIn() {
        Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.statusNotIn(List.of("A", "B"));
        assertNotNull(spec);
    }

    @Test
    void testChecksumEq() {
        Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.checksumEq("checksum");
        assertNotNull(spec);
    }

    @Test
    void testBatchTypeEq() {
        Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.batchTypeEq("PAYMENT");
        assertNotNull(spec);
    }

    @Test
    void testBatchNameEq() {
        Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.batchNameEq("batchName");
        assertNotNull(spec);
    }

    @Test
    void testCifNoEq() {
        Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.cifNoEq("cif");
        assertNotNull(spec);
    }

    @Test
    void testCheckFileExistSpec() {
        org.springframework.web.multipart.MultipartFile file = mock(org.springframework.web.multipart.MultipartFile.class);
        when(file.getOriginalFilename()).thenReturn("test.xlsx");
        try (org.mockito.MockedStatic<com.bidv.ibank.external.microservice.user.AuthenticationUtils> utils = org.mockito.Mockito.mockStatic(com.bidv.ibank.external.microservice.user.AuthenticationUtils.class)) {
            com.bidv.ibank.external.microservice.user.CurrentUser user = mock(com.bidv.ibank.external.microservice.user.CurrentUser.class);
            com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo userInfo = mock(com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo.class);
            when(user.getUser()).thenReturn(userInfo);
            utils.when(com.bidv.ibank.external.microservice.user.AuthenticationUtils::getCurrentUser).thenReturn(user);
            when(userInfo.getCif()).thenReturn("cif");
            Specification<com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity> spec = BatchSpecifications.checkFileExistSpec(file, "checksum", com.bidv.ibank.dvc.util.constant.BatchTypeEnum.PAYMENT);
            assertNotNull(spec);
        }
    }
}