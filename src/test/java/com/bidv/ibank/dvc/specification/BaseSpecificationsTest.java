package com.bidv.ibank.dvc.specification;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class BaseSpecificationsTest {

    @Mock
    private Root<TestEntity> root;

    @Mock
    private CriteriaQuery<TestEntity> query;

    @Mock
    private CriteriaBuilder cb;

    @Test
    void createdByEq_WithValidUsername() {
        // Given
        String username = "testUser";
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        when(root.<String>get("createdBy")).thenReturn(path);
        when(cb.equal(path, username)).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.createdByEq(username);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).equal(path, username);
    }

    @Test
    void createdByEq_WithBlankUsername() {
        // Given
        String username = "";

        // When
        Specification<TestEntity> spec = BaseSpecifications.createdByEq(username);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).conjunction();
        verifyNoMoreInteractions(root);
    }

    @Test
    void idEq_WithValidId() {
        // Given
        String id = "123";
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        when(root.<String>get("id")).thenReturn(path);
        when(cb.equal(path, id)).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.idEq(id);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).equal(path, id);
    }

    @Test
    void idIn_WithValidIds() {
        // Given
        List<String> ids = Arrays.asList("1", "2", "3");
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        when(root.<String>get("id")).thenReturn(path);
        when(path.in(ids)).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.idIn(ids);
        spec.toPredicate(root, query, cb);

        // Then
        verify(path).in(ids);
    }

    @Test
    void idIn_WithEmptyList() {
        // Given
        List<String> ids = Collections.emptyList();

        // When
        Specification<TestEntity> spec = BaseSpecifications.idIn(ids);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).conjunction();
        verifyNoMoreInteractions(root);
    }

    @Test
    void startDateGte_WithValidDate() {
        // Given
        LocalDate startDate = LocalDate.now();
        @SuppressWarnings("unchecked")
        Path<LocalDateTime> path = (Path<LocalDateTime>) mock(Path.class);
        when(root.<LocalDateTime>get("createdDate")).thenReturn(path);
        when(cb.greaterThanOrEqualTo(path, startDate.atStartOfDay())).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.startDateGte(startDate);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).greaterThanOrEqualTo(eq(path), eq(startDate.atStartOfDay()));
    }

    @Test
    void endDateLte_WithValidDate() {
        // Given
        LocalDate endDate = LocalDate.now();
        @SuppressWarnings("unchecked")
        Path<LocalDateTime> path = (Path<LocalDateTime>) mock(Path.class);
        when(root.<LocalDateTime>get("createdDate")).thenReturn(path);
        when(cb.lessThanOrEqualTo(path, endDate.atTime(LocalTime.MAX))).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.endDateLte(endDate);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).lessThanOrEqualTo(eq(path), eq(endDate.atTime(LocalTime.MAX)));
    }

    @Test
    void columnLike_WithValidValue() {
        // Given
        String columnName = "testColumn";
        String value = "searchValue";
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        Expression<String> expression = mock(Expression.class);
        
        when(root.<String>get(columnName)).thenReturn(path);
        when(path.as(String.class)).thenReturn(path);
        when(cb.lower(path)).thenReturn(expression);
        when(cb.like(eq(expression), anyString(), eq('\\'))).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.columnLike(columnName, value);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).like(eq(expression), eq("%" + value.toLowerCase() + "%"), eq('\\'));
    }

    @Test
    void columnsLike_WithValidValues() {
        // Given
        List<String> columnNames = Arrays.asList("column1", "column2");
        String value = "searchValue";
        @SuppressWarnings("unchecked")
        Path<String> path = (Path<String>) mock(Path.class);
        Expression<String> expression = mock(Expression.class);
        Predicate likePredicate = mock(Predicate.class);
        
        when(root.<String>get(anyString())).thenReturn(path);
        when(path.as(String.class)).thenReturn(path);
        when(cb.lower(path)).thenReturn(expression);
        when(cb.like(eq(expression), anyString(), eq('\\'))).thenReturn(likePredicate);
        doReturn(mock(Predicate.class)).when(cb).or(any(Predicate[].class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.columnsLike(columnNames, value);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb, times(2)).like(eq(expression), eq("%" + value.toLowerCase() + "%"), eq('\\'));
        verify(cb).or(any(Predicate[].class));
    }

    @Test
    void columnGte_WithValidBigDecimal() {
        // Given
        String columnName = "amount";
        BigDecimal value = new BigDecimal("100.00");
        @SuppressWarnings("unchecked")
        Path<BigDecimal> path = (Path<BigDecimal>) mock(Path.class);
        when(root.<BigDecimal>get(columnName)).thenReturn(path);
        when(cb.greaterThanOrEqualTo(path, value)).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.columnGte(columnName, value);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).greaterThanOrEqualTo(path, value);
    }

    @Test
    void columnLte_WithValidBigDecimal() {
        // Given
        String columnName = "amount";
        BigDecimal value = new BigDecimal("100.00");
        @SuppressWarnings("unchecked")
        Path<BigDecimal> path = (Path<BigDecimal>) mock(Path.class);
        when(root.<BigDecimal>get(columnName)).thenReturn(path);
        when(cb.lessThanOrEqualTo(path, value)).thenReturn(mock(Predicate.class));

        // When
        Specification<TestEntity> spec = BaseSpecifications.columnLte(columnName, value);
        spec.toPredicate(root, query, cb);

        // Then
        verify(cb).lessThanOrEqualTo(path, value);
    }

    @Test
    void columnEq_WithBoolean() {
        @SuppressWarnings("unchecked")
        Path<Object> path = (Path<Object>) mock(Path.class);
        when(root.get("active")).thenReturn(path);
        when(cb.equal(path, true)).thenReturn(mock(Predicate.class));
        Specification<TestEntity> spec = BaseSpecifications.columnEq("active", true);
        spec.toPredicate(root, query, cb);
        verify(cb).equal(path, true);
    }

    @Test
    void columnEq_WithNullBoolean() {
        Specification<TestEntity> spec = BaseSpecifications.columnEq("active", (Boolean) null);
        spec.toPredicate(root, query, cb);
        verify(cb).conjunction();
    }

    @Test
    void columnEq_WithLong() {
        @SuppressWarnings("unchecked")
        Path<Object> path = (Path<Object>) mock(Path.class);
        when(root.get("longId")).thenReturn(path);
        when(cb.equal(path, 123L)).thenReturn(mock(Predicate.class));
        Specification<TestEntity> spec = BaseSpecifications.columnEq("longId", 123L);
        spec.toPredicate(root, query, cb);
        verify(cb).equal(path, 123L);
    }

    @Test
    void columnEq_WithNullLong() {
        Specification<TestEntity> spec = BaseSpecifications.columnEq("longId", (Long) null);
        spec.toPredicate(root, query, cb);
        verify(cb).conjunction();
    }

    @Test
    void columnNotIn_WithValues() {
        List<String> values = Arrays.asList("A", "B");
        @SuppressWarnings("unchecked")
        Path<Object> path = (Path<Object>) mock(Path.class);
        when(root.get("field")).thenReturn(path);
        when(cb.not(path.in(values))).thenReturn(mock(Predicate.class));
        Specification<TestEntity> spec = BaseSpecifications.columnNotIn("field", values);
        spec.toPredicate(root, query, cb);
        verify(cb).not(path.in(values));
    }

    @Test
    void columnNotIn_WithEmptyList() {
        Specification<TestEntity> spec = BaseSpecifications.columnNotIn("field", Collections.emptyList());
        spec.toPredicate(root, query, cb);
        verify(cb).conjunction();
    }

    @Test
    void columnContains_WithValue() {
        String value = "val";
        @SuppressWarnings("unchecked")
        Path<Object> path = (Path<Object>) mock(Path.class);
        Expression<String> expr = mock(Expression.class);
        when(root.get("tags")).thenReturn(path);
        when(path.as(String.class)).thenReturn((Path) path);
        // Use expr directly for cb.lower
        when(cb.lower(any())).thenReturn(expr);
        when(cb.equal(expr, value.toLowerCase())).thenReturn(mock(Predicate.class));
        when(cb.like(expr, value.toLowerCase() + ",%", '\\')).thenReturn(mock(Predicate.class));
        when(cb.like(expr, "%," + value.toLowerCase() + ",%", '\\')).thenReturn(mock(Predicate.class));
        when(cb.like(expr, "%," + value.toLowerCase(), '\\')).thenReturn(mock(Predicate.class));
        when(cb.or(any(Predicate.class), any(Predicate.class), any(Predicate.class), any(Predicate.class))).thenReturn(mock(Predicate.class));
        Specification<TestEntity> spec = BaseSpecifications.columnContains("tags", value);
        spec.toPredicate(root, query, cb);
        verify(cb).or(any(Predicate.class), any(Predicate.class), any(Predicate.class), any(Predicate.class));
    }

    @Test
    void columnContains_WithBlankValue() {
        Specification<TestEntity> spec = BaseSpecifications.columnContains("tags", "");
        spec.toPredicate(root, query, cb);
        verify(cb).conjunction();
    }

    @Test
    @SuppressWarnings("unchecked")
    void columnLike_WithJoin() {
        Join join = mock(Join.class);
        Expression<String> expr = mock(Expression.class);
        when(root.join(eq("relation"), eq(JoinType.LEFT))).thenReturn(join);
        // Ensure join.get("col") returns a non-null Path mock
        Path colPath = mock(Path.class);
        when(join.get("col")).thenReturn(colPath);
        when(colPath.as(String.class)).thenReturn(colPath);
        when(cb.lower(colPath)).thenReturn(expr);
        when(cb.like(expr, "%val%", '\\')).thenReturn(mock(Predicate.class));
        Specification<TestEntity> spec = BaseSpecifications.columnLike("col", "val", "relation", JoinType.LEFT);
        spec.toPredicate(root, query, cb);
        verify(cb).like(expr, "%val%", '\\');
    }

    @Test
    void columnLike_WithJoin_BlankValue() {
        Specification<TestEntity> spec = BaseSpecifications.columnLike("col", "", "relation", JoinType.LEFT);
        spec.toPredicate(root, query, cb);
        verify(cb).conjunction();
    }

    @Test
    void cbConjunction_ReturnsConjunction() {
        Specification<TestEntity> spec = BaseSpecifications.cbConjunction();
        spec.toPredicate(root, query, cb);
        verify(cb).conjunction();
    }

    // columnExists is complex and would require a more advanced mock setup, so we can add a basic invocation test
    @Test
    void columnExists_BasicInvocation() {
        // This test will just ensure the method can be called without error
        Specification<TestEntity> subSpec = BaseSpecifications.createdByEq("user");
        Specification<TestEntity> spec = BaseSpecifications.columnExists(TestEntity.class, "parent", subSpec);
        // We can't fully mock subquery behavior here, but we can check that the method is callable
        assertNotNull(spec);
    }

    // Test entity class for specification testing
    private static class TestEntity {
        private String id;
        private String createdBy;
        private LocalDateTime createdDate;
        private BigDecimal amount;
        // Add getters and setters as needed
    }
}