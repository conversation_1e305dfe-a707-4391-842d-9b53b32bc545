package com.bidv.ibank.dvc.specification;

import static org.junit.jupiter.api.Assertions.*;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;

class TxnItemSpecificationsTest {

    @Test
    void testDeclarationNoLike() {
        Specification<GOVPaymentItemEntity> spec = TxnItemSpecifications.declarationNoLike("decNo");
        assertNotNull(spec);
    }

    @Test
    void testTxnItemIdLike() {
        Specification<GOVPaymentItemEntity> spec = TxnItemSpecifications.txnItemIdLike("txnId");
        assertNotNull(spec);
    }
} 