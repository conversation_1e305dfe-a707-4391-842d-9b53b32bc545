package com.bidv.ibank.dvc.specification;

import static org.junit.jupiter.api.Assertions.*;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateEntity;
import com.bidv.ibank.dvc.model.request.TemplateListReq;
import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;
import java.util.List;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo;

class TemplateSpecificationsTest {

    @Test
    void testSearchLike() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.searchLike("search");
        assertNotNull(spec);
    }

    @Test
    void testCusIdEq() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.cusIdEq(123L);
        assertNotNull(spec);
    }

    @Test
    void testIsPublicEq() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.isPublicEq(true);
        assertNotNull(spec);
    }

    @Test
    void testTreasuryNameLike() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.treasuryNameLike("treasury");
        assertNotNull(spec);
    }

    @Test
    void testRevAuthNameLike() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.revAuthNameLike("auth");
        assertNotNull(spec);
    }

    @Test
    void testCifNoEq() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.cifNoEq("cif123");
        assertNotNull(spec);
    }

    @Test
    void testTemplateNameEq() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.templateNameEq("template");
        assertNotNull(spec);
    }

    @Test
    void testStatusIn() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.statusIn(List.of("ACTIVE", "INACTIVE"));
        assertNotNull(spec);
    }

    @Test
    void testDebitAccNoIn() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.debitAccNoIn(List.of("123", "456"));
        assertNotNull(spec);
    }

    @Test
    void testStatusEq() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.statusEq("ACTIVE");
        assertNotNull(spec);
    }

    @Test
    void testTxnTypeEq() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.txnTypeEq("TXN");
        assertNotNull(spec);
    }

    @Test
    void testCheckExistingTemplatePublic() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.checkExistingTemplate("template", true, "cif123", "user1");
        assertNotNull(spec);
    }

    @Test
    void testCheckExistingTemplatePrivate() {
        Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.checkExistingTemplate("template", false, "cif123", "user1");
        assertNotNull(spec);
    }

    @Test
    void testCreateTxnTemplateListSpec() {
        TemplateListReq req = Mockito.mock(TemplateListReq.class);
        Mockito.when(req.getSearch()).thenReturn("search");
        Mockito.when(req.getTxnType()).thenReturn("TXN");
        // Mock static AuthenticationUtils
        try (MockedStatic<AuthenticationUtils> utils = Mockito.mockStatic(AuthenticationUtils.class)) {
            CurrentUser user = Mockito.mock(CurrentUser.class);
            UserInfo userInfo = Mockito.mock(UserInfo.class);
            Mockito.when(user.getUser()).thenReturn(userInfo);
            utils.when(AuthenticationUtils::getCurrentUser).thenReturn(user);
            Mockito.when(userInfo.getCusId()).thenReturn(1L);
            Specification<GOVPaymentTemplateEntity> spec = TemplateSpecifications.createTxnTemplateListSpec(req);
            assertNotNull(spec);
        }
    }
} 