package com.bidv.ibank.dvc.specification;

import static org.junit.jupiter.api.Assertions.*;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;

class BatchItemSpecificationsTest {

    @Test
    void testBatchIdEq() {
        Specification<GOVPaymentBatchItemEntity> spec = BatchItemSpecifications.batchIdEq("batch123");
        assertNotNull(spec);
    }

    @Test
    void testStatusEq() {
        Specification<GOVPaymentBatchItemEntity> spec = BatchItemSpecifications.statusEq("ACTIVE");
        assertNotNull(spec);
    }
} 