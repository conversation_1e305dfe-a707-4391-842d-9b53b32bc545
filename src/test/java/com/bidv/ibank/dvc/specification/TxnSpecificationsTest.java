package com.bidv.ibank.dvc.specification;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;

@ExtendWith(MockitoExtension.class)
class TxnSpecificationsTest {

    @Test
    void testDebitAccNoEq() {
        String debitAccNo = "*********";
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.debitAccNoEq(debitAccNo);
        assertNotNull(spec);

        // Test with empty value
        spec = TxnSpecifications.debitAccNoEq("");
        assertNotNull(spec);
    }

    @Test
    void testDebitAccNoIn() {
        List<String> debitAccNos = Arrays.asList("123", "456");
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.debitAccNoIn(debitAccNos);
        assertNotNull(spec);

        // Test with empty list
        spec = TxnSpecifications.debitAccNoIn(List.of());
        assertNotNull(spec);
    }

    @Test
    void testTaxCodeEq() {
        String taxCode = "TAX123";
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.taxCodeEq(taxCode);
        assertNotNull(spec);
    }

    @Test
    void testBatchNoEq() {
        String batchNo = "BATCH123";
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.batchNoEq(batchNo);
        assertNotNull(spec);
    }

    @Test
    void testMinAmountGte() {
        BigDecimal minAmount = new BigDecimal("1000.00");
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.minAmountGte(minAmount);
        assertNotNull(spec);

        // Test with null value
        spec = TxnSpecifications.minAmountGte(null);
        assertNotNull(spec);
    }

    @Test
    void testMaxAmountLte() {
        BigDecimal maxAmount = new BigDecimal("5000.00");
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.maxAmountLte(maxAmount);
        assertNotNull(spec);

        // Test with null value
        spec = TxnSpecifications.maxAmountLte(null);
        assertNotNull(spec);
    }

    @Test
    void testStatusIn() {
        List<String> statuses = Arrays.asList(TransactionStatusEnum.INIT.name(), TransactionStatusEnum.REJECTED.name());
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.statusIn(statuses);
        assertNotNull(spec);

        // Test with empty list
        spec = TxnSpecifications.statusIn(List.of());
        assertNotNull(spec);
    }

    @Test
    void testStatusNotIn() {
        List<String> statuses = Arrays.asList(TransactionStatusEnum.INIT.name(), TransactionStatusEnum.REJECTED.name());
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.statusNotIn(statuses);
        assertNotNull(spec);

        // Test with empty list
        spec = TxnSpecifications.statusNotIn(List.of());
        assertNotNull(spec);
    }

    @Test
    void testSearchLike() {
        String search = "test";
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.searchLike(search);
        assertNotNull(spec);

        // Test with empty search
        spec = TxnSpecifications.searchLike("");
        assertNotNull(spec);
    }

    @Test
    void testCreateTxnPendingListSpec() {
        TxnPendingListReq request = mock(TxnPendingListReq.class);
        when(request.getSearch()).thenReturn("test");
        when(request.getStartDate()).thenReturn(LocalDate.now().minusDays(7));
        when(request.getEndDate()).thenReturn(LocalDate.now());
        when(request.getMinAmount()).thenReturn(new BigDecimal("1000.00"));
        when(request.getMaxAmount()).thenReturn(new BigDecimal("5000.00"));
        when(request.getTaxCode()).thenReturn("TAX123");
        when(request.getDeclarationNo()).thenReturn("DEC123");
        when(request.getBatchNo()).thenReturn("BATCH123");
        when(request.getDebitAccNo()).thenReturn("ACC123");
        when(request.getStatuses()).thenReturn(Arrays.asList(TransactionStatusEnum.INIT.name()));

        try (MockedStatic<AuthenticationUtils> mockedAuthUtils = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockCurrentUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            lenient().when(mockUserInfo.getCusId()).thenReturn(123L);
            lenient().when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
            mockedAuthUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);

            // Just verify that the specification can be created without throwing exceptions
            assertDoesNotThrow(() -> {
                Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.createTxnPendingListSpec(request);
                assertNotNull(spec);
            });
        }
    }

    @Test
    void testApprovalUsersLike() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.approvalUsersLike("user");
        assertNotNull(spec);
        spec = TxnSpecifications.approvalUsersLike("");
        assertNotNull(spec);
    }

    @Test
    void testCusIdEq() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.cusIdEq(123L);
        assertNotNull(spec);
        spec = TxnSpecifications.cusIdEq(null);
        assertNotNull(spec);
    }

    @Test
    void testTxnIdIn() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.txnIdIn(Arrays.asList("id1", "id2"));
        assertNotNull(spec);
        spec = TxnSpecifications.txnIdIn(List.of());
        assertNotNull(spec);
    }

    @Test
    void testCcyIn() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.ccyIn(Arrays.asList("VND", "USD"));
        assertNotNull(spec);
        spec = TxnSpecifications.ccyIn(List.of());
        assertNotNull(spec);
    }

    @Test
    void testDebitAccNoLike() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.debitAccNoLike("acc");
        assertNotNull(spec);
        spec = TxnSpecifications.debitAccNoLike("");
        assertNotNull(spec);
    }

    @Test
    void testTaxCodeLike() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.taxCodeLike("tax");
        assertNotNull(spec);
        spec = TxnSpecifications.taxCodeLike("");
        assertNotNull(spec);
    }

    @Test
    void testBatchNoLike() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.batchNoLike("batch");
        assertNotNull(spec);
        spec = TxnSpecifications.batchNoLike("");
        assertNotNull(spec);
    }

    @Test
    void testStatusEq() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.statusEq("INIT");
        assertNotNull(spec);
        spec = TxnSpecifications.statusEq("");
        assertNotNull(spec);
    }

    @Test
    void testTxnTypeIn() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.txnTypeIn(Arrays.asList("PAYMENT", "INQUIRY"));
        assertNotNull(spec);
        spec = TxnSpecifications.txnTypeIn(List.of());
        assertNotNull(spec);
    }

    @Test
    void testCoreRefLike() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.coreRefLike("ref");
        assertNotNull(spec);
        spec = TxnSpecifications.coreRefLike("");
        assertNotNull(spec);
    }

    @Test
    void testChannelIn() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.channelIn(Arrays.asList("IB", "MB"));
        assertNotNull(spec);
        spec = TxnSpecifications.channelIn(List.of());
        assertNotNull(spec);
    }

    @Test
    void testDeclarationNoExists() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.declarationNoExists("dec");
        assertNotNull(spec);
        spec = TxnSpecifications.declarationNoExists("");
        assertNotNull(spec);
    }

    @Test
    void testTxnItemIdExists() {
        Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.txnItemIdExists("item");
        assertNotNull(spec);
        spec = TxnSpecifications.txnItemIdExists("");
        assertNotNull(spec);
    }

    @Test
    void testCreateTxnPendingApprovalListSpec() {
        com.bidv.ibank.dvc.model.request.TxnPendingApprovalListReq req = mock(com.bidv.ibank.dvc.model.request.TxnPendingApprovalListReq.class);
        when(req.getSearch()).thenReturn("search");
        when(req.getStartDate()).thenReturn(LocalDate.now().minusDays(7));
        when(req.getEndDate()).thenReturn(LocalDate.now());
        when(req.getMinAmount()).thenReturn(new BigDecimal("1000.00"));
        when(req.getMaxAmount()).thenReturn(new BigDecimal("5000.00"));
        when(req.getTaxCode()).thenReturn("TAX123");
        when(req.getDeclarationNo()).thenReturn("DEC123");
        when(req.getBatchNo()).thenReturn("BATCH123");
        when(req.getDebitAccNo()).thenReturn("ACC123");
        when(req.getChannels()).thenReturn(Arrays.asList("IB", "MB"));
        when(req.getTxnTypes()).thenReturn(Arrays.asList("PAYMENT"));
        when(req.getTxnItemId()).thenReturn("item");
        try (MockedStatic<AuthenticationUtils> mockedAuthUtils = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockCurrentUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            lenient().when(mockUserInfo.getCusId()).thenReturn(123L);
            lenient().when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
            mockedAuthUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);
            assertDoesNotThrow(() -> {
                Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.createTxnPendingApprovalListSpec(req);
                assertNotNull(spec);
            });
        }
    }

    @Test
    void testCreateTxnListSpec() {
        com.bidv.ibank.dvc.model.request.TxnListReq req = mock(com.bidv.ibank.dvc.model.request.TxnListReq.class);
        when(req.getSearch()).thenReturn("search");
        when(req.getStartDate()).thenReturn(LocalDate.now().minusDays(7));
        when(req.getEndDate()).thenReturn(LocalDate.now());
        when(req.getMinAmount()).thenReturn(new BigDecimal("1000.00"));
        when(req.getMaxAmount()).thenReturn(new BigDecimal("5000.00"));
        when(req.getTaxCode()).thenReturn("TAX123");
        when(req.getDeclarationNo()).thenReturn("DEC123");
        when(req.getBatchNo()).thenReturn("BATCH123");
        when(req.getDebitAccNo()).thenReturn("ACC123");
        when(req.getStatuses()).thenReturn(Arrays.asList(TransactionStatusEnum.INIT.name()));
        when(req.getCcys()).thenReturn(Arrays.asList("VND"));
        try (MockedStatic<AuthenticationUtils> mockedAuthUtils = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockCurrentUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            lenient().when(mockUserInfo.getCusId()).thenReturn(123L);
            lenient().when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
            mockedAuthUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);
            assertDoesNotThrow(() -> {
                Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.createTxnListSpec(req);
                assertNotNull(spec);
            });
        }
    }

    @Test
    void testCreateTxnReportListSpec() {
        com.bidv.ibank.dvc.model.request.TxnReportListReq req = mock(com.bidv.ibank.dvc.model.request.TxnReportListReq.class);
        when(req.getSearch()).thenReturn("search");
        when(req.getStartDate()).thenReturn(LocalDate.now().minusDays(7));
        when(req.getEndDate()).thenReturn(LocalDate.now());
        when(req.getMinAmount()).thenReturn(new BigDecimal("1000.00"));
        when(req.getMaxAmount()).thenReturn(new BigDecimal("5000.00"));
        when(req.getTaxCode()).thenReturn("TAX123");
        when(req.getDeclarationNo()).thenReturn("DEC123");
        when(req.getBatchNo()).thenReturn("BATCH123");
        when(req.getDebitAccNo()).thenReturn("ACC123");
        when(req.getStatuses()).thenReturn(Arrays.asList(TransactionStatusEnum.INIT.name()));
        when(req.getCcys()).thenReturn(Arrays.asList("VND"));
        when(req.getTccRefNo()).thenReturn("ref");
        when(req.getChannels()).thenReturn(Arrays.asList("IB"));
        when(req.getTxnItemId()).thenReturn("item");
        try (MockedStatic<AuthenticationUtils> mockedAuthUtils = mockStatic(AuthenticationUtils.class)) {
            CurrentUser mockCurrentUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            lenient().when(mockUserInfo.getCusId()).thenReturn(123L);
            lenient().when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
            mockedAuthUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);
            assertDoesNotThrow(() -> {
                Specification<GOVPaymentTransactionEntity> spec = TxnSpecifications.createTxnReportListSpec(req);
                assertNotNull(spec);
            });
        }
    }
}