package com.bidv.ibank.dvc.validation;

import static org.junit.jupiter.api.Assertions.*;

import java.time.Year;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.model.validation.FieldValidator;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;

class BatchTaxImportValidatorsTest {

    @Test
    void testGetValidators() {
        Map<String, FieldValidator> validators = BatchTaxImportValidators.getValidators();
        assertNotNull(validators);
        assertEquals(2, validators.size());
    }

    @Test
    void testDeclarationNoValidator() {
        FieldValidator validator = BatchTaxImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.DECLARATION_NO);
        assertNotNull(validator);

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.INQUIRY_DECLARATION_NO_REQUIRED, errors.get(0));

        String longNo = "a".repeat(AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH + 1);
        errors = validator.validate(longNo);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.INQUIRY_DECLARATION_NO_EXCEED_MAX_LENGTH, errors.get(0));

        errors = validator.validate("invalid no with space");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.INQUIRY_DECLARATION_NO_INVALID_FORMAT, errors.get(0));

        errors = validator.validate("ABC123-@&.()/,_"); // valid pattern, within length
        assertTrue(errors.isEmpty());
    }

    @Test
    void testDeclarationYearValidator() {
        FieldValidator validator = BatchTaxImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.DECLARATION_YEAR);
        assertNotNull(validator);

        // Not required, so null/empty is valid
        List<ResponseCode> errors = validator.validate(null);
        assertTrue(errors.isEmpty());
        errors = validator.validate("");
        assertTrue(errors.isEmpty());

        // Invalid pattern
        errors = validator.validate("20A0");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DECLARATION_YEAR_INVALID_FORMAT, errors.get(0));

        // Valid pattern, but future year
        int nextYear = Year.now().getValue() + 1;
        errors = validator.validate(String.valueOf(nextYear));
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DECLARATION_YEAR_INVALID_YEAR, errors.get(0));

        // Valid current year
        int currentYear = Year.now().getValue();
        errors = validator.validate(String.valueOf(currentYear));
        assertTrue(errors.isEmpty());

        // Valid past year
        errors = validator.validate("2000");
        assertTrue(errors.isEmpty());
    }
} 