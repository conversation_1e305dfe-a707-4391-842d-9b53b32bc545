package com.bidv.ibank.dvc.validation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;

import com.bidv.ibank.dvc.model.validation.FieldValidator;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;

class BatchImportValidatorsTest {

    @Test
    void testGetValidators() {
        Map<String, FieldValidator> validators = BatchImportValidators.getValidators();
        assertNotNull(validators);
        assertEquals(23, validators.size());
    }

    @Test
    void testDebitAccNoValidator() {
        FieldValidator validator = BatchImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO);
        assertNotNull(validator);

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_REQUIRED, errors.get(0));

        errors = validator.validate("*********0*********0123"); // 23 chars
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH, errors.get(0));

        errors = validator.validate("ABC123!@#"); // Invalid format
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT, errors.get(0));

        errors = validator.validate("*********");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testTaxCodeValidator() {
        FieldValidator validator = BatchImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.TAX_CODE);
        assertNotNull(validator);

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.TAX_CODE_REQUIRED, errors.get(0));

        errors = validator.validate("*********0*********01"); // 21 chars
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.TAX_CODE_EXCEED_MAX_LENGTH, errors.get(0));

        errors = validator.validate("ABC123!@#"); // Invalid format
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.TAX_CODE_INVALID_FORMAT, errors.get(0));

        errors = validator.validate("0*********");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testPayerNameValidator() {
        FieldValidator validator = BatchImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.PAYER_NAME);
        assertNotNull(validator);

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.PAYER_NAME_REQUIRED, errors.get(0));

        String longName = "a".repeat(141);
        errors = validator.validate(longName);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.PAYER_NAME_EXCEED_MAX_LENGTH, errors.get(0));

        errors = validator.validate("Test Payer");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testAmountValidator() {
        FieldValidator validator = BatchImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.AMOUNT);
        assertNotNull(validator);

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.AMOUNT_REQUIRED, errors.get(0));

        errors = validator.validate("ABC123"); // Invalid format
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.AMOUNT_INVALID_FORMAT, errors.get(0));

        errors = validator.validate("0");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.AMOUNT_CANNOT_BE_ZERO, errors.get(0));

        errors = validator.validate("1000");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testCurrencyValidator() {
        FieldValidator validator = BatchImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.CCY);
        assertNotNull(validator);

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.CCY_REQUIRED, errors.get(0));

        errors = validator.validate("USD");
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.CCY_MUST_BE_VND, errors.get(0));

        errors = validator.validate("VND");
        assertTrue(errors.isEmpty());
    }

    @Test
    void testPayerTypeValidator() {
        FieldValidator validator = BatchImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.PAYER_TYPE);
        assertNotNull(validator);

        List<ResponseCode> errors = validator.validate(null);
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.PAYER_TYPE_REQUIRED, errors.get(0));

        errors = validator.validate("3"); // Invalid value
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.PAYER_TYPE_INVALID_FORMAT, errors.get(0));

        errors = validator.validate("1"); // Business
        assertTrue(errors.isEmpty());

        errors = validator.validate("2"); // Individual
        assertTrue(errors.isEmpty());
    }

    @Test
    void testOptionalFieldValidators() {
        FieldValidator altTaxCodeValidator = BatchImportValidators.getValidators().get(AppConstants.BATCH_FIELD_CODE.ALT_TAX_CODE);
        assertNotNull(altTaxCodeValidator);

        List<ResponseCode> errors = altTaxCodeValidator.validate(null);
        assertTrue(errors.isEmpty());

        errors = altTaxCodeValidator.validate("*********0*********01"); // 21 chars
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.ALT_TAX_CODE_EXCEED_MAX_LENGTH, errors.get(0));

        errors = altTaxCodeValidator.validate("ABC123!@#"); // Invalid format
        assertEquals(1, errors.size());
        assertEquals(ResponseCode.ALT_TAX_CODE_INVALID_FORMAT, errors.get(0));

        errors = altTaxCodeValidator.validate("0*********");
        assertTrue(errors.isEmpty());
    }
}