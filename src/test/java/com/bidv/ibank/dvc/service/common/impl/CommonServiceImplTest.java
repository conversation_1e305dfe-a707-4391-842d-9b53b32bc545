package com.bidv.ibank.dvc.service.common.impl;

import com.bidv.ibank.client.common.api.FeeServiceClient;
import com.bidv.ibank.client.common.api.MasterdataServiceClient;
import com.bidv.ibank.client.common.api.TransLimitServiceClient;
import com.bidv.ibank.client.common.api.TransWorkflowClient;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.client.common.dto.masterdata.AuthorizedAccountListCriteriaDto;
import com.bidv.ibank.client.common.dto.masterdata.CheckAuthorizedAccountCriteriaDto;
import com.bidv.ibank.common.txn.service.BulkTransactionService;
import com.bidv.ibank.common.txn.service.ProcessTransactionCacheService;
import com.bidv.ibank.common.txn.service.TransactionBaseService;
import com.bidv.ibank.common.txn.service.TransactionCommonService;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.domain.response.DataList;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.Translator;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonServiceImplTest {

    @Mock
    private MasterdataServiceClient masterdataServiceClient;

    @Mock
    private FeeServiceClient feeServiceClient;

    @Mock
    private TransWorkflowClient transWorkflowClient;

    @Mock
    private TransLimitServiceClient transLimitServiceClient;

    @Mock
    private BulkTransactionService<GOVPaymentTransactionEntity> bulkTransactionService;

    @Mock
    private TransactionBaseService<GOVPaymentTransactionEntity> transactionBaseService;

    @Mock
    private TransactionCommonService transactionCommonService;

    @Mock
    private ProcessTransactionCacheService processTransactionCacheService;

    @InjectMocks
    private CommonServiceImpl commonService;

    private MockedStatic<Translator> translatorMock;

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(any(String.class))).thenReturn("Mocked Translation");
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(Object[].class))).thenReturn("Mocked Translation with params");
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class))).thenReturn("Mocked Translation with default");
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void getAuthorizedAccounts_Success() {
        // Given
        String debitAccType = "some_type";
        String grpType = "some_group";
        AuthAccountDto authAccountDto = new AuthAccountDto();
        DataList<AuthAccountDto> dataList = new DataList<>(Collections.singletonList(authAccountDto));
        ResultList<AuthAccountDto> expectedResponse = ResultList.success(dataList.getItems(), dataList.getTotal());
        when(masterdataServiceClient.authorizedAccountList(any(AuthorizedAccountListCriteriaDto.class)))
                .thenReturn(expectedResponse);

        // When
        List<AuthAccountDto> actualAccounts = commonService.getAuthorizedAccounts(debitAccType, grpType);

        // Then
        assertFalse(actualAccounts.isEmpty());
        assertEquals(1, actualAccounts.size());
        assertEquals(authAccountDto, actualAccounts.get(0));
    }

    @Test
    void getAuthorizedAccounts_Failure() {
        // Given
        String debitAccType = "some_type";
        String grpType = "some_group";
        // Create error result directly without using ResponseCode.message() to avoid Translator dependency
        ResultList<AuthAccountDto> expectedResponse = new ResultList<>();
        expectedResponse.setCode(ResponseCode.USER_ACCOUNT_INVALID.code());
        expectedResponse.setMessage("User account invalid");

        when(masterdataServiceClient.authorizedAccountList(any(AuthorizedAccountListCriteriaDto.class)))
                .thenReturn(expectedResponse);

        // When
        List<AuthAccountDto> actualAccounts = commonService.getAuthorizedAccounts(debitAccType, grpType);

        // Then
        assertTrue(actualAccounts.isEmpty());
    }

    @Test
    void checkAuthorizationAccount_Success() {
        // Given
        String debitAccNo = "12345";
        String debitAccType = "some_type";
        String grpType = "some_group";
        Result<Boolean> expectedResponse = Result.success(true);
        when(masterdataServiceClient.checkAuthorizationAccount(any(CheckAuthorizedAccountCriteriaDto.class)))
                .thenReturn(expectedResponse);

        // When
        boolean isAuthorized = commonService.checkAuthorizationAccount(debitAccNo, debitAccType, grpType);

        // Then
        assertTrue(isAuthorized);
    }

    @Test
    void checkAuthorizationAccount_Failure() {
        // Given
        String debitAccNo = "12345";
        String debitAccType = "some_type";
        String grpType = "some_group";
        // Create error result directly without using ResponseCode.message() to avoid Translator dependency
        Result<Boolean> expectedResponse = new Result<>();
        expectedResponse.setCode(ResponseCode.USER_ACCOUNT_INVALID.code());
        expectedResponse.setMessage("User account invalid");

        when(masterdataServiceClient.checkAuthorizationAccount(any(CheckAuthorizedAccountCriteriaDto.class)))
                .thenReturn(expectedResponse);

        // When
        boolean isAuthorized = commonService.checkAuthorizationAccount(debitAccNo, debitAccType, grpType);

        // Then
        assertFalse(isAuthorized);
    }
}
