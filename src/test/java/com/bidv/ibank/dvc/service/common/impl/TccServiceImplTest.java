package com.bidv.ibank.dvc.service.common.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.spy;

import java.util.function.Function;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.i18n.CompositeMessageSource;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseRequest;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.base.RequestHeader;
import com.bidv.ibank.integrate.entity.base.ResponseHeader;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

@ExtendWith(MockitoExtension.class)
class TccServiceImplTest {

    @InjectMocks
    private TccServiceImpl tccService;

    @Test
    void executeTccCall_WhenSuccessful_ShouldReturnSuccessResult() {
        String requestBody = "test-request";
        String responseBody = "test-response";
        EsbTccBaseBodyMessage<String> messageBody = new EsbTccBaseBodyMessage<>();
        messageBody.setMessageBody(responseBody);

        try (MockedStatic<IntegrateServiceFactory> mockedFactory = mockStatic(IntegrateServiceFactory.class)) {
            mockedFactory.when(IntegrateServiceFactory::createRequestHeader)
                    .thenReturn(mock(RequestHeader.class));

            @SuppressWarnings("unchecked")
            Function<IntegrateBaseRequest<String>, IntegrateBaseResponse<EsbTccBaseBodyMessage<String>>> tccFunction = mock(Function.class);
            IntegrateBaseResponse<EsbTccBaseBodyMessage<String>> response = spy(new IntegrateBaseResponse<>());
            ResponseHeader header = new ResponseHeader();
            response.setHeader(header);
            response.setBody(messageBody);
            when(response.isSuccess()).thenReturn(true);

            when(tccFunction.apply(any())).thenReturn(response);

            Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<String>>> result = tccService.executeTccCall(requestBody, tccFunction);

            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData().getBody().getMessageBody()).isEqualTo(responseBody);
        }
    }

    @Test
    void executeTccCall_WhenTccCallFails_ShouldReturnErrorResult() {
        String requestBody = "test-request";
        String errorCode = "ERR001";
        String errorMessage = "Test Error";

        try (MockedStatic<IntegrateServiceFactory> mockedFactory = mockStatic(IntegrateServiceFactory.class)) {
            mockedFactory.when(IntegrateServiceFactory::createRequestHeader)
                    .thenReturn(mock(RequestHeader.class));

            @SuppressWarnings("unchecked")
            Function<IntegrateBaseRequest<String>, IntegrateBaseResponse<EsbTccBaseBodyMessage<String>>> tccFunction = mock(Function.class);
            IntegrateBaseResponse<EsbTccBaseBodyMessage<String>> response = spy(new IntegrateBaseResponse<>());
            ResponseHeader header = new ResponseHeader();
            header.setErrorCode(errorCode);
            header.setErrorDesc(errorMessage);
            response.setHeader(header);
            when(response.isSuccess()).thenReturn(false);

            when(tccFunction.apply(any())).thenReturn(response);

            Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<String>>> result = tccService.executeTccCall(requestBody, tccFunction);

            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getCode()).isEqualTo(errorCode);
            assertThat(result.getMessage()).isEqualTo(errorMessage);
        }
    }

    @Test
    void executeTccCall_WhenExceptionOccurs_ShouldReturnTimeoutError() {
        String requestBody = "test-request";
        String mockedErrorMessage = "Mocked Error Message";

        CompositeMessageSource messageSource = mock(CompositeMessageSource.class);
        when(messageSource.getMessage(any(), any(), any(), any())).thenReturn(mockedErrorMessage);
        ReflectionTestUtils.setField(Translator.class, "messageSource", messageSource);

        try (MockedStatic<IntegrateServiceFactory> mockedFactory = mockStatic(IntegrateServiceFactory.class)) {
            mockedFactory.when(IntegrateServiceFactory::createRequestHeader)
                    .thenReturn(mock(RequestHeader.class));

            @SuppressWarnings("unchecked")
            Function<IntegrateBaseRequest<String>, IntegrateBaseResponse<EsbTccBaseBodyMessage<String>>> tccFunction = mock(Function.class);
            when(tccFunction.apply(any())).thenThrow(new RuntimeException("Test Exception"));

            Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<String>>> result = tccService.executeTccCall(requestBody, tccFunction);

            assertThat(result.isSuccess()).isFalse();
            assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
            assertThat(result.getMessage()).isEqualTo(mockedErrorMessage);
        }
    }
}