package com.bidv.ibank.dvc.service.customsduty.impl;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.request.BatchDetailEditReq;
import com.bidv.ibank.dvc.model.request.BatchConfirmReq;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.BatchCalcFeeRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.BatchProcessResultRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.repository.customsduty.GovPaymentBatchItemCustomRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchItemRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLhxnkRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.customsduty.AsyncService;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.dvc.util.constant.StatusEnum;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.FileUtils;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import com.bidv.ibank.util.excel.ExcelUtils;
import com.bidv.ibank.dvc.model.dto.ValidateFileDto;
import com.bidv.ibank.client.common.dto.fee.FeeResult;
import com.bidv.ibank.client.common.dto.masterdata.CustomerPmtSpecDto;
import com.bidv.ibank.common.txn.util.constant.CacheField;
import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doNothing;
import static org.mockito.ArgumentMatchers.argThat;

import org.springframework.core.env.Environment;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;

import com.bidv.ibank.common.txn.model.dto.ConfirmResDto;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionResponse;
import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;

class BatchServiceImplTest {

    @Mock
    private FileService fileService;

    @Mock
    private AsyncService asyncService;

    @Mock
    private TccDmKhobacRepository tccDmKhobacRepository;

    @Mock
    private TccDmTkNsnnRepository tccDmTkNsnnRepository;

    @Mock
    private TccDmCqthuRepository tccDmCqthuRepository;

    @Mock
    private TccDmDbhcRepository tccDmDbhcRepository;

    @Mock
    private TccDmChuongRepository tccDmChuongRepository;

    @Mock
    private TccDmNdktRepository tccDmNdktRepository;

    @Mock
    private TccDmLhxnkRepository tccDmLhxnkRepository;

    @Mock
    private TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;

    @Mock
    private TccDmSthueHqaRepository tccDmSthueHqaRepository;

    @InjectMocks
    private BatchServiceImpl batchService;

    @Mock
    private GOVPaymentBatchRepository govPaymentBatchRepository;

    @Mock
    private GOVPaymentBatchMapper govPaymentBatchMapper;

    @Mock
    private TaxService taxService;

    @Mock
    private CommonService commonService;

    @Mock
    private GOVPaymentBatchItemRepository govPaymentBatchItemRepository;

    @Mock
    private GovPaymentBatchItemCustomRepository govPaymentBatchItemCustomRepository;

    @Mock
    private IntegrateServiceFactory integrateServiceFactory;

    private static final String TEMPLATE_PATH = "templates/batch_txn.xlsx";
    private static final String TEMPLATE_PATH_RESULT = "templates/batch_txn_result.xlsx";
    private static final String DEBIT_ACC_NO = "********";
    private MockedStatic<Translator> translatorMock;
    private MockedStatic<AuthenticationUtils> authenticationUtilsMock;
    private MockedStatic<com.bidv.ibank.framework.context.AppContext> appContextMock;
    private Workbook mockWorkbook;
    private Environment mockEnv;
    private CurrentUser mockCurrentUser;
    private CurrentUser.UserInfo mockUser;

    @BeforeEach
    void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);

        // Setup translator mock
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(anyString())).thenReturn("Mocked Message");

        // Setup authentication mock
        mockUser = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        mockCurrentUser = mock(CurrentUser.class);
        when(mockCurrentUser.getUser()).thenReturn(mockUser);
        authenticationUtilsMock = mockStatic(AuthenticationUtils.class);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);

        // Setup AppContext environment
        mockEnv = mock(Environment.class);
        appContextMock = mockStatic(com.bidv.ibank.framework.context.AppContext.class);
        appContextMock.when(() -> com.bidv.ibank.framework.context.AppContext.getEnv()).thenReturn(mockEnv);
        when(mockEnv.getProperty(anyString())).thenReturn("mocked_value");

        // Create a mock workbook with required sheets
        mockWorkbook = new XSSFWorkbook();
        for (int i = 0; i < 11; i++) {
            mockWorkbook.createSheet("Sheet" + i);
        }
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
        if (authenticationUtilsMock != null) {
            authenticationUtilsMock.close();
        }
        if (appContextMock != null) {
            appContextMock.close();
        }
    }

    @Test
    void downloadTemplate_success() throws IOException {
        String expectedFileUrl = "http://s3.example.com/batch_txn.xlsx";
        FileInfo expectedFileInfo = new FileInfo();
        expectedFileInfo.setFileUrl(expectedFileUrl);
        Result<FileInfo> s3SuccessResult = Result.success(expectedFileInfo);

        setupRepositoryMocks();

        try (MockedStatic<ExcelUtils> excelUtilsMock = Mockito.mockStatic(ExcelUtils.class);
                MockedStatic<FileUtils> fileUtilsMock = Mockito.mockStatic(FileUtils.class)) {

            excelUtilsMock.when(() -> ExcelUtils.createWorkbookFromResource(AppConstants.BATCH_TXN_TEMPLATE_PATH))
                    .thenReturn(mockWorkbook);
            excelUtilsMock.when(() -> ExcelUtils.writeAndClose(any(Workbook.class), any(ByteArrayOutputStream.class)))
                    .thenAnswer(invocation -> {
                        ByteArrayOutputStream baos = invocation.getArgument(1);
                        mockWorkbook.write(baos);
                        return baos;
                    });

            String expectedFileName = "TEMP_IMPORT_THUE_XNK.xlsx";
            fileUtilsMock.when(() -> FileUtils.formatExcelName(AppConstants.BATCH_IMPORT_TEMPLATE_FILE_NAME))
                    .thenReturn(expectedFileName);

            when(fileService.uploadToS3(any(ByteArrayOutputStream.class), eq(expectedFileName), eq(true)))
                    .thenReturn(s3SuccessResult);

            Result<ExportFileRes> result = batchService.downloadTemplate();

            assertTrue(result.isSuccess());
            assertEquals(expectedFileUrl, result.getData().getUrl());
        }
    }

    @Test
    void downloadTemplate_s3UploadFails() throws IOException {
        String errorCode = "S3_UPLOAD_ERROR";
        String errorMessage = "Failed to upload to S3";
        Result<FileInfo> s3ErrorResult = Result.error(errorCode, errorMessage);

        setupRepositoryMocks();

        try (MockedStatic<ExcelUtils> excelUtilsMock = Mockito.mockStatic(ExcelUtils.class);
                MockedStatic<FileUtils> fileUtilsMock = Mockito.mockStatic(FileUtils.class)) {

            excelUtilsMock.when(() -> ExcelUtils.createWorkbookFromResource(AppConstants.BATCH_TXN_TEMPLATE_PATH))
                    .thenReturn(mockWorkbook);
            excelUtilsMock.when(() -> ExcelUtils.writeAndClose(any(Workbook.class), any(ByteArrayOutputStream.class)))
                    .thenAnswer(invocation -> {
                        ByteArrayOutputStream baos = invocation.getArgument(1);
                        mockWorkbook.write(baos);
                        return baos;
                    });

            String expectedFileName = "TEMP_IMPORT_THUE_XNK.xlsx";
            fileUtilsMock.when(() -> FileUtils.formatExcelName(AppConstants.BATCH_IMPORT_TEMPLATE_FILE_NAME))
                    .thenReturn(expectedFileName);

            when(fileService.uploadToS3(any(ByteArrayOutputStream.class), eq(expectedFileName), eq(true)))
                    .thenReturn(s3ErrorResult);

            Result<ExportFileRes> result = batchService.downloadTemplate();

            assertFalse(result.isSuccess());
            assertEquals(errorCode, result.getCode());
            assertEquals(errorMessage, result.getMessage());
        }
    }

    @Test
    void downloadTemplate_exceptionHandling() throws IOException {
        try (MockedStatic<ExcelUtils> excelUtilsMock = Mockito.mockStatic(ExcelUtils.class)) {
            excelUtilsMock.when(() -> ExcelUtils.createWorkbookFromResource(TEMPLATE_PATH))
                    .thenThrow(new IOException("Failed to read template"));

            Result<ExportFileRes> result = batchService.downloadTemplate();

            assertFalse(result.isSuccess());
            assertEquals(ResponseCode.TIMEOUT_01.code(), result.getCode());
            assertNotNull(result.getMessage());
        }
    }

    private void setupRepositoryMocks() {
        when(tccDmKhobacRepository.findTreasuriesByCodes(eq(StatusEnum.ACTIVE.getValue()), any()))
                .thenReturn(Collections.emptyList());
        when(tccDmTkNsnnRepository.findTkNsnnByCodes(any()))
                .thenReturn(Collections.emptyList());
        when(tccDmCqthuRepository.findCqthuByCodes(eq(StatusEnum.ACTIVE.getValue()), any()))
                .thenReturn(Collections.emptyList());
        when(tccDmDbhcRepository.findDbhcByCodes(any()))
                .thenReturn(Collections.emptyList());
        when(tccDmChuongRepository.findChuongByCodes(any()))
                .thenReturn(Collections.emptyList());
        when(tccDmNdktRepository.findNdktByCodes(any()))
                .thenReturn(Collections.emptyList());
        when(tccDmLhxnkRepository.findLhxnkByCodes(any()))
                .thenReturn(Collections.emptyList());
        when(tccDmLoaitienhqaRepository.findLoaitienhqaByCodes(any()))
                .thenReturn(Collections.emptyList());
        when(tccDmSthueHqaRepository.findTaxTypeByCodes(any()))
                .thenReturn(Collections.emptyList());
    }

    @Test
    void list_WithValidRequest_ShouldReturnBatchList() {
        // Arrange
        BatchListReq request = BatchListReq.builder()
                .search("test")
                .startDate(LocalDate.now().minusDays(7))
                .endDate(LocalDate.now())
                .batchName("Test Batch")
                .statuses(List.of("PROCESSING"))
                .batchNo("BDR123")
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusId()).thenReturn(123L);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch entities
        GOVPaymentBatchEntity batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setId(UUID.randomUUID().toString());
        batchEntity.setName("Test Batch");
        batchEntity.setBatchNo("BDR123");
        batchEntity.setStatus(BatchStatusEnum.PROCESSING);
        batchEntity.setCreatedDate(LocalDateTime.now());

        List<GOVPaymentBatchEntity> batchEntities = List.of(batchEntity);
        Page<GOVPaymentBatchEntity> batchPage = new PageImpl<>(batchEntities, Pageable.unpaged(), 1);

        when(govPaymentBatchRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(batchPage);

        // Mock mapper
        BatchListRes batchListRes = BatchListRes.builder()
                .batchName(batchEntity.getName())
                .batchNo(batchEntity.getBatchNo())
                .status(batchEntity.getStatus().name())
                .createdDate(batchEntity.getCreatedDate())
                .build();
        when(govPaymentBatchMapper.toListDto(batchEntity)).thenReturn(batchListRes);

        // Act
        ResultList<BatchListRes> result = batchService.list(request);

        // Assert
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().getTotal());
        assertEquals(1, result.getData().getItems().size());

        BatchListRes firstBatch = result.getData().getItems().get(0);
        assertEquals(batchEntity.getName(), firstBatch.getBatchName());
        assertEquals(batchEntity.getBatchNo(), firstBatch.getBatchNo());
        assertEquals(batchEntity.getStatus().name(), firstBatch.getStatus());
        assertEquals(batchEntity.getCreatedDate(), firstBatch.getCreatedDate());

        // Verify interactions
        verify(govPaymentBatchRepository).findAll(any(Specification.class), any(Pageable.class));
        verify(govPaymentBatchMapper).toListDto(batchEntity);
    }

    @Test
    void downloadResult_Success() throws IOException {
        // Arrange
        String batchNo = "BDR20250529172533579";
        String batchName = "Test Batch";
        String expectedFileUrl = "http://s3.example.com/result.xlsx";

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(UUID.randomUUID().toString())
                .name(batchName)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .govPaymentBatchItemList(Collections.emptySet())
                .build();

        try (MockedStatic<ExcelUtils> excelUtilsMock = Mockito.mockStatic(ExcelUtils.class);
                MockedStatic<FileUtils> fileUtilsMock = Mockito.mockStatic(FileUtils.class)) {

            // Mock ExcelUtils
            excelUtilsMock.when(() -> ExcelUtils.createWorkbookFromResource(TEMPLATE_PATH_RESULT))
                    .thenReturn(mockWorkbook);
            excelUtilsMock.when(() -> ExcelUtils.writeAndClose(any(Workbook.class), any(ByteArrayOutputStream.class)))
                    .thenAnswer(invocation -> {
                        ByteArrayOutputStream baos = invocation.getArgument(1);
                        mockWorkbook.write(baos);
                        return baos;
                    });

            // Mock FileUtils
            fileUtilsMock.when(() -> FileUtils.formatExcelName(batchName))
                    .thenReturn("TEST_BATCH_RESULT.xlsx");

            // Mock repository and mapper
            when(govPaymentBatchRepository.findOne(any(Specification.class)))
                    .thenReturn(java.util.Optional.of(batchEntity));
            when(govPaymentBatchMapper.toExportDto(any()))
                    .thenReturn(Collections.emptyList());
            when(fileService.uploadToS3(any(ByteArrayOutputStream.class), eq("TEST_BATCH_RESULT.xlsx"), eq(true)))
                    .thenReturn(Result.success(new FileInfo()));

            // Act
            Result<ExportFileRes> result = batchService.downloadResult(req);

            // Assert
            assertNull(result.getData());
        }
    }

    @Test
    void downloadResult_WhenBatchNotFound_ShouldReturnError() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("NONEXISTENT_BATCH")
                .build();

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(java.util.Optional.empty());

        // Act
        Result<ExportFileRes> result = batchService.downloadResult(req);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void downloadResult_WhenTemplateNotFound_ShouldReturnError() throws IOException {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20250529172533579")
                .build();

        try (MockedStatic<ExcelUtils> excelUtilsMock = Mockito.mockStatic(ExcelUtils.class)) {
            excelUtilsMock.when(() -> ExcelUtils.createWorkbookFromResource(TEMPLATE_PATH_RESULT))
                    .thenReturn(null);

            // Act
            Result<ExportFileRes> result = batchService.downloadResult(req);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
        }
    }

    @Test
    void downloadResult_WhenS3UploadFails_ShouldReturnError() throws IOException {
        // Arrange
        String batchNo = "BDR20250529172533579";
        String batchName = "Test Batch";
        String errorCode = "GOV0002";
        String errorMessage = "Mocked Message";

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(UUID.randomUUID().toString())
                .name(batchName)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .govPaymentBatchItemList(Collections.emptySet())
                .build();

        try (MockedStatic<ExcelUtils> excelUtilsMock = Mockito.mockStatic(ExcelUtils.class);
                MockedStatic<FileUtils> fileUtilsMock = Mockito.mockStatic(FileUtils.class)) {

            // Mock ExcelUtils
            excelUtilsMock.when(() -> ExcelUtils.createWorkbookFromResource(TEMPLATE_PATH_RESULT))
                    .thenReturn(mockWorkbook);
            excelUtilsMock.when(() -> ExcelUtils.writeAndClose(any(Workbook.class), any(ByteArrayOutputStream.class)))
                    .thenAnswer(invocation -> {
                        ByteArrayOutputStream baos = invocation.getArgument(1);
                        mockWorkbook.write(baos);
                        return baos;
                    });

            // Mock FileUtils
            fileUtilsMock.when(() -> FileUtils.formatExcelName(batchName))
                    .thenReturn("TEST_BATCH_RESULT.xlsx");

            // Mock repository, mapper and file service
            when(govPaymentBatchRepository.findOne(any(Specification.class)))
                    .thenReturn(java.util.Optional.of(batchEntity));
            when(govPaymentBatchMapper.toExportDto(any()))
                    .thenReturn(Collections.emptyList());
            when(fileService.uploadToS3(any(ByteArrayOutputStream.class), eq("TEST_BATCH_RESULT.xlsx"), eq(true)))
                    .thenReturn(Result.error(errorCode, errorMessage));

            // Act
            Result<ExportFileRes> result = batchService.downloadResult(req);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals(errorCode, result.getCode());
            assertEquals(errorMessage, result.getMessage());
        }
    }

    @Test
    void downloadResult_WhenExceptionOccurs_ShouldReturnError() throws IOException {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20250529172533579")
                .build();

        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(UUID.randomUUID().toString())
                .name("Test Batch")
                .batchNo("BDR20250529172533579")
                .status(BatchStatusEnum.CHECKED)
                .govPaymentBatchItemList(Collections.emptySet())
                .build();

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(java.util.Optional.of(batchEntity));

        try (MockedStatic<ExcelUtils> excelUtilsMock = Mockito.mockStatic(ExcelUtils.class)) {
            excelUtilsMock.when(() -> ExcelUtils.createWorkbookFromResource(TEMPLATE_PATH_RESULT))
                    .thenThrow(new IOException("Failed to read template"));

            // Act
            Result<ExportFileRes> result = batchService.downloadResult(req);

            // Assert
            assertFalse(result.isSuccess());
            assertEquals(ResponseCode.TIMEOUT_01.code(), result.getCode());
        }
    }

    @Test
    void detail_WhenBatchExists_ShouldReturnBatchDetails() {
        // Arrange
        String batchNo = "BDR20240321123456";
        String username = "testUser";
        Long cusId = 123L;

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user and UserInfo
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusId()).thenReturn(cusId);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch entity
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(UUID.randomUUID().toString())
                .name("Test Batch")
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .build();

        // Mock repository
        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(batchEntity));

        // Mock mapper
        BatchDetailRes expectedResponse = BatchDetailRes.builder()
                .validItems(List.of())
                .invalidItems(List.of())
                .build();
        when(govPaymentBatchMapper.toDetailDto(batchEntity)).thenReturn(expectedResponse);

        // Act
        Result<BatchDetailRes> result = batchService.detail(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData())
                .satisfies(res -> {
                    assertThat(res.getValidItems()).isEmpty();
                    assertThat(res.getInvalidItems()).isEmpty();
                    assertThat(res.getTotalRecords()).isZero();
                    assertThat(res.getTotalValidRecords()).isZero();
                    assertThat(res.getTotalInvalidRecords()).isZero();
                });

        // Verify interactions
        verify(govPaymentBatchRepository).findOne(any(Specification.class));
        verify(govPaymentBatchMapper).toDetailDto(batchEntity);
    }

    @Test
    void detail_WhenBatchNotFound_ShouldReturnError() {
        // Arrange
        String batchNo = "BDR20240321123456";
        String username = "testUser";
        Long cusId = 123L;
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user and UserInfo
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusId()).thenReturn(cusId);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock repository to return empty
        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        // Act
        Result<BatchDetailRes> result = batchService.detail(req);

        // Assert
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.RESOURCE_NOTFOUND.code());

        // Verify interactions
        verify(govPaymentBatchRepository).findOne(any(Specification.class));
        verify(govPaymentBatchMapper, never()).toDetailDto(any());
    }

    @Test
    void detail_WhenBatchHasWrongStatus_ShouldReturnError() {
        // Arrange
        String batchNo = "BDR20240321123456";
        String username = "testUser";
        Long cusId = 123L;
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user and UserInfo
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusId()).thenReturn(cusId);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock repository
        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        // Act
        Result<BatchDetailRes> result = batchService.detail(req);

        // Assert
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.RESOURCE_NOTFOUND.code());

        // Verify interactions
        verify(govPaymentBatchRepository).findOne(any(Specification.class));
        verify(govPaymentBatchMapper, never()).toDetailDto(any());
    }

    @Test
    void uploadFile_success() throws IOException {
        // Prepare test data
        String fileName = "test.xlsx";
        String fileContent = "test content";
        MockMultipartFile file = new MockMultipartFile(
                "file",
                fileName,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                fileContent.getBytes(StandardCharsets.UTF_8));

        String checksum = "testChecksum";
        String fileKey = "testFileKey";
        String batchNo = "BDR20240321123456";

        // Mock CommonService.generateChecksum
        when(commonService.generateChecksum(file)).thenReturn(checksum);

        // Mock file validation
        when(fileService.validateAndReadExcelFile(eq(file), any(ValidateFileDto.class)))
                .thenReturn(Result.<List<BatchImportItemDto>>success(Collections.emptyList()));

        // Mock repository to return no existing batches
        when(govPaymentBatchRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        // Mock file upload
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileKey(fileKey);
        Result<FileInfo> uploadResult = Result.success(fileInfo);
        when(fileService.uploadToS3(eq(file), eq(false)))
                .thenReturn(uploadResult);

        // Mock batch entity creation
        GOVPaymentBatchEntity batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setBatchNo(batchNo);
        when(govPaymentBatchMapper.toEntity(any(MultipartFile.class), anyString(), anyString(), any(BatchTypeEnum.class)))
                .thenReturn(batchEntity);

        // Execute test
        Result<String> result = batchService.uploadFile(file);

        // Verify
        assertTrue(result.isSuccess());
        assertEquals(batchNo, result.getData());
        verify(govPaymentBatchRepository).save(any(GOVPaymentBatchEntity.class));
        verify(asyncService).handleBatchUpload(batchEntity.getId());
    }

    @Test
    void uploadFile_existingFile() throws IOException {
        // Prepare test data
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test content".getBytes(StandardCharsets.UTF_8));

        String checksum = "testChecksum";
        GOVPaymentBatchEntity existingBatch = new GOVPaymentBatchEntity();

        // Mock CommonService.generateChecksum
        when(commonService.generateChecksum(file)).thenReturn(checksum);

        // Mock file validation first
        when(fileService.validateAndReadExcelFile(eq(file), any(ValidateFileDto.class)))
                .thenReturn(Result.<List<BatchImportItemDto>>success(Collections.emptyList()));

        // Mock repository to return existing batch
        when(govPaymentBatchRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.singletonList(existingBatch));

        // Execute test
        Result<String> result = batchService.uploadFile(file);

        // Verify
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.FILE_EXISTS.code(), result.getCode());
        verify(fileService, never()).uploadToS3(any(MultipartFile.class), anyBoolean());
    }

    @Test
    void downloadFile_success() {
        // Prepare test data
        String batchNo = "BDR20240321123456";
        String fileKey = "testFileKey";
        String username = "testUser";
        byte[] expectedContent = "test content".getBytes(StandardCharsets.UTF_8);

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        GOVPaymentBatchEntity batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setFileKey(fileKey);
        batchEntity.setCreatedBy(username);

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn(username);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock repository
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), eq(username)))
                .thenReturn(Optional.of(batchEntity));

        // Mock file service with success response
        when(fileService.downloadFile(fileKey))
                .thenReturn(Result.success(expectedContent));

        // Execute test
        Result<byte[]> result = batchService.downloadFile(req);

        // Verify
        assertTrue(result.isSuccess());
        assertArrayEquals(expectedContent, result.getData());
        verify(govPaymentBatchRepository).findByBatchNo(eq(batchNo), eq(username));
        verify(fileService).downloadFile(fileKey);
    }

    @Test
    void downloadFile_batchNotFound() {
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("NONEXISTENT")
                .build();

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        Result<byte[]> result = batchService.downloadFile(req);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void delete_success() {
        // Prepare test data
        String batchNo = "BDR20240321123456";
        String username = "testUser";
        Long cusId = 123L;

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user and UserInfo
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusId()).thenReturn(cusId);
        when(mockUserInfo.getUsername()).thenReturn(username);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch entity
        GOVPaymentBatchEntity batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setStatus(BatchStatusEnum.ERROR);
        batchEntity.setCreatedBy(username);
        batchEntity.setBatchNo(batchNo);

        when(govPaymentBatchRepository.findByBatchNo(batchNo, null))
                .thenReturn(Optional.of(batchEntity));

        // Mock update status
        doNothing().when(govPaymentBatchRepository).updateStatusByBatchNo(
                eq(BatchStatusEnum.DELETED),
                isNull(),
                eq(batchNo),
                eq(cusId));

        // Execute test
        Result<String> result = batchService.delete(req);

        // Verify
        assertTrue(result.isSuccess());
        verify(govPaymentBatchRepository).updateStatusByBatchNo(
                eq(BatchStatusEnum.DELETED),
                isNull(),
                eq(batchNo),
                eq(cusId));
    }

    @Test
    void delete_batchNotFound() {
        // Prepare test data
        String batchNo = "NONEXISTENT";
        String username = "testUser";
        Long cusId = 123L;

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user and UserInfo
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusId()).thenReturn(cusId);
        when(mockUserInfo.getUsername()).thenReturn(username);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        when(govPaymentBatchRepository.findByBatchNo(req.getBatchNo(), null))
                .thenReturn(Optional.empty());

        Result<String> result = batchService.delete(req);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void delete_wrongStatus() {
        // Prepare test data
        String batchNo = "BDR20240321123456";
        String username = "testUser";
        Long cusId = 123L;

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user and UserInfo
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusId()).thenReturn(cusId);
        when(mockUserInfo.getUsername()).thenReturn(username);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        GOVPaymentBatchEntity batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setStatus(BatchStatusEnum.PROCESSING);

        when(govPaymentBatchRepository.findByBatchNo(batchNo, null))
                .thenReturn(Optional.of(batchEntity));

        Result<String> result = batchService.delete(req);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_STATUS.code(), result.getCode());
    }

    @Test
    void delete_wrongUser() {
        String batchNo = "BDR20240321123456";
        String username = "testUser";
        String createdBy = "otherUser";

        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn(username);
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn(username);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch entity
        GOVPaymentBatchEntity batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setStatus(BatchStatusEnum.ERROR);
        batchEntity.setCreatedBy(createdBy);

        when(govPaymentBatchRepository.findByBatchNo(batchNo, null))
                .thenReturn(Optional.of(batchEntity));

        Result<String> result = batchService.delete(req);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_INFO.code(), result.getCode());
    }

    @Test
    void validateDetail_ShouldSetIsInBatchAndCallTaxService() {
        ValidateCustomsDutyReq request = new ValidateCustomsDutyReq();
        ValidateCustomsDutyRes expectedResponse = new ValidateCustomsDutyRes();
        Result<ValidateCustomsDutyRes> expectedResult = Result.success(expectedResponse);

        when(taxService.validate(any(ValidateCustomsDutyReq.class))).thenReturn(expectedResult);

        Result<ValidateCustomsDutyRes> result = batchService.validateDetail(request);

        assertTrue(request.getIsInBatch());
        verify(taxService).validate(request);
        assertEquals(expectedResult, result);
    }

    @Test
    void editDetail_WhenTransactionNotFound_ShouldReturnError() {
        BatchDetailEditReq request = new BatchDetailEditReq();
        request.setTransKey("nonexistent-key");

        when(commonService.getCacheValue(eq("nonexistent-key"), eq(CacheField.TRANSACTION_DATA.getKey()), eq(GOVPaymentTransactionEntity.class)))
                .thenReturn(null);

        Result<String> result = batchService.editDetail(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void editDetail_WhenBatchItemNotFound_ShouldReturnError() {
        BatchDetailEditReq request = new BatchDetailEditReq();
        request.setTransKey("valid-key");
        request.setBatchItemId("nonexistent-item");

        GOVPaymentTransactionEntity transactionEntity = new GOVPaymentTransactionEntity();
        transactionEntity.setDebitAccNo(DEBIT_ACC_NO);
        when(commonService.getCacheValue(eq("valid-key"), eq(CacheField.TRANSACTION_DATA.getKey()), eq(GOVPaymentTransactionEntity.class)))
                .thenReturn(transactionEntity);
        when(commonService.checkAuthorizationAccount(
                eq(transactionEntity.getDebitAccNo()),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(true);

        when(govPaymentBatchItemRepository.findById(
                eq("nonexistent-item")))
                        .thenReturn(Optional.empty());

        Result<String> result = batchService.editDetail(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void editDetail_Success_WithNoDuplicates() {
        BatchDetailEditReq request = new BatchDetailEditReq();
        request.setTransKey("valid-key");
        request.setBatchItemId("valid-item");

        GOVPaymentTransactionEntity transactionEntity = new GOVPaymentTransactionEntity();
        transactionEntity.setDebitAccNo(DEBIT_ACC_NO);
        when(commonService.getCacheValue(eq("valid-key"), eq(CacheField.TRANSACTION_DATA.getKey()), eq(GOVPaymentTransactionEntity.class)))
                .thenReturn(transactionEntity);
        when(commonService.checkAuthorizationAccount(
                eq(transactionEntity.getDebitAccNo()),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(true);

        GOVPaymentBatchItemEntity existingItem = new GOVPaymentBatchItemEntity();
        existingItem.setId("valid-item");
        existingItem.setBatchId("batch-1");
        existingItem.setStatus(BatchItemStatusEnum.INVALID);
        existingItem.setCreatedBy("testUser");
        existingItem.setGovPaymentBatchEntity(GOVPaymentBatchEntity.builder().batchType(BatchTypeEnum.PAYMENT).build());

        when(govPaymentBatchItemRepository.findById(
                eq("valid-item")))
                        .thenReturn(Optional.of(existingItem));

        GOVPaymentBatchItemEntity updatedItem = new GOVPaymentBatchItemEntity();
        when(govPaymentBatchMapper.toUpdatedEntity(eq(existingItem), eq(transactionEntity)))
                .thenReturn(updatedItem);

        when(govPaymentBatchItemRepository.findByBatchIdAndStatus(
                eq("batch-1"),
                eq(BatchItemStatusEnum.VALID)))
                        .thenReturn(Collections.emptyList());

        Result<String> result = batchService.editDetail(request);

        assertTrue(result.isSuccess());
        verify(govPaymentBatchItemRepository).save(updatedItem);
        verify(govPaymentBatchItemRepository, never()).updateErrCode(any(), any());
    }

    @Test
    void editDetail_Success_WithDuplicates() {
        BatchDetailEditReq request = new BatchDetailEditReq();
        request.setTransKey("valid-key");
        request.setBatchItemId("valid-item");

        GOVPaymentTransactionEntity transactionEntity = new GOVPaymentTransactionEntity();
        transactionEntity.setDebitAccNo(DEBIT_ACC_NO);
        when(commonService.getCacheValue(eq("valid-key"), eq(CacheField.TRANSACTION_DATA.getKey()), eq(GOVPaymentTransactionEntity.class)))
                .thenReturn(transactionEntity);
        when(commonService.checkAuthorizationAccount(
                eq(transactionEntity.getDebitAccNo()),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(true);

        GOVPaymentBatchItemEntity existingItem = new GOVPaymentBatchItemEntity();
        existingItem.setId("valid-item");
        existingItem.setBatchId("batch-1");
        existingItem.setTaxCode("123");
        existingItem.setDeclarationNo("ABC");
        existingItem.setStatus(BatchItemStatusEnum.INVALID);
        existingItem.setCreatedBy("testUser");
        existingItem.setGovPaymentBatchEntity(GOVPaymentBatchEntity.builder().batchType(BatchTypeEnum.PAYMENT).build());

        when(govPaymentBatchItemRepository.findById(
                eq("valid-item")))
                        .thenReturn(Optional.of(existingItem));

        GOVPaymentBatchItemEntity updatedItem = new GOVPaymentBatchItemEntity();
        updatedItem.setTaxCode("123");
        updatedItem.setDeclarationNo("ABC");
        when(govPaymentBatchMapper.toUpdatedEntity(eq(existingItem), eq(transactionEntity)))
                .thenReturn(updatedItem);

        GOVPaymentBatchItemEntity duplicateItem = new GOVPaymentBatchItemEntity();
        duplicateItem.setId("duplicate-item");
        duplicateItem.setTaxCode("123");
        duplicateItem.setDeclarationNo("ABC");
        when(govPaymentBatchItemRepository.findByBatchIdAndStatus(
                eq("batch-1"),
                eq(BatchItemStatusEnum.VALID)))
                        .thenReturn(Collections.singletonList(duplicateItem));

        Result<String> result = batchService.editDetail(request);

        assertTrue(result.isSuccess());
        verify(govPaymentBatchItemRepository).save(updatedItem);
        verify(govPaymentBatchItemRepository).updateErrCode(
                argThat(argument -> argument != null &&
                        argument.size() == 2 &&
                        argument.contains("duplicate-item") &&
                        argument.contains("valid-item")),
                eq(ResponseCode.BATCH_DUPLICATE_RECORD.code()));
    }

    @Test
    void calcFee_WhenBatchNotFound_ShouldReturnError() {
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo("NONEXISTENT")
                .build();

        when(govPaymentBatchRepository.findByBatchNo(eq("NONEXISTENT"), isNull()))
                .thenReturn(Optional.empty());

        Result<BatchCalcFeeRes> result = batchService.calcFee(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void calcFee_WhenWrongStatus_ShouldReturnError() {
        String batchNo = "BDR20240321123456";
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch with wrong status
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(UUID.randomUUID().toString())
                .batchNo(batchNo)
                .status(BatchStatusEnum.PROCESSING)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        Result<BatchCalcFeeRes> result = batchService.calcFee(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_STATUS.code(), result.getCode());
    }

    @Test
    void calcFee_WhenWrongUser_ShouldReturnError() {
        String batchNo = "BDR20240321123456";
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch with different user
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(UUID.randomUUID().toString())
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("differentUser"); // Different user
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        Result<BatchCalcFeeRes> result = batchService.calcFee(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_INFO.code(), result.getCode());
    }

    @Test
    void calcFee_WhenNoValidItems_ShouldReturnError() {
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock valid batch
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Mock empty batch items
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());

        Result<BatchCalcFeeRes> result = batchService.calcFee(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void calcFee_WhenSetFeeInfoFails_ShouldReturnError() {
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock valid batch
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Mock batch items
        List<GOVPaymentBatchItemEntity> batchItems = Collections.singletonList(new GOVPaymentBatchItemEntity());
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(batchItems);

        // Mock fee info setup failure
        when(commonService.getCusPmtSpec())
                .thenReturn(Result.error(ResponseCode.TIMEOUT_01.code(), "Failed to get payment spec"));

        Result<BatchCalcFeeRes> result = batchService.calcFee(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TIMEOUT_01.code(), result.getCode());
        assertEquals("Failed to get payment spec", result.getMessage());
    }

    @Test
    void calcFee_WhenCalculateFeeFails_ShouldReturnError() {
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock valid batch
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Mock batch items
        List<GOVPaymentBatchItemEntity> batchItems = Collections.singletonList(new GOVPaymentBatchItemEntity());
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(batchItems);

        // Mock successful fee info setup
        CustomerPmtSpecDto pmtSpec = CustomerPmtSpecDto.builder()
                .chargeFeeOpt("INST")
                .currCode("VND")
                .chargeFreq("SINGLE")
                .build();
        when(commonService.getCusPmtSpec())
                .thenReturn(Result.success(pmtSpec));

        // Mock fee calculation failure
        when(commonService.calculateFeeBatch(any()))
                .thenReturn(ResultList.error(ResponseCode.TIMEOUT_01.code(), "Fee calculation failed"));

        Result<BatchCalcFeeRes> result = batchService.calcFee(request);

        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TIMEOUT_01.code(), result.getCode());
        assertEquals("Fee calculation failed", result.getMessage());
    }

    @Test
    void calcFee_Success() {
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock valid batch
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Mock batch items
        GOVPaymentBatchItemEntity batchItem = new GOVPaymentBatchItemEntity();
        batchItem.setId("item-1");
        batchItem.setDebitAccNo("ACC123");
        batchItem.setCcy("VND");
        batchItem.setAmount("1000000");
        List<GOVPaymentBatchItemEntity> batchItems = Collections.singletonList(batchItem);
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(batchItems);

        // Mock successful fee info setup
        CustomerPmtSpecDto pmtSpec = CustomerPmtSpecDto.builder()
                .chargeFeeOpt("INST")
                .currCode("VND")
                .chargeFreq("SINGLE")
                .build();
        when(commonService.getCusPmtSpec())
                .thenReturn(Result.success(pmtSpec));

        // Mock successful fee calculation
        FeeResult feeResult = FeeResult.builder()
                .id("id")
                .cusId(1L)
                .feeCode("feeCode")
                .feeName("feeName")
                .txnCcy("VND")
                .txnAmount(new BigDecimal("10000"))
                .txnFee(new BigDecimal("10000"))
                .feeCcy("VND")
                .feeAmount(new BigDecimal("10000"))
                .vat(new BigDecimal("10000"))
                .vatAmount(new BigDecimal("10000"))
                .vatRate(10F)
                .totalFee(new BigDecimal("10000"))
                .success(true)
                .build();
        when(commonService.calculateFeeBatch(any()))
                .thenReturn(ResultList.success(Collections.singletonList(feeResult)));

        // Mock batch items with fee
        GOVPaymentBatchItemEntity itemWithFee = new GOVPaymentBatchItemEntity();
        itemWithFee.setId("item-1");
        itemWithFee.setFeeAmount(new BigDecimal("10000"));
        itemWithFee.setFeeAccNo("ACC123");
        itemWithFee.setFeeCcy("VND");
        itemWithFee.setFeeOpt("INST");
        itemWithFee.setFeeFreq("SINGLE");
        List<GOVPaymentBatchItemEntity> itemsWithFee = Collections.singletonList(itemWithFee);

        when(govPaymentBatchMapper.toBatchItemEntitiesWithFee(eq(batchItems), any()))
                .thenReturn(itemsWithFee);
        doNothing().when(govPaymentBatchItemCustomRepository).update(itemsWithFee);
        // Mock fee response
        BatchCalcFeeRes expectedResponse = BatchCalcFeeRes.builder()
                .totalFee(new BigDecimal("10000"))
                .feeCcy("VND")
                .build();
        when(govPaymentBatchMapper.toBatchCalcFeeRes(eq(itemsWithFee)))
                .thenReturn(expectedResponse);

        Result<BatchCalcFeeRes> result = batchService.calcFee(request);

        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(expectedResponse, result.getData());
    }

    @Test
    void initPush_WhenBatchNotFound_ShouldReturnError() {
        // Arrange
        String batchNo = "BDR20240321123456";
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch not found
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.empty());

        // Act
        Result<TxnInitPushRes> result = batchService.initPush(request);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void initPush_WhenWrongStatus_ShouldReturnError() {
        // Arrange
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch with wrong status
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.PROCESSING) // Wrong status - should be CHECKED
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Act
        Result<TxnInitPushRes> result = batchService.initPush(request);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_STATUS.code(), result.getCode());
    }

    @Test
    void initPush_WhenWrongUser_ShouldReturnError() {
        // Arrange
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock batch with different user
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("differentUser"); // Different user
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Act
        Result<TxnInitPushRes> result = batchService.initPush(request);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_INFO.code(), result.getCode());
    }

    @Test
    void initPush_Success() {
        // Arrange
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchDetailReq request = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock valid batch
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.CHECKED)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Mock batch items
        GOVPaymentBatchItemEntity batchItem = new GOVPaymentBatchItemEntity();
        batchItem.setId("item-1");
        batchItem.setDebitAccNo("ACC123");
        batchItem.setCcy("VND");
        batchItem.setAmount("1000000");
        List<GOVPaymentBatchItemEntity> batchItems = Collections.singletonList(batchItem);
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(batchItems);

        // Mock transaction entities
        GOVPaymentTransactionEntity txnEntity = new GOVPaymentTransactionEntity();
        txnEntity.setId("txn-1");
        List<GOVPaymentTransactionEntity> txnEntities = Collections.singletonList(txnEntity);
        when(govPaymentBatchMapper.toTransactionEntities(any()))
                .thenReturn(txnEntities);

        // Mock successful transaction initialization
        ProcessTransactionResponse<GOVPaymentTransactionEntity> processResponse = ProcessTransactionResponse.<GOVPaymentTransactionEntity>builder()
                .transKey("TRANS123")
                .requireAuth(true)
                .build();
        when(commonService.initBulkTransactions(any(), any()))
                .thenReturn(Result.success(processResponse));

        // Act
        Result<TxnInitPushRes> result = batchService.initPush(request);

        // Assert
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("TRANS123", result.getData().getTransKey());
        assertTrue(result.getData().isRequireAuth());
        verify(commonService).initBulkTransactions(any(), any());
    }

    @Test
    void confirmPush_WhenBatchNotFound_ShouldReturnError() {
        // Arrange
        BatchConfirmReq request = new BatchConfirmReq();
        request.setTransKey("TRANS123");
        request.setConfirmValue("confirmValue");

        // Mock commonService to return an error for batch not found
        when(commonService.authConfirmRqApprovalBulk(anyString(), anyString()))
                .thenReturn(Result.error(ResponseCode.RESOURCE_NOTFOUND.code(), "Batch not found"));

        // Act
        Result<BatchProcessResultRes> result = batchService.confirmPush(request);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
    }

    @Test
    void confirmPush_WhenWrongStatus_ShouldReturnError() {
        // Arrange
        BatchConfirmReq request = new BatchConfirmReq();
        request.setTransKey("TRANS123");
        request.setConfirmValue("confirmValue");

        // Mock commonService to return an error for wrong status
        when(commonService.authConfirmRqApprovalBulk(anyString(), anyString()))
                .thenReturn(Result.error(ResponseCode.TRANS_STATUS.code(), "Wrong status"));

        // Act
        Result<BatchProcessResultRes> result = batchService.confirmPush(request);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_STATUS.code(), result.getCode());
    }

    @Test
    void confirmPush_WhenWrongUser_ShouldReturnError() {
        // Arrange
        BatchConfirmReq request = new BatchConfirmReq();
        request.setTransKey("TRANS123");
        request.setConfirmValue("confirmValue");

        // Mock commonService to return an error for wrong user
        when(commonService.authConfirmRqApprovalBulk(anyString(), anyString()))
                .thenReturn(Result.error(ResponseCode.TRANS_INFO.code(), "Wrong user"));

        // Act
        Result<BatchProcessResultRes> result = batchService.confirmPush(request);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TRANS_INFO.code(), result.getCode());
    }

    @Test
    void confirmPush_Success() {
        // Arrange
        String batchNo = "BDR20240321123456";
        String batchId = UUID.randomUUID().toString();
        BatchConfirmReq request = new BatchConfirmReq();
        request.setTransKey("TRANS123");
        request.setConfirmValue("confirmValue");

        // Mock authenticated user
        CurrentUser mockUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        when(mockUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getUsername()).thenReturn("testUser");
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockUser);

        // Mock valid batch
        GOVPaymentBatchEntity batchEntity = GOVPaymentBatchEntity.builder()
                .id(batchId)
                .batchNo(batchNo)
                .status(BatchStatusEnum.PROCESSED)
                .batchType(BatchTypeEnum.PAYMENT)
                .build();
        batchEntity.setCreatedBy("testUser");
        when(govPaymentBatchRepository.findByBatchNo(eq(batchNo), isNull()))
                .thenReturn(Optional.of(batchEntity));

        // Mock batch items
        GOVPaymentBatchItemEntity batchItem = new GOVPaymentBatchItemEntity();
        batchItem.setId("item-1");
        batchItem.setDebitAccNo("ACC123");
        batchItem.setCcy("VND");
        batchItem.setAmount("1000000");
        List<GOVPaymentBatchItemEntity> batchItems = Collections.singletonList(batchItem);
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(batchItems);

        // Mock successful transaction confirmation
        GOVPaymentTransactionEntity successTxn = new GOVPaymentTransactionEntity();
        successTxn.setId("txn-1");
        successTxn.setBatchNo(batchNo);
        successTxn.setPaymentItems(Collections.emptySet());
        List<GOVPaymentTransactionEntity> successTxns = Collections.singletonList(successTxn);
        List<TransactionResDetail> failTxns = Collections.emptyList();

        ConfirmResDto<GOVPaymentTransactionEntity> confirmRes = ConfirmResDto.<GOVPaymentTransactionEntity>builder()
                .successfulTransactions(successTxns)
                .failedTransactions(failTxns)
                .build();
        when(commonService.authConfirmRqApprovalBulk(anyString(), anyString()))
                .thenReturn(Result.success(confirmRes));

        // Act
        Result<BatchProcessResultRes> result = batchService.confirmPush(request);

        // Assert
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getTotal());
        assertEquals(1L, result.getData().getTotalSuccess());
        assertEquals(0L, result.getData().getTotalFail());
        verify(commonService).authConfirmRqApprovalBulk(anyString(), anyString());
    }
}

