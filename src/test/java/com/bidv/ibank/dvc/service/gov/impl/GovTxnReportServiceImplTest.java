package com.bidv.ibank.dvc.service.gov.impl;

import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GovTxnReportServiceImplTest {

    @Mock
    private CommonService commonService;

    @Mock
    private GOVPaymentTransactionRepository govPaymentTransactionRepository;

    @Mock
    private GOVPaymentTransactionMapper govPaymentTransactionMapper;

    @InjectMocks
    private GovTxnReportServiceImpl govTxnReportService;

    private MockedStatic<AuthenticationUtils> mockedAuthUtils;

    @BeforeEach
    void setUp() {
        CurrentUser mockCurrentUser = mock(CurrentUser.class);
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        lenient().when(mockUserInfo.getUsername()).thenReturn("testuser");
        lenient().when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
        mockedAuthUtils = Mockito.mockStatic(AuthenticationUtils.class);
        mockedAuthUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);
    }

    @AfterEach
    void tearDown() {
        mockedAuthUtils.close();
    }

    @Test
    void listReport() {
        // Given
        TxnReportListReq req = new TxnReportListReq();
        AuthAccountDto authAccountDto = new AuthAccountDto();
        authAccountDto.setAccountNo("12345");
        List<AuthAccountDto> accounts = Collections.singletonList(authAccountDto);
        when(commonService.getAuthorizedAccounts(any(), any())).thenReturn(accounts);

        GOVPaymentTransactionEntity entity = new GOVPaymentTransactionEntity();
        Page<GOVPaymentTransactionEntity> page = new PageImpl<>(Collections.singletonList(entity));
        when(govPaymentTransactionRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(page);

        TxnReportListRes res = new TxnReportListRes();
        when(govPaymentTransactionMapper.toReportListRes(any())).thenReturn(res);

        // When
        ResultList<TxnReportListRes> result = govTxnReportService.listReport(req);

        // Then
        assertNotNull(result);
        assertFalse(result.getData().getItems().isEmpty());
    }
} 