package com.bidv.ibank.dvc.service.customsduty.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class TxnServiceDetailTest {

    @Mock
    private CommonService commonService;

    @Mock
    private GOVPaymentTransactionRepository govPaymentTransactionRepository;

    @Mock
    private GOVPaymentTransactionMapper govPaymentTransactionMapper;

    @InjectMocks
    private TxnServiceImpl txnService;

    private MockedStatic<Translator> translatorMock;

    private static final String TXN_ID = "TXN001";
    private static final String DEBIT_ACC_NO = "********";
    private static final BigDecimal AMOUNT = new BigDecimal("1000.00");
    private static final String TAX_CODE = "**********";
    private static final String CCY = "VND";
    private static final String STATUS = "SUCCESS";

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(Object[].class))).thenReturn("Mocked message");
        translatorMock.when(() -> Translator.toLocale(any(String.class))).thenReturn("Mocked message");
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void detail_WhenTransactionNotFound_ShouldReturnError() {
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(TXN_ID);

        when(govPaymentTransactionRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        Result<TxnDetailRes> result = txnService.detail(req);

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.TXN_NOT_FOUND.code());
    }

    @Test
    void detail_WhenTransactionIsDeleted_ShouldReturnError() {
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(TXN_ID);

        when(govPaymentTransactionRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        Result<TxnDetailRes> result = txnService.detail(req);

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.TXN_NOT_FOUND.code());
    }

    @Test
    void detail_WhenUserNotAuthorized_ShouldReturnError() {
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(TXN_ID);

        GOVPaymentTransactionEntity txnEntity = mock(GOVPaymentTransactionEntity.class);
        when(txnEntity.getDebitAccNo()).thenReturn(DEBIT_ACC_NO);

        when(govPaymentTransactionRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(txnEntity));

        when(commonService.checkAuthorizationAccount(
                eq(DEBIT_ACC_NO),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(false);

        Result<TxnDetailRes> result = txnService.detail(req);

        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.USER_ACCOUNT_INVALID.code());
    }

    @Test
    void detail_WhenValidRequest_ShouldReturnSuccess() {
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(TXN_ID);

        GOVPaymentTransactionEntity txnEntity = mock(GOVPaymentTransactionEntity.class);
        when(txnEntity.getDebitAccNo()).thenReturn(DEBIT_ACC_NO);

        TxnDetailRes expectedResponse = TxnDetailRes.builder()
                .txnId(TXN_ID)
                .debitAccNo(DEBIT_ACC_NO)
                .amount(AMOUNT)
                .taxCode(TAX_CODE)
                .ccy(CCY)
                .status(STATUS)
                .build();

        when(govPaymentTransactionRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(txnEntity));

        when(commonService.checkAuthorizationAccount(
                eq(DEBIT_ACC_NO),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(true);

        when(govPaymentTransactionMapper.toDetailDto(txnEntity))
                .thenReturn(expectedResponse);

        Result<TxnDetailRes> result = txnService.detail(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData())
                .isNotNull()
                .satisfies(data -> {
                    assertThat(data.getTxnId()).isEqualTo(TXN_ID);
                    assertThat(data.getDebitAccNo()).isEqualTo(DEBIT_ACC_NO);
                    assertThat(data.getAmount()).isEqualTo(AMOUNT);
                    assertThat(data.getTaxCode()).isEqualTo(TAX_CODE);
                    assertThat(data.getCcy()).isEqualTo(CCY);
                    assertThat(data.getStatus()).isEqualTo(STATUS);
                });
    }

    @Test
    void detail_WhenTransactionHasTaxItems_ShouldReturnWithTaxItems() {
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(TXN_ID);

        GOVPaymentTransactionEntity txnEntity = mock(GOVPaymentTransactionEntity.class);
        when(txnEntity.getDebitAccNo()).thenReturn(DEBIT_ACC_NO);

        List<TxnTaxFullItemDto> taxItems = new ArrayList<>();
        taxItems.add(TxnTaxFullItemDto.builder()
                .declarationNo("DEC001")
                .declarationDate(LocalDate.parse("2024-03-20"))
                .amount("500")
                .ccy(CCY)
                .build());

        TxnDetailRes expectedResponse = TxnDetailRes.builder()
                .txnId(TXN_ID)
                .debitAccNo(DEBIT_ACC_NO)
                .taxItems(taxItems)
                .build();

        when(govPaymentTransactionRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(txnEntity));

        when(commonService.checkAuthorizationAccount(
                eq(DEBIT_ACC_NO),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(true);

        when(govPaymentTransactionMapper.toDetailDto(txnEntity))
                .thenReturn(expectedResponse);

        Result<TxnDetailRes> result = txnService.detail(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTaxItems())
                .hasSize(1)
                .first()
                .satisfies(item -> {
                    assertThat(item.getDeclarationNo()).isEqualTo("DEC001");
                    assertThat(item.getDeclarationDate()).isEqualTo(LocalDate.parse("2024-03-20"));
                    assertThat(item.getAmount()).isEqualTo("500");
                    assertThat(item.getCcy()).isEqualTo(CCY);
                });
    }

    @Test
    void detail_WhenTransactionHasNullFields_ShouldHandleGracefully() {
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(TXN_ID);

        GOVPaymentTransactionEntity txnEntity = mock(GOVPaymentTransactionEntity.class);
        when(txnEntity.getDebitAccNo()).thenReturn(DEBIT_ACC_NO);

        TxnDetailRes expectedResponse = TxnDetailRes.builder()
                .txnId(TXN_ID)
                .debitAccNo(DEBIT_ACC_NO)
                .build();

        when(govPaymentTransactionRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(txnEntity));

        when(commonService.checkAuthorizationAccount(
                eq(DEBIT_ACC_NO),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(true);

        when(govPaymentTransactionMapper.toDetailDto(txnEntity))
                .thenReturn(expectedResponse);

        Result<TxnDetailRes> result = txnService.detail(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData())
                .isNotNull()
                .satisfies(data -> {
                    assertThat(data.getTxnId()).isEqualTo(TXN_ID);
                    assertThat(data.getDebitAccNo()).isEqualTo(DEBIT_ACC_NO);
                    assertThat(data.getAmount()).isNull();
                    assertThat(data.getTaxCode()).isNull();
                    assertThat(data.getCcy()).isNull();
                    assertThat(data.getStatus()).isNull();
                    assertThat(data.getTaxItems()).isNull();
                });
    }

    @Test
    void detail_ShouldVerifyRepositoryAndMapperCalls() {
        TxnDetailReq req = new TxnDetailReq();
        req.setTxnId(TXN_ID);

        GOVPaymentTransactionEntity txnEntity = mock(GOVPaymentTransactionEntity.class);
        when(txnEntity.getDebitAccNo()).thenReturn(DEBIT_ACC_NO);

        when(govPaymentTransactionRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(txnEntity));

        when(commonService.checkAuthorizationAccount(
                eq(DEBIT_ACC_NO),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name())))
                        .thenReturn(true);

        when(govPaymentTransactionMapper.toDetailDto(txnEntity))
                .thenReturn(TxnDetailRes.builder().build());

        txnService.detail(req);

        verify(govPaymentTransactionRepository).findOne(any(Specification.class));
        verify(govPaymentTransactionMapper).toDetailDto(txnEntity);
        verify(commonService).checkAuthorizationAccount(
                eq(DEBIT_ACC_NO),
                eq(CoreAccTypeEnum.DDA.name()),
                eq(ServiceGroupTypeEnum.ACC_FIN.name()));
    }
}