package com.bidv.ibank.dvc.service.h2h.impl;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.dto.h2h.H2hBatchItemDetailDto;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hBatchDetailRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.util.Translator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.data.jpa.domain.Specification;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class H2hBatchServiceImplTest {

    private GOVPaymentTransactionRepository txnRepo;
    private GOVPaymentBatchRepository batchRepo;
    private GOVPaymentBatchMapper batchMapper;
    private H2hBatchServiceImpl service;
    private CommonService commonService;

    @BeforeEach
    void setUp() {
        txnRepo = mock(GOVPaymentTransactionRepository.class);
        batchRepo = mock(GOVPaymentBatchRepository.class);
        batchMapper = mock(GOVPaymentBatchMapper.class);
        commonService = mock(CommonService.class);
        service = new H2hBatchServiceImpl(txnRepo, batchRepo, batchMapper, commonService);
    }

    @Test
    void testDetail_BatchNotFound() {
        BatchDetailReq req = new BatchDetailReq();
        req.setBatchNo("BATCH123");
        when(batchRepo.findOne(any(Specification.class))).thenReturn(Optional.empty());
        try (
            MockedStatic<AuthenticationUtils> authMock = mockStatic(AuthenticationUtils.class);
            MockedStatic<Translator> translatorMock = mockStatic(Translator.class)
        ) {
            translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Mocked Message");
            translatorMock.when(() -> Translator.toLocale(anyString())).thenReturn("Mocked Message");
            CurrentUser mockCurrentUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
            when(mockUserInfo.getUsername()).thenReturn("user1");
            authMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);

            Result<H2hBatchDetailRes> result = service.detail(req);
            assertEquals(ResponseCode.RESOURCE_NOTFOUND.code(), result.getCode());
            assertNull(result.getData());
        }
    }

    @Test
    void testDetail_BatchFound() {
        BatchDetailReq req = new BatchDetailReq();
        req.setBatchNo("BATCH123");

        // Create a proper batch entity with valid batch items
        GOVPaymentBatchEntity batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setBatchNo("BATCH123");
        batchEntity.setStatus(BatchStatusEnum.PROCESSED);
        batchEntity.setCreatedBy("user1");

        // Create batch items with VALID status
        GOVPaymentBatchItemEntity batchItem1 = new GOVPaymentBatchItemEntity();
        batchItem1.setId("item-1");
        batchItem1.setStatus(BatchItemStatusEnum.VALID);
        batchItem1.setBatchId("BATCH123");

        GOVPaymentBatchItemEntity batchItem2 = new GOVPaymentBatchItemEntity();
        batchItem2.setId("item-2");
        batchItem2.setStatus(BatchItemStatusEnum.VALID);
        batchItem2.setBatchId("BATCH123");

        List<GOVPaymentBatchItemEntity> batchItems = List.of(batchItem1, batchItem2);
        batchEntity.setGovPaymentBatchItemList(new HashSet<>(batchItems));

        // Authorized account
        AuthAccountDto authDto = new AuthAccountDto();
        authDto.setAccountNo("123456");
        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name()))
                .thenReturn(List.of(authDto));

        // Transactions matching filters - must match the batch item IDs
        GOVPaymentTransactionEntity txn1 = new GOVPaymentTransactionEntity();
        txn1.setDebitAccNo("123456");
        txn1.setStatus(TransactionStatusEnum.SUCCESS.name());
        txn1.setBatchItemId("item-1");
        txn1.setCreatedBy("user1");

        GOVPaymentTransactionEntity txn2 = new GOVPaymentTransactionEntity();
        txn2.setDebitAccNo("123456");
        txn2.setStatus(TransactionStatusEnum.SUCCESS.name());
        txn2.setBatchItemId("item-2");
        txn2.setCreatedBy("user1");

        List<GOVPaymentTransactionEntity> txnEntities = List.of(txn1, txn2);

        // Mapper expectations
        H2hBatchDetailRes detailRes = new H2hBatchDetailRes();
        detailRes.setValidItems(List.of(new H2hBatchItemDetailDto()));

        when(batchRepo.findOne(any(Specification.class))).thenReturn(Optional.of(batchEntity));
        when(txnRepo.findAll(any(Specification.class))).thenReturn(txnEntities);
        when(batchMapper.toH2hDetailDto(batchEntity, txnEntities)).thenReturn(detailRes);
        try (
            MockedStatic<AuthenticationUtils> authMock = mockStatic(AuthenticationUtils.class);
            MockedStatic<Translator> translatorMock = mockStatic(Translator.class)
        ) {
            translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Mocked Message");
            translatorMock.when(() -> Translator.toLocale(anyString())).thenReturn("Mocked Message");
            CurrentUser mockCurrentUser = mock(CurrentUser.class);
            CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
            when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
            when(mockUserInfo.getUsername()).thenReturn("user1");
            authMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);

            Result<H2hBatchDetailRes> result = service.detail(req);
            assertEquals(Result.success(detailRes).getCode(), result.getCode());
            assertEquals(detailRes, result.getData());
        }
    }
}