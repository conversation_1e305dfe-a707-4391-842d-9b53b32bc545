package com.bidv.ibank.dvc.service.customsduty.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Answers.RETURNS_SMART_NULLS;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Arrays;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.common.txn.model.dto.InitTransactionRes;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.dvc.model.request.TxnSaveReq;
import com.bidv.ibank.dvc.model.response.TxnListRes;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentItemRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.framework.util.Translator;
import org.mockito.ArgumentCaptor;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.model.mapper.tcc.TccReqMapper;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TxnServiceImplTest {

    @Mock
    private TccService tccService;
    @Mock
    private TccReqMapper tccReqMapper;
    @Mock
    private IntegrateServiceFactory integrateServiceFactory;
    @Mock
    private CommonService commonService;

    @Mock
    private GOVPaymentTransactionRepository govPaymentTransactionRepository;

    @Mock
    private GOVPaymentItemRepository govPaymentItemRepository;

    @Mock
    private GOVPaymentTransactionMapper govPaymentTransactionMapper;

    @Mock
    private TccDmDbhcRepository tccDmDbhcRepository;

    @Mock
    private TccDmCqthuRepository tccDmCqthuRepository;

    @Mock
    private TccDmTkNsnnRepository tccDmTkNsnnRepository;

    private MockedStatic<AuthenticationUtils> authUtilsMock;
    private MockedStatic<QueryUtils> queryUtilsMock;
    private MockedStatic<Translator> translatorMock;
    private Pageable mockPageable;
    private CurrentUser mockCurrentUser;

    private GOVPaymentTransactionEntity transactionEntity;
    private TxnListRes txnListAllRes;

    private TxnServiceImpl txnService;

    @BeforeEach
    void setUp() {
        mockCurrentUser = mock(CurrentUser.class);
        mockPageable = mock(Pageable.class);
        authUtilsMock = mockStatic(AuthenticationUtils.class);
        queryUtilsMock = mockStatic(QueryUtils.class);
        translatorMock = mockStatic(Translator.class);

        authUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);
        when(mockCurrentUser.getUsername()).thenReturn("testUser");

        // Add UserInfo mock
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusName()).thenReturn("testUser");

        transactionEntity = mock(GOVPaymentTransactionEntity.class, RETURNS_SMART_NULLS);
        when(transactionEntity.getId()).thenReturn("TXN001");
        when(transactionEntity.getDebitAccNo()).thenReturn("**********");
        when(transactionEntity.getTaxCode()).thenReturn("TAX001");
        when(transactionEntity.getAmount()).thenReturn(BigDecimal.valueOf(1000));
        when(transactionEntity.getCcy()).thenReturn("VND");
        when(transactionEntity.getStatus()).thenReturn("PENDING");
        when(transactionEntity.getCreatedDate()).thenReturn(LocalDateTime.now());
        when(transactionEntity.getGovPaymentItemList()).thenReturn(Set.of(mock(GOVPaymentItemEntity.class)));

        txnListAllRes = TxnListRes.builder()
                .txnId("TXN001")
                .debitAccNo("**********")
                .taxCode("TAX001")
                .amount(BigDecimal.valueOf(1000))
                .ccy("VND")
                .status("PENDING")
                .build();

        queryUtilsMock.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order[].class)))
                .thenReturn(mockPageable);
        translatorMock.when(() -> Translator.toLocale(any(String.class))).thenReturn("Mocked Translation");
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(Object[].class))).thenReturn("Mocked Translation with params");
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class))).thenReturn("Mocked Translation with default");

        txnService = new TxnServiceImpl(tccService, tccReqMapper, commonService, integrateServiceFactory, govPaymentTransactionRepository, govPaymentItemRepository, govPaymentTransactionMapper);
    }

    @AfterEach
    void tearDown() {
        if (authUtilsMock != null) {
            authUtilsMock.close();
        }
        if (queryUtilsMock != null) {
            queryUtilsMock.close();
        }
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void listPending_WhenDataExists_ShouldReturnTransactionList() {
        // Given
        when(mockCurrentUser.getUsername()).thenReturn("testuser");
        queryUtilsMock.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                .thenReturn(mockPageable);

        TxnPendingListReq request = new TxnPendingListReq();
        AuthAccountDto mockAuthAccountDto = mock(AuthAccountDto.class);
        when(mockAuthAccountDto.getAccountNo()).thenReturn("********");

        List<AuthAccountDto> accounts = Collections.singletonList(mockAuthAccountDto);
        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name()))
                .thenReturn(accounts);

        // Create a simple mock entity - we don't need to stub its methods since we're
        // mocking the mapper response
        GOVPaymentTransactionEntity mockTxnEntity = mock(GOVPaymentTransactionEntity.class);
        Page<GOVPaymentTransactionEntity> page = new PageImpl<>(
                Collections.singletonList(mockTxnEntity),
                mockPageable,
                1);

        when(govPaymentTransactionRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(page);

        // Create the expected response
        TxnPendingListRes expectedResponse = TxnPendingListRes.builder()
                .txnId("TXN001")
                .debitAccNo("********")
                .taxCode("TAX001")
                .declarationNo("DEC001")
                .amount(BigDecimal.valueOf(1000))
                .ccy("VND")
                .treasuryCode("TR01")
                .treasuryName("Treasury 1")
                .batchNo("BATCH001")
                .status("INIT")
                .createdDate(LocalDateTime.now())
                .build();

        // Mock the mapper to return our expected response
        when(govPaymentTransactionMapper.toPendingDto(mockTxnEntity)).thenReturn(expectedResponse);

        // When
        ResultList<TxnPendingListRes> result = txnService.listPending(request);

        // Then
        assertThat(result.getData().getItems().get(0)).isEqualTo(expectedResponse);

        verify(commonService).getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());
        verify(govPaymentTransactionRepository).findAll(any(Specification.class), any(Pageable.class));
        verify(govPaymentTransactionMapper).toPendingDto(mockTxnEntity);
    }

    @Test
    void listPending_WhenNoData_ShouldReturnEmptyList() {
        // Given
        when(mockCurrentUser.getUsername()).thenReturn("testuser");
        queryUtilsMock.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                .thenReturn(mockPageable);

        TxnPendingListReq request = new TxnPendingListReq();
        AuthAccountDto mockAuthAccountDto = mock(AuthAccountDto.class);
        when(mockAuthAccountDto.getAccountNo()).thenReturn("********");

        List<AuthAccountDto> accounts = Collections.singletonList(mockAuthAccountDto);
        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name()))
                .thenReturn(accounts);

        Page<GOVPaymentTransactionEntity> emptyPage = new PageImpl<>(
                Collections.emptyList(),
                mockPageable,
                0);

        when(govPaymentTransactionRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(emptyPage);

        // When
        ResultList<TxnPendingListRes> result = txnService.listPending(request);

        // Then
        assertThat(result.getData().getItems()).isEmpty();

        verify(govPaymentTransactionRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void listPending_WhenNoAuthorizedAccounts_ShouldReturnEmptyList() {
        // Given
        when(mockCurrentUser.getUsername()).thenReturn("testuser");
        queryUtilsMock.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                .thenReturn(mockPageable);

        TxnPendingListReq request = new TxnPendingListReq();
        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name()))
                .thenReturn(Collections.emptyList());

        Page<GOVPaymentTransactionEntity> emptyPage = new PageImpl<>(
                Collections.emptyList(),
                mockPageable,
                0);
        when(govPaymentTransactionRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(emptyPage);

        // When
        ResultList<TxnPendingListRes> result = txnService.listPending(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isZero();
        assertThat(result.getData().getItems()).isEmpty();

        verify(commonService).getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());
    }

    @Test
    void save_WhenSuccessful_ShouldReturnTransactionId() {
        // Arrange
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey("TEST_KEY")
                .build();

        GOVPaymentTransactionEntity govTxnEntity = new GOVPaymentTransactionEntity();
        govTxnEntity.setId("TXN_001");

        GOVPaymentItemEntity item1 = new GOVPaymentItemEntity();
        GOVPaymentItemEntity item2 = new GOVPaymentItemEntity();
        Set<GOVPaymentItemEntity> itemSet = new HashSet<>(List.of(item1, item2));
        govTxnEntity.setGovPaymentItemList(itemSet);

        @SuppressWarnings({ "unchecked" })
        InitTransactionRes<GOVPaymentTransactionEntity> initTxnRes = mock(InitTransactionRes.class);
        when(initTxnRes.getTransaction()).thenReturn(govTxnEntity);

        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initResult = Result.success(initTxnRes);
        when(commonService.initTransaction(eq("TEST_KEY"), eq(TransactionActionEnum.INIT)))
                .thenReturn(initResult);

        when(govPaymentItemRepository.saveAll(any())).thenReturn(List.of(item1, item2));

        // Act
        Result<String> result = txnService.save(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("TXN_001");

        verify(commonService).initTransaction("TEST_KEY", TransactionActionEnum.INIT);
        verify(govPaymentItemRepository).saveAll(any());
    }

    @Test
    void save_WhenInitTransactionFails_ShouldReturnError() {
        // Arrange
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey("TEST_KEY")
                .build();

        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initResult = Result.error("ERR001", "Init transaction failed");
        when(commonService.initTransaction(eq("TEST_KEY"), eq(TransactionActionEnum.INIT)))
                .thenReturn(initResult);

        // Act
        Result<String> result = txnService.save(req);

        // Assert
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo("ERR001");
        assertThat(result.getMessage()).isEqualTo("Init transaction failed");

        verify(govPaymentItemRepository, never()).saveAll(any());
    }

    @Test
    void save_WhenSaveItemsFails_ShouldThrowException() {
        // Arrange
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey("TEST_KEY")
                .build();

        GOVPaymentTransactionEntity govTxnEntity = new GOVPaymentTransactionEntity();
        govTxnEntity.setId("TXN_001");

        GOVPaymentItemEntity item1 = new GOVPaymentItemEntity();
        GOVPaymentItemEntity item2 = new GOVPaymentItemEntity();
        Set<GOVPaymentItemEntity> itemSet = new HashSet<>(List.of(item1, item2));
        govTxnEntity.setGovPaymentItemList(itemSet);

        @SuppressWarnings({ "unchecked" })
        InitTransactionRes<GOVPaymentTransactionEntity> initTxnRes = mock(InitTransactionRes.class);
        when(initTxnRes.getTransaction()).thenReturn(govTxnEntity);

        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initResult = Result.success(initTxnRes);
        when(commonService.initTransaction(eq("TEST_KEY"), eq(TransactionActionEnum.INIT)))
                .thenReturn(initResult);

        when(govPaymentItemRepository.saveAll(any())).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        assertThatThrownBy(() -> txnService.save(req))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Database error");

        verify(commonService).initTransaction("TEST_KEY", TransactionActionEnum.INIT);
        verify(govPaymentItemRepository).saveAll(any());
    }

    @Test
    void save_WhenNoItemsToSave_ShouldReturnTransactionId() {
        // Arrange
        TxnSaveReq req = TxnSaveReq.builder()
                .transKey("TEST_KEY")
                .build();

        GOVPaymentTransactionEntity govTxnEntity = new GOVPaymentTransactionEntity();
        govTxnEntity.setId("TXN_001");
        govTxnEntity.setGovPaymentItemList(new HashSet<>());

        @SuppressWarnings({ "unchecked" })
        InitTransactionRes<GOVPaymentTransactionEntity> initTxnRes = mock(InitTransactionRes.class);
        when(initTxnRes.getTransaction()).thenReturn(govTxnEntity);

        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initResult = Result.success(initTxnRes);
        when(commonService.initTransaction(eq("TEST_KEY"), eq(TransactionActionEnum.INIT)))
                .thenReturn(initResult);

        when(govPaymentItemRepository.saveAll(Collections.emptyList())).thenReturn(Collections.emptyList());

        // Act
        Result<String> result = txnService.save(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("TXN_001");

        verify(commonService).initTransaction("TEST_KEY", TransactionActionEnum.INIT);
        verify(govPaymentItemRepository).saveAll(Collections.emptyList());
    }

    @Test
    void list_WhenValidRequest_ShouldReturnSuccessResult() {
        // Arrange
        TxnListReq req = new TxnListReq();
        req.setCcys(Arrays.asList("VND")); // Set required field to avoid NPE

        AuthAccountDto account1 = new AuthAccountDto();
        account1.setAccountNo("**********");
        AuthAccountDto account2 = new AuthAccountDto();
        account2.setAccountNo("**********");
        List<AuthAccountDto> accounts = List.of(account1, account2);

        Page<GOVPaymentTransactionEntity> page = new PageImpl<>(
                List.of(transactionEntity),
                Pageable.unpaged(),
                1);

        when(commonService.getAuthorizedAccounts(
                CoreAccTypeEnum.DDA.name(),
                ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(accounts);

        when(govPaymentTransactionRepository.findAll(
                any(Specification.class),
                any(Pageable.class))).thenReturn(page);

        when(govPaymentTransactionMapper.toListDto(any(GOVPaymentTransactionEntity.class)))
                .thenReturn(txnListAllRes);

        // Act
        ResultList<TxnListRes> result = txnService.list(req);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems())
                .hasSize(1)
                .first()
                .satisfies(item -> {
                    assertThat(item.getTxnId()).isEqualTo("TXN001");
                    assertThat(item.getDebitAccNo()).isEqualTo("**********");
                    assertThat(item.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
                    assertThat(item.getStatus()).isEqualTo("PENDING");
                });
    }

    @Test
    void list_WhenNoTransactionsFound_ShouldReturnEmptyList() {
        // Arrange
        TxnListReq req = new TxnListReq();
        req.setCcys(Arrays.asList("VND")); // Set required field to avoid NPE

        AuthAccountDto account = new AuthAccountDto();
        account.setAccountNo("**********");
        List<AuthAccountDto> accounts = List.of(account);

        Page<GOVPaymentTransactionEntity> emptyPage = new PageImpl<>(
                List.of(),
                Pageable.unpaged(),
                0);

        when(commonService.getAuthorizedAccounts(
                CoreAccTypeEnum.DDA.name(),
                ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(accounts);

        when(govPaymentTransactionRepository.findAll(
                any(Specification.class),
                any(Pageable.class))).thenReturn(emptyPage);

        // Act
        ResultList<TxnListRes> result = txnService.list(req);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
    }

    @Test
    void list_WhenMultipleTransactions_ShouldReturnAllMapped() {
        // Arrange
        TxnListReq req = new TxnListReq();
        req.setCcys(Arrays.asList("VND")); // Set required field to avoid NPE

        AuthAccountDto account = new AuthAccountDto();
        account.setAccountNo("**********");
        List<AuthAccountDto> accounts = List.of(account);

        GOVPaymentTransactionEntity transaction2 = mock(GOVPaymentTransactionEntity.class);
        when(transaction2.getId()).thenReturn("TXN002");
        when(transaction2.getDebitAccNo()).thenReturn("**********");
        when(transaction2.getAmount()).thenReturn(BigDecimal.valueOf(2000));
        when(transaction2.getStatus()).thenReturn("COMPLETED");

        TxnListRes txnListAllRes2 = TxnListRes.builder()
                .txnId("TXN002")
                .debitAccNo("**********")
                .amount(BigDecimal.valueOf(2000))
                .status("COMPLETED")
                .build();

        Page<GOVPaymentTransactionEntity> page = new PageImpl<>(
                List.of(transactionEntity, transaction2),
                Pageable.unpaged(),
                2);

        when(commonService.getAuthorizedAccounts(
                CoreAccTypeEnum.DDA.name(),
                ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(accounts);

        when(govPaymentTransactionRepository.findAll(
                any(Specification.class),
                any(Pageable.class))).thenReturn(page);

        when(govPaymentTransactionMapper.toListDto(transactionEntity))
                .thenReturn(txnListAllRes);
        when(govPaymentTransactionMapper.toListDto(transaction2))
                .thenReturn(txnListAllRes2);

        // Act
        ResultList<TxnListRes> result = txnService.list(req);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(2);
        assertThat(result.getData().getItems())
                .hasSize(2)
                .containsExactly(txnListAllRes, txnListAllRes2)
                .satisfies(items -> {
                    TxnListRes first = items.get(0);
                    assertThat(first.getTxnId()).isEqualTo("TXN001");
                    assertThat(first.getDebitAccNo()).isEqualTo("**********");
                    assertThat(first.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
                    assertThat(first.getStatus()).isEqualTo("PENDING");

                    TxnListRes second = items.get(1);
                    assertThat(second.getTxnId()).isEqualTo("TXN002");
                    assertThat(second.getDebitAccNo()).isEqualTo("**********");
                    assertThat(second.getAmount()).isEqualTo(BigDecimal.valueOf(2000));
                    assertThat(second.getStatus()).isEqualTo("COMPLETED");
                });
    }

    @Test
    void list_WithPagination_ShouldReturnPagedResults() {
        // Arrange
        TxnListReq req = new TxnListReq();
        req.setCcys(Arrays.asList("VND")); // Set required field to avoid NPE

        // Set pagination through QueryUtils instead of directly
        Pageable expectedPageable = Pageable.ofSize(10).withPage(0);
        queryUtilsMock.when(() -> QueryUtils.buildPageRequest(eq(req), any(Sort.Order.class), any(Sort.Order.class)))
                .thenReturn(expectedPageable);

        AuthAccountDto account = new AuthAccountDto();
        account.setAccountNo("**********");
        List<AuthAccountDto> accounts = List.of(account);

        Page<GOVPaymentTransactionEntity> page = new PageImpl<>(
                List.of(transactionEntity),
                expectedPageable,
                15 // total elements
        );

        when(commonService.getAuthorizedAccounts(
                CoreAccTypeEnum.DDA.name(),
                ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(accounts);

        when(govPaymentTransactionRepository.findAll(
                any(Specification.class),
                any(Pageable.class))).thenReturn(page);

        when(govPaymentTransactionMapper.toListDto(transactionEntity))
                .thenReturn(txnListAllRes);

        // Act
        ResultList<TxnListRes> result = txnService.list(req);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(15);
        assertThat(result.getData().getItems())
                .hasSize(1)
                .first()
                .satisfies(item -> {
                    assertThat(item.getTxnId()).isEqualTo("TXN001");
                    assertThat(item.getDebitAccNo()).isEqualTo("**********");
                    assertThat(item.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
                    assertThat(item.getStatus()).isEqualTo("PENDING");
                });

        verify(govPaymentTransactionRepository).findAll(
                any(Specification.class),
                any(Pageable.class));
    }

    private GOVPaymentTransactionEntity createMockTxnEntity(String id, String status, String createdBy) {
        GOVPaymentTransactionEntity entity = new GOVPaymentTransactionEntity();
        entity.setId(id);
        entity.setStatus(status);
        entity.setCreatedBy(createdBy);
        return entity;
    }

    @Test
    void edit_WhenInitTransactionFails_ShouldReturnError() {
        // Arrange
        TxnSaveReq req = new TxnSaveReq();
        req.setTransKey("testKey");

        when(commonService.initTransaction(eq("testKey"), eq(TransactionActionEnum.EDIT)))
                .thenReturn(Result.error("ERROR", "Init transaction failed"));

        // Act
        Result<String> result = txnService.edit(req);

        // Assert
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo("ERROR");
        assertThat(result.getMessage()).isEqualTo("Init transaction failed");

        verify(govPaymentItemRepository, never()).deleteByTxnId(any());
        verify(govPaymentItemRepository, never()).saveAll(any());
    }

    @Test
    void edit_WhenSuccessful_ShouldUpdateItemsAndReturnSuccess() {
        // Arrange
        TxnSaveReq req = new TxnSaveReq();
        req.setTransKey("testKey");

        GOVPaymentTransactionEntity txnEntity = new GOVPaymentTransactionEntity();
        txnEntity.setId("txn123");
        txnEntity.setStatus(TransactionStatusEnum.INIT.name());
        txnEntity.setCreatedBy("testUser");

        Set<GOVPaymentItemEntity> items = new HashSet<>();
        GOVPaymentItemEntity item = new GOVPaymentItemEntity();
        items.add(item);
        txnEntity.setPaymentItems(items);

        @SuppressWarnings({ "unchecked" })
        InitTransactionRes<GOVPaymentTransactionEntity> initTxnRes = mock(InitTransactionRes.class);
        when(initTxnRes.getTransaction()).thenReturn(txnEntity);

        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initResult = Result.success(initTxnRes);
        when(commonService.initTransaction(eq("testKey"), eq(TransactionActionEnum.EDIT)))
                .thenReturn(initResult);

        // Act
        Result<String> result = txnService.edit(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("txn123");

        verify(govPaymentItemRepository).deleteByTxnId("txn123");

        ArgumentCaptor<List<GOVPaymentItemEntity>> itemsCaptor = ArgumentCaptor.forClass(List.class);
        verify(govPaymentItemRepository).saveAll(itemsCaptor.capture());

        List<GOVPaymentItemEntity> savedItems = itemsCaptor.getValue();
        assertThat(savedItems).hasSize(1);
        assertThat(savedItems.get(0).getTxnId()).isEqualTo("txn123");
    }

    @Test
    void edit_WhenTransactionStatusRejected_ShouldSucceed() {
        // Arrange
        TxnSaveReq req = new TxnSaveReq();
        req.setTransKey("testKey");

        GOVPaymentTransactionEntity txnEntity = new GOVPaymentTransactionEntity();
        txnEntity.setId("txn123");
        txnEntity.setStatus(TransactionStatusEnum.REJECTED.name());
        txnEntity.setCreatedBy("testUser");

        Set<GOVPaymentItemEntity> items = new HashSet<>();
        GOVPaymentItemEntity item = new GOVPaymentItemEntity();
        items.add(item);
        txnEntity.setGovPaymentItemList(items);

        @SuppressWarnings({ "unchecked" })
        InitTransactionRes<GOVPaymentTransactionEntity> initTxnRes = mock(InitTransactionRes.class);
        when(initTxnRes.getTransaction()).thenReturn(txnEntity);

        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initResult = Result.success(initTxnRes);
        when(commonService.initTransaction(eq("testKey"), eq(TransactionActionEnum.EDIT)))
                .thenReturn(initResult);

        // Act
        Result<String> result = txnService.edit(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("txn123");

        verify(govPaymentItemRepository).deleteByTxnId("txn123");
        verify(govPaymentItemRepository).saveAll(any());
    }
}
