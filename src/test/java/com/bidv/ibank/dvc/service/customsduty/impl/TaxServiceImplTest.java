package com.bidv.ibank.dvc.service.customsduty.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.mapper.tcc.TccReqMapper;
import com.bidv.ibank.dvc.model.mapper.tcc.TccResMapper;
import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.service.common.ValidationService;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.base.ResponseHeader;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationItem;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationItemDebtDetail;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationReq;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationRes;
import com.bidv.ibank.integrate.entity.tcc.TccMessageReq;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

import com.bidv.ibank.client.common.dto.masterdata.BalanceAccountDto;
import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionRes;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import com.bidv.ibank.dvc.model.dto.TxnTaxItemDto;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;

@ExtendWith(MockitoExtension.class)
class TaxServiceImplTest {

    @Mock
    private TccService tccService;
    @Mock
    private ParamGovService paramGovService;
    @Mock
    private CommonService commonService;
    @Mock
    private IntegrateServiceFactory integrateServiceFactory;
    @Mock
    private TccReqMapper tccReqMapper;
    @Mock
    private TccResMapper tccResMapper;
    @Mock
    private GOVPaymentTransactionMapper govPaymentTransactionMapper;
    @Mock
    private TccDmNdktRepository tccDmNdktRepository;
    @Mock
    private TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;
    @Mock
    private TccDmKhobacRepository tccDmKhobacRepository;
    @Mock
    private TccDmChuongRepository tccDmChuongRepository;
    @Mock
    private TccDmTkNsnnRepository tccDmTkNsnnRepository;
    @Mock
    private TccDmSthueHqaRepository tccDmSthueHqaRepository;
    @Mock
    private ValidationService validationService;

    @InjectMocks
    private TaxServiceImpl taxService;

    private MockedStatic<Translator> translatorMock;
    private MockedStatic<AuthenticationUtils> authenticationUtilsMock;

    @BeforeEach
    void setUp() {
        translatorMock = mockStatic(Translator.class);
        // Return a simple string for any translation, or the key itself
        translatorMock.when(() -> Translator.toLocale(anyString())).thenReturn("Mocked Translation");
        translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Mocked Translation");

        // Setup AuthenticationUtils mock only when needed
        authenticationUtilsMock = mockStatic(AuthenticationUtils.class);

        lenient().when(tccDmChuongRepository.findByMaChuongIn(any())).thenReturn(Arrays.asList(
                createTccDmChuongEntity("CH001", "Chapter 1"),
                createTccDmChuongEntity("CH002", "Chapter 2")));

        lenient().when(tccDmTkNsnnRepository.findByMaTkIn(any())).thenReturn(Arrays.asList(
                createTccDmTkNsnnEntity("RA001", "Revenue Account 1"),
                createTccDmTkNsnnEntity("RA002", "Revenue Account 2")));

        lenient().when(tccDmSthueHqaRepository.findByMaSthueIn(any())).thenReturn(Arrays.asList(
                createTccDmSthueHqaEntity("TT001", "Tax Type 1"),
                createTccDmSthueHqaEntity("TT002", "Tax Type 2")));

        // Mock validationService.validateTaxCode() methods to always return success
        lenient().when(validationService.validateTaxCode(anyString())).thenReturn(Result.success("Valid"));
        lenient().when(validationService.validateTaxCode(anyString(), anyString())).thenReturn(Result.success("Valid"));
        lenient().when(validationService.validateTaxCode(anyString(), any())).thenReturn(Result.success("Valid"));
        lenient().when(validationService.validateDuplicatePayments(any())).thenReturn(Result.success("Valid"));
    }

    @Test
    @SuppressWarnings("unchecked")
    void inquiry_WhenSuccessful_ShouldReturnValidResponse() {
        // Setup authentication mocks for this test
        CurrentUser.UserInfo mockUser = mock(CurrentUser.UserInfo.class);
        lenient().when(mockUser.getCif()).thenReturn("12345");
        CurrentUser mockCurrentUser = mock(CurrentUser.class);
        lenient().when(mockCurrentUser.getUser()).thenReturn(mockUser);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);

        // Mock CommonService.getCustomerInfo to return a customer with matching tax code
        CustomerDto customerDto = new CustomerDto();
        customerDto.setTaxCode("3500948285");
        lenient().when(commonService.getCustomerInfo(anyString())).thenReturn(Result.success(customerDto));

        // Given
        InquiryCustomsDutyReq request = InquiryCustomsDutyReq.builder()
                .taxCode("3500948285")
                .declarationNo("***********")
                .declarationYear("2024")
                .build();

        TccMessageReq<TccInquiryDeclarationReq> messageReq = new TccMessageReq<>();
        when(tccReqMapper.toInquiryDeclarationReq(request)).thenReturn(messageReq);

        TccInquiryDeclarationItem item = createTccInquiryDeclarationItem();
        TccMessageRes<TccInquiryDeclarationRes> messageRes = new TccMessageRes<>();
        TccInquiryDeclarationRes inquiryRes = new TccInquiryDeclarationRes();
        inquiryRes.setItems(Arrays.asList(item));
        messageRes.setMessageResponse(inquiryRes);

        IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccInquiryDeclarationRes>>> tccResponse = spy(new IntegrateBaseResponse<>());
        ResponseHeader header = new ResponseHeader();
        tccResponse.setHeader(header);
        EsbTccBaseBodyMessage<TccMessageRes<TccInquiryDeclarationRes>> body = new EsbTccBaseBodyMessage<>();
        body.setMessageBody(messageRes);
        tccResponse.setBody(body);

        when(tccService.executeTccCall(any(), any())).thenAnswer(invocation -> {
            Object[] args = invocation.getArguments();
            TccMessageReq<TccInquiryDeclarationReq> req = (TccMessageReq<TccInquiryDeclarationReq>) args[0];
            if (req.equals(messageReq)) {
                return Result.success(tccResponse);
            }
            return Result.error("ERR-001", "TCC call failed");
        });

        InquiryCustomsDutyRes customsDutyRes = createInquiryCustomsDutyRes();
        when(tccResMapper.toInquiryCustomsDutyRes(item)).thenReturn(Arrays.asList(customsDutyRes));

        // When
        ResultList<InquiryCustomsDutyRes> result = taxService.inquiry(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getItems()).asList().hasSize(1);
        verify(tccReqMapper).toInquiryDeclarationReq(request);
        verify(tccService).executeTccCall(eq(messageReq), any());
        verify(tccResMapper).toInquiryCustomsDutyRes(item);
    }

    @Test
    @SuppressWarnings({ "unchecked", "rawtypes" })
    void inquiry_WhenTccCallFails_ShouldReturnError() {
        // Setup authentication mocks for this test
        CurrentUser.UserInfo mockUser = mock(CurrentUser.UserInfo.class);
        lenient().when(mockUser.getCif()).thenReturn("12345");
        CurrentUser mockCurrentUser = mock(CurrentUser.class);
        lenient().when(mockCurrentUser.getUser()).thenReturn(mockUser);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);

        // Mock CommonService.getCustomerInfo to return a customer with matching tax code
        CustomerDto customerDto = new CustomerDto();
        customerDto.setTaxCode("3500948285");
        lenient().when(commonService.getCustomerInfo(anyString())).thenReturn(Result.success(customerDto));

        // Given
        InquiryCustomsDutyReq request = InquiryCustomsDutyReq.builder()
                .taxCode("3500948285")
                .declarationNo("***********")
                .declarationYear("2024")
                .build();

        TccMessageReq<TccInquiryDeclarationReq> messageReq = new TccMessageReq<>();
        when(tccReqMapper.toInquiryDeclarationReq(request)).thenReturn(messageReq);

        // Mock TCC service to return error
        Result tccResponse = Result.error("ERR-001", "TCC call failed");
        when(tccService.executeTccCall(any(), any())).thenReturn(tccResponse);
        when(tccService.getErrResultList(tccResponse)).thenReturn(ResultList.error("ERR-001", "TCC call failed"));

        // When
        ResultList<InquiryCustomsDutyRes> result = taxService.inquiry(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo("ERR-001");
        assertThat(result.getMessage()).isEqualTo("TCC call failed");
        verify(tccReqMapper).toInquiryDeclarationReq(request);
        verify(tccService).executeTccCall(eq(messageReq), any());
        verify(tccService).getErrResultList(tccResponse);
        verify(tccResMapper, never()).toInquiryCustomsDutyRes(any());
    }

    @Test
    void validate_WhenSuccessful_ShouldReturnValidResponse() {
        // Given
        ValidateCustomsDutyReq request = createValidateCustomsDutyReq();
        BalanceAccountDto balanceAccountDto = createBalanceAccountDto("VND", "BRANCH001");
        MappingTreasuryBenBankCodeDto mappingTreasuryBBCodeDto = createMappingTreasuryBBCodeDto("TREASURY_CODE", "BB_CODE");
        GOVPaymentTransactionEntity govPaymentTransactionEntity = createGOVPaymentTransactionEntity();
        ValidateTransactionRes<GOVPaymentTransactionEntity> validateTransactionRes = createValidateTransactionRes(govPaymentTransactionEntity);

        TccDmCqthuEntity revAuthEntity = new TccDmCqthuEntity();
        revAuthEntity.setMaCqthu(request.getRevAuthCode());
        revAuthEntity.setShkb(request.getTreasuryCode());
        revAuthEntity.setTen("REV_AUTH_SUCCESS");

        when(commonService.getPmtAccountDetail(request.getDebitAccNo(), "GOV01")).thenReturn(Result.success(balanceAccountDto));
        when(paramGovService.checkRevAuthInTreasury(request.getRevAuthCode(), request.getTreasuryCode()))
                .thenReturn(Result.success(revAuthEntity));
        when(paramGovService.getMappingTreasuryBenBankCode(request.getTreasuryCode())).thenReturn(mappingTreasuryBBCodeDto);
        when(govPaymentTransactionMapper.toEntity(request, mappingTreasuryBBCodeDto.getBenBankCode(), balanceAccountDto)).thenReturn(
                govPaymentTransactionEntity);
        when(commonService.validateTransaction(
                eq(govPaymentTransactionEntity),
                eq(TransactionActionEnum.INIT),
                eq(mappingTreasuryBBCodeDto.isInBidv()),
                eq(true), // !request.isInBatch()
                eq(true), // !request.isInBatch()
                any())).thenReturn(Result.success(validateTransactionRes));

        // When
        Result<ValidateCustomsDutyRes> result = taxService.validate(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();
        assertThat(result.getData().getTransKey()).isEqualTo("TRANS_KEY");
        verify(commonService).getPmtAccountDetail(request.getDebitAccNo(), "GOV01");
        verify(paramGovService).getMappingTreasuryBenBankCode(request.getTreasuryCode());
        verify(govPaymentTransactionMapper).toEntity(request, mappingTreasuryBBCodeDto.getBenBankCode(), balanceAccountDto);
        verify(commonService).validateTransaction(
                eq(govPaymentTransactionEntity),
                eq(TransactionActionEnum.INIT),
                eq(mappingTreasuryBBCodeDto.isInBidv()),
                eq(true), // !request.isInBatch()
                eq(true), // !request.isInBatch()
                any());
    }

    @Test
    void validate_WhenGetPmtAccountDetailFails_ShouldReturnError() {
        // Given
        ValidateCustomsDutyReq request = createValidateCustomsDutyReq();
        when(commonService.getPmtAccountDetail(request.getDebitAccNo(), "GOV01")).thenReturn(Result.error("ACC_ERROR", "Account detail error"));

        // When
        Result<ValidateCustomsDutyRes> result = taxService.validate(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo("ACC_ERROR");
        assertThat(result.getMessage()).isEqualTo("Account detail error");
    }

    @Test
    void validate_WhenCurrencyMismatch_ShouldReturnError() {
        // Given
        ValidateCustomsDutyReq request = createValidateCustomsDutyReq(); // Default CCY is VND
        BalanceAccountDto balanceAccountDto = createBalanceAccountDto("USD", "BRANCH001"); // Different CCY
        when(commonService.getPmtAccountDetail(request.getDebitAccNo(), "GOV01")).thenReturn(Result.success(balanceAccountDto));

        // When
        Result<ValidateCustomsDutyRes> result = taxService.validate(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.CURRENCY_NOT_MATCH.code());
        assertThat(result.getMessage()).isEqualTo(ResponseCode.CURRENCY_NOT_MATCH.message());
    }

    @Test
    void validate_WhenRevAuthNotFoundInTreasury_ShouldReturnError() {
        // Given
        ValidateCustomsDutyReq request = createValidateCustomsDutyReq();
        BalanceAccountDto balanceAccountDto = createBalanceAccountDto("VND", "BRANCH001");
        when(commonService.getPmtAccountDetail(request.getDebitAccNo(), "GOV01")).thenReturn(Result.success(balanceAccountDto));
        when(paramGovService.checkRevAuthInTreasury(request.getRevAuthCode(), request.getTreasuryCode())).thenReturn(Result.error(ResponseCode.REV_AUTH_NOT_IN_TREASURY.code(),
                "Mocked Translation"));

        // When
        Result<ValidateCustomsDutyRes> result = taxService.validate(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.REV_AUTH_NOT_IN_TREASURY.code());
        assertThat(result.getMessage()).isEqualTo("Mocked Translation");
        verify(paramGovService, never()).getMappingTreasuryBenBankCode(any());
    }

    @Test
    void validate_WhenTreasuryMappingNotFound_ShouldStillProceedWithEmptyBbCode() {
        // Given
        ValidateCustomsDutyReq request = createValidateCustomsDutyReq();
        BalanceAccountDto balanceAccountDto = createBalanceAccountDto("VND", "BRANCH001");
        MappingTreasuryBenBankCodeDto mappingTreasuryBBCodeDto = createMappingTreasuryBBCodeDto("TREASURY_CODE", "BB_CODE");
        GOVPaymentTransactionEntity govPaymentTransactionEntity = createGOVPaymentTransactionEntity();
        ValidateTransactionRes<GOVPaymentTransactionEntity> validateTransactionRes = createValidateTransactionRes(govPaymentTransactionEntity);

        TccDmCqthuEntity revAuthEntity = new TccDmCqthuEntity();
        revAuthEntity.setMaCqthu(request.getRevAuthCode());
        revAuthEntity.setShkb(request.getTreasuryCode());
        revAuthEntity.setTen("REV_AUTH_SUCCESS");

        when(commonService.getPmtAccountDetail(request.getDebitAccNo(), "GOV01")).thenReturn(Result.success(balanceAccountDto));
        when(paramGovService.checkRevAuthInTreasury(request.getRevAuthCode(), request.getTreasuryCode()))
                .thenReturn(Result.success(revAuthEntity));
        when(paramGovService.getMappingTreasuryBenBankCode(request.getTreasuryCode())).thenReturn(mappingTreasuryBBCodeDto); // Use valid mapping
        when(govPaymentTransactionMapper.toEntity(request, mappingTreasuryBBCodeDto.getBenBankCode(), balanceAccountDto)).thenReturn(govPaymentTransactionEntity);
        when(commonService.validateTransaction(
                eq(govPaymentTransactionEntity),
                eq(TransactionActionEnum.INIT),
                eq(mappingTreasuryBBCodeDto.isInBidv()),
                eq(true), // !request.isInBatch()
                eq(true), // !request.isInBatch()
                any())).thenReturn(Result.success(validateTransactionRes));

        // When
        Result<ValidateCustomsDutyRes> result = taxService.validate(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        verify(govPaymentTransactionMapper).toEntity(request, mappingTreasuryBBCodeDto.getBenBankCode(), balanceAccountDto);
        verify(commonService).validateTransaction(
                eq(govPaymentTransactionEntity),
                eq(TransactionActionEnum.INIT),
                eq(mappingTreasuryBBCodeDto.isInBidv()),
                eq(true), // !request.isInBatch()
                eq(true), // !request.isInBatch()
                any());
    }

    @Test
    void validate_WhenTransactionValidationFails_ShouldReturnError() {
        // Given
        ValidateCustomsDutyReq request = createValidateCustomsDutyReq();
        BalanceAccountDto balanceAccountDto = createBalanceAccountDto("VND", "BRANCH001");
        MappingTreasuryBenBankCodeDto mappingTreasuryBBCodeDto = createMappingTreasuryBBCodeDto("TREASURY_CODE", "BB_CODE");
        GOVPaymentTransactionEntity govPaymentTransactionEntity = createGOVPaymentTransactionEntity();

        TccDmCqthuEntity revAuthEntity = new TccDmCqthuEntity();
        revAuthEntity.setMaCqthu(request.getRevAuthCode());
        revAuthEntity.setShkb(request.getTreasuryCode());
        revAuthEntity.setTen("REV_AUTH_SUCCESS");

        when(commonService.getPmtAccountDetail(request.getDebitAccNo(), "GOV01")).thenReturn(Result.success(balanceAccountDto));
        when(paramGovService.checkRevAuthInTreasury(request.getRevAuthCode(), request.getTreasuryCode()))
                .thenReturn(Result.success(revAuthEntity));
        when(paramGovService.getMappingTreasuryBenBankCode(request.getTreasuryCode())).thenReturn(mappingTreasuryBBCodeDto);
        when(govPaymentTransactionMapper.toEntity(request, mappingTreasuryBBCodeDto.getBenBankCode(), balanceAccountDto)).thenReturn(
                govPaymentTransactionEntity);
        when(commonService.validateTransaction(
                eq(govPaymentTransactionEntity),
                eq(TransactionActionEnum.INIT),
                eq(mappingTreasuryBBCodeDto.isInBidv()),
                eq(true), // !request.isInBatch()
                eq(true), // !request.isInBatch()
                any())).thenReturn(Result.error("VALIDATE_TXN_ERROR", "Transaction validation failed"));

        // When
        Result<ValidateCustomsDutyRes> result = taxService.validate(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo("VALIDATE_TXN_ERROR");
        assertThat(result.getMessage()).isEqualTo("Transaction validation failed");
    }

    @Test
    @SuppressWarnings("unchecked")
    void validate_WhenValidatorBeforeCallTccFails_ShouldReturnErrorFromValidateTransaction() {
        // Given
        ValidateCustomsDutyReq request = createValidateCustomsDutyReq();
        BalanceAccountDto balanceAccountDto = createBalanceAccountDto("VND", "BRANCH001");
        MappingTreasuryBenBankCodeDto mappingTreasuryBBCodeDto = createMappingTreasuryBBCodeDto("TREASURY_CODE", "BB_CODE");
        GOVPaymentTransactionEntity govPaymentTransactionEntity = createGOVPaymentTransactionEntity();

        TccDmCqthuEntity revAuthEntity = new TccDmCqthuEntity();
        revAuthEntity.setMaCqthu(request.getRevAuthCode());
        revAuthEntity.setShkb(request.getTreasuryCode());
        revAuthEntity.setTen("REV_AUTH_SUCCESS");

        when(commonService.getPmtAccountDetail(request.getDebitAccNo(), "GOV01")).thenReturn(Result.success(balanceAccountDto));
        when(paramGovService.checkRevAuthInTreasury(request.getRevAuthCode(), request.getTreasuryCode()))
                .thenReturn(Result.success(revAuthEntity));
        when(paramGovService.getMappingTreasuryBenBankCode(request.getTreasuryCode())).thenReturn(mappingTreasuryBBCodeDto);
        when(govPaymentTransactionMapper.toEntity(request, mappingTreasuryBBCodeDto.getBenBankCode(), balanceAccountDto)).thenReturn(
                govPaymentTransactionEntity);

        // Simulate failure in the validatorBeforeCallTcc lambda by making commonService.validateTransaction return an error
        // when the lambda (any()) is executed.
        when(commonService.validateTransaction(
                eq(govPaymentTransactionEntity),
                eq(TransactionActionEnum.INIT),
                eq(mappingTreasuryBBCodeDto.isInBidv()),
                eq(true), // !request.isInBatch()
                eq(true), // !request.isInBatch()
                any(Function.class))).thenAnswer(invocation -> {
                    // Execute the validator to simulate its behavior. We expect it to return an error,
                    // which then causes validateTransaction to return an error.
                    // For this test, we assume the validator itself would trigger a TCC call that fails.
                    // So, we directly make validateTransaction return the error that would come from such a failure.
                    // In a real scenario, the lambda would call tccService.executeTccCall which would return an error.
                    return Result.error("TCC_VALIDATOR_ERROR", "Validator before TCC failed");
                });

        // When
        Result<ValidateCustomsDutyRes> result = taxService.validate(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo("TCC_VALIDATOR_ERROR");
        assertThat(result.getMessage()).isEqualTo("Validator before TCC failed");
    }

    private ValidateCustomsDutyReq createValidateCustomsDutyReq() {
        return ValidateCustomsDutyReq.builder()
                .debitAccNo("1234567890")
                .ccy("VND")
                .revAuthCode("REV_AUTH_CODE")
                .treasuryCode("TREASURY_CODE")
                .taxCode("TAXCODE123")
                .payerName("Payer Name")
                .payerAddr("Payer Address")
                .amount("100000")
                .taxItems(List.of(TxnTaxItemDto.builder()
                        .declarationNo("DEC001")
                        .declarationDate(LocalDate.now())
                        .ecCode("EC001")
                        .chapterCode("CH001")
                        .ccCode("CC001")
                        .eiTypeCode("EI001")
                        .taxTypeCode("TT001")
                        .amount("100000")
                        .ccy("VND")
                        .transDesc("Tax Item 1")
                        .build()))
                .build();
    }

    private BalanceAccountDto createBalanceAccountDto(String currency, String branchCode) {
        BalanceAccountDto dto = new BalanceAccountDto();
        dto.setCurrCode(currency);
        dto.setBranchCode(branchCode);
        return dto;
    }

    private MappingTreasuryBenBankCodeDto createMappingTreasuryBBCodeDto(String treasuryCode, String benBankCode) {
        MappingTreasuryBenBankCodeDto dto = new MappingTreasuryBenBankCodeDto();
        dto.setTreasuryCode(treasuryCode);
        dto.setBenBankCode(benBankCode);
        return dto;
    }

    private GOVPaymentTransactionEntity createGOVPaymentTransactionEntity() {
        GOVPaymentTransactionEntity entity = new GOVPaymentTransactionEntity();
        entity.setFeeCcy("VND");
        entity.setFeeOpt("OUR");
        entity.setFeeTotal(BigDecimal.TEN);
        entity.setAmount(new BigDecimal("100000"));
        entity.setCcy("VND");
        return entity;
    }

    private ValidateTransactionRes<GOVPaymentTransactionEntity> createValidateTransactionRes(GOVPaymentTransactionEntity entity) {
        return ValidateTransactionRes.<GOVPaymentTransactionEntity>builder()
                .transKey("TRANS_KEY")
                .transaction(entity)
                .build();
    }

    private TccInquiryDeclarationItem createTccInquiryDeclarationItem() {
        TccInquiryDeclarationItem item = new TccInquiryDeclarationItem();
        item.setSoTk("***********");
        item.setNgayDk("2024-03-20");
        item.setMaChuong("CH001");
        item.setMaHqCqt("HQ001");
        item.setTenHqPh("Customs Authority 1");
        item.setTkkb("RA001");
        item.setMaLh("EI001");
        item.setTenLh("Export/Import Type 1");
        item.setMaLt("CC001");
        item.setMaKb("TR001");
        item.setTenKb("Treasury 1");

        TccInquiryDeclarationItemDebtDetail debtDetail = new TccInquiryDeclarationItemDebtDetail();
        debtDetail.setTieuMuc("EC001");
        debtDetail.setLoaiThue("TT001");
        debtDetail.setDuNo("1000000");
        item.setCtNo(Arrays.asList(debtDetail));

        return item;
    }

    private InquiryCustomsDutyRes createInquiryCustomsDutyRes() {
        return InquiryCustomsDutyRes.builder()
                .declarationNo("***********")
                .declarationDate(LocalDate.of(2024, 3, 20))
                .chapterCode("CH001")
                .ecCode("EC001")
                .revAuthCode("HQ001")
                .revAuthName("Customs Authority 1")
                .revAccCode("RA001")
                .taxTypeCode("TT001")
                .eiTypeCode("EI001")
                .eiTypeName("Export/Import Type 1")
                .ccCode("CC001")
                .treasuryCode("TR001")
                .treasuryName("Treasury 1")
                .amount("1000000")
                .ccy("VND")
                .build();
    }

    private TccDmNdktEntity createTccDmNdktEntity(String code, String name) {
        TccDmNdktEntity entity = new TccDmNdktEntity();
        entity.setMaNdkt(code);
        entity.setTen(name);
        return entity;
    }

    private TccDmLoaitienhqaEntity createTccDmLoaitienhqaEntity(String code, String name) {
        TccDmLoaitienhqaEntity entity = new TccDmLoaitienhqaEntity();
        entity.setMaLthq(code);
        entity.setTenLthq(name);
        return entity;
    }

    private TccDmChuongEntity createTccDmChuongEntity(String code, String name) {
        TccDmChuongEntity entity = new TccDmChuongEntity();
        entity.setMaChuong(code);
        entity.setTen(name);
        return entity;
    }

    private TccDmTkNsnnEntity createTccDmTkNsnnEntity(String code, String name) {
        TccDmTkNsnnEntity entity = new TccDmTkNsnnEntity();
        entity.setMaTk(code);
        entity.setTen(name);
        return entity;
    }

    private TccDmSthueHqaEntity createTccDmSthueHqaEntity(String code, String name) {
        TccDmSthueHqaEntity entity = new TccDmSthueHqaEntity();
        entity.setMaSthue(code);
        entity.setTenSthue(name);
        return entity;
    }

    @AfterEach
    void tearDown() {
        if (translatorMock != null) {
            translatorMock.close();
        }
        if (authenticationUtilsMock != null) {
            authenticationUtilsMock.close();
        }
    }
}
