package com.bidv.ibank.dvc.service.h2h.impl;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnDetailRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnListRes;
import com.bidv.ibank.dvc.service.customsduty.TxnService;
import com.bidv.ibank.dvc.service.gov.GovTxnReportService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.mapper.ModelMapperUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class H2hTxnServiceImplTest {

    @Mock
    private GovTxnReportService govTxnReportService;
    @Mock
    private TxnService txnService;
    @InjectMocks
    private H2hTxnServiceImpl h2hTxnService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testList_success() {
        TxnReportListReq req = new TxnReportListReq();
        TxnReportListRes txnReportListRes = new TxnReportListRes();
        H2hTxnListRes h2hTxnListRes = new H2hTxnListRes();
        List<TxnReportListRes> txnList = List.of(txnReportListRes);
        ResultList<TxnReportListRes> govResult = ResultList.success(txnList, 1L);

        when(govTxnReportService.listReport(req)).thenReturn(govResult);
        try (MockedStatic<ModelMapperUtils> mapperMock = mockStatic(ModelMapperUtils.class)) {
            mapperMock.when(() -> ModelMapperUtils.map(any(TxnReportListRes.class), eq(H2hTxnListRes.class))).thenReturn(h2hTxnListRes);

            ResultList<H2hTxnListRes> result = h2hTxnService.list(req);
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(1, result.getData().getItems().size());
            assertEquals(h2hTxnListRes, result.getData().getItems().get(0));
            assertEquals(1, result.getData().getTotal());
        }
        verify(govTxnReportService, times(1)).listReport(req);
    }

    @Test
    void testList_error() {
        TxnReportListReq req = new TxnReportListReq();
        ResultList<TxnReportListRes> govResult = ResultList.error("E001", "error");
        when(govTxnReportService.listReport(req)).thenReturn(govResult);

        ResultList<H2hTxnListRes> result = h2hTxnService.list(req);
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("E001", result.getCode());
        assertEquals("error", result.getMessage());
    }

    @Test
    void testDetail_success() {
        TxnDetailReq req = new TxnDetailReq();
        TxnDetailRes txnDetailRes = new TxnDetailRes();
        H2hTxnDetailRes h2hTxnDetailRes = new H2hTxnDetailRes();
        Result<TxnDetailRes> txnResult = Result.success(txnDetailRes);
        when(txnService.detail(req)).thenReturn(txnResult);
        try (MockedStatic<ModelMapperUtils> mapperMock = mockStatic(ModelMapperUtils.class)) {
            mapperMock.when(() -> ModelMapperUtils.map(txnDetailRes, H2hTxnDetailRes.class)).thenReturn(h2hTxnDetailRes);

            Result<H2hTxnDetailRes> result = h2hTxnService.detail(req);
            assertNotNull(result);
            assertTrue(result.isSuccess());
            assertEquals(h2hTxnDetailRes, result.getData());
        }
        verify(txnService, times(1)).detail(req);
    }

    @Test
    void testDetail_error() {
        TxnDetailReq req = new TxnDetailReq();
        Result<TxnDetailRes> txnResult = Result.error("E002", "fail");
        when(txnService.detail(req)).thenReturn(txnResult);

        Result<H2hTxnDetailRes> result = h2hTxnService.detail(req);
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("E002", result.getCode());
        assertEquals("fail", result.getMessage());
    }
}