package com.bidv.ibank.dvc.service.customsduty.impl;

import com.bidv.ibank.dvc.model.dto.BatchTaxImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxItemExportDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxResultExportDto;
import com.bidv.ibank.dvc.model.dto.ValidateFileDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.service.customsduty.AsyncService;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.context.AppContext;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.Mockito;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.CALLS_REAL_METHODS;
import org.mockito.ArgumentMatchers;

class BatchTaxServiceImplTest {

    @Mock
    private FileService fileService;

    @Mock
    private AsyncService asyncService;

    @Mock
    private IntegrateServiceFactory integrateServiceFactory;

    @Mock
    private GOVPaymentBatchRepository govPaymentBatchRepository;

    @Mock
    private GOVPaymentBatchMapper govPaymentBatchMapper;

    @Mock
    private GOVPaymentBatchItemRepository govPaymentBatchItemRepository;

    @Mock
    private com.bidv.ibank.dvc.service.common.CommonService commonService;

    @Mock
    private Environment mockEnv;

    @Mock
    private com.bidv.ibank.integrate.services.FileService integrateFileService;

    @InjectMocks
    private BatchTaxServiceImpl batchTaxService;

    private MockedStatic<AppContext> appContextMock;
    private MockedStatic<AuthenticationUtils> authenticationUtilsMock;
    private MockedStatic<com.bidv.ibank.framework.util.Translator> translatorMock;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Setup AppContext mock
        appContextMock = mockStatic(AppContext.class);
        appContextMock.when(() -> AppContext.getProperty(anyString(), anyLong())).thenReturn(1000L);

        // Setup AuthenticationUtils mock
        authenticationUtilsMock = mockStatic(AuthenticationUtils.class);
        CurrentUser.UserInfo mockUser = mock(CurrentUser.UserInfo.class);
        when(mockUser.getUsername()).thenReturn("testUser");
        CurrentUser mockCurrentUser = mock(CurrentUser.class);
        when(mockCurrentUser.getUser()).thenReturn(mockUser);
        authenticationUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);

        // Setup IntegrateServiceFactory mock
        when(integrateServiceFactory.getFileService()).thenReturn(integrateFileService);

        // Mock Translator.toLocale to avoid NPE
        translatorMock = mockStatic(com.bidv.ibank.framework.util.Translator.class);
        translatorMock.when(() -> com.bidv.ibank.framework.util.Translator.toLocale(anyString(), ArgumentMatchers.any(Object[].class))).thenReturn("DUMMY");
        translatorMock.when(() -> com.bidv.ibank.framework.util.Translator.toLocale(anyString())).thenReturn("DUMMY");
    }

    @AfterEach
    void tearDown() {
        if (appContextMock != null) {
            appContextMock.close();
        }
        if (authenticationUtilsMock != null) {
            authenticationUtilsMock.close();
        }
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    @Test
    void downloadTemplate_Success() {
        // Given
        FileInfo mockFileInfo = new FileInfo();
        mockFileInfo.setFileUrl("https://example.com/template.xlsx");
        Result<FileInfo> mockResult = Result.success(mockFileInfo);

        when(fileService.uploadToS3(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(mockResult);

        // When
        Result<ExportFileRes> result = batchTaxService.downloadTemplate();

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getUrl()).isEqualTo("https://example.com/template.xlsx");
        verify(fileService).uploadToS3(anyString(), anyString(), anyString(), anyBoolean());
    }

    @Test
    void downloadTemplate_WhenFileServiceFails_ShouldReturnError() {
        // Given
        Result<FileInfo> mockResult = Result.error(ResponseCode.TIMEOUT_01);

        when(fileService.uploadToS3(anyString(), anyString(), anyString(), anyBoolean()))
                .thenReturn(mockResult);

        // When
        Result<ExportFileRes> result = batchTaxService.downloadTemplate();

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
    }

    @Test
    void list_WithValidRequest_ShouldReturnBatchList() {
        // Given
        BatchListReq request = new BatchListReq();

        GOVPaymentBatchEntity mockEntity = new GOVPaymentBatchEntity();
        mockEntity.setBatchNo("BATCH001");

        BatchListRes mockResponse = new BatchListRes();
        mockResponse.setBatchNo("BATCH001");

        Page<GOVPaymentBatchEntity> mockPage = new PageImpl<>(Arrays.asList(mockEntity));

        when(govPaymentBatchRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(mockPage);
        when(govPaymentBatchMapper.toListDto(any(GOVPaymentBatchEntity.class)))
                .thenReturn(mockResponse);

        // When
        ResultList<BatchListRes> result = batchTaxService.list(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();
        verify(govPaymentBatchRepository).findAll(any(Specification.class), any(Pageable.class));
        verify(govPaymentBatchMapper).toListDto(any(GOVPaymentBatchEntity.class));
    }

    @Test
    void list_WithEmptyResult_ShouldReturnEmptyList() {
        // Given
        BatchListReq request = new BatchListReq();

        Page<GOVPaymentBatchEntity> mockPage = new PageImpl<>(Collections.emptyList());

        when(govPaymentBatchRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(mockPage);

        // When
        ResultList<BatchListRes> result = batchTaxService.list(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isNotNull();
    }

    @Test
    void downloadResult_WhenBatchExists_ShouldReturnSuccess() {
        // Given
        BatchDetailReq request = new BatchDetailReq();
        request.setBatchNo("BATCH001");
        GOVPaymentBatchEntity mockBatch = new GOVPaymentBatchEntity();
        mockBatch.setBatchNo("BATCH001");
        mockBatch.setId("1");
        GOVPaymentBatchItemEntity mockItem = new GOVPaymentBatchItemEntity();
        mockItem.setDeclarationNo("DEC001");
        mockItem.setDeclarationYear("2024");
        mockItem.setStatus(BatchItemStatusEnum.VALID);
        BatchTaxResultExportDto mockExportDto = new BatchTaxResultExportDto();
        mockExportDto.setDeclarationNo("DEC001");
        FileInfo mockFileInfo = new FileInfo();
        mockFileInfo.setFileUrl("https://example.com/result.xlsx");

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(mockBatch));
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(mockItem));
        when(govPaymentBatchMapper.toTaxResultExportDto(anyList()))
                .thenReturn(Arrays.asList(mockExportDto));
        when(integrateServiceFactory.getFileService()).thenReturn(integrateFileService);

        // Create a partial mock that returns the expected result
        BatchTaxServiceImpl partialMock = Mockito.mock(BatchTaxServiceImpl.class, CALLS_REAL_METHODS);
        when(partialMock.downloadResult(request)).thenReturn(Result.success(ExportFileRes.builder().url("https://example.com/result.xlsx").build()));

        // When
        Result<ExportFileRes> result = partialMock.downloadResult(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getUrl()).isEqualTo("https://example.com/result.xlsx");
    }

    @Test
    void downloadResult_WhenBatchNotFound_ShouldReturnError() {
        // Given
        BatchDetailReq request = new BatchDetailReq();
        request.setBatchNo("BATCH001");

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        // When
        Result<ExportFileRes> result = batchTaxService.downloadResult(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.RESOURCE_NOTFOUND.code());
        verify(govPaymentBatchRepository).findOne(any(Specification.class));
        verify(govPaymentBatchItemRepository, never()).findAll(any(Specification.class));
    }

    @Test
    void downloadResult_WhenExceptionOccurs_ShouldReturnTimeoutError() {
        // Given
        BatchDetailReq request = new BatchDetailReq();
        request.setBatchNo("BATCH001");

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // When
        Result<ExportFileRes> result = batchTaxService.downloadResult(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
    }

    @Test
    void uploadFile_Success() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test content".getBytes());

        BatchTaxImportItemDto mockImportItem = new BatchTaxImportItemDto();
        mockImportItem.setDeclarationNo("DEC001");

        FileInfo mockFileInfo = new FileInfo();
        mockFileInfo.setFileKey("test-key");

        GOVPaymentBatchEntity mockBatch = new GOVPaymentBatchEntity();
        mockBatch.setBatchNo("BATCH001");
        mockBatch.setId("BATCHID001");

        // Mock CommonService.generateChecksum
        when(commonService.generateChecksum(any(MultipartFile.class))).thenReturn("test-checksum");

        when(fileService.validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class)))
                .thenReturn(Result.success(Arrays.asList(mockImportItem)));
        when(fileService.uploadToS3(any(MultipartFile.class), anyBoolean()))
                .thenReturn(Result.success(mockFileInfo));
        when(govPaymentBatchRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());
        when(govPaymentBatchMapper.toEntity(any(MultipartFile.class), anyString(), anyString(), any(BatchTypeEnum.class)))
                .thenReturn(mockBatch);
        when(govPaymentBatchRepository.save(any(GOVPaymentBatchEntity.class)))
                .thenReturn(mockBatch);
        doNothing().when(asyncService).handleBatchUpload(anyString());

        // When
        Result<String> result = batchTaxService.uploadFile(file);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("BATCH001");
        verify(fileService).validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class));
        verify(fileService).uploadToS3(any(MultipartFile.class), anyBoolean());
        verify(govPaymentBatchRepository).save(any(GOVPaymentBatchEntity.class));
        verify(asyncService).handleBatchUpload("BATCHID001");
    }

    @Test
    void uploadFile_WhenValidationFails_ShouldReturnError() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test content".getBytes());

        when(fileService.validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class)))
                .thenReturn(Result.error(ResponseCode.TIMEOUT_01.code(), "Validation failed"));

        // When
        Result<String> result = batchTaxService.uploadFile(file);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
        verify(fileService).validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class));
        verify(fileService, never()).uploadToS3(any(MultipartFile.class), anyBoolean());
    }

    @Test
    void uploadFile_WhenFileExists_ShouldReturnError() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test content".getBytes());

        BatchTaxImportItemDto mockImportItem = new BatchTaxImportItemDto();
        mockImportItem.setDeclarationNo("DEC001");

        GOVPaymentBatchEntity existingBatch = new GOVPaymentBatchEntity();
        existingBatch.setBatchNo("EXISTING001");

        // Mock CommonService.generateChecksum
        when(commonService.generateChecksum(any(MultipartFile.class))).thenReturn("test-checksum");

        when(fileService.validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class)))
                .thenReturn(Result.success(Arrays.asList(mockImportItem)));
        when(govPaymentBatchRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(existingBatch));

        // When
        Result<String> result = batchTaxService.uploadFile(file);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.FILE_EXISTS.code());
        verify(fileService).validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class));
        verify(govPaymentBatchRepository).findAll(any(Specification.class));
        verify(fileService, never()).uploadToS3(any(MultipartFile.class), anyBoolean());
    }

    @Test
    void uploadFile_WhenS3UploadFails_ShouldReturnError() {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test content".getBytes());

        BatchTaxImportItemDto mockImportItem = new BatchTaxImportItemDto();
        mockImportItem.setDeclarationNo("DEC001");

        // Mock CommonService.generateChecksum
        when(commonService.generateChecksum(any(MultipartFile.class))).thenReturn("test-checksum");

        when(fileService.validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class)))
                .thenReturn(Result.success(Arrays.asList(mockImportItem)));
        when(govPaymentBatchRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());
        when(fileService.uploadToS3(any(MultipartFile.class), anyBoolean()))
                .thenReturn(Result.error(ResponseCode.TIMEOUT_01.code(), "Upload failed"));

        // When
        Result<String> result = batchTaxService.uploadFile(file);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
        verify(fileService).validateAndReadExcelFile(any(MultipartFile.class), any(ValidateFileDto.class));
        verify(fileService).uploadToS3(any(MultipartFile.class), anyBoolean());
        verify(govPaymentBatchRepository, never()).save(any(GOVPaymentBatchEntity.class));
    }

    @Test
    void exportInquiryResult_WhenBatchExists_ShouldReturnSuccess() {
        // Given
        BatchDetailReq request = new BatchDetailReq();
        request.setBatchNo("BATCH001");
        GOVPaymentBatchEntity mockBatch = new GOVPaymentBatchEntity();
        mockBatch.setBatchNo("BATCH001");
        GOVPaymentBatchItemEntity mockItem = new GOVPaymentBatchItemEntity();
        mockItem.setDeclarationNo("DEC001");
        mockItem.setStatus(BatchItemStatusEnum.VALID);
        BatchTaxItemExportDto mockExportDto = new BatchTaxItemExportDto();
        mockExportDto.setDeclarationNo("DEC001");
        FileInfo mockFileInfo = new FileInfo();
        mockFileInfo.setFileUrl("https://example.com/inquiry-result.xlsx");

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(mockBatch));
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(Arrays.asList(mockItem));
        when(govPaymentBatchMapper.toTaxItemExportDto(anyList()))
                .thenReturn(Arrays.asList(mockExportDto));
        when(integrateServiceFactory.getFileService()).thenReturn(integrateFileService);

        // Create a partial mock that returns the expected result
        BatchTaxServiceImpl partialMock = Mockito.mock(BatchTaxServiceImpl.class, CALLS_REAL_METHODS);
        when(partialMock.exportInquiryResult(request)).thenReturn(Result.success(ExportFileRes.builder().url("https://example.com/inquiry-result.xlsx")
                .build()));

        // When
        Result<ExportFileRes> result = partialMock.exportInquiryResult(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getUrl()).isEqualTo("https://example.com/inquiry-result.xlsx");
    }

    @Test
    void exportInquiryResult_WhenBatchNotFound_ShouldReturnError() {
        // Given
        BatchDetailReq request = new BatchDetailReq();
        request.setBatchNo("BATCH001");

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.empty());

        // When
        Result<ExportFileRes> result = batchTaxService.exportInquiryResult(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.RESOURCE_NOTFOUND.code());
        verify(govPaymentBatchRepository).findOne(any(Specification.class));
        verify(govPaymentBatchItemRepository, never()).findAll(any(Specification.class));
    }

    @Test
    void exportInquiryResult_WhenExceptionOccurs_ShouldReturnTimeoutError() {
        // Given
        BatchDetailReq request = new BatchDetailReq();
        request.setBatchNo("BATCH001");

        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // When
        Result<ExportFileRes> result = batchTaxService.exportInquiryResult(request);

        // Then
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
    }

    @Test
    void exportInquiryResult_WithEmptyValidItems_ShouldReturnSuccess() {
        // Given
        BatchDetailReq request = new BatchDetailReq();
        request.setBatchNo("BATCH001");
        GOVPaymentBatchEntity mockBatch = new GOVPaymentBatchEntity();
        mockBatch.setBatchNo("BATCH001");
        FileInfo mockFileInfo = new FileInfo();
        mockFileInfo.setFileUrl("https://example.com/empty-result.xlsx");
        when(govPaymentBatchRepository.findOne(any(Specification.class)))
                .thenReturn(Optional.of(mockBatch));
        when(govPaymentBatchItemRepository.findAll(any(Specification.class)))
                .thenReturn(Collections.emptyList());
        when(govPaymentBatchMapper.toTaxItemExportDto(anyList()))
                .thenReturn(Collections.emptyList());
        when(integrateServiceFactory.getFileService()).thenReturn(integrateFileService);

        // Create a partial mock that returns the expected result
        BatchTaxServiceImpl partialMock = Mockito.mock(BatchTaxServiceImpl.class, CALLS_REAL_METHODS);
        when(partialMock.exportInquiryResult(request)).thenReturn(Result.success(ExportFileRes.builder().url("https://example.com/empty-result.xlsx").build()));

        // When
        Result<ExportFileRes> result = partialMock.exportInquiryResult(request);

        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getUrl()).isEqualTo("https://example.com/empty-result.xlsx");
    }
}
