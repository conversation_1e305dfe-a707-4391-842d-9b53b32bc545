package com.bidv.ibank.dvc.service.gov.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTemplateMapper;
import com.bidv.ibank.dvc.model.request.TemplateListReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTemplateRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.mockito.MockedStatic;

import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.util.Translator;
import org.springframework.test.util.ReflectionTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

@ExtendWith(MockitoExtension.class)
@DisplayName("TemplateServiceImpl Tests")
class GovTemplateServiceImplTest {

    @Mock
    private GOVPaymentTemplateRepository templateRepository;

    @Mock
    private GOVPaymentTemplateMapper govPaymentTemplateMapper;

    @InjectMocks
    private GovTemplateServiceImpl govTemplateService;

    @Captor
    private ArgumentCaptor<Specification<GOVPaymentTemplateEntity>> specificationCaptor;

    @Captor
    private ArgumentCaptor<Pageable> pageableCaptor;

    private CurrentUser mockCurrentUser(String username) {
        CurrentUser currentUser = mock(CurrentUser.class);
        UserInfo userInfo = mock(UserInfo.class);
        when(userInfo.getUsername()).thenReturn(username);
        when(currentUser.getUser()).thenReturn(userInfo);
        return currentUser;
    }

    @Nested
    @DisplayName("listTxnTemplate Tests")
    class ListTxnTemplateTests {

        @Test
        @DisplayName("Should return paginated and sorted transaction templates")
        void shouldReturnPaginatedAndSortedTransactionTemplates() {
            // Given
            TemplateListReq req = TemplateListReq.builder().build();

            GOVPaymentTemplateEntity template1 = new GOVPaymentTemplateEntity();
            GOVPaymentTemplateEntity template2 = new GOVPaymentTemplateEntity();
            List<GOVPaymentTemplateEntity> templates = List.of(template1, template2);

            TemplateListRes dto1 = new TemplateListRes();
            TemplateListRes dto2 = new TemplateListRes();

            Page<GOVPaymentTemplateEntity> page = new PageImpl<>(templates);

            // Create a Pageable with the expected sort order
            Sort sort = Sort.by(Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
            Pageable pageable = PageRequest.of(0, 10, sort);

            // Mock QueryUtils and AuthenticationUtils
            try (var queryUtils = mockStatic(QueryUtils.class);
                    var authUtils = mockStatic(AuthenticationUtils.class)) {

                queryUtils.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                        .thenReturn(pageable);

                CurrentUser currentUser = mockCurrentUser("testUser");
                authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

                // Mock repository and mapper
                when(templateRepository.findAll(any(Specification.class), any(Pageable.class)))
                        .thenReturn(page);
                when(govPaymentTemplateMapper.toListDto(template1)).thenReturn(dto1);
                when(govPaymentTemplateMapper.toListDto(template2)).thenReturn(dto2);

                // When
                ResultList<TemplateListRes> result = govTemplateService.list(req);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getData().getItems()).hasSize(2);
                assertThat(result.getData().getTotal()).isEqualTo(2);

                // Verify pageable
                verify(templateRepository).findAll(specificationCaptor.capture(), pageableCaptor.capture());
                Pageable capturedPageable = pageableCaptor.getValue();
                assertThat(capturedPageable.getPageNumber()).isEqualTo(0);
                assertThat(capturedPageable.getPageSize()).isEqualTo(10);
                assertThat(capturedPageable.getSort().toString())
                        .contains("createdDate: DESC")
                        .contains("id: DESC");

                // Verify specification
                Specification<GOVPaymentTemplateEntity> spec = specificationCaptor.getValue();
                assertThat(spec).isNotNull();
            }
        }

        @Test
        @DisplayName("Should handle empty result")
        void shouldHandleEmptyResult() {
            // Given
            TemplateListReq req = TemplateListReq.builder().build();
            Page<GOVPaymentTemplateEntity> emptyPage = new PageImpl<>(List.of());

            // Create a Pageable with the expected sort order
            Sort sort = Sort.by(Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
            Pageable pageable = PageRequest.of(0, 10, sort);

            // Mock QueryUtils and AuthenticationUtils
            try (var queryUtils = mockStatic(QueryUtils.class);
                    var authUtils = mockStatic(AuthenticationUtils.class)) {

                queryUtils.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                        .thenReturn(pageable);

                CurrentUser currentUser = mockCurrentUser("testUser");
                authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

                // Mock repository
                when(templateRepository.findAll(any(Specification.class), any(Pageable.class)))
                        .thenReturn(emptyPage);

                // When
                ResultList<TemplateListRes> result = govTemplateService.list(req);

                // Then
                assertThat(result.getData().getItems()).isEmpty();
                assertThat(result.getData().getTotal()).isZero();
            }
        }

        @Test
        @DisplayName("Should apply search criteria from request")
        void shouldApplySearchCriteriaFromRequest() {
            // Given
            TemplateListReq req = TemplateListReq.builder()
                    .search("test search")
                    .build();

            Page<GOVPaymentTemplateEntity> emptyPage = new PageImpl<>(List.of());

            // Create a Pageable with the expected sort order
            Sort sort = Sort.by(Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
            Pageable pageable = PageRequest.of(0, 10, sort);

            // Mock QueryUtils and AuthenticationUtils
            try (var queryUtils = mockStatic(QueryUtils.class);
                    var authUtils = mockStatic(AuthenticationUtils.class)) {

                queryUtils.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                        .thenReturn(pageable);

                CurrentUser currentUser = mockCurrentUser("testUser");
                authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

                // Mock repository
                when(templateRepository.findAll(any(Specification.class), any(Pageable.class)))
                        .thenReturn(emptyPage);

                // When
                govTemplateService.list(req);

                // Then
                verify(templateRepository).findAll(specificationCaptor.capture(), pageableCaptor.capture());

                // Verify pageable
                Pageable capturedPageable = pageableCaptor.getValue();
                assertThat(capturedPageable.getSort().toString())
                        .contains("createdDate: DESC")
                        .contains("id: DESC");

                // Verify specification
                Specification<GOVPaymentTemplateEntity> spec = specificationCaptor.getValue();
                assertThat(spec).isNotNull();
            }
        }

        @Test
        @DisplayName("Should apply current user filter")
        void shouldApplyCurrentUserFilter() {
            // Given
            TemplateListReq req = TemplateListReq.builder().build();
            Page<GOVPaymentTemplateEntity> emptyPage = new PageImpl<>(List.of());

            // Create a Pageable with the expected sort order
            Sort sort = Sort.by(Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
            Pageable pageable = PageRequest.of(0, 10, sort);

            // Mock QueryUtils and AuthenticationUtils
            try (var queryUtils = mockStatic(QueryUtils.class);
                    var authUtils = mockStatic(AuthenticationUtils.class)) {

                queryUtils.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                        .thenReturn(pageable);

                CurrentUser currentUser = mockCurrentUser("testUser");
                authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

                // Mock repository
                when(templateRepository.findAll(any(Specification.class), any(Pageable.class)))
                        .thenReturn(emptyPage);

                // When
                govTemplateService.list(req);

                // Then
                verify(templateRepository).findAll(specificationCaptor.capture(), pageableCaptor.capture());

                // Verify pageable
                Pageable capturedPageable = pageableCaptor.getValue();
                assertThat(capturedPageable.getSort().toString())
                        .contains("createdDate: DESC")
                        .contains("id: DESC");

                // Verify specification
                Specification<GOVPaymentTemplateEntity> spec = specificationCaptor.getValue();
                assertThat(spec).isNotNull();
            }
        }

        @Test
        @DisplayName("Should map GOVPaymentTemplateEntity to TemplateListRes correctly")
        void shouldMapEntityToTemplateListRes() {
            // Given
            GOVPaymentTemplateEntity entity = new GOVPaymentTemplateEntity();
            // set fields on entity as needed for the test

            TemplateListRes expectedRes = TemplateListRes.builder()
                .taxCode("taxCode")
                .payerName("payerName")
                .isPublic(true)
                // set other fields as needed
                .build();

            when(govPaymentTemplateMapper.toListDto(entity)).thenReturn(expectedRes);

            Page<GOVPaymentTemplateEntity> page = new PageImpl<>(List.of(entity));

            Sort sort = Sort.by(Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
            Pageable pageable = PageRequest.of(0, 10, sort);

            try (var queryUtils = mockStatic(QueryUtils.class);
                var authUtils = mockStatic(AuthenticationUtils.class)) {

                queryUtils.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                    .thenReturn(pageable);

                CurrentUser currentUser = mockCurrentUser("testUser");
                authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

                when(templateRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(page);

                // When
                ResultList<TemplateListRes> result = govTemplateService.list(TemplateListReq.builder().build());

                // Then
                assertThat(result.getData().getItems()).hasSize(1);
                TemplateListRes actualRes = result.getData().getItems().get(0);
                assertThat(actualRes.getTaxCode()).isEqualTo("taxCode");
                assertThat(actualRes.isPublic()).isTrue();
                // add more assertions as needed
            }
        }
    }

    @Nested
    @DisplayName("delete Tests")
    class DeleteTests {
        @Mock
        private CommonService commonService;
        private MockedStatic<Translator> translatorMock;

        @BeforeEach
        void setupTranslator() {
            translatorMock = org.mockito.Mockito.mockStatic(Translator.class, org.mockito.Mockito.CALLS_REAL_METHODS);
            ReflectionTestUtils.setField(Translator.class, "messageSource", org.mockito.Mockito.mock(com.bidv.ibank.framework.i18n.CompositeMessageSource.class));
        }

        @AfterEach
        void tearDownTranslator() {
            if (translatorMock != null) {
                translatorMock.close();
            }
        }

        @Test
        void delete_single_success() {
            String username = "user1";
            String accNo = "123456";
            String templateId = "id1";
            GOVPaymentTemplateEntity entity = new GOVPaymentTemplateEntity();
            entity.setId(templateId);
            entity.setStatus(AppConstants.TEMPLATE_STATUS.ACTIVE);
            entity.setCreatedBy(username);
            entity.setDebitAccNo(accNo);
            AuthAccountDto account = new AuthAccountDto();
            account.setAccountNo(accNo);
        }
    }
}