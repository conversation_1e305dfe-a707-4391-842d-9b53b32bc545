package com.bidv.ibank.dvc.service.common.impl;

import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.util.FileUtils;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseRequest;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.base.RequestHeader;
import com.bidv.ibank.integrate.entity.base.ResponseHeader;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FileServiceImplTest {

    @InjectMocks
    private FileServiceImpl fileService;

    @Mock
    private IntegrateServiceFactory integrateServiceFactoryMock;

    @Mock
    private com.bidv.ibank.integrate.services.FileService integrateFileServiceMock;

    private MockedStatic<IntegrateServiceFactory> integrateServiceFactoryStaticMock;
    private MockedStatic<FileUtils> fileUtilsStaticMock;
    private MockedStatic<Translator> translatorStaticMock;

    @BeforeEach
    void setUp() {
        integrateServiceFactoryStaticMock = Mockito.mockStatic(IntegrateServiceFactory.class);
        fileUtilsStaticMock = Mockito.mockStatic(FileUtils.class);
        translatorStaticMock = Mockito.mockStatic(Translator.class);

        Mockito.lenient().when(integrateServiceFactoryMock.getFileService()).thenReturn(integrateFileServiceMock);
        integrateServiceFactoryStaticMock.when(IntegrateServiceFactory::createRequestHeader)
                .thenReturn(new RequestHeader());

        translatorStaticMock.when(() -> Translator.toLocale(anyString(), anyString(), any(Object[].class), any(Locale.class)))
                .thenReturn("Mocked Translated Message");
        translatorStaticMock.when(() -> Translator.toLocale(anyString()))
                .thenReturn("Mocked Translated Message");
    }

    @AfterEach
    void tearDown() {
        integrateServiceFactoryStaticMock.close();
        fileUtilsStaticMock.close();
        if (translatorStaticMock != null) {
            translatorStaticMock.close();
        }
    }

    @Test
    void uploadToS3_ByteArray_Success() {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.writeBytes("test data".getBytes());
        String fileName = "test.txt";
        String fileUrl = "http://s3.example.com/test.txt";

        IntegrateBaseResponse<List<FileInfo>> apiResponse = new IntegrateBaseResponse<>();
        ResponseHeader resHeader = new ResponseHeader();
        apiResponse.setHeader(resHeader);
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFilename(fileName);
        fileInfo.setFileUrl(fileUrl);
        apiResponse.setBody(List.of(fileInfo));

        integrateServiceFactoryStaticMock.when(() -> IntegrateServiceFactory.isSuccess(apiResponse)).thenReturn(true);
        when(integrateFileServiceMock.uploadFile(any(IntegrateBaseRequest.class))).thenReturn(apiResponse);

        Result<FileInfo> result = fileService.uploadToS3(baos, fileName, true);

        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(fileUrl, result.getData().getFileUrl());
        assertEquals(fileName, result.getData().getFilename());
    }

    @Test
    void uploadToS3_ByteArray_ApiFailure() {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.writeBytes("test data".getBytes());
        String fileName = "test.txt";

        IntegrateBaseResponse<List<FileInfo>> apiResponse = new IntegrateBaseResponse<>();
        ResponseHeader resHeader = new ResponseHeader();
        resHeader.setErrorCode("API_ERR_01");
        resHeader.setErrorDesc("API Error Description");
        apiResponse.setHeader(resHeader);

        integrateServiceFactoryStaticMock.when(() -> IntegrateServiceFactory.isSuccess(apiResponse)).thenReturn(false);
        when(integrateFileServiceMock.uploadFile(any(IntegrateBaseRequest.class))).thenReturn(apiResponse);

        Result<FileInfo> result = fileService.uploadToS3(baos, fileName, true);

        assertFalse(result.isSuccess());
        assertEquals("API_ERR_01", result.getCode());
        assertEquals("API Error Description", result.getMessage());
    }

    @Test
    void uploadToS3_ByteArray_Success_NoMatchingFileUrl() {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.writeBytes("test data".getBytes());
        String fileName = "test.txt";
        String otherFileName = "other.txt";
        String fileUrl = "http://s3.example.com/other.txt";

        IntegrateBaseResponse<List<FileInfo>> apiResponse = new IntegrateBaseResponse<>();
        ResponseHeader resHeader = new ResponseHeader();
        apiResponse.setHeader(resHeader);
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFilename(otherFileName);
        fileInfo.setFileUrl(fileUrl);
        apiResponse.setBody(List.of(fileInfo));

        integrateServiceFactoryStaticMock.when(() -> IntegrateServiceFactory.isSuccess(apiResponse)).thenReturn(true);
        when(integrateFileServiceMock.uploadFile(any(IntegrateBaseRequest.class))).thenReturn(apiResponse);

        Result<FileInfo> result = fileService.uploadToS3(baos, fileName, true);

        assertTrue(result.isSuccess());
        assertNull(result.getData());
    }

    @Test
    void uploadToS3_ByteArray_Success_EmptyFileList() {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.writeBytes("test data".getBytes());
        String fileName = "test.txt";

        IntegrateBaseResponse<List<FileInfo>> apiResponse = new IntegrateBaseResponse<>();
        ResponseHeader resHeader = new ResponseHeader();
        apiResponse.setHeader(resHeader);
        apiResponse.setBody(Collections.emptyList());

        integrateServiceFactoryStaticMock.when(() -> IntegrateServiceFactory.isSuccess(apiResponse)).thenReturn(true);
        when(integrateFileServiceMock.uploadFile(any(IntegrateBaseRequest.class))).thenReturn(apiResponse);

        Result<FileInfo> result = fileService.uploadToS3(baos, fileName, true);

        assertTrue(result.isSuccess());
        assertNull(result.getData());
    }

    @Test
    void uploadToS3_FilePath_Success() throws IOException {
        String filePath = "dummy/path/file.txt";
        String fileName = "file";
        String fileType = "txt";
        String formattedFileName = "file.txt";
        String fileUrl = "http://s3.example.com/file.txt";
        byte[] fileContent = "test content from classpath file".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(fileContent);

        try (MockedConstruction<ClassPathResource> mockedCprConstruction = Mockito.mockConstruction(ClassPathResource.class,
                (mockCpr, context) -> {
                    when(mockCpr.getInputStream()).thenReturn(mockInputStream);
                })) {

            fileUtilsStaticMock.when(() -> FileUtils.formatFileName(fileName, fileType)).thenReturn(formattedFileName);

            IntegrateBaseResponse<List<FileInfo>> apiResponse = new IntegrateBaseResponse<>();
            ResponseHeader resHeader = new ResponseHeader();
            apiResponse.setHeader(resHeader);
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFilename(formattedFileName);
            fileInfo.setFileUrl(fileUrl);
            apiResponse.setBody(List.of(fileInfo));

            integrateServiceFactoryStaticMock.when(() -> IntegrateServiceFactory.isSuccess(apiResponse)).thenReturn(true);
            when(integrateFileServiceMock.uploadFile(any(IntegrateBaseRequest.class))).thenReturn(apiResponse);

            Result<FileInfo> result = fileService.uploadToS3(filePath, fileName, fileType, true);

            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            assertEquals(fileUrl, result.getData().getFileUrl());
            assertEquals(formattedFileName, result.getData().getFilename());
            assertEquals(1, mockedCprConstruction.constructed().size());
            fileUtilsStaticMock.verify(() -> FileUtils.formatFileName(fileName, fileType));
        }
    }

    @Test
    void uploadToS3_FilePath_IOException() throws IOException {
        String filePath = "dummy/path/file.txt";
        String fileName = "file";
        String fileType = "txt";

        try (MockedConstruction<ClassPathResource> mockedCprConstruction = Mockito.mockConstruction(ClassPathResource.class,
                (mockCpr, context) -> {
                    when(mockCpr.getInputStream()).thenThrow(new IOException("Failed to read file"));
                })) {

            Result<FileInfo> result = fileService.uploadToS3(filePath, fileName, fileType, true);

            assertFalse(result.isSuccess());
            assertEquals(ResponseCode.TIMEOUT_01.code(), result.getCode());
            assertEquals("Mocked Translated Message", result.getMessage());
            assertEquals(1, mockedCprConstruction.constructed().size());
        }
    }
}