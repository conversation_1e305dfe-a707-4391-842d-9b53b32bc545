package com.bidv.ibank.dvc.service.customsduty.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.lenient;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.client.common.dto.masterdata.ParProcessTransactionDto;
import com.bidv.ibank.client.common.util.AppConstants.TransProcessMethodResponse;
import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchValidationStatusDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLhxnkRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.i18n.CompositeMessageSource;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.excel.ExcelImporter;

@ExtendWith(MockitoExtension.class)
class AsyncServiceImplTest {

    @Mock
    private GOVPaymentBatchRepository govPaymentBatchRepository;

    @Mock
    private GOVPaymentBatchItemRepository govPaymentBatchItemRepository;

    @Mock
    private FileService fileService;

    @Mock
    private CommonService commonService;

    @Mock
    private TccDmSthueHqaRepository tccDmSthueHqaRepository;

    @Mock
    private TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;

    @Mock
    private TccDmLhxnkRepository tccDmLhxnkRepository;

    @Mock
    private TccDmNdktRepository tccDmNdktRepository;

    @Mock
    private TccDmChuongRepository tccDmChuongRepository;

    @Mock
    private TccDmDbhcRepository tccDmDbhcRepository;

    @Mock
    private TccDmCqthuRepository tccDmCqthuRepository;

    @Mock
    private TccDmKhobacRepository tccDmKhobacRepository;

    @Mock
    private TccDmTkNsnnRepository tccDmTkNsnnRepository;

    @Mock
    private ParamGovService paramGovService;

    @Mock
    private TaxService taxService;

    @Mock
    private GOVPaymentBatchMapper govPaymentBatchMapper;

    @InjectMocks
    private AsyncServiceImpl asyncService;

    private static final String BATCH_NO = "BATCH001";
    private static final Long CUS_ID = 123L;
    private static final String CREATED_BY = "test";
    private static final String FILE_KEY = "file/key/path";
    private static final String BATCH_ID = "BATCH001";

    private CurrentUser.UserInfo userInfo;
    private CurrentUser currentUser;
    private GOVPaymentBatchEntity batchEntity;
    private CompositeMessageSource messageSource;

    @BeforeEach
    void setUp() {
        currentUser = mock(CurrentUser.class);
        userInfo = mock(CurrentUser.UserInfo.class);
        lenient().when(userInfo.getCusId()).thenReturn(CUS_ID);
        lenient().when(userInfo.getUsername()).thenReturn(CREATED_BY);
        lenient().when(currentUser.getUser()).thenReturn(userInfo);

        batchEntity = new GOVPaymentBatchEntity();
        batchEntity.setBatchNo(BATCH_NO);
        batchEntity.setCusId(CUS_ID);
        batchEntity.setCreatedBy(CREATED_BY);
        batchEntity.setFileKey(FILE_KEY);
        batchEntity.setId(BATCH_ID);

        // Mock message source for translations
        messageSource = mock(CompositeMessageSource.class);
        lenient().when(messageSource.getMessage(anyString(), any(), anyString(), any())).thenReturn("Mocked Message");
        ReflectionTestUtils.setField(Translator.class, "messageSource", messageSource);
    }

    @Test
    void handleBatchUpload_WhenBatchNotFound_ShouldNotUpdateStatus() {
        try (MockedStatic<AuthenticationUtils> authUtils = Mockito.mockStatic(AuthenticationUtils.class)) {
            authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

            when(govPaymentBatchRepository.findById(BATCH_ID))
                    .thenReturn(Optional.empty());

            asyncService.handleBatchUpload(BATCH_ID);

            verify(govPaymentBatchRepository, never()).updateStatusByBatchNo(any(), any(), any(), any());
        }
    }

    @Test
    void handleBatchUpload_WhenFileDownloadFails_ShouldUpdateStatusWithError() {
        try (MockedStatic<AuthenticationUtils> authUtils = Mockito.mockStatic(AuthenticationUtils.class)) {
            authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

            when(govPaymentBatchRepository.findById(BATCH_ID))
                    .thenReturn(Optional.of(batchEntity));

            Result<byte[]> errorResult = Result.error(ResponseCode.FILE_UPLOAD_01);
            when(fileService.downloadFile(FILE_KEY)).thenReturn(errorResult);

            asyncService.handleBatchUpload(BATCH_ID);

            verify(govPaymentBatchRepository).updateStatusByBatchNo(
                    eq(BatchStatusEnum.ERROR),
                    eq(ResponseCode.FILE_UPLOAD_01.code()),
                    eq(batchEntity.getBatchNo()),
                    eq(CUS_ID));
        }
    }

    @Test
    void handleBatchUpload_WhenExceptionOccurs_ShouldUpdateStatusWithError() {
        try (MockedStatic<AuthenticationUtils> authUtils = Mockito.mockStatic(AuthenticationUtils.class)) {
            authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

            when(govPaymentBatchRepository.findById(BATCH_ID))
                    .thenReturn(Optional.of(batchEntity));
            when(fileService.downloadFile(FILE_KEY))
                    .thenThrow(new RuntimeException("Test exception"));

            asyncService.handleBatchUpload(BATCH_ID);

            verify(govPaymentBatchRepository).updateStatusByBatchNo(
                    eq(BatchStatusEnum.ERROR),
                    eq(ResponseCode.TIMEOUT_01.code()),
                    eq(batchEntity.getBatchNo()),
                    eq(CUS_ID));
        }
    }

    @Test
    void handleBatchUpload_WhenPaymentBatchTypeAndSuccess_ShouldProcessPaymentBatch() {
        try (MockedStatic<AuthenticationUtils> authUtils = Mockito.mockStatic(AuthenticationUtils.class);
                MockedStatic<ExcelImporter> excelImporter = Mockito.mockStatic(ExcelImporter.class)) {

            authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

            batchEntity.setBatchType(BatchTypeEnum.PAYMENT);
            List<BatchImportItemDto> batchItems = new ArrayList<>();
            BatchImportItemDto batchItem = mock(BatchImportItemDto.class);
            BatchValidationStatusDto validationStatus = BatchValidationStatusDto.builder()
                    .debitAccNoValid(true)
                    .taxCodeValid(true)
                    .payerNameValid(true)
                    .payerAddrValid(true)
                    .altTaxCodeValid(true)
                    .altPayerNameValid(true)
                    .altPayerAddrValid(true)
                    .declarationNoValid(true)
                    .declarationDateValid(true)
                    .treasuryCodeValid(true)
                    .revAccCodeValid(true)
                    .revAuthCodeValid(true)
                    .admAreaCodeValid(true)
                    .chapterCodeValid(true)
                    .ecCodeValid(true)
                    .amountValid(true)
                    .ccyValid(true)
                    .transDescValid(true)
                    .taxTypeCodeValid(true)
                    .ccCodeValid(true)
                    .eiTypeCodeValid(true)
                    .payerTypeValid(true)
                    .build();
            lenient().when(batchItem.getFieldValidationStatusAfterValidate()).thenReturn(validationStatus);
            batchItems.add(batchItem);

            byte[] fileData = "test data".getBytes();

            when(govPaymentBatchRepository.findById(BATCH_ID))
                    .thenReturn(Optional.of(batchEntity));
            when(fileService.downloadFile(FILE_KEY))
                    .thenReturn(Result.success(fileData));
            excelImporter.when(() -> ExcelImporter.readExcel(any(InputStream.class),
                    eq(BatchImportItemDto.class), eq(AppConstants.BATCH_DOWNLOAD_RESULT_START_ROW)))
                    .thenReturn(batchItems);

            // Mock customer info
            CustomerDto customerDto = new CustomerDto();
            customerDto.setTaxCode("*********");
            lenient().when(commonService.getCustomerInfo(any())).thenReturn(Result.success(customerDto));

            // Mock batch item repository
            lenient().when(govPaymentBatchItemRepository.saveAll(any())).thenReturn(new ArrayList<>());

            // Mock tax code validation
            lenient().when(tccDmSthueHqaRepository.findTaxTypeByCodes(any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmLoaitienhqaRepository.findLoaitienhqaByCodes(any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmLhxnkRepository.findLhxnkByCodes(any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmNdktRepository.findNdktByCodes(any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmChuongRepository.findChuongByCodes(any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmDbhcRepository.findDbhcByCodes(any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmCqthuRepository.findCqthuByCodes(any(), any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmKhobacRepository.findTreasuriesByCodes(any(), any())).thenReturn(new ArrayList<>());
            lenient().when(tccDmTkNsnnRepository.findTkNsnnByCodes(any())).thenReturn(new ArrayList<>());
            lenient().when(paramGovService.getMappingTreasuryBbByCode(any())).thenReturn(new ArrayList<>());

            // Mock batch mapper
            lenient().when(govPaymentBatchMapper.toBatchTaxItemEntity(any(), any(), any(), any()))
                    .thenReturn(new GOVPaymentBatchItemEntity());
            lenient().when(govPaymentBatchMapper.toItemEntity(any(), any()))
                    .thenReturn(new GOVPaymentBatchItemEntity());

            // Mock transaction flow validation
            List<ParProcessTransactionDto> parProcessTransactionDtos = new ArrayList<>();
            ParProcessTransactionDto parProcessTransactionDto = mock(ParProcessTransactionDto.class);
            lenient().when(parProcessTransactionDto.getProcessMethod()).thenReturn(TransProcessMethodResponse.AUTO);
            parProcessTransactionDtos.add(parProcessTransactionDto);
            lenient().when(commonService.getProcessTransaction()).thenReturn(ResultList.success(parProcessTransactionDtos));

            asyncService.handleBatchUpload(BATCH_ID);

            verify(govPaymentBatchRepository).updateStatusByBatchNo(
                    eq(BatchStatusEnum.CHECKED),
                    eq(null),
                    eq(batchEntity.getBatchNo()),
                    eq(CUS_ID));
        }
    }

    @Test
    void handleBatchUpload_WhenInquiryBatchTypeAndSuccess_ShouldProcessInquiryBatch() {
        try (MockedStatic<AuthenticationUtils> authUtils = Mockito.mockStatic(AuthenticationUtils.class);
                MockedStatic<ExcelImporter> excelImporter = Mockito.mockStatic(ExcelImporter.class)) {

            authUtils.when(AuthenticationUtils::getCurrentUser).thenReturn(currentUser);

            batchEntity.setBatchType(BatchTypeEnum.INQUIRY);
            List<BatchTaxImportItemDto> batchItems = new ArrayList<>();
            BatchTaxImportItemDto batchItem = mock(BatchTaxImportItemDto.class);
            when(batchItem.isValidRow()).thenReturn(true);
            when(batchItem.getDeclarationNo()).thenReturn("123");
            when(batchItem.getDeclarationYear()).thenReturn("2024");
            batchItems.add(batchItem);

            byte[] fileData = "test data".getBytes();

            when(govPaymentBatchRepository.findById(BATCH_ID))
                    .thenReturn(Optional.of(batchEntity));
            when(fileService.downloadFile(FILE_KEY))
                    .thenReturn(Result.success(fileData));
            excelImporter.when(() -> ExcelImporter.readExcel(any(InputStream.class),
                    eq(BatchTaxImportItemDto.class), eq(AppConstants.BATCH_TAX_FILE_START_ROW)))
                    .thenReturn(batchItems);

            // Mock customer info
            CustomerDto customerDto = new CustomerDto();
            customerDto.setTaxCode("*********");
            when(commonService.getCustomerInfo(any())).thenReturn(Result.success(customerDto));

            List<InquiryCustomsDutyRes> inquiryResults = new ArrayList<>();
            inquiryResults.add(mock(InquiryCustomsDutyRes.class));
            ResultList<InquiryCustomsDutyRes> resultList = ResultList.success(inquiryResults);
            when(taxService.inquiry(any(InquiryCustomsDutyReq.class))).thenReturn(resultList);

            // Mock batch mapper
            when(govPaymentBatchMapper.toBatchTaxItemEntity(any(), any(), any(), any()))
                    .thenReturn(new GOVPaymentBatchItemEntity());

            asyncService.handleBatchUpload(BATCH_ID);

            verify(govPaymentBatchRepository).updateStatusByBatchNo(
                    eq(BatchStatusEnum.CHECKED),
                    eq(null),
                    eq(batchEntity.getBatchNo()),
                    eq(CUS_ID));
        }
    }
}