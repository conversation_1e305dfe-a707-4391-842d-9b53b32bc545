package com.bidv.ibank.dvc.service.param.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.mapper.param.ParamMapper;
import com.bidv.ibank.dvc.model.request.AdministrativeAreaReq;
import com.bidv.ibank.dvc.model.request.RevenueAuthorityReq;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKbnnNhtmRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLhxnkRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.framework.context.AppContext;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.Translator;

@ExtendWith(MockitoExtension.class)
class ParamGovServiceImplTest {

    @Mock
    private TccDmChuongRepository tccDmChuongRepository;

    @Mock
    private TccDmNdktRepository tccDmNdktRepository;

    @Mock
    private TccDmSthueHqaRepository tccDmSthueHqaRepository;

    @Mock
    private TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;

    @Mock
    private TccDmLhxnkRepository tccDmLhxnkRepository;

    @Mock
    private TccDmKhobacRepository tccDmKhobacRepository;

    @Mock
    private TccDmTkNsnnRepository tccDmTkNsnnRepository;

    @Mock
    private TccDmCqthuRepository tccDmCqthuRepository;

    @Mock
    private TccDmDbhcRepository tccDmDbhcRepository;

    @Mock
    private TccDmKbnnNhtmRepository tccDmKbnnNhtmRepository;

    @Mock
    private ParamMapper paramMapper;

    @InjectMocks
    private ParamGovServiceImpl paramService;

    private MockedStatic<AppContext> appContextMock;
    private MockedStatic<Translator> translatorMock;

    private TccDmChuongEntity mockChapterEntity;
    private TccDmNdktEntity mockEcEntity;
    private TccDmSthueHqaEntity mockTaxEntity;
    private TccDmLoaitienhqaEntity mockCcEntity;
    private TccDmLhxnkEntity mockIeEntity;
    private TccDmKhobacEntity mockTreasuryEntity;
    private TccDmTkNsnnEntity mockRevenueAccountEntity;
    private TccDmCqthuEntity mockRevenueAuthorityEntity;
    private TccDmDbhcEntity mockAdministrativeAreaEntity;

    private ChapterRes mockChapterRes;
    private EconomicContentRes mockEcRes;
    private TaxTypeRes mockTaxRes;
    private CustomsCurrencyRes mockCcRes;
    private ExportImportType mockIeRes;
    private TreasuryRes mockTreasuryRes;
    private RevenueAccountRes mockRevenueAccountRes;
    private RevenueAuthorityRes mockRevenueAuthorityRes;
    private AdministrativeAreaRes mockAdministrativeAreaRes;

    @BeforeEach
    void setUp() {
        // Setup AppContext mock
        appContextMock = mockStatic(AppContext.class);
        appContextMock.when(() -> AppContext.getProperty(anyString())).thenReturn("ACC001,ACC002,ACC003");

        // Setup Translator mock
        translatorMock = mockStatic(Translator.class);
        translatorMock.when(() -> Translator.toLocale(anyString())).thenReturn("Mocked Translation");
        translatorMock.when(() -> Translator.toLocale(anyString(), any(Object[].class))).thenReturn("Mocked Translation");
        translatorMock.when(() -> Translator.toLocale(anyString(), anyString())).thenReturn("Mocked Translation");

        mockChapterEntity = TccDmChuongEntity.builder()
                .maChuong("CH001")
                .ten("Chapter 1")
                .build();

        mockEcEntity = TccDmNdktEntity.builder()
                .maNdkt("EC001")
                .ten("Economic Content 1")
                .build();

        mockTaxEntity = TccDmSthueHqaEntity.builder()
                .maSthue("TX001")
                .tenSthue("Tax 1")
                .build();

        mockCcEntity = TccDmLoaitienhqaEntity.builder()
                .maLthq("CC001")
                .tenLthq("Customs Currency 1")
                .build();

        mockIeEntity = TccDmLhxnkEntity.builder()
                .maLh("IE001")
                .ten("Import Export 1")
                .build();

        mockTreasuryEntity = TccDmKhobacEntity.builder()
                .shkb("TR001")
                .ten("Treasury 1")
                .maDbhc("DBHC001")
                .bk01("1")
                .build();

        mockRevenueAccountEntity = TccDmTkNsnnEntity.builder()
                .maTk("RA001")
                .ten("Revenue Account 1")
                .build();

        mockRevenueAuthorityEntity = TccDmCqthuEntity.builder()
                .maCqthu("RV001")
                .ten("Revenue Authority 1")
                .build();

        mockAdministrativeAreaEntity = TccDmDbhcEntity.builder()
                .maDbhc("DBHC001")
                .ten("Administrative Area 1")
                .build();

        mockChapterRes = ChapterRes.builder()
                .chapterCode("CH001")
                .chapterName("Chapter 1")
                .build();

        mockEcRes = EconomicContentRes.builder()
                .ecCode("EC001")
                .ecName("Economic Content 1")
                .build();

        mockTaxRes = TaxTypeRes.builder()
                .taxTypeCode("TX001")
                .taxTypeName("Tax 1")
                .build();

        mockCcRes = CustomsCurrencyRes.builder()
                .ccCode("CC001")
                .ccName("Customs Currency 1")
                .build();

        mockIeRes = ExportImportType.builder()
                .eiTypeCode("IE001")
                .eiTypeName("Import Export 1")
                .build();

        mockTreasuryRes = TreasuryRes.builder()
                .treasuryCode("TR001")
                .treasuryName("Treasury 1")
                .build();

        mockRevenueAccountRes = RevenueAccountRes.builder()
                .revAccCode("RA001")
                .revAccName("Revenue Account 1")
                .build();

        mockRevenueAuthorityRes = RevenueAuthorityRes.builder()
                .revAuthCode("RV001")
                .revAuthName("Revenue Authority 1")
                .build();

        mockAdministrativeAreaRes = AdministrativeAreaRes.builder()
                .admAreaCode("DBHC001")
                .admAreaName("Administrative Area 1")
                .build();
    }

    @Test
    void listChapter_WhenDataExists_ShouldReturnChapterList() {
        List<TccDmChuongEntity> entities = Arrays.asList(mockChapterEntity);
        when(tccDmChuongRepository.findAllByOrderByMaChuongAsc()).thenReturn(entities);
        when(paramMapper.toChapterRes(mockChapterEntity)).thenReturn(mockChapterRes);

        ResultList<ChapterRes> result = paramService.listChapter();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockChapterRes);
        verify(tccDmChuongRepository).findAllByOrderByMaChuongAsc();
        verify(paramMapper).toChapterRes(mockChapterEntity);
    }

    @Test
    void listChapter_WhenNoData_ShouldReturnEmptyList() {
        when(tccDmChuongRepository.findAllByOrderByMaChuongAsc()).thenReturn(Collections.emptyList());

        ResultList<ChapterRes> result = paramService.listChapter();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmChuongRepository).findAllByOrderByMaChuongAsc();
    }

    @Test
    void listEconomicContent_WhenDataExists_ShouldReturnEconomicContentList() {
        List<TccDmNdktEntity> entities = Arrays.asList(mockEcEntity);
        when(tccDmNdktRepository.findAllByOrderByMaNdktAsc()).thenReturn(entities);
        when(paramMapper.toEconomicContentRes(mockEcEntity)).thenReturn(mockEcRes);

        ResultList<EconomicContentRes> result = paramService.listEconomicContent();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockEcRes);
        verify(tccDmNdktRepository).findAllByOrderByMaNdktAsc();
        verify(paramMapper).toEconomicContentRes(mockEcEntity);
    }

    @Test
    void listEconomicContent_WhenNoData_ShouldReturnEmptyList() {
        when(tccDmNdktRepository.findAllByOrderByMaNdktAsc()).thenReturn(Collections.emptyList());

        ResultList<EconomicContentRes> result = paramService.listEconomicContent();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmNdktRepository).findAllByOrderByMaNdktAsc();
    }

    @Test
    void listTax_WhenDataExists_ShouldReturnTaxList() {
        List<TccDmSthueHqaEntity> entities = Arrays.asList(mockTaxEntity);
        when(tccDmSthueHqaRepository.findAllByOrderByMaSthueAsc()).thenReturn(entities);
        when(paramMapper.toTaxTypeRes(mockTaxEntity)).thenReturn(mockTaxRes);

        ResultList<TaxTypeRes> result = paramService.listTaxType();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockTaxRes);
        verify(tccDmSthueHqaRepository).findAllByOrderByMaSthueAsc();
        verify(paramMapper).toTaxTypeRes(mockTaxEntity);
    }

    @Test
    void listTax_WhenNoData_ShouldReturnEmptyList() {
        when(tccDmSthueHqaRepository.findAllByOrderByMaSthueAsc()).thenReturn(Collections.emptyList());

        ResultList<TaxTypeRes> result = paramService.listTaxType();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmSthueHqaRepository).findAllByOrderByMaSthueAsc();
    }

    @Test
    void listCustomsCurrency_WhenDataExists_ShouldReturnCustomsCurrencyList() {
        List<TccDmLoaitienhqaEntity> entities = Arrays.asList(mockCcEntity);
        when(tccDmLoaitienhqaRepository.findAllByOrderByMaLthqAsc()).thenReturn(entities);
        when(paramMapper.toCustomsCurrencyRes(mockCcEntity)).thenReturn(mockCcRes);

        ResultList<CustomsCurrencyRes> result = paramService.listCustomsCurrency();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockCcRes);
        verify(tccDmLoaitienhqaRepository).findAllByOrderByMaLthqAsc();
        verify(paramMapper).toCustomsCurrencyRes(mockCcEntity);
    }

    @Test
    void listCustomsCurrency_WhenNoData_ShouldReturnEmptyList() {
        when(tccDmLoaitienhqaRepository.findAllByOrderByMaLthqAsc()).thenReturn(Collections.emptyList());

        ResultList<CustomsCurrencyRes> result = paramService.listCustomsCurrency();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmLoaitienhqaRepository).findAllByOrderByMaLthqAsc();
    }

    @Test
    void listImportExport_WhenDataExists_ShouldReturnImportExportList() {
        List<TccDmLhxnkEntity> entities = Arrays.asList(mockIeEntity);
        when(tccDmLhxnkRepository.findAllByOrderByMaLhAsc()).thenReturn(entities);
        when(paramMapper.toExportImportType(mockIeEntity)).thenReturn(mockIeRes);

        ResultList<ExportImportType> result = paramService.listExportImportType();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockIeRes);
        verify(tccDmLhxnkRepository).findAllByOrderByMaLhAsc();
        verify(paramMapper).toExportImportType(mockIeEntity);
    }

    @Test
    void listImportExport_WhenNoData_ShouldReturnEmptyList() {
        when(tccDmLhxnkRepository.findAllByOrderByMaLhAsc()).thenReturn(Collections.emptyList());

        ResultList<ExportImportType> result = paramService.listExportImportType();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmLhxnkRepository).findAllByOrderByMaLhAsc();
    }

    @Test
    void listTreasury_WhenDataExists_ShouldReturnTreasuryList() {
        List<TccDmKhobacEntity> entities = Arrays.asList(mockTreasuryEntity);
        when(tccDmKhobacRepository.findAllByBk01OrderByShkbAsc("1")).thenReturn(entities);
        when(paramMapper.toTreasuryRes(mockTreasuryEntity)).thenReturn(mockTreasuryRes);

        ResultList<TreasuryRes> result = paramService.listTreasury();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockTreasuryRes);
        verify(tccDmKhobacRepository).findAllByBk01OrderByShkbAsc("1");
        verify(paramMapper).toTreasuryRes(mockTreasuryEntity);
    }

    @Test
    void listTreasury_WhenNoData_ShouldReturnEmptyList() {
        when(tccDmKhobacRepository.findAllByBk01OrderByShkbAsc("1")).thenReturn(Collections.emptyList());

        ResultList<TreasuryRes> result = paramService.listTreasury();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmKhobacRepository).findAllByBk01OrderByShkbAsc("1");
    }

    @Test
    void listRevenueAccount_WhenDataExists_ShouldReturnRevenueAccountList() {
        List<TccDmTkNsnnEntity> entities = Arrays.asList(mockRevenueAccountEntity);
        when(tccDmTkNsnnRepository.findAllByOrderByMaTkAsc(any(List.class))).thenReturn(entities);
        when(paramMapper.toRevenueAccountRes(mockRevenueAccountEntity)).thenReturn(mockRevenueAccountRes);

        ResultList<RevenueAccountRes> result = paramService.listRevenueAccount();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockRevenueAccountRes);
        verify(tccDmTkNsnnRepository).findAllByOrderByMaTkAsc(any(List.class));
        verify(paramMapper).toRevenueAccountRes(mockRevenueAccountEntity);
    }

    @Test
    void listRevenueAccount_WhenNoData_ShouldReturnEmptyList() {
        when(tccDmTkNsnnRepository.findAllByOrderByMaTkAsc(any(List.class))).thenReturn(Collections.emptyList());

        ResultList<RevenueAccountRes> result = paramService.listRevenueAccount();

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmTkNsnnRepository).findAllByOrderByMaTkAsc(any(List.class));
    }

    @Test
    void listRevenueAuthority_WhenDataExists_ShouldReturnRevenueAuthorityList() {
        RevenueAuthorityReq req = new RevenueAuthorityReq();
        req.setTreasuryCode("TR001");
        List<TccDmCqthuEntity> entities = Arrays.asList(mockRevenueAuthorityEntity);
        when(tccDmCqthuRepository.findAllByOrderByMaCqthuAsc("TR001")).thenReturn(entities);
        when(paramMapper.toRevenueAuthorityRes(mockRevenueAuthorityEntity)).thenReturn(mockRevenueAuthorityRes);

        ResultList<RevenueAuthorityRes> result = paramService.listRevenueAuthority(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockRevenueAuthorityRes);
        verify(tccDmCqthuRepository).findAllByOrderByMaCqthuAsc("TR001");
        verify(paramMapper).toRevenueAuthorityRes(mockRevenueAuthorityEntity);
    }

    @Test
    void listRevenueAuthority_WhenNoData_ShouldReturnEmptyList() {
        RevenueAuthorityReq req = new RevenueAuthorityReq();
        req.setTreasuryCode("TR001");
        when(tccDmCqthuRepository.findAllByOrderByMaCqthuAsc("TR001")).thenReturn(Collections.emptyList());

        ResultList<RevenueAuthorityRes> result = paramService.listRevenueAuthority(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmCqthuRepository).findAllByOrderByMaCqthuAsc("TR001");
    }

    @Test
    void listAdministrativeArea_WhenDataExists_ShouldReturnAdministrativeAreaList() {
        AdministrativeAreaReq req = new AdministrativeAreaReq();
        req.setTreasuryCode("TR001");

        when(tccDmKhobacRepository.findByShkb("TR001")).thenReturn(Optional.of(mockTreasuryEntity));
        when(tccDmDbhcRepository.findAllByOrderByMaDbhcAsc("DBHC001")).thenReturn(Arrays.asList(mockAdministrativeAreaEntity));
        when(paramMapper.toAdministrativeAreaRes(mockAdministrativeAreaEntity)).thenReturn(mockAdministrativeAreaRes);

        ResultList<AdministrativeAreaRes> result = paramService.listAdministrativeArea(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotal()).isEqualTo(1);
        assertThat(result.getData().getItems().get(0)).isEqualTo(mockAdministrativeAreaRes);
        verify(tccDmKhobacRepository).findByShkb("TR001");
        verify(tccDmDbhcRepository).findAllByOrderByMaDbhcAsc("DBHC001");
        verify(paramMapper).toAdministrativeAreaRes(mockAdministrativeAreaEntity);
    }

    @Test
    void listAdministrativeArea_WhenTreasuryNotFound_ShouldReturnEmptyList() {
        AdministrativeAreaReq req = new AdministrativeAreaReq();
        req.setTreasuryCode("TR001");

        when(tccDmKhobacRepository.findByShkb("TR001")).thenReturn(Optional.empty());

        ResultList<AdministrativeAreaRes> result = paramService.listAdministrativeArea(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmKhobacRepository).findByShkb("TR001");
    }

    @Test
    void listAdministrativeArea_WhenNoAdministrativeAreas_ShouldReturnEmptyList() {
        AdministrativeAreaReq req = new AdministrativeAreaReq();
        req.setTreasuryCode("TR001");

        when(tccDmKhobacRepository.findByShkb("TR001")).thenReturn(Optional.of(mockTreasuryEntity));
        when(tccDmDbhcRepository.findAllByOrderByMaDbhcAsc("DBHC001")).thenReturn(Collections.emptyList());

        ResultList<AdministrativeAreaRes> result = paramService.listAdministrativeArea(req);

        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        verify(tccDmKhobacRepository).findByShkb("TR001");
        verify(tccDmDbhcRepository).findAllByOrderByMaDbhcAsc("DBHC001");
    }

    @Test
    void checkRevAuthInTreasury_WhenFound_ShouldReturnSuccess() {
        String revAuthCode = "RV001";
        String treasuryCode = "TR001";
        TccDmCqthuEntity entity = TccDmCqthuEntity.builder().maCqthu(revAuthCode).shkb(treasuryCode).ten("CQ Thu 1").build();
        when(tccDmCqthuRepository.findByMaCqthuAndShkb(revAuthCode, treasuryCode)).thenReturn(Optional.of(entity));

        var result = paramService.checkRevAuthInTreasury(revAuthCode, treasuryCode);
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTen()).isEqualTo("CQ Thu 1");
        verify(tccDmCqthuRepository).findByMaCqthuAndShkb(revAuthCode, treasuryCode);
    }

    @Test
    void checkRevAuthInTreasury_WhenNotFound_ShouldReturnError() {
        String revAuthCode = "RV002";
        String treasuryCode = "TR002";
        when(tccDmCqthuRepository.findByMaCqthuAndShkb(revAuthCode, treasuryCode)).thenReturn(Optional.empty());

        var result = paramService.checkRevAuthInTreasury(revAuthCode, treasuryCode);
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(com.bidv.ibank.dvc.util.constant.ResponseCode.REV_AUTH_NOT_IN_TREASURY.code());
        verify(tccDmCqthuRepository).findByMaCqthuAndShkb(revAuthCode, treasuryCode);
    }

    @Test
    void getMappingTreasuryBenBankCode_WhenFound_ShouldReturnMapping() {
        String treasuryCode = "TR001";
        TccDmKhobacEntity entity = TccDmKhobacEntity.builder()
                .shkb(treasuryCode)
                .maNh("BB001").ten("Bank 1").bk01("1").build();
        when(tccDmKhobacRepository.findByShkbIn(List.of(treasuryCode), "1")).thenReturn(List.of(entity));

        var result = paramService.getMappingTreasuryBenBankCode(treasuryCode);
        assertThat(result.getTreasuryCode()).isEqualTo(treasuryCode);
        assertThat(result.getBenBankCode()).isEqualTo("BB001");
    }

    @Test
    void getMappingTreasuryBenBankCode_WhenNotFound_ShouldReturnEmptyMapping() {
        String treasuryCode = "TR002";
        when(paramService.getMappingTreasuryBbByCode(List.of(treasuryCode))).thenReturn(List.of());

        var result = paramService.getMappingTreasuryBenBankCode(treasuryCode);
        assertThat(result.getTreasuryCode()).isNull();
        assertThat(result.getBenBankCode()).isNull();
    }

    @AfterEach
    void tearDown() {
        if (appContextMock != null) {
            appContextMock.close();
        }
        if (translatorMock != null) {
            translatorMock.close();
        }
    }
}
