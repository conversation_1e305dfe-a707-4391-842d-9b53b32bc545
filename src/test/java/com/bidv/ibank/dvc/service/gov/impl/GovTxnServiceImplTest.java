package com.bidv.ibank.dvc.service.gov.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Answers.RETURNS_SMART_NULLS;
import static org.mockito.Mockito.doThrow;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.ArrayList;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.DisplayName;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.common.txn.model.dto.ConfirmResDto;
import com.bidv.ibank.common.txn.model.dto.InitTransactionRes;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionResponse;
import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.common.txn.model.dto.TransAuth;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.TxnProcessResultRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentItemRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.dvc.util.constant.TxnPushTypeEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.dvc.model.request.TxnConfirmReq;
import com.bidv.ibank.dvc.model.request.TxnDeleteReq;
import com.bidv.ibank.dvc.model.request.TxnInitPushReq;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class GovTxnServiceImplTest {

    @Mock
    private CommonService commonService;

    @Mock
    private GOVPaymentTransactionRepository govPaymentTransactionRepository;

    @Mock
    private GOVPaymentItemRepository govPaymentItemRepository;

    @Mock
    private GOVPaymentTransactionMapper govPaymentTransactionMapper;

    @Mock
    private TccDmDbhcRepository tccDmDbhcRepository;

    @Mock
    private TccDmCqthuRepository tccDmCqthuRepository;

    @Mock
    private TccDmTkNsnnRepository tccDmTkNsnnRepository;

    @InjectMocks
    private GovTxnServiceImpl txnService;

    private MockedStatic<AuthenticationUtils> authUtilsMock;
    private MockedStatic<QueryUtils> queryUtilsMock;
    private MockedStatic<Translator> translatorMock;
    private Pageable mockPageable;
    private CurrentUser mockCurrentUser;

    private GOVPaymentTransactionEntity transactionEntity;

    @BeforeEach
    void setUp() {
        mockCurrentUser = mock(CurrentUser.class);
        mockPageable = mock(Pageable.class);
        authUtilsMock = mockStatic(AuthenticationUtils.class);
        queryUtilsMock = mockStatic(QueryUtils.class);
        translatorMock = mockStatic(Translator.class);

        authUtilsMock.when(AuthenticationUtils::getCurrentUser).thenReturn(mockCurrentUser);
        when(mockCurrentUser.getUsername()).thenReturn("testUser");

        // Add UserInfo mock
        CurrentUser.UserInfo mockUserInfo = mock(CurrentUser.UserInfo.class);
        when(mockCurrentUser.getUser()).thenReturn(mockUserInfo);
        when(mockUserInfo.getCusName()).thenReturn("testUser");

        transactionEntity = mock(GOVPaymentTransactionEntity.class, RETURNS_SMART_NULLS);
        when(transactionEntity.getId()).thenReturn("TXN001");
        when(transactionEntity.getDebitAccNo()).thenReturn("********90");
        when(transactionEntity.getTaxCode()).thenReturn("TAX001");
        when(transactionEntity.getAmount()).thenReturn(BigDecimal.valueOf(1000));
        when(transactionEntity.getCcy()).thenReturn("VND");
        when(transactionEntity.getStatus()).thenReturn("PENDING");
        when(transactionEntity.getCreatedDate()).thenReturn(LocalDateTime.now());
        when(transactionEntity.getGovPaymentItemList()).thenReturn(Set.of(mock(GOVPaymentItemEntity.class)));

        queryUtilsMock.when(() -> QueryUtils.buildPageRequest(any(), any(Sort.Order.class), any(Sort.Order.class)))
                .thenReturn(mockPageable);
        translatorMock.when(() -> Translator.toLocale(any(String.class))).thenReturn("Mocked Translation");
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(Object[].class))).thenReturn("Mocked Translation with params");
        translatorMock.when(() -> Translator.toLocale(any(String.class), any(String.class))).thenReturn("Mocked Translation with default");
    }

    @AfterEach
    void tearDown() {
        if (authUtilsMock != null) {
            authUtilsMock.close();
        }
        if (queryUtilsMock != null) {
            queryUtilsMock.close();
        }
        if (translatorMock != null) {
            translatorMock.close();
        }
    }

    private GOVPaymentTransactionEntity createMockTxnEntity(String id, String status, String createdBy) {
        GOVPaymentTransactionEntity entity = new GOVPaymentTransactionEntity();
        entity.setId(id);
        entity.setStatus(status);
        entity.setCreatedBy(createdBy);
        return entity;
    }

    @Test
    @DisplayName("deleteSingle - Success - Status INIT")
    void deleteSingle_Success_StatusInit() {
        TxnDeleteReq req = new TxnDeleteReq();
        String txnId = "txnInit";
        req.setTxnIds(List.of(txnId));

        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(List.of(
                new AuthAccountDto()));

        GOVPaymentTransactionEntity entity = createMockTxnEntity(txnId, TransactionStatusEnum.INIT.name(), "testUser");
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(List.of(entity));

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.SUCCESS.code());
        assertThat(result.getData()).isNull();
        verify(govPaymentTransactionRepository).updateStatus(List.of(txnId), TransactionStatusEnum.DELETED.name(), "testUser");
    }

    @Test
    @DisplayName("deleteSingle - Success - Status REJECTED")
    void deleteSingle_Success_StatusRejected() {
        TxnDeleteReq req = new TxnDeleteReq();
        String txnId = "txnRejected";
        req.setTxnIds(List.of(txnId));

        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(List.of(
                new AuthAccountDto()));

        GOVPaymentTransactionEntity entity = createMockTxnEntity(txnId, TransactionStatusEnum.REJECTED.name(), "testUser");
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(List.of(entity));

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.SUCCESS.code());
        verify(govPaymentTransactionRepository).updateStatus(List.of(txnId), TransactionStatusEnum.DELETED.name(), "testUser");
    }

    @Test
    @DisplayName("deleteSingle - Failure - Not Found")
    void deleteSingle_Failure_NotFound() {
        TxnDeleteReq req = new TxnDeleteReq();
        req.setTxnIds(List.of("txnNotFound"));

        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(Collections.emptyList());

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.TXN_NOT_FOUND.code());
        verify(govPaymentTransactionRepository, never()).updateStatus(any(), any(), any());
    }

    @Test
    @DisplayName("deleteSingle - Failure - Invalid Status (e.g., APPROVED)")
    void deleteSingle_Failure_InvalidStatus() {
        TxnDeleteReq req = new TxnDeleteReq();
        String txnId = "txnApproved";
        req.setTxnIds(List.of(txnId));

        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(List.of(
                new AuthAccountDto()));

        GOVPaymentTransactionEntity entity = createMockTxnEntity(txnId, TransactionStatusEnum.APPROVED.name(), "testUser");
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(List.of(entity));

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.TRANS_STATUS.code());
        verify(govPaymentTransactionRepository, never()).updateStatus(any(), any(), any());
    }

    @Test
    @DisplayName("deleteSingle - Failure - Creator Mismatch")
    void deleteSingle_Failure_CreatorMismatch() {
        TxnDeleteReq req = new TxnDeleteReq();
        String txnId = "txnOtherUser";
        req.setTxnIds(List.of(txnId));

        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(List.of(
                new AuthAccountDto()));

        GOVPaymentTransactionEntity entity = createMockTxnEntity(txnId, TransactionStatusEnum.INIT.name(), "otherUser");
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(List.of(entity));
        // AuthenticationUtils.getCurrentUser().getUsername() is mocked to "testUser" in setUp

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.TRANS_INFO.code());
        verify(govPaymentTransactionRepository, never()).updateStatus(any(), any(), any());
    }

    @Test
    @DisplayName("deleteSingle - Failure - SaveAll Exception")
    void deleteSingle_Failure_SaveAllException() {
        TxnDeleteReq req = new TxnDeleteReq();
        String txnId = "txnSaveFail";
        req.setTxnIds(List.of(txnId));

        when(commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name())).thenReturn(List.of(
                new AuthAccountDto()));

        GOVPaymentTransactionEntity entity = createMockTxnEntity(txnId, TransactionStatusEnum.INIT.name(), "testUser");
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(List.of(entity));
        doThrow(new RuntimeException("DB error")).when(govPaymentTransactionRepository).updateStatus(any(), any(), any());

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
        assertThat(result.isSuccess()).isFalse();
        verify(govPaymentTransactionRepository).updateStatus(List.of(txnId), TransactionStatusEnum.DELETED.name(), "testUser");
    }

    @Test
    @DisplayName("deleteBulk - Success")
    void deleteBulk_Success() {
        TxnDeleteReq req = new TxnDeleteReq();
        String txnId1 = "bulkTxn1";
        String txnId2 = "bulkTxn2";
        req.setTxnIds(List.of(txnId1, txnId2));

        GOVPaymentTransactionEntity entity1 = createMockTxnEntity(txnId1, TransactionStatusEnum.INIT.name(), "testUser");
        GOVPaymentTransactionEntity entity2 = createMockTxnEntity(txnId2, TransactionStatusEnum.REJECTED.name(), "testUser");
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(List.of(entity1, entity2));

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.SUCCESS.code());
        verify(govPaymentTransactionRepository).updateStatus(List.of(txnId1, txnId2), TransactionStatusEnum.DELETED.name(), "testUser");
    }

    @Test
    @DisplayName("deleteBulk - Failure - Repository SaveAll Exception")
    void deleteBulk_Failure_SaveAllException() {
        TxnDeleteReq req = new TxnDeleteReq();
        String txnId1 = "bulkSaveFail1";
        String txnId2 = "bulkSaveFail2";
        req.setTxnIds(List.of(txnId1, txnId2));

        GOVPaymentTransactionEntity entity1 = createMockTxnEntity(txnId1, TransactionStatusEnum.INIT.name(), "testUser");
        GOVPaymentTransactionEntity entity2 = createMockTxnEntity(txnId2, TransactionStatusEnum.REJECTED.name(), "testUser");
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(List.of(entity1, entity2));
        doThrow(new RuntimeException("DB error")).when(govPaymentTransactionRepository).updateStatus(any(), any(), any());

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.TIMEOUT_01.code());
        assertThat(result.isSuccess()).isFalse();
        verify(govPaymentTransactionRepository).updateStatus(List.of(txnId1, txnId2), TransactionStatusEnum.DELETED.name(), "testUser");
    }

    @Test
    @DisplayName("delete - Success - Empty TxnIds List Input")
    void delete_Success_EmptyTxnIdsListInput() {
        TxnDeleteReq req = new TxnDeleteReq();
        req.setTxnIds(Collections.emptyList());

        // When txnIds is empty, it goes to the 'else' (bulk) branch.
        // The findAll query with empty IDs in the IN clause and other criteria will return an empty list.
        when(govPaymentTransactionRepository.findAll(any(Specification.class))).thenReturn(Collections.emptyList());

        Result<String> result = txnService.delete(req);

        assertThat(result.getCode()).isEqualTo(ResponseCode.SUCCESS.code());
        // updateStatus will be called with an empty list, which is a successful operation.
        verify(govPaymentTransactionRepository).updateStatus(Collections.emptyList(), TransactionStatusEnum.DELETED.name(), "testUser");
    }

    @Nested
    @DisplayName("initPush Tests")
    class InitPushTests {

        @Test
        @DisplayName("initPush - Success - PUSH_SAVE type")
        void initPush_Success_PushSaveType() {
            // Given
            TxnInitPushReq request = TxnInitPushReq.builder()
                    .type(TxnPushTypeEnum.PUSH_SAVE)
                    .transKey("TEST_KEY")
                    .build();

            TransAuth transAuth = TransAuth.builder()
                    .authType("OTP")
                    .build();

            // Create a mock transaction entity with an ID
            GOVPaymentTransactionEntity mockTransaction = new GOVPaymentTransactionEntity();
            mockTransaction.setId("MOCK_TRANSACTION_ID");

            InitTransactionRes<GOVPaymentTransactionEntity> initResponse = InitTransactionRes.<GOVPaymentTransactionEntity>builder()
                    .requireAuth(true)
                    .transAuth(transAuth)
                    .transKey("TRANS_KEY")
                    .transaction(mockTransaction)
                    .build();

            when(commonService.initTransaction(eq("TEST_KEY"), eq(TransactionActionEnum.REQUEST_APPROVAL)))
                    .thenReturn(Result.success(initResponse));

            when(govPaymentTransactionMapper.toInitPushRes(any(InitTransactionRes.class))).thenReturn(TxnInitPushRes.builder()
                    .requireAuth(true)
                    .transAuth(transAuth)
                    .transKey("TRANS_KEY")
                    .build());

            // When
            Result<TxnInitPushRes> result = txnService.initPush(request);

            // Then
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isNotNull();
            assertThat(result.getData().isRequireAuth()).isTrue();
            assertThat(result.getData().getTransAuth()).isEqualTo(transAuth);
            assertThat(result.getData().getTransKey()).isEqualTo("TRANS_KEY");
        }

        @Test
        @DisplayName("initPush - Success - PUSH type")
        void initPush_Success_PushType() {
            // Given
            TxnInitPushReq request = TxnInitPushReq.builder()
                    .type(TxnPushTypeEnum.PUSH)
                    .txnIds(List.of("TXN1", "TXN2"))
                    .build();

            TransAuth transAuth = TransAuth.builder()
                    .authType("OTP")
                    .build();

            ProcessTransactionResponse<GOVPaymentTransactionEntity> processResponse = ProcessTransactionResponse.<GOVPaymentTransactionEntity>builder()
                    .requireAuth(true)
                    .transAuth(transAuth)
                    .transKey("TRANS_KEY")
                    .build();

            when(commonService.requestApprovalTransaction(eq(List.of("TXN1", "TXN2"))))
                    .thenReturn(Result.success(processResponse));

            when(govPaymentTransactionMapper.toInitPushRes(any(ProcessTransactionResponse.class))).thenReturn(TxnInitPushRes.builder()
                    .requireAuth(true)
                    .transAuth(transAuth)
                    .transKey("TRANS_KEY")
                    .build());

            // When
            Result<TxnInitPushRes> result = txnService.initPush(request);

            // Then
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isNotNull();
            assertThat(result.getData().isRequireAuth()).isTrue();
            assertThat(result.getData().getTransAuth()).isEqualTo(transAuth);
            assertThat(result.getData().getTransKey()).isEqualTo("TRANS_KEY");
        }
    }

    @Nested
    @DisplayName("confirmPush Tests")
    class ConfirmPushTests {

        @Test
        @DisplayName("confirmPush - Success - All Transactions Processed")
        void confirmPush_Success_AllTransactionsProcessed() {
            // Given
            TxnConfirmReq request = TxnConfirmReq.builder()
                    .transKey("TEST_KEY")
                    .confirmValue("CONFIRM_VALUE")
                    .build();

            GOVPaymentTransactionEntity successTxn = mock(GOVPaymentTransactionEntity.class);
            when(successTxn.getId()).thenReturn("TXN1");
            when(successTxn.getAmount()).thenReturn(new BigDecimal("1000"));
            when(successTxn.getCcy()).thenReturn("VND");
            when(successTxn.getDebitAccNo()).thenReturn("********");
            when(successTxn.getDebitAccName()).thenReturn("Test Account");
            when(successTxn.getCreatedDate()).thenReturn(LocalDateTime.now());

            // Create non-empty payment items list
            Set<GOVPaymentItemEntity> paymentItems = new HashSet<>();
            GOVPaymentItemEntity paymentItem = new GOVPaymentItemEntity();
            paymentItems.add(paymentItem);
            when(successTxn.getPaymentItems()).thenReturn(paymentItems);
            when(successTxn.getGovPaymentItemList()).thenReturn(paymentItems);

            // Mock the fields used in getTxnProcessResultDtoResult
            when(successTxn.getMaDbhc()).thenReturn("DBHC001");
            when(successTxn.getMaCqthu()).thenReturn("CQTHU001");
            when(successTxn.getMaTk()).thenReturn("TK001");
            when(successTxn.getShkb()).thenReturn("SHKB001");

            // Mock repository responses
            when(tccDmDbhcRepository.findDbhcByCodes(any())).thenReturn(Collections.emptyList());
            when(tccDmCqthuRepository.findCqthuByCodes(any(), any())).thenReturn(Collections.emptyList());
            when(tccDmTkNsnnRepository.findTkNsnnByCodes(any())).thenReturn(Collections.emptyList());

            List<GOVPaymentTransactionEntity> successTxns = new ArrayList<>();
            successTxns.add(successTxn);
            List<TransactionResDetail> failTxns = new ArrayList<>();

            ConfirmResDto<GOVPaymentTransactionEntity> confirmResponse = ConfirmResDto.<GOVPaymentTransactionEntity>builder()
                    .successfulTransactions(successTxns)
                    .failedTransactions(failTxns)
                    .transaction(successTxn)
                    .build();

            when(commonService.confirmAuthTransaction(
                    eq("TEST_KEY"),
                    eq("CONFIRM_VALUE"),
                    eq(TransactionActionEnum.REQUEST_APPROVAL)))
                            .thenReturn(Result.success(confirmResponse));

            // Mock the mapper
            when(govPaymentTransactionMapper.toProcessResultDto(any(TxnProcessResultRes.class), eq(successTxn)))
                    .thenAnswer(invocation -> {
                        TxnProcessResultRes res = invocation.getArgument(0);
                        res.setTxnId("TXN1");
                        res.setTotalAmount(new BigDecimal("1000"));
                        res.setCcy("VND");
                        res.setDebitAccNo("********");
                        res.setDebitAccName("Test Account");
                        return res;
                    });

            // When
            Result<TxnProcessResultRes> result = txnService.confirmPush(request);

            // Then
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isNotNull();
            assertThat(result.getData().getTotalSuccess()).isEqualTo(1L);
            assertThat(result.getData().getTotal()).isEqualTo(1L);
            assertThat(result.getData().getTxnId()).isEqualTo("TXN1");
            assertThat(result.getData().getTotalAmount()).isEqualTo(new BigDecimal("1000"));
            assertThat(result.getData().getCcy()).isEqualTo("VND");
            assertThat(result.getData().getDebitAccNo()).isEqualTo("********");
            assertThat(result.getData().getDebitAccName()).isEqualTo("Test Account");
            verify(govPaymentItemRepository).deleteByTxnIds(List.of("TXN1"));
            verify(govPaymentItemRepository).saveAll(any());
            verify(govPaymentTransactionMapper).toProcessResultDto(any(TxnProcessResultRes.class), eq(successTxn));
        }

        @Test
        @DisplayName("confirmPush - Failure - Save Error")
        void confirmPush_Failure_SaveError() {
            // Given
            TxnConfirmReq request = TxnConfirmReq.builder()
                    .transKey("TEST_KEY")
                    .confirmValue("CONFIRM_VALUE")
                    .build();

            GOVPaymentTransactionEntity successTxn = mock(GOVPaymentTransactionEntity.class);
            when(successTxn.getId()).thenReturn("TXN1");
            when(successTxn.getAmount()).thenReturn(new BigDecimal("1000"));
            when(successTxn.getCcy()).thenReturn("VND");
            when(successTxn.getDebitAccNo()).thenReturn("********");
            when(successTxn.getDebitAccName()).thenReturn("Test Account");
            when(successTxn.getCreatedDate()).thenReturn(LocalDateTime.now());

            // Create non-empty payment items list
            Set<GOVPaymentItemEntity> paymentItems = new HashSet<>();
            GOVPaymentItemEntity paymentItem = new GOVPaymentItemEntity();
            paymentItems.add(paymentItem);
            when(successTxn.getPaymentItems()).thenReturn(paymentItems);
            when(successTxn.getGovPaymentItemList()).thenReturn(paymentItems);

            // Mock the fields used in getTxnProcessResultDtoResult
            when(successTxn.getMaDbhc()).thenReturn("DBHC001");
            when(successTxn.getMaCqthu()).thenReturn("CQTHU001");
            when(successTxn.getMaTk()).thenReturn("TK001");
            when(successTxn.getShkb()).thenReturn("SHKB001");

            // Mock repository responses
            when(tccDmDbhcRepository.findDbhcByCodes(any())).thenReturn(Collections.emptyList());
            when(tccDmCqthuRepository.findCqthuByCodes(any(), any())).thenReturn(Collections.emptyList());
            when(tccDmTkNsnnRepository.findTkNsnnByCodes(any())).thenReturn(Collections.emptyList());

            List<GOVPaymentTransactionEntity> successTxns = new ArrayList<>();
            successTxns.add(successTxn);
            List<TransactionResDetail> failTxns = new ArrayList<>();

            ConfirmResDto<GOVPaymentTransactionEntity> confirmResponse = ConfirmResDto.<GOVPaymentTransactionEntity>builder()
                    .successfulTransactions(successTxns)
                    .failedTransactions(failTxns)
                    .transaction(successTxn)
                    .build();

            when(commonService.confirmAuthTransaction(
                    eq("TEST_KEY"),
                    eq("CONFIRM_VALUE"),
                    eq(TransactionActionEnum.REQUEST_APPROVAL)))
                            .thenReturn(Result.success(confirmResponse));

            // Simulate the exception that will move transactions from success to failure list
            doThrow(new RuntimeException("Save error"))
                    .when(govPaymentItemRepository)
                    .deleteByTxnIds(any());

            // Mock the mapper to handle the case where all transactions are moved to failure
            when(govPaymentTransactionMapper.toProcessResultDto(argThat(res -> res.getTotalSuccess() == 0), eq(successTxn)))
                    .thenAnswer(invocation -> {
                        TxnProcessResultRes res = invocation.getArgument(0);
                        res.setTxnId("TXN1");
                        res.setTotalAmount(new BigDecimal("1000"));
                        res.setCcy("VND");
                        res.setDebitAccNo("********");
                        res.setDebitAccName("Test Account");
                        return res;
                    });

            // When
            Result<TxnProcessResultRes> result = txnService.confirmPush(request);

            // Then
            assertThat(result.isSuccess()).isTrue();
            assertThat(result.getData()).isNotNull();
            assertThat(result.getData().getTotalSuccess()).isEqualTo(0L);
            assertThat(result.getData().getTotal()).isEqualTo(1L);
            assertThat(result.getData().getFailTxns()).hasSize(1);
            assertThat(result.getData().getFailTxns().get(0).getMessage())
                    .isEqualTo(ResponseCode.TIMEOUT_01.message());
            verify(govPaymentTransactionMapper).toProcessResultDto(any(TxnProcessResultRes.class), eq(successTxn));
        }
    }
}
