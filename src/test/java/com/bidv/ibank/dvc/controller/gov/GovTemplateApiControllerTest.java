package com.bidv.ibank.dvc.controller.gov;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.request.TemplateListReq;
import com.bidv.ibank.dvc.model.request.TemplateSaveReq;
import com.bidv.ibank.dvc.model.request.TemplateDeleteReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.dvc.service.gov.GovTemplateService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.dvc.util.constant.ResponseCode;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class GovTemplateApiControllerTest {

    @Mock
    private GovTemplateService govTemplateService;

    @InjectMocks
    private GovTemplateApiController govTemplateApiController;

    private TemplateListReq listReq;
    private ResultList<TemplateListRes> listResult;
    private TemplateDeleteReq deleteReq;
    private Result<String> deleteResult;
    private TemplateSaveReq saveReq;
    private Result<String> saveResult;

    @BeforeEach
    void setUp() {
        listReq = new TemplateListReq();
        listResult = new ResultList<>();
        deleteReq = new TemplateDeleteReq(List.of("26CE7FAC-29E1-4F33-9DE7-0830BFDB0122"));
        deleteResult = Result.success("Deleted");
        saveReq = new TemplateSaveReq();
        saveResult = new Result<>();
    }

    @Test
    void save_ShouldReturnSaveResult() {
        when(govTemplateService.save(saveReq)).thenReturn(saveResult);

        Result<String> result = govTemplateApiController.save(saveReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(saveResult);
        verify(govTemplateService).save(saveReq);
    }

    @Test
    void constructor_ShouldInitializeWithGovTemplateService() {
        GovTemplateApiController controller = new GovTemplateApiController(govTemplateService);

        assertThat(controller).isNotNull();
        assertThat(controller).isInstanceOf(GovTemplateApi.class);
    }

    @Test
    void list_ShouldReturnTemplateList() {
        when(govTemplateService.list(listReq)).thenReturn(listResult);

        ResultList<TemplateListRes> result = govTemplateApiController.list(listReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(listResult);
        verify(govTemplateService).list(listReq);
    }

    @Test
    void constructor_ShouldInitializeWithTemplateService() {
        GovTemplateApiController controller = new GovTemplateApiController(govTemplateService);

        assertThat(controller).isNotNull();
        assertThat(controller).isInstanceOf(GovTemplateApi.class);
    }

    @Test
    void delete_ShouldReturnSuccess() {
        when(govTemplateService.delete(deleteReq)).thenReturn(deleteResult);

        Result<String> result = govTemplateApiController.delete(deleteReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(deleteResult);
        verify(govTemplateService).delete(deleteReq);
    }

    @Test
    void delete_ShouldReturnError() {
        Result<String> errorResult = Result.error(ResponseCode.NOT_FOUND_01.code(), "Not found");
        when(govTemplateService.delete(deleteReq)).thenReturn(errorResult);

        Result<String> result = govTemplateApiController.delete(deleteReq);

        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getCode()).isEqualTo(ResponseCode.NOT_FOUND_01.code());
        verify(govTemplateService).delete(deleteReq);
    }
}