package com.bidv.ibank.dvc.controller.gov;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.request.TxnDeleteReq;
import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.service.gov.GovTxnService;
import com.bidv.ibank.framework.domain.response.Result;

@ExtendWith(MockitoExtension.class)
class GovTxnApiControllerTest {

    @Mock
    private GovTxnService txnService;

    @InjectMocks
    private GovTxnApiController txnApiController;

    private TxnListReq listReq;
    private TxnDeleteReq deleteReq;
    private Result<String> deleteResult;

    @BeforeEach
    void setUp() {
        listReq = new TxnListReq();
        listReq.setCcys(List.of("VND"));

        deleteReq = new TxnDeleteReq();
        deleteResult = new Result<>();
    }

    @Test
    void delete_ShouldReturnDeleteResult() {
        when(txnService.delete(deleteReq)).thenReturn(deleteResult);

        Result<String> result = txnApiController.delete(deleteReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(deleteResult);
        verify(txnService).delete(deleteReq);
    }
}