package com.bidv.ibank.dvc.controller.h2h;

import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hBatchDetailRes;
import com.bidv.ibank.dvc.service.h2h.H2hBatchService;
import com.bidv.ibank.framework.domain.response.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class H2hBatchApiControllerTest {

    @Mock
    private H2hBatchService batchService;

    @InjectMocks
    private H2hBatchApiController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testDetail() {
        BatchDetailReq req = new BatchDetailReq();
        H2hBatchDetailRes res = new H2hBatchDetailRes();
        Result<H2hBatchDetailRes> expected = Result.success(res);
        when(batchService.detail(req)).thenReturn(expected);

        Result<H2hBatchDetailRes> actual = controller.detail(req);
        assertEquals(expected, actual);
        verify(batchService, times(1)).detail(req);
    }
}