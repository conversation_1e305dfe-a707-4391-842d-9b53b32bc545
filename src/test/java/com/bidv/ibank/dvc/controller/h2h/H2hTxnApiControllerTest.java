package com.bidv.ibank.dvc.controller.h2h;

import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnListRes;
import com.bidv.ibank.dvc.service.h2h.H2hTxnService;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnDetailRes;
import com.bidv.ibank.framework.domain.response.Result;

class H2hTxnApiControllerTest {

    @Mock
    private H2hTxnService txnService;

    @InjectMocks
    private H2hTxnApiController h2hTxnApiController;

    @InjectMocks
    private H2hTxnApiController controller;

    private TxnDetailReq detailReq;
    private Result<H2hTxnDetailRes> detailResult;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        detailReq = new TxnDetailReq();
        detailResult = new Result<>();
    }

    @Test
    void testList() {
        TxnReportListReq req = new TxnReportListReq();
        ResultList<H2hTxnListRes> expected = new ResultList<>();
        when(txnService.list(req)).thenReturn(expected);

        ResultList<H2hTxnListRes> actual = controller.list(req);
        assertEquals(expected, actual);
        verify(txnService, times(1)).list(req);
    }




    @Test
    void detail_ShouldReturnTransactionDetail() {
        when(txnService.detail(detailReq)).thenReturn(detailResult);

        Result<H2hTxnDetailRes> result = h2hTxnApiController.detail(detailReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(detailResult);
        verify(txnService).detail(detailReq);
    }
}
