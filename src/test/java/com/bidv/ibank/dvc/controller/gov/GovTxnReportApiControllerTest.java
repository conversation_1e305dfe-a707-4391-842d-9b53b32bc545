package com.bidv.ibank.dvc.controller.gov;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;
import com.bidv.ibank.dvc.service.customsduty.TxnService;
import com.bidv.ibank.dvc.service.gov.GovTxnReportService;
import com.bidv.ibank.dvc.service.gov.GovTxnService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GovTxnReportApiControllerTest {

    @Mock
    private GovTxnReportService txnReportService;

    @Mock
    private TxnService txnService;

    @Mock
    private GovTxnService govTxnService;

    @InjectMocks
    private GovTxnReportApiController controller;

    @Test
    void listReport() {
        TxnReportListReq req = new TxnReportListReq();
        ResultList<TxnReportListRes> expected = new ResultList<>();
        when(txnReportService.listReport(req)).thenReturn(expected);

        ResultList<TxnReportListRes> actual = controller.listReport(req);

        assertEquals(expected, actual);
    }

    @Test
    void detailReport() {
        TxnDetailReq req = new TxnDetailReq();
        Result<TxnDetailRes> expected = new Result<>();
        when(txnService.detail(req)).thenReturn(expected);

        Result<TxnDetailRes> actual = controller.detailReport(req);

        assertEquals(expected, actual);
    }

    @Test
    void print() {
        TxnPrintDocumentReq req = new TxnPrintDocumentReq();
        Result<ExportFileRes> expected = new Result<>();
        when(govTxnService.print(req)).thenReturn(expected);

        Result<ExportFileRes> actual = controller.print(req);

        assertEquals(expected, actual);
    }
}