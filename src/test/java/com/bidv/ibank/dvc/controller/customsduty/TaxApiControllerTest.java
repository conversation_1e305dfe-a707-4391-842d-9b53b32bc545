package com.bidv.ibank.dvc.controller.customsduty;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

@ExtendWith(MockitoExtension.class)
class TaxApiControllerTest {

    @Mock
    private TaxService taxService;

    @InjectMocks
    private TaxApiController taxApiController;

    private InquiryCustomsDutyReq inquiryReq;
    private ValidateCustomsDutyReq validateReq;
    private ResultList<InquiryCustomsDutyRes> inquiryResultList;
    private Result<ValidateCustomsDutyRes> validateResult;

    @BeforeEach
    void setUp() {
        inquiryReq = new InquiryCustomsDutyReq();
        validateReq = new ValidateCustomsDutyReq();
        inquiryResultList = new ResultList<>();
        validateResult = new Result<>();
    }

    @Test
    void inquiry_ShouldReturnInquiryResultList() {
        when(taxService.inquiry(inquiryReq)).thenReturn(inquiryResultList);

        ResultList<InquiryCustomsDutyRes> result = taxApiController.inquiry(inquiryReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(inquiryResultList);
        verify(taxService).inquiry(inquiryReq);
    }

    @Test
    void validate_ShouldReturnValidateResult() {
        when(taxService.validate(validateReq)).thenReturn(validateResult);

        Result<ValidateCustomsDutyRes> result = taxApiController.validate(validateReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(validateResult);
        verify(taxService).validate(validateReq);
    }

    @Test
    void constructor_ShouldInitializeWithTaxService() {
        TaxApiController controller = new TaxApiController(taxService);

        assertThat(controller).isNotNull();
        assertThat(controller).isInstanceOf(TaxApi.class);
    }
}