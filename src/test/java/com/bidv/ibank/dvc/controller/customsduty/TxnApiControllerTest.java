package com.bidv.ibank.dvc.controller.customsduty;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.dvc.model.request.TxnSaveReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnListRes;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.dvc.service.customsduty.TxnService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

@ExtendWith(MockitoExtension.class)
class TxnApiControllerTest {

    @Mock
    private TxnService txnService;

    @InjectMocks
    private TxnApiController txnApiController;

    private TxnPendingListReq pendingListReq;
    private ResultList<TxnPendingListRes> pendingList;
    private TxnDetailReq detailReq;
    private Result<TxnDetailRes> detailResult;
    private TxnSaveReq saveReq;
    private Result<String> saveResult;
    private TxnListReq listReq;
    private TxnListRes txnListAllRes;

    @BeforeEach
    void setUp() {
        pendingListReq = new TxnPendingListReq();
        pendingList = new ResultList<>();
        detailReq = new TxnDetailReq();
        detailResult = new Result<>();
        saveReq = new TxnSaveReq();
        saveResult = new Result<>();

        listReq = new TxnListReq();
        listReq.setCcys(List.of("VND"));

        txnListAllRes = TxnListRes.builder()
                .txnId("TXN001")
                .debitAccNo("1234567890")
                .taxCode("TAX001")
                .amount(BigDecimal.valueOf(1000))
                .ccy("VND")
                .status("PENDING")
                .build();
    }

    @Test
    void listPending_ShouldReturnPendingList() {
        when(txnService.listPending(pendingListReq)).thenReturn(pendingList);

        ResultList<TxnPendingListRes> result = txnApiController.listPending(pendingListReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(pendingList);
        verify(txnService).listPending(pendingListReq);
    }

    @Test
    void detail_ShouldReturnTransactionDetail() {
        when(txnService.detail(detailReq)).thenReturn(detailResult);

        Result<TxnDetailRes> result = txnApiController.detail(detailReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(detailResult);
        verify(txnService).detail(detailReq);
    }

    @Test
    void save_ShouldReturnSaveResult() {
        when(txnService.save(saveReq)).thenReturn(saveResult);

        Result<String> result = txnApiController.save(saveReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(saveResult);
        verify(txnService).save(saveReq);
    }

    @Test
    void constructor_ShouldInitializeWithTxnService() {
        TxnApiController controller = new TxnApiController(txnService);

        assertThat(controller).isNotNull();
        assertThat(controller).isInstanceOf(TxnApi.class);
    }

    @Test
    void list_WhenValidRequest_ShouldReturnSuccessResult() {
        // Arrange
        ResultList<TxnListRes> expectedResult = ResultList.success(List.of(txnListAllRes), 1L);
        when(txnService.list(any(TxnListReq.class))).thenReturn(expectedResult);

        // Act
        ResultList<TxnListRes> result = txnApiController.list(listReq);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems())
                .hasSize(1)
                .first()
                .satisfies(item -> {
                    assertThat(item.getTxnId()).isEqualTo("TXN001");
                    assertThat(item.getDebitAccNo()).isEqualTo("1234567890");
                    assertThat(item.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
                    assertThat(item.getStatus()).isEqualTo("PENDING");
                });

        verify(txnService).list(listReq);
    }

    @Test
    void list_WhenNoTransactionsFound_ShouldReturnEmptyList() {
        // Arrange
        ResultList<TxnListRes> expectedResult = ResultList.success(List.of(), 0L);
        when(txnService.list(any(TxnListReq.class))).thenReturn(expectedResult);

        // Act
        ResultList<TxnListRes> result = txnApiController.list(listReq);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems()).isEmpty();
        assertThat(result.getData().getTotal()).isZero();

        verify(txnService).list(listReq);
    }

    @Test
    void list_WhenMultipleTransactions_ShouldReturnAllResults() {
        // Arrange
        TxnListRes txnListAllRes2 = TxnListRes.builder()
                .txnId("TXN002")
                .debitAccNo("0987654321")
                .taxCode("TAX002")
                .amount(BigDecimal.valueOf(2000))
                .ccy("VND")
                .status("COMPLETED")
                .build();

        ResultList<TxnListRes> expectedResult = ResultList.success(List.of(txnListAllRes, txnListAllRes2), 2L);
        when(txnService.list(any(TxnListReq.class))).thenReturn(expectedResult);

        // Act
        ResultList<TxnListRes> result = txnApiController.list(listReq);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getItems())
                .hasSize(2)
                .satisfies(items -> {
                    TxnListRes first = items.get(0);
                    assertThat(first.getTxnId()).isEqualTo("TXN001");
                    assertThat(first.getDebitAccNo()).isEqualTo("1234567890");
                    assertThat(first.getAmount()).isEqualTo(BigDecimal.valueOf(1000));
                    assertThat(first.getStatus()).isEqualTo("PENDING");

                    TxnListRes second = items.get(1);
                    assertThat(second.getTxnId()).isEqualTo("TXN002");
                    assertThat(second.getDebitAccNo()).isEqualTo("0987654321");
                    assertThat(second.getAmount()).isEqualTo(BigDecimal.valueOf(2000));
                    assertThat(second.getStatus()).isEqualTo("COMPLETED");
                });

        verify(txnService).list(listReq);
    }
}