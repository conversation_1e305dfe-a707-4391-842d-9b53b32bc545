package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.service.customsduty.BatchTaxService;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.Translator;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BatchTaxApiControllerTest {

    @Mock
    private BatchTaxService batchTaxService;

    @InjectMocks
    private BatchTaxApiController batchTaxApiController;

    private BatchDetailReq batchDetailReq;
    private BatchListReq batchListReq;
    private MultipartFile mockFile;
    private ExportFileRes exportFileRes;
    private List<BatchListRes> batchListResList;
    private MockedStatic<Translator> translatorMockedStatic;

    @BeforeEach
    void setUp() {
        // Initialize test data
        batchDetailReq = BatchDetailReq.builder()
                .batchNo("TEST_BATCH_001")
                .build();

        batchListReq = BatchListReq.builder()
                .build();

        mockFile = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test content".getBytes()
        );

        exportFileRes = ExportFileRes.builder()
                .url("http://example.com/test.xlsx")
                .build();

        batchListResList = Arrays.asList(
                BatchListRes.builder().batchNo("BATCH_001").build(),
                BatchListRes.builder().batchNo("BATCH_002").build()
        );

        // Mock static Translator
        translatorMockedStatic = mockStatic(Translator.class);
        translatorMockedStatic.when(() -> Translator.toLocale(anyString())).thenReturn("Mocked Message");
        translatorMockedStatic.when(() -> Translator.toLocale(anyString(), any(Object[].class))).thenReturn("Mocked Message");
    }

    @AfterEach
    void tearDown() {
        if (translatorMockedStatic != null) {
            translatorMockedStatic.close();
        }
    }

    @Test
    void downloadTemplate_Success() {
        // Arrange
        when(batchTaxService.downloadTemplate()).thenReturn(Result.success(exportFileRes));

        // Act
        Result<ExportFileRes> result = batchTaxApiController.downloadTemplate();

        // Assert
        assertTrue(result.isSuccess());
        assertEquals(exportFileRes.getUrl(), result.getData().getUrl());
        verify(batchTaxService, times(1)).downloadTemplate();
    }

    @Test
    void uploadFile_Success() {
        // Arrange
        String expectedBatchNo = "BATCH_001";
        when(batchTaxService.uploadFile(any(MultipartFile.class))).thenReturn(Result.success(expectedBatchNo));

        // Act
        Result<String> result = batchTaxApiController.uploadFile(mockFile);

        // Assert
        assertTrue(result.isSuccess());
        assertEquals(expectedBatchNo, result.getData());
        verify(batchTaxService, times(1)).uploadFile(mockFile);
    }

    @Test
    void uploadFile_Error() {
        // Arrange
        Result<String> errorResult = Result.error(ResponseCode.FILE_EXISTS.code(), "File exists");
        when(batchTaxService.uploadFile(any(MultipartFile.class))).thenReturn(errorResult);

        // Act
        Result<String> result = batchTaxApiController.uploadFile(mockFile);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.FILE_EXISTS.code(), result.getCode());
        verify(batchTaxService, times(1)).uploadFile(mockFile);
    }

    @Test
    void list_Success() {
        // Arrange
        ResultList<BatchListRes> expectedResult = ResultList.success(batchListResList, (long) batchListResList.size());
        when(batchTaxService.list(any(BatchListReq.class))).thenReturn(expectedResult);

        // Act
        ResultList<BatchListRes> result = batchTaxApiController.list(batchListReq);

        // Assert
        assertTrue(result.isSuccess());
        assertEquals(batchListResList.size(), result.getData().getItems().size());
        assertEquals((long) batchListResList.size(), result.getData().getTotal());
        verify(batchTaxService, times(1)).list(batchListReq);
    }

    @Test
    void downloadResult_Success() {
        // Arrange
        when(batchTaxService.downloadResult(any(BatchDetailReq.class)))
                .thenReturn(Result.success(exportFileRes));

        // Act
        Result<ExportFileRes> result = batchTaxApiController.downloadResult(batchDetailReq);

        // Assert
        assertTrue(result.isSuccess());
        assertEquals(exportFileRes.getUrl(), result.getData().getUrl());
        verify(batchTaxService, times(1)).downloadResult(batchDetailReq);
    }

    @Test
    void downloadResult_Error() {
        // Arrange
        Result<ExportFileRes> errorResult = Result.error(ResponseCode.TXN_NOT_FOUND.code(), "Transaction not found");
        when(batchTaxService.downloadResult(any(BatchDetailReq.class))).thenReturn(errorResult);

        // Act
        Result<ExportFileRes> result = batchTaxApiController.downloadResult(batchDetailReq);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TXN_NOT_FOUND.code(), result.getCode());
        verify(batchTaxService, times(1)).downloadResult(batchDetailReq);
    }

    @Test
    void exportInquiryResult_Success() {
        // Arrange
        when(batchTaxService.exportInquiryResult(any(BatchDetailReq.class)))
                .thenReturn(Result.success(exportFileRes));

        // Act
        Result<ExportFileRes> result = batchTaxApiController.exportInquiryResult(batchDetailReq);

        // Assert
        assertTrue(result.isSuccess());
        assertEquals(exportFileRes.getUrl(), result.getData().getUrl());
        verify(batchTaxService, times(1)).exportInquiryResult(batchDetailReq);
    }

    @Test
    void exportInquiryResult_Error() {
        // Arrange
        Result<ExportFileRes> errorResult = Result.error(ResponseCode.TXN_NOT_FOUND.code(), "Transaction not found");
        when(batchTaxService.exportInquiryResult(any(BatchDetailReq.class))).thenReturn(errorResult);

        // Act
        Result<ExportFileRes> result = batchTaxApiController.exportInquiryResult(batchDetailReq);

        // Assert
        assertFalse(result.isSuccess());
        assertEquals(ResponseCode.TXN_NOT_FOUND.code(), result.getCode());
        verify(batchTaxService, times(1)).exportInquiryResult(batchDetailReq);
    }
} 