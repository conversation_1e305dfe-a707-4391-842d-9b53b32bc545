package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchDetailEditReq;
import com.bidv.ibank.dvc.model.request.BatchConfirmReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchCalcFeeRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.BatchProcessResultRes;
import com.bidv.ibank.dvc.service.customsduty.BatchService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import com.bidv.ibank.framework.domain.response.DataList;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class BatchApiControllerTest {

    @Mock
    private BatchService batchService;

    @InjectMocks
    private BatchApiController batchApiController;

    private BatchListReq batchListReq;
    private BatchDetailReq batchDetailReq;
    private MultipartFile multipartFile;

    @BeforeEach
    void setUp() {
        batchListReq = new BatchListReq();
        batchDetailReq = new BatchDetailReq();
        multipartFile = new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "test content".getBytes());
    }

    @Test
    void downloadTemplate_ShouldReturnExportFileRes() {
        ExportFileRes expectedResponse = new ExportFileRes();
        Result<ExportFileRes> expectedResult = new Result<>();
        expectedResult.setData(expectedResponse);

        when(batchService.downloadTemplate()).thenReturn(expectedResult);

        Result<ExportFileRes> result = batchApiController.downloadTemplate();

        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(batchService).downloadTemplate();
    }

    @Test
    void uploadFile_ShouldReturnSuccessResult() {
        Result<String> expectedResult = new Result<>();
        expectedResult.setData("Upload successful");

        when(batchService.uploadFile(multipartFile)).thenReturn(expectedResult);

        Result<String> result = batchApiController.uploadFile(multipartFile);

        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(batchService).uploadFile(multipartFile);
    }

    @Test
    void list_ShouldReturnBatchListResults() {
        List<BatchListRes> items = Arrays.asList(new BatchListRes(), new BatchListRes());
        ResultList<BatchListRes> expectedResult = new ResultList<>();
        DataList<BatchListRes> dataList = new DataList<>();
        dataList.setItems(items);
        dataList.setTotal(2L);
        expectedResult.setData(dataList);

        when(batchService.list(batchListReq)).thenReturn(expectedResult);

        ResultList<BatchListRes> result = batchApiController.list(batchListReq);

        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(batchService).list(batchListReq);
    }

    @Test
    void delete_ShouldReturnSuccessResult() {
        Result<String> expectedResult = new Result<>();
        expectedResult.setData("Delete successful");

        when(batchService.delete(batchDetailReq)).thenReturn(expectedResult);

        Result<String> result = batchApiController.delete(batchDetailReq);

        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(batchService).delete(batchDetailReq);
    }

    @Test
    void downloadResult_success() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20240321123456789")
                .build();

        String fileUrl = "http://example.com/result.xlsx";
        ExportFileRes expectedRes = ExportFileRes.builder()
                .url(fileUrl)
                .build();
        Result<ExportFileRes> expectedResult = Result.success(expectedRes);

        when(batchService.downloadResult(req)).thenReturn(expectedResult);

        // Act
        Result<ExportFileRes> result = batchApiController.downloadResult(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getUrl()).isEqualTo(fileUrl);
        verify(batchService).downloadResult(req);
    }

    @Test
    void detail_success() {
        // Arrange
        String batchNo = "BDR20240321123456789";
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo(batchNo)
                .build();

        BatchDetailRes expectedResponse = BatchDetailRes.builder()
                .validItems(List.of())
                .invalidItems(List.of())
                .build();

        Result<BatchDetailRes> expectedResult = Result.success(expectedResponse);
        when(batchService.detail(req)).thenReturn(expectedResult);

        // Act
        Result<BatchDetailRes> result = batchApiController.detail(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData())
                .satisfies(res -> {
                    assertThat(res.getValidItems()).isEmpty();
                    assertThat(res.getInvalidItems()).isEmpty();
                    assertThat(res.getTotalRecords()).isZero();
                    assertThat(res.getTotalValidRecords()).isZero();
                    assertThat(res.getTotalInvalidRecords()).isZero();
                });

        verify(batchService).detail(req);
    }

    @Test
    void validateDetail_ShouldReturnValidationResult() {
        // Arrange
        ValidateCustomsDutyReq req = ValidateCustomsDutyReq.builder()
                .amount("1000000")
                .ccy("VND")
                .treasuryCode("123")
                .admAreaCode("01")
                .revAuthCode("AUTH001")
                .revAccCode("ACC001")
                .taxCode("TAX001")
                .payerName("Test Payer")
                .payerAddr("Test Address")
                .debitAccNo("**********")
                .payerType(com.bidv.ibank.dvc.util.constant.PayerTypeEnum.BUSINESS)
                .taxItems(List.of())
                .build();

        ValidateCustomsDutyRes expectedResponse = ValidateCustomsDutyRes.builder()
                .transKey("TRANS123")
                .feeTotal(new java.math.BigDecimal("50000"))
                .feeCcy("VND")
                .feeOpt("Phí khoán")
                .amount(new java.math.BigDecimal("1000000"))
                .ccy("VND")
                .build();

        Result<ValidateCustomsDutyRes> expectedResult = Result.success(expectedResponse);
        when(batchService.validateDetail(req)).thenReturn(expectedResult);

        // Act
        Result<ValidateCustomsDutyRes> result = batchApiController.validateDetail(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTransKey()).isEqualTo("TRANS123");
        verify(batchService).validateDetail(req);
    }

    @Test
    void editDetail_ShouldReturnSuccessResult() {
        // Arrange
        BatchDetailEditReq req = BatchDetailEditReq.builder()
                .batchItemId("a614d77b-9ee6-481f-b321-838ed39ddba8")
                .build();

        Result<String> expectedResult = Result.success("Edit successful");
        when(batchService.editDetail(req)).thenReturn(expectedResult);

        // Act
        Result<String> result = batchApiController.editDetail(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo("Edit successful");
        verify(batchService).editDetail(req);
    }

    @Test
    void calcFee_ShouldReturnFeeCalculationResult() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20240321123456789")
                .build();

        BatchCalcFeeRes expectedResponse = BatchCalcFeeRes.builder()
                .totalAmount(new java.math.BigDecimal("1000000"))
                .totalFee(new java.math.BigDecimal("50000"))
                .ccy("VND")
                .feeCcy("VND")
                .build();

        Result<BatchCalcFeeRes> expectedResult = Result.success(expectedResponse);
        when(batchService.calcFee(req)).thenReturn(expectedResult);

        // Act
        Result<BatchCalcFeeRes> result = batchApiController.calcFee(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotalAmount()).isEqualTo(new java.math.BigDecimal("1000000"));
        assertThat(result.getData().getTotalFee()).isEqualTo(new java.math.BigDecimal("50000"));
        verify(batchService).calcFee(req);
    }

    @Test
    void initPush_ShouldReturnInitPushResult() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20240321123456789")
                .build();

        TxnInitPushRes expectedResponse = TxnInitPushRes.builder()
                .transKey("TRANS123")
                .requireAuth(true)
                .build();

        Result<TxnInitPushRes> expectedResult = Result.success(expectedResponse);
        when(batchService.initPush(req)).thenReturn(expectedResult);

        // Act
        Result<TxnInitPushRes> result = batchApiController.initPush(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTransKey()).isEqualTo("TRANS123");
        assertThat(result.getData().isRequireAuth()).isTrue();
        verify(batchService).initPush(req);
    }

    @Test
    void confirmPush_ShouldReturnProcessResult() {
        // Arrange
        BatchConfirmReq req = new BatchConfirmReq();
        req.setTransKey("TRANS123");
        req.setConfirmValue("confirmValue");

        BatchProcessResultRes expectedResponse = BatchProcessResultRes.builder()
                .total(10L)
                .totalSuccess(10L)
                .failTxns(List.of())
                .build();

        Result<BatchProcessResultRes> expectedResult = Result.success(expectedResponse);
        when(batchService.confirmPush(req)).thenReturn(expectedResult);

        // Act
        Result<BatchProcessResultRes> result = batchApiController.confirmPush(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData().getTotalSuccess()).isEqualTo(10L);
        assertThat(result.getData().getTotalFail()).isEqualTo(0L);
        verify(batchService).confirmPush(req);
    }

    @Test
    void downloadFile_ShouldReturnFileBytes() {
        // Arrange
        BatchDetailReq req = BatchDetailReq.builder()
                .batchNo("BDR20240321123456789")
                .build();

        byte[] expectedFileContent = "file content".getBytes();
        Result<byte[]> expectedResult = Result.success(expectedFileContent);
        when(batchService.downloadFile(req)).thenReturn(expectedResult);

        // Act
        Result<byte[]> result = batchApiController.downloadFile(req);

        // Assert
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(expectedFileContent);
        verify(batchService).downloadFile(req);
    }
}