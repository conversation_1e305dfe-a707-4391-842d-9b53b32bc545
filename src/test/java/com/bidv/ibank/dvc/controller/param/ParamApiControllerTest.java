package com.bidv.ibank.dvc.controller.param;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.dvc.model.request.AdministrativeAreaReq;
import com.bidv.ibank.dvc.model.request.RevenueAuthorityReq;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.framework.domain.response.ResultList;

@ExtendWith(MockitoExtension.class)
class ParamApiControllerTest {

    @Mock
    private ParamGovService paramService;

    @InjectMocks
    private ParamApiController paramApiController;

    private ResultList<ChapterRes> chapterList;
    private ResultList<EconomicContentRes> economicContentList;
    private ResultList<TaxTypeRes> taxList;
    private ResultList<CustomsCurrencyRes> customsCurrencyList;
    private ResultList<ExportImportType> importExportList;
    private ResultList<TreasuryRes> treasuryList;
    private ResultList<RevenueAccountRes> revenueAccountList;
    private ResultList<RevenueAuthorityRes> revenueAuthorityList;
    private RevenueAuthorityReq revenueAuthorityReq;
    private ResultList<AdministrativeAreaRes> administrativeAreaList;
    private AdministrativeAreaReq administrativeAreaReq;

    @BeforeEach
    void setUp() {
        chapterList = new ResultList<>();
        economicContentList = new ResultList<>();
        taxList = new ResultList<>();
        customsCurrencyList = new ResultList<>();
        importExportList = new ResultList<>();
        treasuryList = new ResultList<>();
        revenueAccountList = new ResultList<>();
        revenueAuthorityList = new ResultList<>();
        revenueAuthorityReq = new RevenueAuthorityReq();
        administrativeAreaList = new ResultList<>();
        administrativeAreaReq = new AdministrativeAreaReq();
    }

    @Test
    void listChapter_ShouldReturnChapterList() {
        when(paramService.listChapter()).thenReturn(chapterList);

        ResultList<ChapterRes> result = paramApiController.listChapter();

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(chapterList);
        verify(paramService).listChapter();
    }

    @Test
    void listEconomicContent_ShouldReturnEconomicContentList() {
        when(paramService.listEconomicContent()).thenReturn(economicContentList);

        ResultList<EconomicContentRes> result = paramApiController.listEconomicContent();

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(economicContentList);
        verify(paramService).listEconomicContent();
    }

    @Test
    void listTaxType_ShouldReturnTaxTypeList() {
        when(paramService.listTaxType()).thenReturn(taxList);

        ResultList<TaxTypeRes> result = paramApiController.listTaxType();

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(taxList);
        verify(paramService).listTaxType();
    }

    @Test
    void listCustomsCurrency_ShouldReturnCustomsCurrencyList() {
        when(paramService.listCustomsCurrency()).thenReturn(customsCurrencyList);

        ResultList<CustomsCurrencyRes> result = paramApiController.listCustomsCurrency();

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(customsCurrencyList);
        verify(paramService).listCustomsCurrency();
    }

    @Test
    void listExportImportType_ShouldReturnExportImportTypeList() {
        when(paramService.listExportImportType()).thenReturn(importExportList);

        ResultList<ExportImportType> result = paramApiController.listExportImportType();

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(importExportList);
        verify(paramService).listExportImportType();
    }

    @Test
    void listTreasury_ShouldReturnTreasuryList() {
        when(paramService.listTreasury()).thenReturn(treasuryList);

        ResultList<TreasuryRes> result = paramApiController.listTreasury();

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(treasuryList);
        verify(paramService).listTreasury();
    }

    @Test
    void listRevenueAccount_ShouldReturnRevenueAccountList() {
        when(paramService.listRevenueAccount()).thenReturn(revenueAccountList);

        ResultList<RevenueAccountRes> result = paramApiController.listRevenueAccount();

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(revenueAccountList);
        verify(paramService).listRevenueAccount();
    }

    @Test
    void listRevenueAuthority_ShouldReturnRevenueAuthorityList() {
        when(paramService.listRevenueAuthority(revenueAuthorityReq)).thenReturn(revenueAuthorityList);

        ResultList<RevenueAuthorityRes> result = paramApiController.listRevenueAuthority(revenueAuthorityReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(revenueAuthorityList);
        verify(paramService).listRevenueAuthority(revenueAuthorityReq);
    }

    @Test
    void listAdministrativeArea_ShouldReturnAdministrativeAreaList() {
        when(paramService.listAdministrativeArea(administrativeAreaReq)).thenReturn(administrativeAreaList);

        ResultList<AdministrativeAreaRes> result = paramApiController.listAdministrativeArea(administrativeAreaReq);

        assertThat(result).isNotNull();
        assertThat(result).isSameAs(administrativeAreaList);
        verify(paramService).listAdministrativeArea(administrativeAreaReq);
    }

    @Test
    void constructor_ShouldInitializeWithParamService() {
        ParamApiController controller = new ParamApiController(paramService);

        assertThat(controller).isNotNull();
        assertThat(controller).isInstanceOf(ParamApi.class);
    }
}