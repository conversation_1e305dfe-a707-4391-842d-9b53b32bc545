package com.bidv.ibank.dvc.controller.h2h;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class H2hTaxApiControllerTest {

    @Mock
    private TaxService taxService;

    @InjectMocks
    private H2hTaxApiController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testInquiry() {
        InquiryCustomsDutyReq req = new InquiryCustomsDutyReq();
        ResultList<InquiryCustomsDutyRes> expected = new ResultList<>();
        when(taxService.inquiry(req)).thenReturn(expected);

        ResultList<InquiryCustomsDutyRes> actual = controller.inquiry(req);
        assertEquals(expected, actual);
        verify(taxService, times(1)).inquiry(req);
    }
}