package com.bidv.ibank.dvc.config;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class PrimaryDataSourceConfigTest {
	@Test
	void dataSource() {
		Assertions.assertTrue(true);
	}

	@Test
	void dataSourceProperties() {
		Assertions.assertTrue(true);
	}

	@Test
	void entityManagerFactory() {
		Assertions.assertTrue(true);
	}

	@Test
	void transactionManager() {
		Assertions.assertTrue(true);
	}
}
