package com.bidv.ibank.dvc.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.bidv.ibank.integrate.config.IntegrateProperties;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

@ExtendWith(MockitoExtension.class)
class IntegrateConfigurationTest {

    @InjectMocks
    private IntegrateConfiguration integrateConfiguration;

    private IntegrateProperties mockProperties;

    @BeforeEach
    void setUp() {
        mockProperties = new IntegrateProperties();
    }

    @Test
    void testCreateProperties() {
        // When
        IntegrateProperties result = integrateConfiguration.createProperties();

        // Then
        assertNotNull(result, "IntegrateProperties should not be null");
        assertInstanceOf(IntegrateProperties.class, result, "Result should be an instance of IntegrateProperties");
    }

    @Test
    void testCreateIntegrateServiceFactoryWithMockedProperties() {
        // Given
        IntegrateConfiguration spyConfiguration = Mockito.spy(integrateConfiguration);
        when(spyConfiguration.createProperties()).thenReturn(mockProperties);

        // When
        IntegrateServiceFactory result = spyConfiguration.createIntegrateServiceFactory();

        // Then
        assertNotNull(result, "IntegrateServiceFactory should not be null");
    }

    @Test
    void testCreateIntegrateServiceFactoryWithActualProperties() {
        // When
        IntegrateServiceFactory result = integrateConfiguration.createIntegrateServiceFactory();

        // Then
        assertNotNull(result, "IntegrateServiceFactory should not be null");
    }

    @Test
    void testConfigurationAnnotationPresent() {
        // Then
        assertTrue(IntegrateConfiguration.class.isAnnotationPresent(org.springframework.context.annotation.Configuration.class),
                "Class should be annotated with @Configuration");
    }

    @Test
    void testCreatePropertiesMethodHasConfigurationPropertiesAnnotation() throws NoSuchMethodException {
        // When
        var method = IntegrateConfiguration.class.getDeclaredMethod("createProperties");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.boot.context.properties.ConfigurationProperties.class),
                "createProperties method should be annotated with @ConfigurationProperties");

        var annotation = method.getAnnotation(org.springframework.boot.context.properties.ConfigurationProperties.class);
        assertNotNull(annotation, "ConfigurationProperties annotation should not be null");
        assertEquals("bidv.integrate", annotation.prefix(),
                "ConfigurationProperties prefix should be 'bidv.integrate'");
    }

    @Test
    void testCreateIntegrateServiceFactoryMethodHasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = IntegrateConfiguration.class.getDeclaredMethod("createIntegrateServiceFactory");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class),
                "createIntegrateServiceFactory method should be annotated with @Bean");
    }

    @Test
    void testCreatePropertiesMethodHasBeanAnnotation() throws NoSuchMethodException {
        // When
        var method = IntegrateConfiguration.class.getDeclaredMethod("createProperties");

        // Then
        assertTrue(method.isAnnotationPresent(org.springframework.context.annotation.Bean.class),
                "createProperties method should be annotated with @Bean");
    }
}
