<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
	<Properties>
		<Property name="LOG_PATTERN">[%d{yyyy-MM-dd HH:mm:ss.SSS} %X{traceId} ]%notEmpty{ [%X{requestId}]}%notEmpty{ [creqID: %X{clientRequestId}]} - [%-5level] [${hostName}] [%thread] %c{1}: %m%n%ex</Property>
		<Property name="LOG_PATTERN_SOA">[%X{soaType}: %X{requestId}] %notEmpty{[soaMessID: %X{soaMessID}]} [%d{yyyy-MM-dd HH:mm:ss.SSS}] - [%-5level] [${hostName}] [%thread]%notEmpty{ - [processTime: %X{soaProcessTime}ms]}: %m%n%ex</Property>
		<Property name="LOG_PATTERN_IB_GATEWAY">[%X{ibGWType}: %X{requestId}] %notEmpty{[refNo: %X{ibGWRef}]} [%d{yyyy-MM-dd HH:mm:ss.SSS}] - [%-5level] [${hostName}] [%thread]%notEmpty{ - [processTime: %X{ibGWProcessTime}ms]}: %m%n%ex</Property>
		<Property name="LOG_PATTERN_REQ_RES">[%X{traceId} %X{type}: %X{requestId}] [creqID: %X{clientRequestId}] [%d{yyyy-MM-dd HH:mm:ss.SSS}] - [${hostName}] [%thread] - [user: %X{username}]\n\t[api: %X{uri}] [%X{channel}] [device_info: %X{mobileOS}|%X{mobileVer}|%X{deviceId}] %notEmpty{\n\t[Response_info: %X{responseCode}}%notEmpty{|%X{responseDesc}}%notEmpty{|%X{processTime}ms]} %notEmpty{hasReqBody: %X{haveRequestBody}]} \n\t%m%n%ex</Property>
		<Property name = "KAFKA_SERVER">************:9092</Property>
		<Property name = "KAFKA_TOPIC">bidv-ibank-1.5</Property>
		
		<Property name = "LOG_FOLDER">${sys:user.dir}/logs</Property>
		<Property name = "LOG_FILE_SIZE">10MB</Property>
		<Property name = "LOG_MAX_FILE">100</Property>
		<Property name = "LOG_MAX_DATE">3d</Property>
	</Properties>
	<Appenders>
	
		<!-- FILE INFO, ERROR -->
		<Console name="ConsoleAppender" target="SYSTEM_OUT"
			follow="true">
			<PatternLayout pattern="${LOG_PATTERN}" />
		</Console>
		
		<RollingFile name="logInfoFileAppender"
			fileName="${LOG_FOLDER}/ibank_mobile_info.log"
			filePattern="${LOG_FOLDER}/ibank_mobile_info-%d{yyyy-MM-dd}-%i.log.gz">
			<PatternLayout>
				<Pattern>${LOG_PATTERN}</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${LOG_FILE_SIZE}" />
			</Policies>
			<!-- <DefaultRolloverStrategy max="${LOG_MAX_FILE}" /> -->
			<DefaultRolloverStrategy max="${LOG_MAX_FILE}">
		        <Delete basePath="${LOG_FOLDER}" maxDepth="1">
		            <IfFileName glob="ibank_mobile_info-*.log.gz" />
		            <IfLastModified age="${LOG_MAX_DATE}" />
		        </Delete>
    		</DefaultRolloverStrategy>
    
			<Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
		</RollingFile>
		
		<RollingFile name="logErrorFileAppender"
			fileName="${LOG_FOLDER}/ibank_mobile_error.log"
			filePattern="${LOG_FOLDER}/ibank_mobile_error-%d{yyyy-MM-dd}-%i.log.gz">
			<PatternLayout>
				<Pattern>${LOG_PATTERN}</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${LOG_FILE_SIZE}" />
			</Policies>
			<!-- <DefaultRolloverStrategy max="${LOG_MAX_FILE}" /> -->
			<DefaultRolloverStrategy max="${LOG_MAX_FILE}">
		        <Delete basePath="${LOG_FOLDER}" maxDepth="1">
		            <IfFileName glob="ibank_mobile_error-*.log.gz" />
		            <IfLastModified age="${LOG_MAX_DATE}" />
		        </Delete>
    		</DefaultRolloverStrategy>
			<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
		</RollingFile>
		
	  <!-- API -->
	  <RollingFile name="logReqResFileAppender"
			fileName="${LOG_FOLDER}/ibank_mobile_req_res.log"
			filePattern="${LOG_FOLDER}/ibank_mobile_req_res-%d{yyyy-MM-dd}-%i.log.gz">
			<PatternLayout>
				<Pattern>${LOG_PATTERN_REQ_RES}</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${LOG_FILE_SIZE}" />
			</Policies>
			<!-- <DefaultRolloverStrategy max="${LOG_MAX_FILE}" /> -->
			<DefaultRolloverStrategy max="${LOG_MAX_FILE}">
		        <Delete basePath="${LOG_FOLDER}" maxDepth="1">
		            <IfFileName glob="ibank_mobile_req_res-*.log.gz" />
		            <IfLastModified age="${LOG_MAX_DATE}" />
		        </Delete>
    		</DefaultRolloverStrategy>
	  </RollingFile>
			
	  <Kafka name="ELKAppenderAPI" topic="${KAFKA_TOPIC}">
		      <PatternLayout pattern="${LOG_PATTERN_REQ_RES}"/>
		      <Property name="bootstrap.servers">${KAFKA_SERVER}</Property>
	  </Kafka>
		
 	  <!-- <JDBC name="databaseAppender" tableName="log_req_res_api">
 	 		<ConnectionFactory class="com.bidv.ibank.config.logging.ConnectionFactory" method="getDatabaseConnection" />
	      	<Column name="ID" literal="LOG_REQ_RES_API_SEQ.NEXTVAL" />
	      	<Column name="REQUEST_ID" pattern="%X{requestId}"/>
	      	<Column name="LEVEL_CODE" pattern="%level"/>
	      	<Column name="USER_NAME" pattern="%X{username}"/>
	      	<Column name="OBJECT_VALUE" pattern="%X{value}"/>
	      	<Column name="MESSAGE" pattern="%m" isClob = "true"/>
	      	<Column name="TYPE" pattern="%X{type}"/>
	      	<Column name="URI_API" pattern="%X{uri}"/>
	      	<Column name="DATE_TIME" literal="sysdate"/>
	      	<Column name="CLIENT_REQUEST_ID" pattern="%X{clientRequestId}"/>
	      	<Column name="IP" pattern="%X{remoteAddress}"/>
	      	<Column name="CHANNEL" pattern="%X{channel}"/>
	      	<Column name="TOKEN" pattern="%X{token}"/>
	      	<Column name="HAVE_REQUEST_BODY" pattern="%X{haveRequestBody}"/>
	      	<Column name="DEVICE_ID" pattern="%X{deviceId}"/>
	      	<Column name="DEVICE_VER" pattern="%X{mobileVer}"/>
	      	<Column name="DEVICE_OS" pattern="%X{mobileOS}"/>
	      	<Column name="RESPONSE_CODE" pattern="%X{responseCode}"/>
	      	<Column name="RESPONSE_DESC" pattern="%X{responseDesc}"/>
	      	<Column name="PROCESS_TIME" pattern="%X{processTime}"/>
	      	<Column name="HOSTNAME" pattern="${hostName}"/>
	      	<Column name="USER_AGENT" pattern="%X{userAgent}"/>
	   </JDBC> -->
    
		
	    <!-- SOA, ESB -->
		<RollingFile name="logSOAFileAppender"
			fileName="${LOG_FOLDER}/ibank_mobile_SOA.log"
			filePattern="${LOG_FOLDER}/ibank_mobile_SOA-%d{yyyy-MM-dd}-%i.log.gz">
			<PatternLayout>
				<Pattern>${LOG_PATTERN_SOA}</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${LOG_FILE_SIZE}" />
			</Policies>
			<!-- <DefaultRolloverStrategy max="${LOG_MAX_FILE}" /> -->
			<DefaultRolloverStrategy max="${LOG_MAX_FILE}">
		        <Delete basePath="${LOG_FOLDER}" maxDepth="1">
		            <IfFileName glob="ibank_mobile_SOA-*.log.gz" />
		            <IfLastModified age="${LOG_MAX_DATE}" />
		        </Delete>
    		</DefaultRolloverStrategy>
		</RollingFile>
		
		<Kafka name="ELKAppenderSOA" topic="${KAFKA_TOPIC}">
		      <PatternLayout pattern="${LOG_PATTERN_SOA}"/>
		      <Property name="bootstrap.servers">${KAFKA_SERVER}</Property>
		</Kafka>
		
		<!-- IBANK GATEWAY -->
		<RollingFile name="logIBGATEWAYFileAppender"
			fileName="${LOG_FOLDER}/ibank_mobile_IB_GATEWAY.log"
			filePattern="${LOG_FOLDER}/ibank_mobile_IB_GATEWAY-%d{yyyy-MM-dd}-%i.log.gz">
			<PatternLayout>
				<Pattern>${LOG_PATTERN_IB_GATEWAY}</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy/>
				<SizeBasedTriggeringPolicy size="${LOG_FILE_SIZE}" />
			</Policies>
			<!-- <DefaultRolloverStrategy max="${LOG_MAX_FILE}" /> -->
			<DefaultRolloverStrategy max="${LOG_MAX_FILE}">
		        <Delete basePath="${LOG_FOLDER}" maxDepth="1">
		            <IfFileName glob="ibank_mobile_IB_GATEWAY-*.log.gz" />
		            <IfLastModified age="${LOG_MAX_DATE}" />
		        </Delete>
    		</DefaultRolloverStrategy>
		</RollingFile>
		
		<Kafka name="ELKAppenderIBGATEWAY" topic="${KAFKA_TOPIC}">
		      <PatternLayout pattern="${LOG_PATTERN_IB_GATEWAY}"/>
		      <Property name="bootstrap.servers">${KAFKA_SERVER}</Property>
		</Kafka>
		
  		
		
		<!-- FILE -->
		<Async name="AsyncConsole">
			<AppenderRef ref="ConsoleAppender" />
			<LinkedTransferQueue />
		</Async>
		
		<Async name="AsyncFileInfo">
			<AppenderRef ref="logInfoFileAppender" />
			<LinkedTransferQueue />
		</Async>
		
		<Async name="AsyncFileError">
			<AppenderRef ref="logErrorFileAppender" />
			<LinkedTransferQueue />
		</Async>
		
		<!-- LOG API -->
		<Async name="AsyncFileReqRes">
			<AppenderRef ref="logReqResFileAppender" />
			<LinkedTransferQueue />
		</Async>
		
		<Async name="AsyncELKAPI">
			<AppenderRef ref="ELKAppenderAPI" />
			<LinkedTransferQueue />
		</Async>
		
	<!-- 	<Async name="AsyncDB">
			<AppenderRef ref="databaseAppender" />
			<LinkedTransferQueue />
		</Async> -->
		
		<!-- SOA, ESB -->
		<Async name="AsyncELKSOA">
			<AppenderRef ref="ELKAppenderSOA" />
			<LinkedTransferQueue />
		</Async>
		
		<Async name="AsyncFileSOA">
			<AppenderRef ref="logSOAFileAppender" />
			<LinkedTransferQueue />
		</Async>
		
		<!-- IBANK GATEWAY -->
		<Async name="AsyncELKIBGATEWAY">
			<AppenderRef ref="ELKAppenderIBGATEWAY" />
			<LinkedTransferQueue />
		</Async>
		
		<Async name="AsyncFileIBGATEWAY">
			<AppenderRef ref="logIBGATEWAYFileAppender" />
			<LinkedTransferQueue />
		</Async>
		
	</Appenders>
	<Loggers>
		 <!-- LOG_API -->
		<Logger name="LOG_API" level="info" additivity="false">
            <AppenderRef ref="AsyncFileReqRes" />
            <AppenderRef ref="AsyncConsole" /> 
            <AppenderRef ref="AsyncELKAPI" />
            <!-- <AppenderRef ref="AsyncDB"/> -->
        </Logger>
        
        <!-- LOG_SOA -->
        <Logger name="org.apache.cxf.services" level="info" additivity="false">
            <AppenderRef ref="AsyncFileSOA" />
            <AppenderRef ref="AsyncELKSOA" />
            <AppenderRef ref="AsyncConsole" />
        </Logger>
        
         <!-- LOG_ESB -->
        <Logger name="com.bidv.ibank.services.impl.SOARestServiceImpl" level="info" additivity="false">
            <AppenderRef ref="AsyncFileSOA"/>
            <AppenderRef ref="AsyncELKSOA"/>
            <AppenderRef ref="AsyncFileError">
            	<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </AppenderRef>
            <AppenderRef ref="AsyncConsole" />
        </Logger>
        
         <!-- LOG_IBANK GATEWAY -->
        <Logger name="com.bidv.ibank.services.impl.EbankGatewayServiceImpl" level="info" additivity="false">
            <AppenderRef ref="AsyncFileIBGATEWAY"/>
            <AppenderRef ref="AsyncELKIBGATEWAY"/>
            <AppenderRef ref="AsyncFileError">
            	<ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </AppenderRef>
            <AppenderRef ref="AsyncConsole" />
        </Logger>
         
        <!-- LOG_COMMON -->
        <Logger name="com.bidv" additivity="false">
            <AppenderRef ref="AsyncFileInfo" />
			<AppenderRef ref="AsyncFileError" />
			<AppenderRef ref="AsyncConsole" />	
        </Logger>
        
        <!-- LOG ROOT -->
		<Root level="info" additivity="false">
			<AppenderRef ref="AsyncFileInfo" />
			<AppenderRef ref="AsyncFileError" />
			<AppenderRef ref="AsyncConsole" />	
		</Root>
		
		<Logger name="org.apache.kafka" level="error" additivity="false">
        	<AppenderRef ref="AsyncFileError" />
        </Logger>
        
     <!--    <Logger name="com.zaxxer.hikari" level="trace" additivity="false">
        	<AppenderRef ref="AsyncConsole" />
        </Logger>
        <Logger name="HikariPool" level="trace" additivity="false">
        	<AppenderRef ref="AsyncConsole" />
        </Logger>
        <Logger name="org.springframework.web" level="debug" additivity="false">
        	<AppenderRef ref="AsyncConsole" />
        </Logger> -->
        
	</Loggers>
</Configuration>