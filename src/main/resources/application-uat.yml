server:
  port: 8087
liquibase:
  shouldRun: false
bidv:
  kafka:
    ibank:
      bootstrap-servers: ${IBANK_KAFKA_URL}
      groupId: ibank
      producerParams:
        "[max.block.ms]": 3000
      topic:
        gov-accounting: ibank_gov_accounting
        notification: IBANK_TRANS_NOTIFY
        revert-limit-one: ibank_revert_one_limit
        revert-limit-all: ibank_revert_all_limit
        revert-limit-amount: ibank_revert_amount_limit
  security:
    jwt:
      publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAut4sXaZg3fmqveExYK9Vv30bbro8jusCsMLixDCSRuQOsNCRR1DEpqGIaVYHitgg3D4Q+EzVcMHdr6duu1nugP4NfcmV2l187a5dGqiNflPP71kWCNijTCbXqq09TxlgQ6EX+710nmS/ty0dae7gbFEVN49M5tcKDRqnaH3nYxvRn+kYZ/HR28XKcezQsBofxF6kzx4MsUpRO2ShZNuSzOvLg4CehuxdbmlXYW90Tk91ge6RFb+Wa8KYjsoLdy0cisAeAjhz5dYwmS7EbYivAn/IQ9DR5/aY46oGBKC7Dp2/tTbr72PVDvrDBXwF49q2lqagPDFfZhcyeuzp1KhpOQIDAQAB
  integrate:
    soa:
      url: ${SOA_URL}
      bussinessDomain: bidv.com.vn
      username: ${SOA_USERNAME}
      password: ${SOA_PASSWORD}
      timeout: 30000
      timeToLive: 30000
      tellerId: IBANK2
      appCode: IBANK2
    soa2:
      url: ${SOA2_URL}
      bussinessDomain: bidv.com.vn
      username: ${SOA2_USERNAME}
      password: ${SOA2_PASSWORD}
      timeout: 30000
      timeToLive: 30000
      appCode: IBANK2
      tellerId: IBANK2
    fileHub:
      url: ${FILEHUB_URL}
      appCode: "iBANK2"
      storageCode: ${FILEHUB_STORAGE_CODE}
      appToken: ${FILEHUB_APPTOKEN}
      shareCode: ${FILEHUB_SHARECODE}
      prefixPath: /ibank2
    esb:
      url: ${ESB_URL}
      timeout: 30000
      appToken: ${ESB_TOKEN}
      addParams:
        notihub.appCode: APP.IBANK@_a8e3fee9-436c-4e5c-8dc0-c09b001a651d
        notihub.accessToken: /SQhae1IjKOixnjVgPT5jZ55GhTuioJBNneLZ5NI0tw=
      iframe:
        app-token: eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJCSURWQVBJIiwiYXBwaWQiOjMyMSwiY2xpZW50aWQiOjIsInBsYW5pZCI6MjkzM30.M7vNfaIIQyUvZVYpVoT7i_PQDngbVR6NC0xbzclsgVM
  iframe:
    publicKey: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tDQpNSUlEL3pDQ0F1ZWdBd0lCQWdJVU11QWY3ZGFEaW1EdjJRZlQ2M3czUkZNQ3JIa3dEUVlKS29aSWh2Y05BUUVMDQpCUUF3Z1kweEN6QUpCZ05WQkFZVEFsWnVNUTR3REFZRFZRUUlEQVZJWVc1dmFURU9NQXdHQTFVRUJ3d0ZTR0Z1DQpiMmt4RWpBUUJnTlZCQW9NQ1dKcFpIWWdZbUZ1YXpFVE1CRUdBMVVFQ3d3S1pHVjJJR05sYm5SbGNqRVFNQTRHDQpBMVVFQXd3SGRIUndkRzVvY3pFak1DRUdDU3FHU0liM0RRRUpBUllVYldsdWFIUjBNVEZBWW1sa2RpNWpiMjB1DQpkbTR3SUJjTk1qUXhNakkxTURneE5qRTVXaGdQTWpBMU1qQTFNVEl3T0RFMk1UbGFNSUdOTVFzd0NRWURWUVFHDQpFd0pXYmpFT01Bd0dBMVVFQ0F3RlNHRnViMmt4RGpBTUJnTlZCQWNNQlVoaGJtOXBNUkl3RUFZRFZRUUtEQWxpDQphV1IySUdKaGJtc3hFekFSQmdOVkJBc01DbVJsZGlCalpXNTBaWEl4RURBT0JnTlZCQU1NQjNSMGNIUnVhSE14DQpJekFoQmdrcWhraUc5dzBCQ1FFV0ZHMXBibWgwZERFeFFHSnBaSFl1WTI5dExuWnVNSUlCSWpBTkJna3Foa2lHDQo5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBdElnUG1UT28wY1dvNjJmcXArVG5xd2VZSTF1dEhqOWxHbCtyDQpIQ3E3NVRXVHljOUNSd3BVZElPb2tsTnE1OUs1NVVPbXlyMDJobUo0L29ONFRzN1VyWmUwK3hUWWhWMGxmODNNDQplSmN5K29PT3djdm5WeHZHTUVaRTNoQVJuRW45ZVdGWlArZjZmTTVuVGFyYmplRXQyUlBPalgyQnFSR042WWVGDQp2YUFRSkZDQWI3SUFOZHh2c2lKUmFUdUdjOEtISXl0K0dOSWtxa3htTUtjV095QWpyVlYvMFFmZWRNMGFkT0JUDQplaklhQUFMcFZpZ2ZDQnIxL0hXR29ueXZkZTNpV012Mm9iWTJCZklkSkZzdUtGcURhcklZbzl1U0Nrd0VpWHNDDQoxR0FGL3RBNW5laEtOUGhuT1lITEpDcWdFRzFPcGdwL0JzdDArWFV0MDU1VnRDUTcyUUlEQVFBQm8xTXdVVEFkDQpCZ05WSFE0RUZnUVUxMFR2RUhlYzNlbFFhT0Zxb1NDYXdpU0hOMTB3SHdZRFZSMGpCQmd3Rm9BVTEwVHZFSGVjDQozZWxRYU9GcW9TQ2F3aVNITjEwd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBTkJna3Foa2lHOXcwQkFRc0ZBQU9DDQpBUUVBVG5xaVJDVzY1L0ZJVnFJeGpxUVVwM3VmclVoOGVoRFB2NExVbEZOdDNtTEx0R2g1VU81YWdJR2ZYMENaDQpobmg4bXR6OWRETWNDWjh3b0wwWkJwTURQWHlFZlF1UTJkR1hvZjlmOWgyaWtCM0F2M2tvaC9IblhydHpPTVFyDQo1OGRVMzJBdHZIRlFSVWZrMkVNdnRROVJQOFR5cGN5VmFCQUszaUZLZWl4c0x4ODc4Q2pkZktHaXFPVmtSQ2JZDQpEYjZBaENRWVA5SUlUSWF0bmxsa3BENmNYRE9pRjQ0L2R2UEpiMkRtY2duVm4zd2ZhQWdCd2NVZnE5Wk4wc0ZnDQpZNnRaN1VhWlRrQXIvNE0rSWtKTnljTTBGV1JHUFFoYkdsODhjT3pxL2o5M3hSSzhKdTBCYkhPR2JwbnJ4emVwDQpKMHRuSmE4eDJxVjFTQUo1WjIrUlBhRkNKdz09DQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tDQo=
  tracing:
    enable: true
    security: false
    bootstrap-servers: ${bidv.logging.kafka.bootstrap_server}
    topic: ${bidv.logging.kafka.tracing_topic}
    properties:
      "[batch.size]": 0
  logging:
    kafka:
      bootstrap_server: ${LTT_KAFKA_URL}
      log_topic: ibank2
      tracing_topic: ibank2-tracing-zipkin
  config:
    datasource:
      driver-class-name: oracle.jdbc.OracleDriver
      url: ${DATASOURCE_CIMBACK_URL}
      username: ${DATASOURCE_CIMBACK_USERNAME}
      password: ${DATASOURCE_CIMBACK_PASSWORD}
  quartz:
    datasource:
      url: ${DATASOURCE_QUARTZ_URL}
      username: ${DATASOURCE_QUARTZ_USERNAME}
      password: ${DATASOURCE_QUARTZ_PASSWORD}
    serviceCode: GOV_JOBS
management:
  endpoints:
    web:
      exposure:
        include: "*"
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
      connect-timeout: 1s
      read-timeout: 10s

spring:
  datasource:
    driver-class-name: oracle.jdbc.OracleDriver
    url: ${DATASOURCE_GOV_URL}
    username: ${DATASOURCE_GOV_USERNAME}
    password: ${DATASOURCE_GOV_PASSWORD}
    hikari:
      pool-name: DatabasePoolDS
      connection-timeout: 60000
      minimum-idle: 0
      maximum-pool-size: 100
      idle-timeout: 300000
      max-lifetime: 1800000
      auto-commit: false
      connection-test-query: select * from dual
  jpa:
    open-in-view: false
    show-sql: true
    generate-ddl: false
    hibernate:
      ddl-auto: none
  data:
    redis:
      database: ${REDIS_DB}
      jedis:
        pool:
          max-active: 100
          max-idle: 100
          max-wait: 30000ms
          min-idle: 2
          time-between-eviction-runs: 60000ms
      username: ${REDIS_USERNAME}
      password: ${REDIS_PASSWORD}
      timeout: 30000ms
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
  cloud:
    openfeign:
      client:
        config:
          trans-limit-service:
            url: http://limit-service.ibank-common-ns
          masterdata-service:
            url: http://masterdata-service.ibank-common-ns
          trans-workflow-service:
            url: http://workflow-service.ibank-common-ns
          auth-method-service:
            url: http://signing-service.ibank-common-ns
          fee-service:
            url: http://fee-service.ibank-common-ns
