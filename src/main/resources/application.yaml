server:
  port: 8087
liquibase:
  shouldRun: false
bidv:
  cache:
    enable: true
    txn.process.ttl: 300
  security:
    jwt:
      publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAut4sXaZg3fmqveExYK9Vv30bbro8jusCsMLixDCSRuQOsNCRR1DEpqGIaVYHitgg3D4Q+EzVcMHdr6duu1nugP4NfcmV2l187a5dGqiNflPP71kWCNijTCbXqq09TxlgQ6EX+710nmS/ty0dae7gbFEVN49M5tcKDRqnaH3nYxvRn+kYZ/HR28XKcezQsBofxF6kzx4MsUpRO2ShZNuSzOvLg4CehuxdbmlXYW90Tk91ge6RFb+Wa8KYjsoLdy0cisAeAjhz5dYwmS7EbYivAn/IQ9DR5/aY46oGBKC7Dp2/tTbr72PVDvrDBXwF49q2lqagPDFfZhcyeuzp1KhpOQIDAQAB
  integrate:
    soa:
      url: 10.53.114.32:10222
      bussinessDomain: bidv.com.vn
      username: ibank
      password: ibank123
      timeout: 30000
      timeToLive: 30000
      appCode: BDR
      tellerId: BDR
    soa2:
      url: 10.53.118.46:30722
      bussinessDomain: bidv.com.vn
      username: ibank
      password: ibank123
      timeout: 30000
      timeToLive: 30000
      appCode: BDR
      tellerId: BDR
    fileHub:
      url: http://uat-s3.apps.uat3ttptnhs.ldapudtest.com
      appCode: iBANK2
      storageCode: KktCrPuXAIy/4V3hTggwRyClWq3jNOz6+vF4EtFwFKTohP56rhX2wPThuiorbiOjHlr/fHTYLx8=
      appToken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************.RldtPDkpmG3mJg3UUiM6FiobZmW5e6_Y1STL0EnnLLs
      shareCode: ya6qnLMxWPEPjOM8ma/+XkTGq+nnPKOiYhUq77Y99KAeQe2hnLifBepmOj+DM944xMFPwlLZAe1LtyeKkMxIVAH316AJuw==
      prefixPath: /ibank2
    esb:
      url: http://uatesbrestful-uat-esbrestful.apps.uat3ttptnhs.ldapudtest.com/esb
      timeout: 30000
      timeToLive: 30000
      appCode: IBANK
      appToken: eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJCSURWQVBJIiwiYXBwaWQiOjMyMSwiY2xpZW50aWQiOjIsInBsYW5pZCI6MTE1Mn0.sTFEmpl0kKr7IrOi-oIMzuaAz5hGqGblfFEJQ7v8xjU
      addParams:
        notihub.appCode: APP.IBANK@_a8e3fee9-436c-4e5c-8dc0-c09b001a651d
        notihub.accessToken: /SQhae1IjKOixnjVgPT5jZ55GhTuioJBNneLZ5NI0tw=
        appCode: APP.IBANK@_a8e3fee9-436c-4e5c-8dc0-c09b001a651d
        accessToken: /SQhae1IjKOixnjVgPT5jZ55GhTuioJBNneLZ5NI0tw=
        tcc.version: 2.0
        tcc.tranReceive: TCCGW
  tracing:
    enable: true
    security: false
    bootstrap-servers: ${bidv.logging.kafka.bootstrap_server}
    topic: ${bidv.logging.kafka.tracing_topic}
    properties:
      "[batch.size]": 0
  logging:
    kafka:
      bootstrap_server: *************:9092,*************:9092,*************:9092
      log_topic: ibank2
      tracing_topic: ibank2-tracing-zipkin
  config:
    datasource:
      driver-class-name: oracle.jdbc.OracleDriver
      # url: ***************************************************************************************************************************************)))
      # username: cimback
      # password: cimback#2024
      url: *******************************************************************************************************************************************)))
      username: cimback
      password: cimb@ck#2025
  esb:
    url: http://uatesbrestful-uat-esbrestful.apps.openshift.ldapudtest.com/esb
    timeout: 30000
    timeToLive: 30000
    appToken: eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJCSURWQVBJIiwiYXBwaWQiOjIzNCwiY2xpZW50aWQiOjIsInBsYW5pZCI6MjM1Mn0.F7iv1kxFkgrqZkX0BrxIF2kLrBcodR8oVw-HOZqBm4U
    addParams:
      notihub.appCode: APP.IBANK@_a8e3fee9-436c-4e5c-8dc0-c09b001a651d
      notihub.accessToken: /SQhae1IjKOixnjVgPT5jZ55GhTuioJBNneLZ5NI0tw=
    iframe:
      app-token: eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJCSURWQVBJIiwiYXBwaWQiOjMyMSwiY2xpZW50aWQiOjIsInBsYW5pZCI6MjkzM30.M7vNfaIIQyUvZVYpVoT7i_PQDngbVR6NC0xbzclsgVM
  iframe:
    publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtIgPmTOo0cWo62fqp+TnqweYI1utHj9lGl+rHCq75TWTyc9CRwpUdIOoklNq59K55UOmyr02hmJ4/oN4Ts7UrZe0+xTYhV0lf83MeJcy+oOOwcvnVxvGMEZE3hARnEn9eWFZP+f6fM5nTarbjeEt2RPOjX2BqRGN6YeFvaAQJFCAb7IANdxvsiJRaTuGc8KHIyt+GNIkqkxmMKcWOyAjrVV/0QfedM0adOBTejIaAALpVigfCBr1/HWGonyvde3iWMv2obY2BfIdJFsuKFqDarIYo9uSCkwEiXsC1GAF/tA5nehKNPhnOYHLJCqgEG1Opgp/Bst0+XUt055VtCQ72QIDAQAB
  kafka:
    ibank:
      bootstrapServers: *************:9092,*************:9093,*************:9094
      groupId: ibank
      topic:
        gov-accounting: ibank_gov_accounting
        notification: IBANK_TRANS_NOTIFY
        revert-limit-one: ibank_revert_one_limit
        revert-limit-all: ibank_revert_all_limit
        revert-limit-amount: ibank_revert_amount_limit
  # quartz:
  #   datasource:
  #     url: *******************************************************************************************************************************************)))
  #     username: ibank2job
  #     password: ibank2job#2024
  #   serviceCode: GOV_JOBS

management:
  endpoints:
    web:
      exposure:
        include: "*"
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
      connect-timeout: 1s
      read-timeout: 10s
spring:
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    driver-class-name: oracle.jdbc.OracleDriver
    # url: ***************************************************************************************************************************************)))
    # username: gov
    # password: gov#2024
    url: *******************************************************************************************************************************************)))
    username: GOV
    password: iBank2#2025
    hikari:
      pool-name: DatabasePoolDS
      connection-timeout: 60000
      minimum-idle: 0
      maximum-pool-size: 100
      idle-timeout: 300000
      max-lifetime: 1800000
      auto-commit: false
      connection-test-query: select * from dual
  jpa:
    open-in-view: false
    show-sql: true
    generate-ddl: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        jdbc:
          batch_size: 500
          fetch_size: 500
  data:
    redis:
      database: 10
      jedis:
        pool:
          max-active: 100
          max-idle: 100
          max-wait: 30000ms
          min-idle: 2
          time-between-eviction-runs: 60000ms
      password:
      timeout: 30000ms
      host: 127.0.0.1
      port: 6379
  cloud:
    openfeign:
      client:
        config:
          trans-limit-service:
            url: http://just4dev.limit.ibank2sit.com
          masterdata-service:
            url: http://just4dev.masterdata.ibank2sit.com
          trans-workflow-service:
            url: http://just4dev.workflow.ibank2sit.com
          auth-method-service:
            url: http://just4dev.signing.ibank2sit2.com
          fee-service:
            url: http://just4dev.fee.ibank2sit.com
          commondata-service:
            url: http://just4dev.masterdata.ibank2sit.com
  #  properties:
  #    "[hibernate.generate_statistics]": true
checksum:
  secretKey: "PRIVATE_KEY"
logging:
  level:
    org.apache.cxf: ERROR
    
cxf:
  logging:
    enabled: false
