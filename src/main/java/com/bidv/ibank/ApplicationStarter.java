package com.bidv.ibank;

import com.bidv.ibank.framework.remote.config.database.annotation.EnableRemoteConfigDatabase;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication
@ComponentScan("com.bidv")
@EnableRemoteConfigDatabase(module = "DVC", apiPermissionConfig = true, appConfig = true, i18nBundleConfig = true)
@EnableDiscoveryClient
@EnableAsync
public class ApplicationStarter {
    public static void main(String[] args) {
        SpringApplication.run(ApplicationStarter.class, args);
    }
}
