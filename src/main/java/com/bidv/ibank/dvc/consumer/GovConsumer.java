package com.bidv.ibank.dvc.consumer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.stereotype.Service;

import com.bidv.ibank.dvc.service.gov.GovTxnAccountingService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.kafka.service.KafkaConsumer;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GovConsumer extends KafkaConsumer {

    private final GovTxnAccountingService txnAccountingService;

    protected GovConsumer(
            ConcurrentKafkaListenerContainerFactory<String, Object> factory,
            @Value("${bidv.kafka.ibank.topic.gov-accounting}") String topic,
            GovTxnAccountingService txnAccountingService) {
        super(factory, topic);
        this.txnAccountingService = txnAccountingService;
    }

    @Override
    protected void handleMessage(Object message) {
        try {
            log.info("[HEARING] <<GovConsumer>>: {}", message);
            String txnId = (String) message;
            Result<String> result = txnAccountingService.processAccounting(txnId);
            if (!result.isSuccess()) {
                log.error("Accounting transaction processed failed: {}", result.getMessage());
            }
        } catch (Exception e) {
            log.error("[ERROR] <<GovConsumer>>: {}", e.getMessage());
        }
    }
}
