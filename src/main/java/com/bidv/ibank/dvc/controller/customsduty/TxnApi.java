package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.model.response.TxnListRes;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.dvc.model.request.TxnSaveReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface TxnApi {

    @Operation(summary = "List pending transaction", description = "List pending transaction", tags = { "gov_txn" }, operationId = "txnPendingList")
    @PostMapping(value = "/gov/customs-duty/txn/pending/list/1.0")
    ResultList<TxnPendingListRes> listPending(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnPendingListReq req);

    @Operation(summary = "Detail transaction", description = "Detail transaction", tags = { "gov_txn" }, operationId = "txnDetail")
    @PostMapping(value = "/gov/customs-duty/txn/detail/1.0")
    Result<TxnDetailRes> detail(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnDetailReq req);

    @Operation(summary = "Save transaction", description = "Save transaction", tags = { "gov_txn" }, operationId = "txnSave")
    @PostMapping(value = "/gov/customs-duty/txn/save/1.0")
    Result<String> save(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnSaveReq req);

    @Operation(summary = "List all transaction", description = "List all transaction", tags = { "gov_txn" }, operationId = "txnList")
    @PostMapping(value = "/gov/customs-duty/txn/list/1.0")
    ResultList<TxnListRes> list(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnListReq req);

    @Operation(summary = "Edit transaction", description = "Edit transaction", tags = { "gov_txn" }, operationId = "txnEdit")
    @PostMapping(value = "/gov/customs-duty/txn/edit/1.0")
    Result<String> edit(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnSaveReq req);

    @Operation(summary = "Update status transaction", description = "Update status transaction", tags = { "gov_txn" }, operationId = "txnStatusUpdate")
    @PostMapping(value = "/gov/customs-duty/txn/status/update/1.0")
    Result<String> updateStatus(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnDetailReq req);
}
