package com.bidv.ibank.dvc.controller.gov;

import com.bidv.ibank.dvc.model.request.TxnApproveReq;
import com.bidv.ibank.dvc.model.request.TxnConfirmReq;
import com.bidv.ibank.dvc.model.request.TxnDeleteReq;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.dvc.model.request.TxnInitPushReq;
import com.bidv.ibank.dvc.model.request.TxnPendingApprovalListReq;
import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnApprovalResultRes;
import com.bidv.ibank.dvc.model.response.TxnInitApproveRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.TxnPendingApprovalListRes;
import com.bidv.ibank.dvc.model.response.TxnProcessResultRes;

import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.TxnRejectReq;
import com.bidv.ibank.dvc.model.response.TxnRejectRes;
import com.bidv.ibank.dvc.service.gov.GovTxnService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GovTxnApiController implements GovTxnApi {

    private final GovTxnService txnService;

    @Override
    public Result<TxnInitPushRes> initPush(@Valid TxnInitPushReq body) {
        return txnService.initPush(body);
    }

    @Override
    public Result<TxnProcessResultRes> confirmPush(@Valid TxnConfirmReq body) {
        return txnService.confirmPush(body);
    }

    @Override
    public Result<String> delete(@Valid TxnDeleteReq req) {
        return txnService.delete(req);
    }

    @Override
    public Result<TxnRejectRes> reject(@Valid TxnRejectReq body) {
        return txnService.reject(body);
    }

    @Override
    public ResultList<TxnPendingApprovalListRes> listPendingApproval(TxnPendingApprovalListReq req) {
        return txnService.listPendingApproval(req);
    }

    @Override
    public Result<ExportFileRes> export(@Valid TxnExportReq body) {
        return txnService.export(body);
    }

    @Override
    public Result<ExportFileRes> print(TxnPrintDocumentReq req) {
        return txnService.print(req);
    }

    @Override
    public Result<TxnInitApproveRes> initApproval(@Valid TxnApproveReq body) {
        return txnService.initApproval(body);
    }

    @Override
    public Result<TxnApprovalResultRes> confirmApproval(@Valid TxnConfirmReq body) {
        return txnService.confirmApproval(body);
    }
}
