package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.BatchConfirmReq;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;

import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;

import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.request.BatchDetailEditReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.BatchCalcFeeRes;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.BatchProcessResultRes;

@Validated
public interface BatchApi {

    @Operation(summary = "Download batch template", description = "Download batch template", tags = { "gov_batch" }, operationId = "batchDownloadTemplate")
    @PostMapping(value = "/gov/customs-duty/batch/download-template/1.0")
    Result<ExportFileRes> downloadTemplate();

    @Operation(summary = "Download file original", description = "Download file original", tags = { "gov_batch" }, operationId = "batchFileDownload")
    @PostMapping(value = "/gov/customs-duty/batch/file/download/1.0")
    Result<byte[]> downloadFile(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

    @Operation(summary = "Upload file batch", description = "Upload file batch", tags = { "gov_batch" }, operationId = "batchUploadFile")
    @PostMapping(value = "/gov/customs-duty/batch/upload/1.0")
    Result<String> uploadFile(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @RequestParam("file") MultipartFile file);

    @Operation(summary = "List batch", description = "List batch", tags = { "gov_batch" }, operationId = "batchList")
    @PostMapping(value = "/gov/customs-duty/batch/list/1.0")
    ResultList<BatchListRes> list(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchListReq req);

    @Operation(summary = "Delete batch", description = "Delete batch", tags = { "gov_batch" }, operationId = "batchDelete")
    @PostMapping(value = "/gov/customs-duty/batch/delete/1.0")
    Result<String> delete(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

    @Operation(summary = "Download result", description = "Download result", tags = { "gov_batch" }, operationId = "batchResultDownload")
    @PostMapping(value = "/gov/customs-duty/batch/result/download/1.0")
    Result<ExportFileRes> downloadResult(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

    @Operation(summary = "Check detail", description = "Check detail", tags = { "gov_batch" }, operationId = "batchDetail")
    @PostMapping(value = "/gov/customs-duty/batch/detail/1.0")
    Result<BatchDetailRes> detail(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

    @Operation(summary = "Validate batch detail", description = "Validate batch detail", tags = { "gov_batch" }, operationId = "batchDetailValidate")
    @PostMapping(value = "/gov/customs-duty/batch/detail/validate/1.0")
    Result<ValidateCustomsDutyRes> validateDetail(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody ValidateCustomsDutyReq req);

    @Operation(summary = "Fee data", description = "Fee data", tags = { "gov_batch" }, operationId = "batchFeeCalc")
    @PostMapping(value = "/gov/customs-duty/batch/fee/calc/1.0")
    Result<BatchCalcFeeRes> calcFee(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

    @Operation(summary = "Edit batch detail", description = "Edit batch detail", tags = { "gov_batch" }, operationId = "batchDetailEdit")
    @PostMapping(value = "/gov/customs-duty/batch/detail/edit/1.0")
    Result<String> editDetail(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailEditReq req);

    @Operation(summary = "Init push batch", description = "Init push batch", tags = { "gov_batch" }, operationId = "batchInitPush")
    @PostMapping(value = "/gov/customs-duty/batch/push/1.0")
    Result<TxnInitPushRes> initPush(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

    @Operation(summary = "Confirm push batch", description = "Confirm push batch", tags = { "gov_batch" }, operationId = "batchConfirmPush")
    @PostMapping(value = "/gov/customs-duty/batch/confirm/1.0")
    Result<BatchProcessResultRes> confirmPush(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchConfirmReq req);
}
