package com.bidv.ibank.dvc.controller.h2h;

import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class H2hTaxApiController implements H2hTaxApi {

    private final TaxService taxService;

    @Override
    public ResultList<InquiryCustomsDutyRes> inquiry(InquiryCustomsDutyReq req) {
        return taxService.inquiry(req);
    }
}
