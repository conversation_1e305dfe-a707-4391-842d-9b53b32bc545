package com.bidv.ibank.dvc.controller.h2h;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hBatchDetailRes;
import com.bidv.ibank.framework.domain.response.Result;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface H2hBatchApi {

    @Operation(summary = "Batch detail", description = "Batch detail", tags = { "gov_h2h_batch" }, operationId = "h2hBatchDetail")
    @PostMapping(value = "/gov/h2h/customs-duty/batch/detail/1.0")
    Result<H2hBatchDetailRes> detail(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

}
