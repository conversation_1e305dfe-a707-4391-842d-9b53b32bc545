package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.service.customsduty.BatchTaxService;
import com.bidv.ibank.framework.domain.response.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
public class BatchTaxApiController implements BatchTaxApi {

    private final BatchTaxService batchTaxService;

    @Override
    public Result<ExportFileRes> downloadTemplate() {
        return batchTaxService.downloadTemplate();
    }

    @Override
    public Result<String> uploadFile(MultipartFile file) {
        return batchTaxService.uploadFile(file);
    }

    @Override
    public ResultList<BatchListRes> list(BatchListReq req) {
        return batchTaxService.list(req);
    }

    @Override
    public Result<ExportFileRes> downloadResult(BatchDetailReq req) {
        return batchTaxService.downloadResult(req);
    }

    @Override
    public Result<ExportFileRes> exportInquiryResult(BatchDetailReq req) {
        return batchTaxService.exportInquiryResult(req);
    }
}
