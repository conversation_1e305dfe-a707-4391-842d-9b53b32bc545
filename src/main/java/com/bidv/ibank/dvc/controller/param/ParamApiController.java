package com.bidv.ibank.dvc.controller.param;

import com.bidv.ibank.framework.domain.response.Result;
import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.AdministrativeAreaReq;
import com.bidv.ibank.dvc.model.request.RevenueAuthorityReq;
import com.bidv.ibank.dvc.model.request.TreasuryDetailReq;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryDetailRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class ParamApiController implements ParamApi {

    private final ParamGovService paramService;

    @Override
    public ResultList<ChapterRes> listChapter() {
        return paramService.listChapter();
    }

    @Override
    public ResultList<EconomicContentRes> listEconomicContent() {
        return paramService.listEconomicContent();
    }

    @Override
    public ResultList<TaxTypeRes> listTaxType() {
        return paramService.listTaxType();
    }

    @Override
    public ResultList<CustomsCurrencyRes> listCustomsCurrency() {
        return paramService.listCustomsCurrency();
    }

    @Override
    public ResultList<ExportImportType> listExportImportType() {
        return paramService.listExportImportType();
    }

    @Override
    public ResultList<TreasuryRes> listTreasury() {
        return paramService.listTreasury();
    }

    @Override
    public ResultList<RevenueAccountRes> listRevenueAccount() {
        return paramService.listRevenueAccount();
    }

    @Override
    public ResultList<RevenueAuthorityRes> listRevenueAuthority(RevenueAuthorityReq req) {
        return paramService.listRevenueAuthority(req);
    }

    @Override
    public ResultList<AdministrativeAreaRes> listAdministrativeArea(AdministrativeAreaReq req) {
        return paramService.listAdministrativeArea(req);
    }

    @Override
    public Result<TreasuryDetailRes> detailTreasury(TreasuryDetailReq req) {
        return paramService.detailTreasury(req);
    }
}
