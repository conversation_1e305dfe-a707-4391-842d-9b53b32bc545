package com.bidv.ibank.dvc.controller.gov;

import com.bidv.ibank.dvc.model.request.TxnApproveReq;
import com.bidv.ibank.dvc.model.request.TxnConfirmReq;
import com.bidv.ibank.dvc.model.request.TxnDeleteReq;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.dvc.model.request.TxnInitPushReq;
import com.bidv.ibank.dvc.model.request.TxnPendingApprovalListReq;
import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnApprovalResultRes;
import com.bidv.ibank.dvc.model.response.TxnInitApproveRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.TxnPendingApprovalListRes;
import com.bidv.ibank.dvc.model.response.TxnProcessResultRes;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.TxnRejectReq;
import com.bidv.ibank.dvc.model.response.TxnRejectRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface GovTxnApi {

    @Operation(summary = "Delete transaction", description = "Delete transaction", tags = { "gov_txn" }, operationId = "txnDelete")
    @PostMapping(value = "/gov/txn/delete/1.0")
    Result<String> delete(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnDeleteReq req);

    @Operation(summary = "Reject transaction", description = "Reject transaction", tags = { "gov_txn" }, operationId = "txnReject")
    @PostMapping(value = "/gov/txn/reject/1.0")
    Result<TxnRejectRes> reject(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnRejectReq body);

    @Operation(summary = "List pending approval transaction", description = "List pending approval transaction", tags = { "gov_txn" }, operationId = "txnPendingApprovalList")
    @PostMapping(value = "/gov/txn/pending-approval/list/1.0")
    ResultList<TxnPendingApprovalListRes> listPendingApproval(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnPendingApprovalListReq body);

    @Operation(summary = "Init push transaction", description = "Init push transaction", tags = { "gov_txn" }, operationId = "txnInitPush")
    @PostMapping(value = "/gov/txn/push/1.0")
    Result<TxnInitPushRes> initPush(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnInitPushReq body);

    @Operation(summary = "Confirm push transaction", description = "Confirm push transaction", tags = { "gov_txn" }, operationId = "txnConfirmPush")
    @PostMapping(value = "/gov/txn/confirm/1.0")
    Result<TxnProcessResultRes> confirmPush(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnConfirmReq body);

    @Operation(summary = "Export report transaction", description = "Export report transaction", tags = { "gov_txn" }, operationId = "txnExport")
    @PostMapping(value = "/gov/txn/export/1.0")
    Result<ExportFileRes> export(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnExportReq body);

    @Operation(summary = "Print document", description = "Print document", tags = { "gov_txn" }, operationId = "txnPrint")
    @PostMapping(value = "/gov/txn/print/1.0")
    Result<ExportFileRes> print(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnPrintDocumentReq req);

    @Operation(summary = "Init approval transaction", description = "Init approval transaction", tags = { "gov_txn" }, operationId = "txnInitApprove")
    @PostMapping(value = "/gov/txn/approve/1.0")
    Result<TxnInitApproveRes> initApproval(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnApproveReq body);

    @Operation(summary = "Confirm approval transaction", description = "Confirm approval transaction", tags = { "gov_txn" }, operationId = "txnApprovalConfirm")
    @PostMapping(value = "/gov/txn/approval/confirm/1.0")
    Result<TxnApprovalResultRes> confirmApproval(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnConfirmReq body);
}
