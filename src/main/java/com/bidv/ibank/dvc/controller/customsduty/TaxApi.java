package com.bidv.ibank.dvc.controller.customsduty;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface TaxApi {

    @Operation(summary = "Inquiry customs duty", description = "Inquiry customs duty", tags = { "gov_tax" }, operationId = "taxInquiry")
    @PostMapping(value = "/gov/customs-duty/tax/inquiry/1.0")
    ResultList<InquiryCustomsDutyRes> inquiry(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody InquiryCustomsDutyReq req);

    @Operation(summary = "Validate customs duty", description = "Validate customs duty", tags = { "gov_tax" }, operationId = "taxValidate")
    @PostMapping(value = "/gov/customs-duty/tax/validate/1.0")
    Result<ValidateCustomsDutyRes> validate(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody ValidateCustomsDutyReq req);
}
