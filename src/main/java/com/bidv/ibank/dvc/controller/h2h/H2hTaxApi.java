package com.bidv.ibank.dvc.controller.h2h;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface H2hTaxApi {

    @Operation(summary = "Inquiry customs duty", description = "Inquiry customs duty", tags = { "gov_h2h_tax" }, operationId = "h2hTaxInquiry")
    @PostMapping(value = "/gov/h2h/customs-duty/tax/inquiry/1.0")
    ResultList<InquiryCustomsDutyRes> inquiry(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody InquiryCustomsDutyReq req);
}
