package com.bidv.ibank.dvc.controller.customsduty;

import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class TaxApiController implements TaxApi {

    private final TaxService taxService;

    @Override
    public ResultList<InquiryCustomsDutyRes> inquiry(InquiryCustomsDutyReq req) {
        return taxService.inquiry(req);
    }

    @Override
    public Result<ValidateCustomsDutyRes> validate(ValidateCustomsDutyReq req) {
        return taxService.validate(req);
    }
}
