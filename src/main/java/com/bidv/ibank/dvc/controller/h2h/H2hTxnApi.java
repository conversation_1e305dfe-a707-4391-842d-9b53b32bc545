package com.bidv.ibank.dvc.controller.h2h;

import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnListRes;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnDetailRes;
import com.bidv.ibank.framework.domain.response.Result;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface H2hTxnApi {

    @Operation(summary = "List report transaction", description = "List report transaction", tags = { "gov_h2h_txn" }, operationId = "h2hTxnList")
    @PostMapping(value = "/gov/h2h/txn/list/1.0")
    ResultList<H2hTxnListRes> list(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnReportListReq body);

    @Operation(summary = "Detail transaction", description = "Detail transaction", tags = { "gov_h2h_txn" }, operationId = "h2hTxnDetail")
    @PostMapping(value = "/gov/h2h/customs-duty/txn/detail/1.0")
    Result<H2hTxnDetailRes> detail(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnDetailReq req);

    @Operation(summary = "Print document", description = "Print document", tags = { "gov_h2h_txn" }, operationId = "h2hTxnPrint")
    @PostMapping(value = "/gov/h2h/txn/print/1.0")
    Result<ExportFileRes> print(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnPrintDocumentReq req);
}
