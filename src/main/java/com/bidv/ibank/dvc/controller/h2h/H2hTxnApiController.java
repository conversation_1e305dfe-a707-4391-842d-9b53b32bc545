package com.bidv.ibank.dvc.controller.h2h;

import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnDetailRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnListRes;
import com.bidv.ibank.dvc.service.h2h.H2hTxnService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class H2hTxnApiController implements H2hTxnApi {

    private final H2hTxnService txnService;

    @Override
    public ResultList<H2hTxnListRes> list(TxnReportListReq req) {
        return txnService.list(req);
    }

    @Override
    public Result<H2hTxnDetailRes> detail(@Valid TxnDetailReq req) {
        return txnService.detail(req);
    }

    @Override
    public Result<ExportFileRes> print(TxnPrintDocumentReq req) {
        return txnService.print(req);
    }

}
