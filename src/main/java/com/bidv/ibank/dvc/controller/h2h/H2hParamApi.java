package com.bidv.ibank.dvc.controller.h2h;

import com.bidv.ibank.framework.domain.response.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.AdministrativeAreaReq;
import com.bidv.ibank.dvc.model.request.RevenueAuthorityReq;
import com.bidv.ibank.dvc.model.request.TreasuryDetailReq;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryDetailRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface H2hParamApi {

    @Operation(summary = "Chapter list", description = "Chapter list", tags = { "gov_h2h_parameter" }, operationId = "h2hChapterList")
    @PostMapping(value = "/gov/h2h/par/chapter/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<ChapterRes> listChapter();

    @Operation(summary = "Economic content list", description = "Economic content list", tags = { "gov_h2h_parameter" }, operationId = "h2hEconomicContentList")
    @PostMapping(value = "/gov/h2h/par/economic-content/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<EconomicContentRes> listEconomicContent();

    @Operation(summary = "Tax type list", description = "Tax type list", tags = { "gov_h2h_parameter" }, operationId = "h2hTaxTypeList")
    @PostMapping(value = "/gov/h2h/par/tax-type/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<TaxTypeRes> listTaxType();

    @Operation(summary = "Customs currency list", description = "Customs currency list", tags = { "gov_h2h_parameter" }, operationId = "h2hCustomsCurrencyList")
    @PostMapping(value = "/gov/h2h/par/customs-currency/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<CustomsCurrencyRes> listCustomsCurrency();

    @Operation(summary = "Export import type list", description = "Export import type list", tags = { "gov_h2h_parameter" }, operationId = "h2hExportImportTypeList")
    @PostMapping(value = "/gov/h2h/par/export-import-type/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<ExportImportType> listExportImportType();

    @Operation(summary = "Treasury list", description = "Treasury list", tags = { "gov_h2h_parameter" }, operationId = "h2hTreasuryList")
    @PostMapping(value = "/gov/h2h/par/treasury/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<TreasuryRes> listTreasury();

    @Operation(summary = "Revenue account list", description = "Revenue account list", tags = { "gov_h2h_parameter" }, operationId = "h2hRevenueAccountList")
    @PostMapping(value = "/gov/h2h/par/revenue-account/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<RevenueAccountRes> listRevenueAccount();

    @Operation(summary = "Revenue authority list", description = "Revenue authority list", tags = { "gov_h2h_parameter" }, operationId = "h2hRevenueAuthorityList")
    @PostMapping(value = "/gov/h2h/par/revenue-authority/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<RevenueAuthorityRes> listRevenueAuthority(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody RevenueAuthorityReq req);

    @Operation(summary = "Administrative area list", description = "Administrative area list", tags = { "gov_h2h_parameter" }, operationId = "h2hAdministrativeAreaList")
    @PostMapping(value = "/gov/h2h/par/administrative-area/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<AdministrativeAreaRes> listAdministrativeArea(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody AdministrativeAreaReq req);

    @Operation(summary = "Treasury detail", description = "Treasury detail", tags = { "gov_h2h_parameter" }, operationId = "h2hTreasuryDetail")
    @PostMapping(value = "/gov/h2h/par/treasury/detail/1.0", produces = { "application/json" }, consumes = { "application/json" })
    Result<TreasuryDetailRes> detailTreasury(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TreasuryDetailReq req);
}
