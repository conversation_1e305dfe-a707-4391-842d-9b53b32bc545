package com.bidv.ibank.dvc.controller.gov;

import com.bidv.ibank.dvc.model.request.TxnExportReportReq;
import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface GovTxnReportApi {

    @Operation(summary = "List report transaction", description = "List report transaction", tags = { "gov_report_txn" }, operationId = "reportTxnList")
    @PostMapping(value = "/gov/report/txn/list/1.0")
    ResultList<TxnReportListRes> listReport(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnReportListReq body);

    @Operation(summary = "Detail report transaction", description = "Detail report transaction", tags = { "gov_report_txn" }, operationId = "reportTxnDetail")
    @PostMapping(value = "/gov/report/txn/detail/1.0")
    Result<TxnDetailRes> detailReport(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnDetailReq body);

    @Operation(summary = "Print document", description = "Print document", tags = { "gov_report_txn" }, operationId = "reportTxnPrint")
    @PostMapping(value = "/gov/report/txn/print/1.0")
    Result<ExportFileRes> print(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnPrintDocumentReq req);

    @Operation(summary = "Export report transaction", description = "Export report transaction", tags = { "gov_report_txn" }, operationId = "reportTxnExport")
    @PostMapping(value = "/gov/report/txn/export/1.0")
    Result<ExportFileRes> export(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TxnExportReportReq req);
}
