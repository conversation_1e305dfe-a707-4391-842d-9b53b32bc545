package com.bidv.ibank.dvc.controller.h2h;

import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hBatchDetailRes;
import com.bidv.ibank.dvc.service.h2h.H2hBatchService;
import com.bidv.ibank.framework.domain.response.Result;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class H2hBatchApiController implements H2hBatchApi {

    private final H2hBatchService batchService;

    @Override
    public Result<H2hBatchDetailRes> detail(BatchDetailReq req) {
        return batchService.detail(req);
    }

}
