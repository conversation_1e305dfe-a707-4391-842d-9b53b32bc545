package com.bidv.ibank.dvc.controller.param;

import com.bidv.ibank.framework.domain.response.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.AdministrativeAreaReq;
import com.bidv.ibank.dvc.model.request.RevenueAuthorityReq;
import com.bidv.ibank.dvc.model.request.TreasuryDetailReq;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryDetailRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface ParamApi {

    @Operation(summary = "Chapter list", description = "Chapter list", tags = { "gov_parameter" }, operationId = "parChapterList")
    @PostMapping(value = "/gov/par/chapter/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<ChapterRes> listChapter();

    @Operation(summary = "Economic content list", description = "Economic content list", tags = { "gov_parameter" }, operationId = "parEconomicContentList")
    @PostMapping(value = "/gov/par/economic-content/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<EconomicContentRes> listEconomicContent();

    @Operation(summary = "Tax type list", description = "Tax type list", tags = { "gov_parameter" }, operationId = "parTaxTypeList")
    @PostMapping(value = "/gov/par/tax-type/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<TaxTypeRes> listTaxType();

    @Operation(summary = "Customs currency list", description = "Customs currency list", tags = { "gov_parameter" }, operationId = "parCustomsCurrencyList")
    @PostMapping(value = "/gov/par/customs-currency/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<CustomsCurrencyRes> listCustomsCurrency();

    @Operation(summary = "Export import type list", description = "Export import type list", tags = { "gov_parameter" }, operationId = "parExportImportTypeList")
    @PostMapping(value = "/gov/par/export-import-type/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<ExportImportType> listExportImportType();

    @Operation(summary = "Treasury list", description = "Treasury list", tags = { "gov_parameter" }, operationId = "parTreasuryList")
    @PostMapping(value = "/gov/par/treasury/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<TreasuryRes> listTreasury();

    @Operation(summary = "Revenue account list", description = "Revenue account list", tags = { "gov_parameter" }, operationId = "parRevenueAccountList")
    @PostMapping(value = "/gov/par/revenue-account/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<RevenueAccountRes> listRevenueAccount();

    @Operation(summary = "Revenue authority list", description = "Revenue authority list", tags = { "gov_parameter" }, operationId = "parRevenueAuthorityList")
    @PostMapping(value = "/gov/par/revenue-authority/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<RevenueAuthorityRes> listRevenueAuthority(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody RevenueAuthorityReq req);

    @Operation(summary = "Administrative area list", description = "Administrative area list", tags = { "gov_parameter" }, operationId = "parAdministrativeAreaList")
    @PostMapping(value = "/gov/par/administrative-area/list/1.0", produces = { "application/json" }, consumes = { "application/json" })
    ResultList<AdministrativeAreaRes> listAdministrativeArea(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody AdministrativeAreaReq req);

    @Operation(summary = "Treasury detail", description = "Treasury detail", tags = { "gov_parameter" }, operationId = "parTreasuryDetail")
    @PostMapping(value = "/gov/par/treasury/detail/1.0", produces = { "application/json" }, consumes = { "application/json" })
    Result<TreasuryDetailRes> detailTreasury(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TreasuryDetailReq req);
}
