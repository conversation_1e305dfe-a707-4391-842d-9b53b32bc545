package com.bidv.ibank.dvc.controller.gov;

import com.bidv.ibank.dvc.model.request.TxnExportReportReq;
import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;

import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.service.customsduty.TxnService;
import com.bidv.ibank.dvc.service.gov.GovTxnReportService;
import com.bidv.ibank.dvc.service.gov.GovTxnService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GovTxnReportApiController implements GovTxnReportApi {

    private final GovTxnReportService txnReportService;
    private final TxnService txnService;
    private final GovTxnService govTxnService;

    @Override
    public ResultList<TxnReportListRes> listReport(TxnReportListReq req) {
        return txnReportService.listReport(req);
    }

    @Override
    public Result<TxnDetailRes> detailReport(TxnDetailReq req) {
        return txnService.detail(req);
    }

    @Override
    public Result<ExportFileRes> print(TxnPrintDocumentReq req) {
        return govTxnService.print(req);
    }

    @Override
    public Result<ExportFileRes> export(TxnExportReportReq req) {
        return govTxnService.export(req);
    }
}
