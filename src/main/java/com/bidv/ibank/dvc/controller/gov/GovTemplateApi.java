package com.bidv.ibank.dvc.controller.gov;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bidv.ibank.dvc.model.request.TemplateDeleteReq;
import com.bidv.ibank.dvc.model.request.TemplateListReq;
import com.bidv.ibank.dvc.model.request.TemplateSaveReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

@Validated
public interface GovTemplateApi {

    @Operation(summary = "Save template", description = "Save template", tags = { "gov_template" }, operationId = "templateSave")
    @PostMapping(value = "/gov/template/save/1.0")
    Result<String> save(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TemplateSaveReq req);

    @Operation(summary = "List template", description = "List template", tags = { "gov_template" }, operationId = "templateList")
    @PostMapping(value = "/gov/template/list/1.0")
    ResultList<TemplateListRes> list(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TemplateListReq req);

    @Operation(summary = "Delete template", description = "Delete template", tags = { "gov_template" }, operationId = "templateDelete")
    @PostMapping(value = "/gov/template/delete/1.0")
    Result<String> delete(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody TemplateDeleteReq req);
}
