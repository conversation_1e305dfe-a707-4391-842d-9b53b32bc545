package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.BatchConfirmReq;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.request.BatchDetailEditReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.BatchCalcFeeRes;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.BatchProcessResultRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.service.customsduty.BatchService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class BatchApiController implements BatchApi {

    private final BatchService batchService;

    @Override
    public Result<ExportFileRes> downloadTemplate() {
        return batchService.downloadTemplate();
    }

    @Override
    public Result<byte[]> downloadFile(BatchDetailReq req) {
        return batchService.downloadFile(req);
    }

    @Override
    public Result<String> uploadFile(MultipartFile file) {
        return batchService.uploadFile(file);
    }

    @Override
    public ResultList<BatchListRes> list(BatchListReq req) {
        return batchService.list(req);
    }

    @Override
    public Result<String> delete(BatchDetailReq req) {
        return batchService.delete(req);
    }

    @Override
    public Result<ExportFileRes> downloadResult(BatchDetailReq req) {
        return batchService.downloadResult(req);
    }

    @Override
    public Result<BatchDetailRes> detail(BatchDetailReq req) {
        return batchService.detail(req);
    }

    @Override
    public Result<ValidateCustomsDutyRes> validateDetail(ValidateCustomsDutyReq req) {
        return batchService.validateDetail(req);
    }

    @Override
    public Result<String> editDetail(BatchDetailEditReq req) {
        return batchService.editDetail(req);
    }

    @Override
    public Result<BatchCalcFeeRes> calcFee(BatchDetailReq req) {
        return batchService.calcFee(req);
    }

    @Override
    public Result<TxnInitPushRes> initPush(BatchDetailReq req) {
        return batchService.initPush(req);
    }

    @Override
    public Result<BatchProcessResultRes> confirmPush(BatchConfirmReq req) {
        return batchService.confirmPush(req);
    }
}
