package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.model.response.TxnListRes;
import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.dvc.model.request.TxnSaveReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.dvc.service.customsduty.TxnService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class TxnApiController implements TxnApi {

    private final TxnService txnService;

    @Override
    public ResultList<TxnPendingListRes> listPending(TxnPendingListReq req) {
        return txnService.listPending(req);
    }

    @Override
    public Result<TxnDetailRes> detail(@Valid TxnDetailReq req) {
        return txnService.detail(req);
    }

    @Override
    public Result<String> save(@Valid TxnSaveReq req) {
        return txnService.save(req);
    }

    @Override
    public ResultList<TxnListRes> list(TxnListReq req) {
        return txnService.list(req);
    }

    @Override
    public Result<String> edit(@Valid TxnSaveReq req) {
        return txnService.edit(req);
    }

    @Override
    public Result<String> updateStatus(@Valid TxnDetailReq req) {
        return txnService.updateStatus(req);
    }
}
