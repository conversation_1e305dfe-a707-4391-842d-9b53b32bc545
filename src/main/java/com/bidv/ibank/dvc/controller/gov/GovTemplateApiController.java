package com.bidv.ibank.dvc.controller.gov;

import org.springframework.web.bind.annotation.RestController;

import com.bidv.ibank.dvc.model.request.TemplateDeleteReq;
import com.bidv.ibank.dvc.model.request.TemplateListReq;
import com.bidv.ibank.dvc.model.request.TemplateSaveReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.dvc.service.gov.GovTemplateService;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
public class GovTemplateApiController implements GovTemplateApi {

    private final GovTemplateService govTemplateService;

    @Override
    public Result<String> save(@Valid TemplateSaveReq req) {
        return govTemplateService.save(req);
    }

    @Override
    public ResultList<TemplateListRes> list(@Valid TemplateListReq req) {
        return govTemplateService.list(req);
    }

    @Override
    public Result<String> delete(@Valid TemplateDeleteReq req) {
        return govTemplateService.delete(req);
    }
}
