package com.bidv.ibank.dvc.controller.customsduty;

import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.framework.domain.response.ResultList;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.framework.domain.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Validated
public interface BatchTaxApi {

    @Operation(summary = "Download template", description = "Download template", tags = { "gov_batch_tax" }, operationId = "batchTaxDownloadTemplate")
    @PostMapping(value = "/gov/customs-duty/batch/tax/download-template/1.0")
    Result<ExportFileRes> downloadTemplate();

    @Operation(summary = "List batch", description = "List batch", tags = { "gov_batch_tax" }, operationId = "batchTaxList")
    @PostMapping(value = "/gov/customs-duty/batch/tax/list/1.0")
    ResultList<BatchListRes> list(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchListReq req);

    @Operation(summary = "Upload file", description = "Upload file", tags = { "gov_batch_tax" }, operationId = "batchTaxUploadFile")
    @PostMapping(value = "/gov/customs-duty/batch/tax/upload/1.0")
    Result<String> uploadFile(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @RequestParam("file") MultipartFile file);

    @Operation(summary = "Download result", description = "Download result", tags = { "gov_batch_tax" }, operationId = "batchTaxResultDownload")
    @PostMapping(value = "/gov/customs-duty/batch/tax/result/download/1.0")
    Result<ExportFileRes> downloadResult(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);

    @Operation(summary = "Export inquiry result", description = "Export inquiry result", tags = { "gov_batch_tax" }, operationId = "batchTaxInquiryResultExport")
    @PostMapping(value = "/gov/customs-duty/batch/tax/inquiry-result/export/1.0")
    Result<ExportFileRes> exportInquiryResult(
            @Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody BatchDetailReq req);
}
