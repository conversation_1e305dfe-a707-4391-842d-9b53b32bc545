package com.bidv.ibank.dvc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.bidv.ibank.dvc.annotation.validator.AmountRangeValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Constraint(validatedBy = AmountRangeValidator.class)
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidAmountRange {
    String message() default "Invalid amount range";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
