package com.bidv.ibank.dvc.annotation.validator;

import java.math.BigDecimal;

import com.bidv.ibank.dvc.annotation.ValidAmountRange;
import com.bidv.ibank.dvc.model.filter.AmountRangeFilter;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class AmountRangeValidator implements ConstraintValidator<ValidAmountRange, AmountRangeFilter> {

    @Override
    public boolean isValid(AmountRangeFilter filter, ConstraintValidatorContext context) {
        try {
            BigDecimal minAmount = filter.getMinAmount();
            BigDecimal maxAmount = filter.getMaxAmount();

            // Validation condition
            if (minAmount == null && maxAmount == null) {
                return true;
            }

            if (minAmount != null && maxAmount != null) {
                return minAmount.compareTo(maxAmount) <= 0;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
