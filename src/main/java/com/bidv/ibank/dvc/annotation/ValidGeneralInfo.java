package com.bidv.ibank.dvc.annotation;

import com.bidv.ibank.dvc.annotation.validator.GeneralInfoValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = GeneralInfoValidator.class)
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidGeneralInfo {
    String message() default "Invalid general info";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}