package com.bidv.ibank.dvc.annotation.validator;

import com.bidv.ibank.dvc.annotation.ValidTxnPushParams;
import com.bidv.ibank.dvc.model.request.TxnInitPushReq;
import com.bidv.ibank.dvc.util.constant.TxnPushTypeEnum;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class TxnPushParamsValidator implements ConstraintValidator<ValidTxnPushParams, TxnInitPushReq> {

    @Override
    public boolean isValid(TxnInitPushReq req, ConstraintValidatorContext context) {

        if (Objects.equals(req.getType(), TxnPushTypeEnum.PUSH) && CollectionUtils.isEmpty(req.getTxnIds())) {
            return false;
        }
        if (!Objects.equals(req.getType(), TxnPushTypeEnum.PUSH) && StringUtils.isBlank(req.getTransKey())) {
            return false;
        }
        return true;
    }
}