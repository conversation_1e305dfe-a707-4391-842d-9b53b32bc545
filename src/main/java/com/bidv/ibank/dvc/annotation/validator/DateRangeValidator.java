package com.bidv.ibank.dvc.annotation.validator;

import java.time.LocalDate;

import com.bidv.ibank.dvc.annotation.ValidDateRange;
import com.bidv.ibank.dvc.model.filter.DateRangeFilter;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class DateRangeValidator implements ConstraintValidator<ValidDateRange, DateRangeFilter> {

    @Override
    public boolean isValid(DateRangeFilter filter, ConstraintValidatorContext context) {
        try {
            LocalDate startDate = filter.getStartDate();
            LocalDate endDate = filter.getEndDate();

            return startDate == null || endDate == null || !startDate.isAfter(endDate);
        } catch (Exception e) {
            // Catch reflection or cast exceptions
            return false;
        }
    }
}
