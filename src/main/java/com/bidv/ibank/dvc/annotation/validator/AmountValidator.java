package com.bidv.ibank.dvc.annotation.validator;

import com.bidv.ibank.dvc.annotation.ValidAmount;
import com.bidv.ibank.dvc.model.dto.AmountDto;
import com.bidv.ibank.dvc.util.CcyUtils;
import com.bidv.ibank.dvc.util.constant.CcyEnum;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

public class AmountValidator implements ConstraintValidator<ValidAmount, AmountDto> {

    private final List<String> validCcyList = List.of(
            CcyEnum.VND.name());

    @Override
    public boolean isValid(AmountDto req, ConstraintValidatorContext context) {
        try {
            String ccy = req.getCcy();
            String amount = req.getAmount();

            if (StringUtils.isBlank(amount))
                return true;
            if (StringUtils.isBlank(ccy))
                return false;
            if (!validCcyList.contains(ccy.toUpperCase()))
                return false;

            BigDecimal amountValue = new BigDecimal(amount);
            if (amountValue.compareTo(BigDecimal.ZERO) < 0)
                return false;

            int maxLength = CcyUtils.getMaxLengthAmountByCcy(ccy);
            String pattern = CcyUtils.getRegexAmountByCcy(ccy);
            return amount.matches(pattern) && amount.length() <= maxLength;
        } catch (Exception e) {
            return false;
        }
    }
}