package com.bidv.ibank.dvc.annotation.validator;

import com.bidv.ibank.dvc.annotation.ValidGeneralInfo;
import com.bidv.ibank.dvc.model.dto.TxnGeneralInfoDto;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class GeneralInfoValidator implements ConstraintValidator<ValidGeneralInfo, TxnGeneralInfoDto> {

    @Override
    public boolean isValid(TxnGeneralInfoDto req, ConstraintValidatorContext context) {
        try {
            String altTaxCode = req.getAltTaxCode();
            String altPayerName = req.getAltPayerName();
            String altPayerAddr = req.getAltPayerAddr();

            if (StringUtils.isBlank(altTaxCode)) {
                req.setAltPayerName(null);
                req.setAltPayerAddr(null);
            }

            if (StringUtils.isNotBlank(altTaxCode) && StringUtils.isAnyBlank(altPayerName, altPayerAddr)) {
                return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}