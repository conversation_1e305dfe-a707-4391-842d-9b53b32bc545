package com.bidv.ibank.dvc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.annotation.validator.StatusesValidator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Constraint(validatedBy = StatusesValidator.class)
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidStatuses {
    String message() default "Invalid statuses";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    TransactionStatusEnum[] statuses() default {};
}
