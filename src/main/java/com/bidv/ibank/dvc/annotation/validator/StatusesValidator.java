package com.bidv.ibank.dvc.annotation.validator;

import java.util.List;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.annotation.ValidStatuses;
import com.bidv.ibank.dvc.model.filter.StatusesFilter;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class StatusesValidator implements ConstraintValidator<ValidStatuses, StatusesFilter> {

    private TransactionStatusEnum[] validStatuses;

    @Override
    public void initialize(ValidStatuses constraintAnnotation) {
        this.validStatuses = constraintAnnotation.statuses();
        if (this.validStatuses == null || this.validStatuses.length == 0) {
            this.validStatuses = TransactionStatusEnum.values();
        }
    }

    @Override
    public boolean isValid(StatusesFilter filter, ConstraintValidatorContext context) {
        try {
            List<String> statuses = filter.getStatuses();

            if (statuses == null) {
                return true;
            }

            return statuses.stream()
                    .allMatch(status -> java.util.Arrays.stream(validStatuses)
                            .anyMatch(validStatus -> validStatus.name().equalsIgnoreCase(status)));

        } catch (Exception e) {
            return false;
        }
    }

}
