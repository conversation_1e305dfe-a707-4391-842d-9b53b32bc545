package com.bidv.ibank.dvc.service.common.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

import com.bidv.ibank.dvc.util.StrUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.client.common.dto.masterdata.CustomerPmtSpecDto;
import com.bidv.ibank.client.common.dto.masterdata.ParInterruptTimeDto;
import com.bidv.ibank.client.common.util.AppConstants.TransProcessMethodResponse;
import com.bidv.ibank.common.txn.util.constant.ChargeFeeOpt;
import com.bidv.ibank.dvc.model.dto.TxnTaxItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.ValidationService;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.framework.context.AppContext;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.integrate.entity.account.CoreAccount;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ValidationServiceImpl implements ValidationService {

    private final CommonService commonService;

    @Override
    public Result<String> validateAfterInitApproval(List<GOVPaymentTransactionEntity> txnEntityList) {
        // Kiểm tra giờ gián đoạn, giờ làm việc (chỉ kiểm tra với checker cuối)
        GOVPaymentTransactionEntity finalTxnEntity = txnEntityList.stream().filter(GOVPaymentTransactionEntity::isFinal).findFirst().orElse(null);
        if (finalTxnEntity != null) {
            Result<String> result = validateInterruptTime(finalTxnEntity.getSubProdCode());
            if (!result.isSuccess()) {
                return Result.error(ResponseCode.INVALID_APPROVE_INTERRUPT_TIME);
            }
        }

        // Kiểm tra ngày hiệu lực của giao dịch
        ResponseCode errorCode = txnEntityList.size() == 1
                ? ResponseCode.INVALID_APPROVE_EFF_DATE
                : ResponseCode.EXIST_INVALID_APPROVE_EFF_DATE;

        for (GOVPaymentTransactionEntity txnEntity : txnEntityList) {
            Result<LocalDate> validateResult = commonService.validateEffDate(txnEntity.getEffDate());
            if (!validateResult.isSuccess()) {
                return Result.error(errorCode);
            }
        }

        // Kiểm tra thêm điều kiện nếu duyệt đơn và checker cuối
        if (txnEntityList.size() == 1 && txnEntityList.get(0).isFinal()) {
            GOVPaymentTransactionEntity txnEntity = txnEntityList.get(0);

            // Kiểm tra điều kiện được phép trích nợ của tài khoản chuyển
            Result<String> validateDebitAccountResult = validateDebitAccount(txnEntity, false);
            if (!validateDebitAccountResult.isSuccess()) {
                return Result.error(validateDebitAccountResult.getCode(), validateDebitAccountResult.getMessage());
            }
        }

        return Result.success(null);
    }

    /**
     * Kiểm tra giờ gián đoạn, giờ làm việc
     *
     * @param subProdCode
     * @param interruptTimeDtos
     * @return
     */
    @Override
    public Result<String> validateInterruptTime(String subProdCode) {
        List<ParInterruptTimeDto> interruptTimeDtos = commonService.getInterruptTime();
        if (interruptTimeDtos == null || interruptTimeDtos.isEmpty()) {
            return Result.success(null);
        }
        List<ParInterruptTimeDto> matchingInterruptTimeDtos = interruptTimeDtos.stream()
                .filter(i -> StringUtils.isNotBlank(i.getSubProdCode()) &&
                        i.getSubProdCode().equals(subProdCode) &&
                        (com.bidv.ibank.client.common.util.AppConstants.ACTION_CODE.APPROVE.equals(i.getFuncCode()) || i.getFuncCode() == null))
                .toList();

        if (CollectionUtils.isNotEmpty(matchingInterruptTimeDtos)) {
            LocalTime currentTime = LocalTime.of(
                    LocalDateTime.now().getHour(),
                    LocalDateTime.now().getMinute(),
                    LocalDateTime.now().getSecond());

            // Kiểm tra từng phần tử trong danh sách, nếu có bất kỳ phần tử nào có thời gian gián đoạn "active"
            for (ParInterruptTimeDto dto : matchingInterruptTimeDtos) {
                if (dto.getStartTime() != null && dto.getEndTime() != null) {
                    LocalTime startTime = LocalTime.of(
                            dto.getStartTime().getHour(),
                            dto.getStartTime().getMinute(),
                            dto.getStartTime().getSecond());

                    LocalTime endTime = LocalTime.of(
                            dto.getEndTime().getHour(),
                            dto.getEndTime().getMinute(),
                            dto.getEndTime().getSecond());

                    if ((currentTime.isAfter(startTime) && currentTime.isBefore(endTime))
                            || currentTime.equals(startTime)
                            || currentTime.equals(endTime)) {
                        log.error("validateInterruptTime interruptTimeData: {}, {}, currentTime: {}", startTime, endTime, currentTime);
                        return Result.error(ResponseCode.INVALID_APPROVE_INTERRUPT_TIME);
                    }
                }
            }
        }
        return Result.success(null);
    }

    @Override
    public Result<String> validateDebitAccount(GOVPaymentTransactionEntity txnEntity, boolean isBeforeAcct) {
        Result<CoreAccount> coreAccountResult = commonService.validateCoreAccount(txnEntity.getDebitAccNo(), txnEntity.getCifNo());
        if (!coreAccountResult.isSuccess()) {
            return Result.error(coreAccountResult.getCode(), coreAccountResult.getMessage());
        }

        CoreAccount debitAccCore = coreAccountResult.getData();
        BigDecimal actualAmount = debitAccCore.getAvailableBalance().subtract(txnEntity.getAmount());
        String flagResult = AppConstants.UNDER_BALANCE_FLAG.NO;

        if (ChargeFeeOpt.INST.code().equals(txnEntity.getFeeOpt())) {
            actualAmount = actualAmount.subtract(txnEntity.getFeeTotal());
        }
        List<String> purposeUncheckList = StrUtils.splitAndTrim(AppContext.getProperty(AppConstants.PURPOSE_CODE_NOT_CHECK), ",", AppConstants.PURPOSE_CODE_NOT_CHECK_LIST);
        if (TransProcessMethodResponse.USING_OVERDRAFT.equals(txnEntity.getProcessFlow()) &&
                (StringUtils.isNotBlank(txnEntity.getAltTaxCode()) || !purposeUncheckList.contains(debitAccCore.getPurposeCode()))) {
            // T = Số dư khả dụng - Số dư thấu chi (overdraftBalance) - Số tiền chuyển - Tổng số phí (bao gồm VAT)
            actualAmount = actualAmount.subtract(debitAccCore.getOverdraftBalance());
            if (actualAmount.compareTo(debitAccCore.getMinBalance()) < 0) {
                // Check tham số Xử lý giao dịch dưới số dư được khai báo cho khách hàng
                if (!canRetryBalance(txnEntity)) {
                    return Result.error(ResponseCode.INVALID_APPROVE_OVER_DRAFT);
                }
                // Đánh dấu tài khoản đăng kí xử lý dưới số dư
                flagResult = AppConstants.UNDER_BALANCE_FLAG.YES;
            }
        } else {
            // T= Số dư khả dụng - Số tiền chuyển - Tổng số phí (bao gồm VAT)
            if (actualAmount.compareTo(debitAccCore.getMinBalance()) < 0) {
                // Check tham số Xử lý giao dịch dưới số dư được khai báo cho khách hàng
                if (!canRetryBalance(txnEntity)) {
                    if (actualAmount.compareTo(BigDecimal.ZERO) < 0) {
                        return Result.error(ResponseCode.INSUFFICIENT_BALANCE);
                    }
                    return Result.error(ResponseCode.INSUFFICIENT_MIN_BALANCE);
                }
                // Đánh dấu tài khoản đăng kí xử lý dưới số dư
                flagResult = AppConstants.UNDER_BALANCE_FLAG.YES;
            }
        }
        // Thêm kiểm tra trc khi hạch toán
        if (isBeforeAcct) {
            Result<String> validatePurposeResult = validatePurposeAndRelation(debitAccCore);
            if (!validatePurposeResult.isSuccess()) {
                return Result.error(validatePurposeResult.getCode(), validatePurposeResult.getMessage());
            }
        }
        // Trả kết quả kiểm tra số dư tài khoản
        return Result.success(flagResult);
    }

    @Override
    public Result<String> validateDuplicatePayments(List<TxnTaxItemDto> taxItems) {
        List<TxnTaxItemDto> uniqueItems = taxItems.stream()
                .distinct()
                .toList();

        return uniqueItems.size() < taxItems.size()
                ? Result.error(ResponseCode.TAX_ITEMS_DUPLICATED)
                : Result.success(null);
    }

    @Override
    public Result<String> validateTaxCode(String taxCode, String altTaxCode) {
        Result<CustomerDto> customerDtoResult = commonService.getCustomerInfo(AuthenticationUtils.getCurrentUser().getUser().getCif());
        if (!customerDtoResult.isSuccess()) {
            return Result.error(customerDtoResult.getCode(), customerDtoResult.getMessage());
        }
        String userTaxCode = customerDtoResult.getData().getTaxCode();
        // AltTaxCode is blank - Pay for business
        if (StringUtils.isBlank(altTaxCode)) {
            if (!taxCode.equals(userTaxCode)) {
                return Result.error(ResponseCode.TAX_CODE_MISMATCH);
            }
        } else {
            // Pay on behalf
            if (!altTaxCode.equals(userTaxCode)) {
                return Result.error(ResponseCode.ALT_TAX_CODE_MISMATCH);
            }
            if (taxCode.equals(altTaxCode)) {
                return Result.error(ResponseCode.TAX_CODE_DUPLICATE);
            }
        }
        return Result.success(null);
    }

    @Override
    public Result<String> validateTaxCode(String taxCode) {
        Result<CustomerDto> customerInfo = commonService.getCustomerInfo(AuthenticationUtils.getCurrentUser().getUser().getCif());
        if (!customerInfo.isSuccess()) {
            return Result.error(customerInfo.getCode(), customerInfo.getMessage());
        }
        String userTaxCode = customerInfo.getData().getTaxCode();
        if (!userTaxCode.equals(taxCode)) {
            return Result.error(ResponseCode.TAX_CODE_MISMATCH_01);
        }
        return Result.success(null);
    }

    private boolean canRetryBalance(GOVPaymentTransactionEntity txnEntity) {
        if (StringUtils.isBlank(txnEntity.getRetryInsfctBal())) {
            Result<CustomerPmtSpecDto> pmtSpecResult = commonService.getCusPmtSpec();
            if (!pmtSpecResult.isSuccess()) {
                return false;
            }

            return AppConstants.UNDER_BALANCE_FLAG.YES.equals(pmtSpecResult.getData().getRetryInsfctBal());
        }
        return AppConstants.UNDER_BALANCE_FLAG.YES.equals(txnEntity.getRetryInsfctBal());
    }

    private Result<String> validatePurposeAndRelation(CoreAccount debitAccCore) {
        List<String> accAllowAcctRelationString = StrUtils.splitAndTrim(
                AppContext.getProperty(AppConstants.ACCOUNT_ALLOW_ACCT_RELATION), ",", List.of());

        List<String> accDebitPurposeList = StrUtils.splitAndTrim(
                AppContext.getProperty(AppConstants.ACCOUNT_DEBIT_PURPOSE), ",", List.of());

        if (accDebitPurposeList.contains(debitAccCore.getPurposeCode()) || accDebitPurposeList.contains(debitAccCore.getSCCode())) {
            return Result.error(ResponseCode.SC_PURPOSE_CODE_INVALID);
        }
        if (!accAllowAcctRelationString.contains(debitAccCore.getAcctRelation())) {
            return Result.error(ResponseCode.DEBIT_ACC_RELATION_INVALID);
        }

        return Result.success(null);
    }
}
