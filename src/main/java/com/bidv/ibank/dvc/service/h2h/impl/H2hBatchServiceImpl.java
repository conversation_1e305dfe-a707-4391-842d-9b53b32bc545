package com.bidv.ibank.dvc.service.h2h.impl;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.dvc.service.common.CommonService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bidv.ibank.dvc.model.response.h2h.H2hBatchDetailRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.h2h.H2hBatchService;
import com.bidv.ibank.dvc.specification.BatchSpecifications;
import com.bidv.ibank.dvc.specification.TxnSpecifications;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.framework.domain.response.Result;
import org.springframework.data.jpa.domain.Specification;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class H2hBatchServiceImpl implements H2hBatchService {

    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;
    private final GOVPaymentBatchRepository govPaymentBatchRepository;
    private final GOVPaymentBatchMapper govPaymentBatchMapper;
    private final CommonService commonService;

    @Override
    public Result<H2hBatchDetailRes> detail(BatchDetailReq req) {
        GOVPaymentBatchEntity govBatchEntity = govPaymentBatchRepository.findOne(
                Specification.where(BatchSpecifications.batchNoEq(req.getBatchNo()))
                        .and(BatchSpecifications.createdByEq(AuthenticationUtils.getCurrentUser().getUser().getUsername()))
                        .and(BatchSpecifications.statusIn(List.of(BatchStatusEnum.PROCESSED.name())))).orElse(null);
        if (govBatchEntity == null) {
            return Result.error(ResponseCode.RESOURCE_NOTFOUND);
        }

        List<String> batchItemIds = govBatchEntity.getGovPaymentBatchItemList().stream()
                .filter(e -> e.getStatus().equals(BatchItemStatusEnum.VALID))
                .map(GOVPaymentBatchItemEntity::getId)
                .toList();

        List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());

        List<GOVPaymentTransactionEntity> validTxnEntities = govPaymentTransactionRepository.findAll(
                Specification.where(TxnSpecifications.batchItemIdIn(batchItemIds))
                        .and(TxnSpecifications.createdByEq(AuthenticationUtils.getCurrentUser().getUsername()))
                        .and(TxnSpecifications.debitAccNoIn(accounts.stream().map(AuthAccountDto::getAccountNo).toList())));
        if (validTxnEntities.size() != batchItemIds.size()) {
            return Result.error(ResponseCode.USER_ACCOUNT_INVALID);
        }

        return Result.success(govPaymentBatchMapper.toH2hDetailDto(govBatchEntity, validTxnEntities));
    }
}