package com.bidv.ibank.dvc.service.customsduty.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.model.response.TxnListRes;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.common.txn.model.dto.InitTransactionRes;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.mapper.tcc.TccReqMapper;
import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.dvc.model.request.TxnSaveReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.service.customsduty.TxnService;
import com.bidv.ibank.dvc.specification.BaseSpecifications;
import com.bidv.ibank.dvc.specification.TxnSpecifications;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.TccAccountingEnum;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingResultTransferInfo;
import com.bidv.ibank.integrate.entity.tcc.TccMessageReq;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;
import com.bidv.ibank.integrate.entity.tcc.TccResendDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccResendDocRes;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class TxnServiceImpl implements TxnService {

    private final TccService tccService;
    private final TccReqMapper tccReqMapper;
    private final CommonService commonService;
    private final IntegrateServiceFactory integrateServiceFactory;
    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;
    private final GOVPaymentItemRepository govPaymentItemRepository;

    private final GOVPaymentTransactionMapper govPaymentTransactionMapper;

    @Override
    public ResultList<TxnPendingListRes> listPending(TxnPendingListReq req) {
        List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());

        Pageable pageable = QueryUtils.buildPageRequest(req, Sort.Order.desc("priority"), Sort.Order.desc("updatedDate"), Sort.Order.desc("id"));

        Page<GOVPaymentTransactionEntity> govTxnEntityList = govPaymentTransactionRepository.findAll(
                TxnSpecifications.createTxnPendingListSpec(req)
                        .and(TxnSpecifications.createdByEq(AuthenticationUtils.getCurrentUser().getUsername()))
                        .and(TxnSpecifications.debitAccNoIn(accounts.stream().map(AuthAccountDto::getAccountNo).toList())),
                pageable);

        return ResultList.success(govTxnEntityList.stream().map(govPaymentTransactionMapper::toPendingDto).toList(), govTxnEntityList.getTotalElements());
    }

    @Override
    public Result<TxnDetailRes> detail(TxnDetailReq req) {
        Optional<GOVPaymentTransactionEntity> govTxnEntity = govPaymentTransactionRepository.findOne(
                Specification.where(BaseSpecifications.<GOVPaymentTransactionEntity>idEq(req.getTxnId()))
                        .and(TxnSpecifications.statusNotIn(List.of(TransactionStatusEnum.DELETED.name()))));
        if (govTxnEntity.isEmpty()) {
            return Result.error(ResponseCode.TXN_NOT_FOUND);
        }

        // Kiểm tra quyền truy cập tài khoản
        if (!commonService.checkAuthorizationAccount(govTxnEntity.get().getDebitAccNo(), CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name())) {
            return Result.error(ResponseCode.USER_ACCOUNT_INVALID);
        }

        return Result.success(govPaymentTransactionMapper.toDetailDto(govTxnEntity.get()));
    }

    @Override
    @Transactional
    public Result<String> save(TxnSaveReq req) {
        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initTxnResponse = commonService.initTransaction(req.getTransKey(), TransactionActionEnum.INIT);
        if (!initTxnResponse.isSuccess()) {
            return Result.error(initTxnResponse.getCode(), initTxnResponse.getMessage());
        }
        GOVPaymentTransactionEntity govTxnEntity = initTxnResponse.getData().getTransaction();
        List<GOVPaymentItemEntity> govPaymentItemList = govTxnEntity.getPaymentItems().stream()
                .map(item -> {
                    item.setTxnId(govTxnEntity.getId());
                    return item;
                }).toList();

        govPaymentItemRepository.saveAll(govPaymentItemList);

        return Result.success(govTxnEntity.getId());
    }

    @Override
    public ResultList<TxnListRes> list(TxnListReq req) {
        List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());

        Pageable pageable = QueryUtils.buildPageRequest(req, Sort.Order.desc("createdDate"), Sort.Order.desc("id"));

        Page<GOVPaymentTransactionEntity> govTxnEntityList = govPaymentTransactionRepository.findAll(
                TxnSpecifications.createTxnListSpec(req)
                        .and(TxnSpecifications.debitAccNoIn(accounts.stream().map(AuthAccountDto::getAccountNo).toList())),
                pageable);

        return ResultList.success(govTxnEntityList.stream().map(govPaymentTransactionMapper::toListDto).toList(), govTxnEntityList.getTotalElements());
    }

    @Override
    @Transactional
    public Result<String> edit(TxnSaveReq req) {
        Result<InitTransactionRes<GOVPaymentTransactionEntity>> initTxnResponse = commonService.initTransaction(req.getTransKey(), TransactionActionEnum.EDIT);
        if (!initTxnResponse.isSuccess()) {
            return Result.error(initTxnResponse.getCode(), initTxnResponse.getMessage());
        }
        GOVPaymentTransactionEntity govTxnEntity = initTxnResponse.getData().getTransaction();
        // Delete old items first
        govPaymentItemRepository.deleteByTxnId(govTxnEntity.getId());

        // Prepare and save new items
        List<GOVPaymentItemEntity> govPaymentItemList = govTxnEntity.getPaymentItems().stream()
                .map(item -> {
                    item.setTxnId(govTxnEntity.getId());
                    return item;
                }).toList();

        govPaymentItemRepository.saveAll(govPaymentItemList);

        return Result.success(govTxnEntity.getId());
    }

    @Override
    @Transactional
    public Result<String> updateStatus(TxnDetailReq req) {
        GOVPaymentTransactionEntity govTxn = govPaymentTransactionRepository.findById(req.getTxnId()).orElse(null);
        if (govTxn == null) {
            return Result.error(ResponseCode.TXN_NOT_FOUND);
        }
        if (!Objects.equals(govTxn.getStatus(), TransactionStatusEnum.SUCCESS.name())) {
            return Result.error(ResponseCode.TRANS_STATUS);
        }
        // Kiểm tra trạng thái hải quan
        if (TransactionStateEnum.SUCCESS.name().equals(govTxn.getState())) {
            return Result.error(ResponseCode.DOC_TAX_EXIST);
        }
        // Kiểm tra số lần thử (tối đa 3 lần)
        if (govTxn.getUpdStatusCnt() != null && govTxn.getUpdStatusCnt() >= AppConstants.NUMBER.THREE) {
            return Result.error(ResponseCode.UPD_STATUS_RETRY_EXCEEDED);
        }

        TccMessageReq<TccResendDocReq> tccReq = new TccMessageReq<>(tccReqMapper.toResendDocReq(govTxn.getTccDocId()));
        // Gọi Tcc gửi lại chứng từ
        Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccResendDocRes>>>> response = tccService.executeTccCall(
                tccReq, input -> integrateServiceFactory.getTccService().resendDoc(input));

        Integer updStatusCnt = govTxn.getUpdStatusCnt() != null ? govTxn.getUpdStatusCnt() + AppConstants.NUMBER.ONE : AppConstants.NUMBER.ONE;
        // Cập nhật số lần thử còn lại -> Lưu và trả về số lần còn lại nếu lỗi
        if (!response.isSuccess()) {
            govPaymentTransactionRepository.updateRetryStatusCount(govTxn.getId(), updStatusCnt);
            return Result.error(ResponseCode.UPD_STATUS_RETRY_EXCEEDED_01, AppConstants.NUMBER.THREE - updStatusCnt);
        }

        TccAccountingResultTransferInfo transferInfo = response.getData()
                .getBody().getMessageBody()
                .getMessageResponse().getResult()
                .getTransferInfo();

        if (!transferInfo.getStatus().equals(TccAccountingEnum.SUCCESS.getValue())) {
            govPaymentTransactionRepository.updateRetryStatusCountAndErrCode(
                    govTxn.getId(),
                    updStatusCnt,
                    transferInfo.getErrCode(),
                    transferInfo.getErrDesc());
            return Result.error(ResponseCode.UPD_STATUS_RETRY_EXCEEDED_01, AppConstants.NUMBER.THREE - updStatusCnt);
        }
        govPaymentTransactionRepository.updateState(List.of(govTxn.getId()), TransactionStateEnum.SUCCESS.name());
        return Result.success(govTxn.getId());
    }
}
