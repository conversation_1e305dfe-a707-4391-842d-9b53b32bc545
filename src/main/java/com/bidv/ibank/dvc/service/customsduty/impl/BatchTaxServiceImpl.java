package com.bidv.ibank.dvc.service.customsduty.impl;

import com.bidv.ibank.dvc.model.dto.BatchTaxItemExportDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxResultExportDto;
import com.bidv.ibank.dvc.model.dto.ValidateFileDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.service.customsduty.AsyncService;
import com.bidv.ibank.dvc.service.customsduty.BatchTaxService;
import com.bidv.ibank.dvc.specification.BatchItemSpecifications;
import com.bidv.ibank.dvc.specification.BatchSpecifications;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.ArrUtils;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.FileTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.context.AppContext;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import com.bidv.ibank.util.excel.ExcelExporter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class BatchTaxServiceImpl implements BatchTaxService {

    private final FileService fileService;
    private final AsyncService asyncService;
    private final CommonService commonService;
    private final IntegrateServiceFactory integrateServiceFactory;

    private final GOVPaymentBatchRepository govPaymentBatchRepository;

    private final GOVPaymentBatchMapper govPaymentBatchMapper;
    private final GOVPaymentBatchItemRepository govPaymentBatchItemRepository;

    @Override
    public Result<ExportFileRes> downloadTemplate() {
        Result<FileInfo> result = fileService.uploadToS3(AppConstants.BATCH_TAX_TEMPLATE_PATH, AppConstants.BATCH_TAX_TEMPLATE_FILE_NAME, FileTypeEnum.XLSX
                .name(), true);
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(ExportFileRes.builder()
                .url(result.getData().getFileUrl())
                .build());
    }

    @Override
    public ResultList<BatchListRes> list(BatchListReq req) {
        Pageable pageable = QueryUtils.buildPageRequest(req, Sort.Order.desc("createdDate"), Sort.Order.desc("batchNo"));

        Page<GOVPaymentBatchEntity> govBatchTaxEntityList = govPaymentBatchRepository.findAll(
                BatchSpecifications.createBatchListSpec(req, BatchTypeEnum.INQUIRY),
                pageable);

        return ResultList.success(govBatchTaxEntityList.stream().map(govPaymentBatchMapper::toListDto).toList(), govBatchTaxEntityList.getTotalElements());
    }

    @Override
    public Result<ExportFileRes> downloadResult(BatchDetailReq req) {
        try {
            GOVPaymentBatchEntity govBatchEntity = govPaymentBatchRepository.findOne(
                    BatchSpecifications.createResultSpec(req.getBatchNo(), BatchTypeEnum.INQUIRY)).orElse(null);
            if (govBatchEntity == null) {
                return Result.error(ResponseCode.RESOURCE_NOTFOUND);
            }

            List<GOVPaymentBatchItemEntity> govBatchItemEntityList = govPaymentBatchItemRepository.findAll(
                    Specification.where(BatchItemSpecifications.batchIdEq(govBatchEntity.getId()))
                            .and(BatchItemSpecifications.statusEq(BatchItemStatusEnum.INVALID.name())));

            govBatchItemEntityList = ArrUtils.distinctList(
                    govBatchItemEntityList,
                    GOVPaymentBatchItemEntity::getDeclarationNo,
                    GOVPaymentBatchItemEntity::getDeclarationYear,
                    GOVPaymentBatchItemEntity::getStatus);

            List<BatchTaxResultExportDto> taxResultExportDtos = govPaymentBatchMapper.toTaxResultExportDto(govBatchItemEntityList);
            String fileName = govBatchEntity.getName() + AppConstants.BATCH_TAX_RESULT_SUFFIX_FILE_NAME;
            FileInfo fileInfo = ExcelExporter.exportExcel(
                    AppConstants.BATCH_TAX_RESULT_TEMPLATE_PATH,
                    AppConstants.BATCH_TAX_RESULT_START_ROW,
                    AppConstants.BATCH_DOWNLOAD_RESULT_SHIFT_ROW,
                    null,
                    taxResultExportDtos,
                    BatchTaxResultExportDto.class,
                    false,
                    integrateServiceFactory.getFileService().createFileHubWriter(fileName, true));
            if (fileInfo == null) {
                log.error("ExportExcel returned null FileInfo");
                return Result.error(ResponseCode.TIMEOUT_01);
            }
            return Result.success(ExportFileRes.builder()
                    .url(fileInfo.getFileUrl())
                    .build());
        } catch (Exception e) {
            log.error("Error downloading result: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    @Override
    @Transactional
    public Result<String> uploadFile(MultipartFile file) {
        long maxItem = AppContext.getProperty(AppConstants.DVC_UPLOAD_MAX_ITEM, 0L);
        ValidateFileDto<BatchTaxImportItemDto> validateDto = ValidateFileDto.<BatchTaxImportItemDto>builder()
                .pattern(AppConstants.BATCH_IMPORT_VALID_PATTERN)
                .startRow(AppConstants.BATCH_TAX_FILE_START_ROW)
                .maxItem(maxItem)
                .maxFileSize(AppContext.getProperty(AppConstants.DVC_UPLOAD_MAX_FILE_SIZE, 0L))
                .fileTypes(List.of(FileTypeEnum.XLSX, FileTypeEnum.XLS))
                .clazz(BatchTaxImportItemDto.class)
                .build();

        Result<List<BatchTaxImportItemDto>> validateResult = fileService.validateAndReadExcelFile(file, validateDto);
        if (!validateResult.isSuccess()) {
            return Result.error(validateResult.getCode(), validateResult.getMessage(), maxItem);
        }

        // Check sum
        String checksum = commonService.generateChecksum(file);
        if (StringUtils.isBlank(checksum)) {
            return Result.error(ResponseCode.TIMEOUT_01);
        }
        List<GOVPaymentBatchEntity> existingBatchs = govPaymentBatchRepository.findAll(BatchSpecifications.checkFileExistSpec(file, checksum,
                BatchTypeEnum.INQUIRY));
        if (!existingBatchs.isEmpty()) {
            return Result.error(ResponseCode.FILE_EXISTS);
        }

        // Upload file to S3
        Result<FileInfo> uploadResult = fileService.uploadToS3(file, false);
        if (!uploadResult.isSuccess()) {
            return Result.error(uploadResult.getCode(), uploadResult.getMessage());
        }

        GOVPaymentBatchEntity batchEntity = govPaymentBatchMapper.toEntity(
                file,
                checksum,
                uploadResult.getData().getFileKey(),
                BatchTypeEnum.INQUIRY);
        govPaymentBatchRepository.save(batchEntity);
        asyncService.handleBatchUpload(batchEntity.getId());

        return Result.success(batchEntity.getBatchNo());
    }

    @Override
    public Result<ExportFileRes> exportInquiryResult(BatchDetailReq req) {
        try {
            GOVPaymentBatchEntity govBatchEntity = govPaymentBatchRepository.findOne(
                    BatchSpecifications.createResultSpec(req.getBatchNo(), BatchTypeEnum.INQUIRY)).orElse(null);
            if (govBatchEntity == null) {
                return Result.error(ResponseCode.RESOURCE_NOTFOUND);
            }

            List<GOVPaymentBatchItemEntity> govBatchItemEntityList = govPaymentBatchItemRepository.findAll(
                    Specification.where(BatchItemSpecifications.batchIdEq(govBatchEntity.getId()))
                            .and(BatchItemSpecifications.statusEq(BatchItemStatusEnum.VALID.name())));

            List<BatchTaxItemExportDto> taxItemExportDtos = govPaymentBatchMapper.toTaxItemExportDto(govBatchItemEntityList);

            FileInfo fileInfo = ExcelExporter.exportExcel(
                    AppConstants.BATCH_TAX_INQUIRY_RESULT_TEMPLATE_PATH,
                    AppConstants.BATCH_TAX_INQUIRY_RESULT_START_ROW,
                    AppConstants.BATCH_DOWNLOAD_RESULT_SHIFT_ROW,
                    null,
                    taxItemExportDtos,
                    BatchTaxItemExportDto.class,
                    true,
                    integrateServiceFactory.getFileService().createFileHubWriter(AppConstants.BATCH_TAX_INQUIRY_RESULT_FILE_NAME, true));
            if (fileInfo == null) {
                log.error("ExportExcel returned null FileInfo");
                return Result.error(ResponseCode.TIMEOUT_01);
            }
            return Result.success(ExportFileRes.builder()
                    .url(fileInfo.getFileUrl())
                    .build());
        } catch (Exception e) {
            log.error("Error exporting detail: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }
}
