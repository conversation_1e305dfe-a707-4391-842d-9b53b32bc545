package com.bidv.ibank.dvc.service.gov;

import com.bidv.ibank.dvc.model.request.TxnRejectReq;
import com.bidv.ibank.dvc.model.request.TxnApproveReq;
import com.bidv.ibank.dvc.model.request.TxnConfirmReq;
import com.bidv.ibank.dvc.model.request.TxnInitPushReq;
import com.bidv.ibank.dvc.model.request.TxnDeleteReq;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.dvc.model.request.TxnPendingApprovalListReq;
import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.response.TxnRejectRes;
import com.bidv.ibank.dvc.model.response.TxnApprovalResultRes;
import com.bidv.ibank.dvc.model.response.TxnInitApproveRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.TxnPendingApprovalListRes;
import com.bidv.ibank.dvc.model.response.TxnProcessResultRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

public interface GovTxnService {

    Result<String> delete(TxnDeleteReq req);

    Result<TxnInitPushRes> initPush(TxnInitPushReq request);

    Result<TxnProcessResultRes> confirmPush(TxnConfirmReq request);

    Result<TxnRejectRes> reject(TxnRejectReq request);

    ResultList<TxnPendingApprovalListRes> listPendingApproval(TxnPendingApprovalListReq req);

    Result<ExportFileRes> export(TxnExportReq req);

    Result<ExportFileRes> print(TxnPrintDocumentReq request);

    Result<TxnInitApproveRes> initApproval(TxnApproveReq request);

    Result<TxnApprovalResultRes> confirmApproval(TxnConfirmReq request);
}
