package com.bidv.ibank.dvc.service.customsduty.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.bidv.ibank.client.common.dto.masterdata.BalanceAccountDto;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bidv.ibank.client.common.dto.masterdata.CustomerPmtSpecDto;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionRes;
import com.bidv.ibank.common.txn.util.constant.ChargeFeeOpt;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionCodeEnum;
import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.mapper.tcc.TccReqMapper;
import com.bidv.ibank.dvc.model.mapper.tcc.TccResMapper;
import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.service.common.ValidationService;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.ParamUtils;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.StatusEnum;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationReq;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationRes;
import com.bidv.ibank.integrate.entity.tcc.TccMessageReq;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocRes;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class TaxServiceImpl implements TaxService {

    private final TccService tccService;
    private final ParamGovService paramGovService;
    private final CommonService commonService;
    private final IntegrateServiceFactory integrateServiceFactory;

    private final TccReqMapper tccReqMapper;
    private final TccResMapper tccResMapper;
    private final GOVPaymentTransactionMapper govPaymentTransactionMapper;

    private final TccDmNdktRepository tccDmNdktRepository;
    private final TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;
    private final TccDmKhobacRepository tccDmKhobacRepository;
    private final TccDmChuongRepository tccDmChuongRepository;
    private final TccDmTkNsnnRepository tccDmTkNsnnRepository;
    private final TccDmSthueHqaRepository tccDmSthueHqaRepository;

    private final ValidationService validationService;

    @Override
    public ResultList<InquiryCustomsDutyRes> inquiry(InquiryCustomsDutyReq request) {
        Result<String> taxCodeCheckResult = validationService.validateTaxCode(request.getTaxCode());
        if (!taxCodeCheckResult.isSuccess()) {
            return ResultList.error(taxCodeCheckResult.getCode(), taxCodeCheckResult.getMessage());
        }

        TccMessageReq<TccInquiryDeclarationReq> messageReq = tccReqMapper.toInquiryDeclarationReq(request);
        Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccInquiryDeclarationRes>>>> response = tccService.executeTccCall(messageReq,
                input -> integrateServiceFactory.getTccService()
                        .inquiryDeclaration(input));

        if (!response.isSuccess()) {
            return tccService.getErrResultList(response);
        }

        List<InquiryCustomsDutyRes> result = response.getData().getBody()
                .getMessageBody().getMessageResponse().getItems()
                .stream()
                .map(tccResMapper::toInquiryCustomsDutyRes)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        enrichInquiryResponseWithParams(result);

        return ResultList.success(result);
    }

    @Override
    public Result<ValidateCustomsDutyRes> validate(ValidateCustomsDutyReq request) {
        // Check Tax code
        Result<String> taxCodeCheckResult = validationService.validateTaxCode(request.getTaxCode(), request.getAltTaxCode());
        if (!taxCodeCheckResult.isSuccess()) {
            return Result.error(taxCodeCheckResult.getCode(), taxCodeCheckResult.getMessage());
        }
        // Check max tax items
        if (request.getTaxItems().size() > AppConstants.NUMBER.FIVE) {
            return Result.error(ResponseCode.MAX_TAX_ITEMS_EXCEED);
        }

        // Check duplicate payments
        Result<String> duplicateCheckResult = validationService.validateDuplicatePayments(request.getTaxItems());
        if (!duplicateCheckResult.isSuccess()) {
            return Result.error(duplicateCheckResult.getCode(), duplicateCheckResult.getMessage());
        }

        // Check debitAccNo ccy is the same with txn ccy
        Result<BalanceAccountDto> accountResult = commonService.getPmtAccountDetail(request.getDebitAccNo(), TransactionCodeEnum.GOV01.code());
        if (!accountResult.isSuccess()) {
            return Result.error(accountResult.getCode(), accountResult.getMessage());
        }
        if (!accountResult.getData().getCurrCode().equals(request.getCcy())) {
            return Result.error(ResponseCode.CURRENCY_NOT_MATCH);
        }

        // Check revAuthCode is in treasury
        Result<TccDmCqthuEntity> revAuthResult = paramGovService.checkRevAuthInTreasury(request.getRevAuthCode(), request.getTreasuryCode());
        if (!revAuthResult.isSuccess()) {
            return Result.error(revAuthResult.getCode(), revAuthResult.getMessage());
        }

        // Get treasury info from mapping treasury ben bank
        MappingTreasuryBenBankCodeDto treasuryInfo = paramGovService.getMappingTreasuryBenBankCode(request.getTreasuryCode());
        GOVPaymentTransactionEntity govPaymentTransaction = govPaymentTransactionMapper.toEntity(
                request,
                treasuryInfo.getBenBankCode(),
                accountResult.getData());

        Result<ValidateTransactionRes<GOVPaymentTransactionEntity>> validateTxnResponse = commonService.validateTransaction(
                govPaymentTransaction,
                TransactionActionEnum.INIT,
                treasuryInfo.isInBidv(),
                !request.getIsInBatch(),
                true,
                input -> {
                    TccValidateDocReq tccReq = tccReqMapper.toValidateDocReq(request, input, treasuryInfo, revAuthResult.getData().getTen());
                    return validatorBeforeCallTcc(request, input, tccReq);
                });

        if (!validateTxnResponse.isSuccess()) {
            return Result.error(validateTxnResponse.getCode(), validateTxnResponse.getMessage());
        }

        GOVPaymentTransactionEntity txnResponse = validateTxnResponse.getData().getTransaction();
        return Result.success(ValidateCustomsDutyRes.builder()
                .transKey(validateTxnResponse.getData().getTransKey())
                .feeCcy(txnResponse.getFeeCcy())
                .feeOpt(txnResponse.getFeeOpt())
                .feeTotal(txnResponse.getFeeTotal())
                .amount(txnResponse.getAmount())
                .ccy(txnResponse.getCcy())
                .build());
    }

    private List<InquiryCustomsDutyRes> enrichInquiryResponseWithParams(List<InquiryCustomsDutyRes> items) {
        List<String> ecCodeList = items.stream().map(InquiryCustomsDutyRes::getEcCode).toList();
        List<String> ccCodeList = items.stream().map(InquiryCustomsDutyRes::getCcCode).toList();
        List<String> treasuryCodeList = items.stream().map(InquiryCustomsDutyRes::getTreasuryCode).toList();
        List<String> chapterCodeList = items.stream().map(InquiryCustomsDutyRes::getChapterCode).toList();
        List<String> revAccCodeList = items.stream().map(InquiryCustomsDutyRes::getRevAccCode).toList();
        List<String> taxTypeCodeList = items.stream().map(InquiryCustomsDutyRes::getTaxTypeCode).toList();

        List<TccDmNdktEntity> ecList = tccDmNdktRepository.findByMaNdktIn(ecCodeList);
        List<TccDmLoaitienhqaEntity> ccList = tccDmLoaitienhqaRepository.findByMaLthqIn(ccCodeList);
        List<TccDmKhobacEntity> treasuryList = tccDmKhobacRepository.findByShkbIn(treasuryCodeList, StatusEnum.ACTIVE.getValue());
        List<TccDmChuongEntity> chapterList = tccDmChuongRepository.findByMaChuongIn(chapterCodeList);
        List<TccDmTkNsnnEntity> revAccList = tccDmTkNsnnRepository.findByMaTkIn(revAccCodeList);
        List<TccDmSthueHqaEntity> taxTypeList = tccDmSthueHqaRepository.findByMaSthueIn(taxTypeCodeList);
        List<MappingTreasuryBenBankCodeDto> mappingTreasuryBb = paramGovService.getMappingTreasuryBb(treasuryList);

        items.forEach(item -> {
            item.setTransDesc(ParamUtils.getEcName(ecList, item.getEcCode()));
            item.setEcName(ParamUtils.getEcName(ecList, item.getEcCode()));
            item.setCcName(ParamUtils.getCcName(ccList, item.getCcCode()));
            item.setChapterName(ParamUtils.getChapterName(chapterList, item.getChapterCode()));
            item.setAdmAreaCode(ParamUtils.getAdmAreaCode(treasuryList, item.getTreasuryCode()));
            item.setAdmAreaName(ParamUtils.getAdmAreaName(treasuryList, item.getTreasuryCode()));
            item.setRevAccName(ParamUtils.getRevAccName(revAccList, item.getRevAccCode()));
            item.setTaxTypeName(ParamUtils.getTaxTypeName(taxTypeList, item.getTaxTypeCode()));
            item.setBenBankCode(ParamUtils.getBenBankCodeByTreasuryCode(mappingTreasuryBb, item.getTreasuryCode()));
            item.setBenBankName(ParamUtils.getBenBankNameByTreasuryCode(mappingTreasuryBb, item.getTreasuryCode()));
        });

        return items;
    }

    private Result<GOVPaymentTransactionEntity> validatorBeforeCallTcc(ValidateCustomsDutyReq request, GOVPaymentTransactionEntity govPaymentTransaction,
            TccValidateDocReq tccReq) {

        // Call TCC validate doc
        Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccValidateDocRes>>>> response = tccService.executeTccCall(
                tccReq, input -> integrateServiceFactory.getTccService().validateDoc(input));

        if (!response.isSuccess()) {
            return tccService.getErrResult(response);
        }

        if (!request.getIsInBatch()) {
            // Get pmt spec
            Result<CustomerPmtSpecDto> pmtSpec = commonService.getCusPmtSpec();
            if (!pmtSpec.isSuccess() || pmtSpec.getData() == null) {
                return Result.error(pmtSpec.getCode(), pmtSpec.getMessage());
            }

            govPaymentTransaction.setFeeAccNo(pmtSpec.getData().getChargeAcc());
            if (Objects.equals(pmtSpec.getData().getChargeFeeOpt(), ChargeFeeOpt.INST.code())) {
                govPaymentTransaction.setFeeAccNo(request.getDebitAccNo());
            }
            govPaymentTransaction.setFeeCcy(Optional.ofNullable(pmtSpec.getData().getCurrCode()).orElse(request.getCcy()));
            govPaymentTransaction.setFeeOpt(pmtSpec.getData().getChargeFeeOpt());
            govPaymentTransaction.setFeeFreq(pmtSpec.getData().getChargeFreq());
        }

        return Result.success(govPaymentTransaction);
    }
}
