package com.bidv.ibank.dvc.service.gov.impl;

import java.util.List;
import com.bidv.ibank.common.txn.service.CheckDuplicateTransactionService;
import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.repository.customsduty.GovPaymentTransactionCustomRepository;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.mapper.tcc.TccReqMapper;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.service.common.ValidationService;
import com.bidv.ibank.dvc.service.customsduty.KafkaService;
import com.bidv.ibank.dvc.service.gov.GovTxnAccountingService;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.dvc.util.constant.TccAccountingEnum;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingReq;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingRes;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingResultSibsInfo;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocRes;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocResult;
import com.bidv.ibank.integrate.entity.tcc.TccMessageReq;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class GovTxnAccountingServiceImpl implements GovTxnAccountingService {

    private static final String ACCOUNTING_LOCK_KEY = "accounting";
    private final TccService tccService;
    private final TccReqMapper tccReqMapper;
    private final KafkaService kafkaService;
    private final ParamGovService paramGovService;
    private final ValidationService validationService;
    private final IntegrateServiceFactory integrateServiceFactory;
    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;
    private final CheckDuplicateTransactionService checkDuplicateTransactionService;
    private final GovPaymentTransactionCustomRepository govPaymentTransactionCustomRepository;

    @Override
    public Result<String> processAccounting(String txnId) {
        GOVPaymentTransactionEntity txnEntity = govPaymentTransactionRepository.findById(txnId).orElse(null);
        boolean isRevertLimit = false;
        try {
            // Lấy giao dịch kèm thông tin khoản nộp
            if (txnEntity == null ||
                    !TransactionStatusEnum.BANK_PROCESSING.name().equals(txnEntity.getStatus()) ||
                    !(TransactionStateEnum.BANK_PROCESSING.name().equals(txnEntity.getState()) ||
                            TransactionStateEnum.UNDER_BALANCE.name().equals(txnEntity.getState()))) {
                return Result.error(ResponseCode.TXN_NOT_FOUND);
            }
            // Lock giao dịch
            if (!checkDuplicateTransactionService.lockTransaction(ACCOUNTING_LOCK_KEY, txnEntity.getCusId().toString(), txnEntity.getId())) {
                return Result.error(ResponseCode.TXN_NOT_FOUND);
            }

            govPaymentTransactionRepository.updateState(List.of(txnId), TransactionStateEnum.ACCOUNTING.name());

            // Kiểm tra số dư tài khoản
            Result<String> result = validationService.validateDebitAccount(txnEntity, true);
            if (!result.isSuccess()) {
                isRevertLimit = true;
                govPaymentTransactionCustomRepository.updateAccountingFailed(txnEntity.getId(), result.getCode(), result.getMessage());
                return Result.error(result.getCode(), result.getMessage());
            }
            if (AppConstants.UNDER_BALANCE_FLAG.YES.equals(result.getData())) {
                govPaymentTransactionCustomRepository.updateAccountingUnderBalance(txnEntity.getId());
                return Result.success(AppConstants.UNDER_BALANCE_FLAG.YES);
            }
            // Gọi Tcc tạo chứng từ
            Result<TccCreateDocResult> createDocResult = callTccCreateDoc(txnEntity);
            if (!createDocResult.isSuccess()) {
                isRevertLimit = true;
                govPaymentTransactionCustomRepository.updateAccountingFailed(txnEntity.getId(), createDocResult.getCode(), createDocResult.getMessage());
                return Result.error(createDocResult.getCode(), createDocResult.getMessage());
            }
            // Cập nhật thời gian hạch toán
            govPaymentTransactionCustomRepository.updatePmtTime(txnEntity.getId());
            // Gọi Tcc hạch toán
            Result<TccAccountingRes> accountingResult = callTccAccounting(createDocResult.getData().getIdCtuHdr());
            if (!accountingResult.isSuccess()) {
                govPaymentTransactionCustomRepository.updateAccountingUndefined(txnEntity.getId());
                return Result.error(accountingResult.getCode(), accountingResult.getMessage());
            }

            isRevertLimit = govPaymentTransactionCustomRepository.updateAccountingResult(txnEntity.getId(), createDocResult.getData(), accountingResult.getData());
            // Trả lỗi hạch toán
            TccAccountingResultSibsInfo sibsInfo = accountingResult.getData().getResult().getSibsInfo();
            if (!TccAccountingEnum.SUCCESS.getValue().equals(sibsInfo.getStatus())) {
                return Result.error(sibsInfo.getErrCode(), sibsInfo.getErrDesc());
            }
            return Result.success(AppConstants.UNDER_BALANCE_FLAG.NO);
        } finally {
            if (txnEntity != null) {
                checkDuplicateTransactionService.unlockTransaction(ACCOUNTING_LOCK_KEY, txnEntity.getCusId().toString(), txnEntity.getId());
                kafkaService.sendTxnNotification(txnEntity.getId());
                if (isRevertLimit) {
                    kafkaService.revertLimit(txnEntity.getId(), txnEntity.getTxnCode());
                }
            }
        }
    }

    private Result<TccCreateDocResult> callTccCreateDoc(GOVPaymentTransactionEntity transaction) {
        try {
            // Check revAuthCode is in treasury
            Result<TccDmCqthuEntity> revAuthResult = paramGovService.checkRevAuthInTreasury(transaction.getMaCqthu(), transaction.getShkb());
            if (!revAuthResult.isSuccess()) {
                return Result.error(revAuthResult.getCode(), revAuthResult.getMessage());
            }

            // Get treasury info from mapping treasury bb
            MappingTreasuryBenBankCodeDto treasuryInfo = paramGovService.getMappingTreasuryBenBankCode(transaction.getShkb());
            TccCreateDocReq tccReq = tccReqMapper.toCreateDocReq(transaction, treasuryInfo, revAuthResult.getData().getTen());

            Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccCreateDocRes>>>> response = tccService.executeTccCall(
                    tccReq, input -> integrateServiceFactory.getTccService().createDoc(input));
            if (!response.isSuccess()) {
                return tccService.getErrResult(response);
            }
            TccCreateDocResult tccCreateDocResult = response.getData().getBody().getMessageBody().getMessageResponse().getChungTuLst().getChungTu();
            return Result.success(tccCreateDocResult);
        } catch (Exception e) {
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    private Result<TccAccountingRes> callTccAccounting(String tccDocId) {
        TccMessageReq<TccAccountingReq> tccReq = new TccMessageReq<>(tccReqMapper.toAccountingReq(tccDocId));

        try {
            Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccAccountingRes>>>> response = tccService.executeTccCall(
                    tccReq, input -> integrateServiceFactory.getTccService().accounting(input));

            if (response.getData().getBody() == null || response.getData().getBody().getMessageBody() == null) {
                return Result.error(ResponseCode.UNDEFINED);
            }
            TccAccountingRes tccAccountingRes = response.getData().getBody().getMessageBody().getMessageResponse();
            if (tccAccountingRes == null) {
                return Result.error(ResponseCode.UNDEFINED);
            }
            return Result.success(tccAccountingRes);
        } catch (Exception e) {
            return Result.error(ResponseCode.UNDEFINED);
        }
    }
}
