package com.bidv.ibank.dvc.service.gov;

import com.bidv.ibank.dvc.model.request.TxnExportReportReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

public interface GovTxnReportService {

    ResultList<TxnReportListRes> listReport(TxnReportListReq req);

    Result<ExportFileRes> export(TxnExportReportReq req);
}
