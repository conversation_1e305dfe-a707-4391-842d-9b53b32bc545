package com.bidv.ibank.dvc.service.common;

import java.util.function.Function;

import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseRequest;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;

public interface TccService {

    <Q, S> Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<S>>> executeTccCall(Q body,
            Function<IntegrateBaseRequest<Q>, IntegrateBaseResponse<EsbTccBaseBodyMessage<S>>> tccFunction);

    <T, R> Result<R> getErrResult(Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<T>>>> response);

    <T, R> ResultList<R> getErrResultList(Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<T>>>> response);

}
