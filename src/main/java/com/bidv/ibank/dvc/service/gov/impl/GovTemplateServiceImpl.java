package com.bidv.ibank.dvc.service.gov.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTemplateMapper;
import com.bidv.ibank.dvc.model.request.TemplateDeleteReq;
import com.bidv.ibank.dvc.model.request.TemplateListReq;
import com.bidv.ibank.dvc.model.request.TemplateSaveReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTemplateItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTemplateRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.gov.GovTemplateService;
import com.bidv.ibank.dvc.specification.BaseSpecifications;
import com.bidv.ibank.dvc.specification.TemplateSpecifications;
import com.bidv.ibank.dvc.specification.TxnSpecifications;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class GovTemplateServiceImpl implements GovTemplateService {

    private final GOVPaymentTemplateItemRepository templateItemRepository;
    private final GOVPaymentTemplateMapper govPaymentTemplateMapper;
    private final GOVPaymentTemplateRepository templateRepository;
    private final GOVPaymentTransactionRepository txnRepository;

    @Override
    @Transactional
    public Result<String> save(TemplateSaveReq request) {
        GOVPaymentTransactionEntity govTxnEntity = txnRepository.findOne(
                Specification.where(BaseSpecifications.<GOVPaymentTransactionEntity>idEq(request.getTxnId()))
                        .and(TxnSpecifications.statusEq(TransactionStatusEnum.PENDING_APPROVAL.name()))
                        .and(TxnSpecifications.createdByEq(AuthenticationUtils.getCurrentUser().getUser().getUsername())))
                .orElse(null);
        if (govTxnEntity == null) {
            return Result.error(ResponseCode.TXN_NOT_FOUND);
        }

        List<GOVPaymentTemplateEntity> templateEntityList = templateRepository.findAll(
                Specification.where(TemplateSpecifications.checkExistingTemplate(
                        request.getTemplateName(),
                        request.getIsPublic(),
                        AuthenticationUtils.getCurrentUser().getUser().getCif(),
                        AuthenticationUtils.getCurrentUser().getUser().getUsername())));
        if (!templateEntityList.isEmpty()) {
            return Result.error(ResponseCode.TEMPLATE_EXISTS);
        }

        GOVPaymentTemplateEntity govPaymentTemplateEntity = templateRepository.saveAndFlush(govPaymentTemplateMapper.toEntity(govTxnEntity, request));
        List<GOVPaymentTemplateItemEntity> templateItemList = new ArrayList<>();
        govTxnEntity.getGovPaymentItemList().forEach(item -> {
            GOVPaymentTemplateItemEntity templateItem = govPaymentTemplateMapper.toItemEntity(item, govPaymentTemplateEntity.getId());
            templateItemList.add(templateItem);
        });
        templateItemRepository.saveAll(templateItemList);
        return Result.success(govPaymentTemplateEntity.getId());
    }

    @Override
    public ResultList<TemplateListRes> list(TemplateListReq req) {
        Pageable pageable = QueryUtils.buildPageRequest(req, Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
        UserInfo userInfo = AuthenticationUtils.getCurrentUser().getUser();

        Page<GOVPaymentTemplateEntity> txnTemplateList = templateRepository.findAll(
                TemplateSpecifications.createTxnTemplateListSpec(req)
                        .and(TemplateSpecifications.statusEq(AppConstants.TEMPLATE_STATUS.ACTIVE))
                        .and(BaseSpecifications.<GOVPaymentTemplateEntity>createdByEq(userInfo.getUsername())
                                .or(TemplateSpecifications.isPublicEq(true)))
                        .and(TemplateSpecifications.cifNoEq(userInfo.getCif())),
                pageable);

        return ResultList.success(txnTemplateList.stream().map(govPaymentTemplateMapper::toListDto).toList(), txnTemplateList
                .getTotalElements());
    }

    @Override
    @Transactional
    public Result<String> delete(TemplateDeleteReq req) {
        UserInfo userInfo = AuthenticationUtils.getCurrentUser().getUser();
        List<GOVPaymentTemplateEntity> govTemplateList = new ArrayList<>();

        if (req.getTemplateIds().size() == 1) {
            govTemplateList = templateRepository.findById(req.getTemplateIds().get(0)).stream().toList();

            if (govTemplateList.isEmpty()) {
                return Result.error(ResponseCode.NOT_FOUND_01);
            }
            GOVPaymentTemplateEntity govTemplateEntity = govTemplateList.get(0);
            if (!Objects.equals(govTemplateEntity.getStatus(), AppConstants.TEMPLATE_STATUS.ACTIVE)) {
                return Result.error(ResponseCode.NOT_FOUND_01);
            }
            if ((!Objects.equals(govTemplateEntity.getCreatedBy(), AuthenticationUtils.getCurrentUser().getUsername())
                    && !govTemplateEntity.getIsPublic()) || !userInfo.getCif().equals(govTemplateList.get(0).getCifNo())) {
                return Result.error(ResponseCode.TEMP_INFO_01);
            }
        } else {
            govTemplateList = templateRepository.findAll(
                    Specification.where(BaseSpecifications.<GOVPaymentTemplateEntity>idIn(req.getTemplateIds()))
                            .and(TemplateSpecifications.statusEq(AppConstants.TEMPLATE_STATUS.ACTIVE))
                            .and(BaseSpecifications.<GOVPaymentTemplateEntity>createdByEq(userInfo.getUsername())
                                    .or(TemplateSpecifications.isPublicEq(true)))
                            .and(TemplateSpecifications.cifNoEq(userInfo.getCif())));

            if (govTemplateList.size() != req.getTemplateIds().size()) {
                return Result.error(ResponseCode.TEMP_INFO_01);
            }
        }

        try {
            List<String> templateIds = govTemplateList.stream().map(GOVPaymentTemplateEntity::getId).toList();
            templateRepository.updateStatus(templateIds, AppConstants.TEMPLATE_STATUS.INACTIVE, userInfo.getCif());
        } catch (Exception e) {
            log.error("Error saving templates: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }

        return Result.success(null);
    }
}
