package com.bidv.ibank.dvc.service.customsduty;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

public interface TaxService {

    ResultList<InquiryCustomsDutyRes> inquiry(InquiryCustomsDutyReq request);

    Result<ValidateCustomsDutyRes> validate(ValidateCustomsDutyReq request);
}
