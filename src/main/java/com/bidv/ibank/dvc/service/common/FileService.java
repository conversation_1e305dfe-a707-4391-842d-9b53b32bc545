package com.bidv.ibank.dvc.service.common;

import java.io.ByteArrayOutputStream;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.bidv.ibank.dvc.model.dto.ValidateFileDto;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.util.excel.Importable;

public interface FileService {

    Result<FileInfo> uploadToS3(ByteArrayOutputStream byteArrayOutputStream, String fileName, boolean isGenerateUrl);

    Result<FileInfo> uploadToS3(String filePath, String fileName, String fileType, boolean isGenerateUrl);

    Result<FileInfo> uploadToS3(MultipartFile file, boolean isGenerateUrl);

    Result<byte[]> downloadFile(String fileKey);

    <T extends Importable> Result<List<T>> validateAndReadExcelFile(MultipartFile file, ValidateFileDto<T> validateDto);
}
