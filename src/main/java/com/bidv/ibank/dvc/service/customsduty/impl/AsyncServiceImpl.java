package com.bidv.ibank.dvc.service.customsduty.impl;

import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.client.common.dto.masterdata.ParProcessTransactionDto;
import com.bidv.ibank.client.common.dto.masterdata.ParTransValueDto;
import com.bidv.ibank.client.common.dto.masterdata.ParTransferTransDto;
import com.bidv.ibank.client.common.dto.transLimit.TransInfoDto;
import com.bidv.ibank.client.common.dto.transLimit.ValidateTxnResponse;
import com.bidv.ibank.client.common.util.AppConstants.LIMIT_TYPE;
import com.bidv.ibank.client.common.util.AppConstants.TransProcessMethodResponse;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionFlowDto;
import com.bidv.ibank.common.txn.util.constant.TransactionCodeEnum;
import com.bidv.ibank.common.txn.util.constant.ValidateTransFlowTypeEnum;
import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchValidationStatusDto;
import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.dto.TccDmChuongCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmCqthuCodeNameKhobacEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmDbhcCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmKhobacCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmLhxnkCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmLoaitienhqaCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmNdktCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmSthueHqaCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmTkNsnnStkEntityDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.mapper.tcc.TccReqMapper;
import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLhxnkRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.service.customsduty.AsyncService;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.ArrUtils;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.dvc.util.constant.StatusEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo;
import com.bidv.ibank.framework.domain.response.ResponseError;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.remote.config.database.context.ProductContext;
import com.bidv.ibank.framework.remote.config.database.entity.ProductMapping;
import com.bidv.ibank.framework.util.ObjectUtils;
import com.bidv.ibank.framework.util.exception.ValidateException;
import com.bidv.ibank.integrate.entity.account.CoreAccount;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessageHeader;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocRes;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import com.bidv.ibank.util.excel.ExcelImporter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncServiceImpl implements AsyncService {

    private final TccReqMapper tccReqMapper;
    private final GOVPaymentBatchMapper govPaymentBatchMapper;

    private final CommonService commonService;
    private final ParamGovService paramGovService;
    private final TccService tccService;
    private final IntegrateServiceFactory integrateServiceFactory;
    private final FileService fileService;

    private final TccDmNdktRepository tccDmNdktRepository;
    private final TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;
    private final TccDmKhobacRepository tccDmKhobacRepository;
    private final TccDmChuongRepository tccDmChuongRepository;
    private final TccDmTkNsnnRepository tccDmTkNsnnRepository;
    private final TccDmSthueHqaRepository tccDmSthueHqaRepository;
    private final TccDmCqthuRepository tccDmCqthuRepository;
    private final TccDmDbhcRepository tccDmDbhcRepository;
    private final TccDmLhxnkRepository tccDmLhxnkRepository;

    private final GOVPaymentBatchRepository govPaymentBatchRepository;
    private final GOVPaymentBatchItemRepository govPaymentBatchItemRepository;
    private final TaxService taxService;

    @Override
    public void handleBatchUpload(String batchId) {
        String errCode = null;
        GOVPaymentBatchEntity batchEntity = govPaymentBatchRepository.findById(batchId)
                .orElse(null);
        if (batchEntity == null) {
            log.error("Batch not found: {}", batchId);
            return;
        }

        InputStream inputStream = null;
        try {
            // Download file from S3
            Result<byte[]> file = fileService.downloadFile(batchEntity.getFileKey());
            if (!file.isSuccess()) {
                errCode = file.getCode();
                return;
            }

            // Read file
            inputStream = new ByteArrayInputStream(file.getData());
            if (batchEntity.getBatchType() == BatchTypeEnum.PAYMENT) {
                List<BatchImportItemDto> batchItems = ExcelImporter.readExcel(inputStream, BatchImportItemDto.class,
                        AppConstants.BATCH_DOWNLOAD_RESULT_START_ROW);

                errCode = handlePaymentBatch(batchItems, batchEntity);
            } else if (batchEntity.getBatchType() == BatchTypeEnum.INQUIRY) {
                List<BatchTaxImportItemDto> batchItems = ExcelImporter.readExcel(inputStream, BatchTaxImportItemDto.class,
                        AppConstants.BATCH_TAX_FILE_START_ROW);
                batchItems = ArrUtils.distinctList(
                        batchItems,
                        BatchTaxImportItemDto::getDeclarationNo,
                        BatchTaxImportItemDto::getDeclarationYear);
                errCode = handleInquiryBatch(batchItems, batchEntity);
            }
        } catch (Exception e) {
            log.error("Error handle batch upload", e);
            errCode = ResponseCode.TIMEOUT_01.code();
        } finally {
            ObjectUtils.close(inputStream);
            if (errCode != null) {
                govPaymentBatchRepository.updateStatusByBatchNo(BatchStatusEnum.ERROR, errCode, batchEntity.getBatchNo(), batchEntity.getCusId());
            }
        }
    }

    private String handlePaymentBatch(List<BatchImportItemDto> batchItems, GOVPaymentBatchEntity batchEntity) {
        Result<String> validateResult = validateBatchItems(batchItems);
        if (!validateResult.isSuccess()) {
            return validateResult.getCode();
        }
        saveToDb(batchItems, batchEntity);
        return null;
    }

    private String handleInquiryBatch(List<BatchTaxImportItemDto> batchItems, GOVPaymentBatchEntity batchEntity) {
        Result<CustomerDto> customerResult = commonService.getCustomerInfo(AuthenticationUtils.getCurrentUser().getUser().getCif());
        if (!customerResult.isSuccess()) {
            return customerResult.getCode();
        }
        CustomerDto customerDto = customerResult.getData();
        List<GOVPaymentBatchItemEntity> govBatchItemEntityList = new ArrayList<>();
        for (BatchTaxImportItemDto item : batchItems) {
            item.validate();
            if (item.isValidRow()) {
                ResultList<InquiryCustomsDutyRes> result = taxService.inquiry(InquiryCustomsDutyReq.builder()
                        .taxCode(customerDto.getTaxCode())
                        .declarationNo(item.getDeclarationNo())
                        .declarationYear(item.getDeclarationYear())
                        .build());
                if (!result.isSuccess()) {
                    item.setTccErrCode(result.getCode());
                    item.setTccErrMsg(result.getMessage());
                } else {
                    List<InquiryCustomsDutyRes> inquiryResultList = result.getData().getItems();
                    inquiryResultList.forEach(inquiryResult -> {
                        GOVPaymentBatchItemEntity entity = govPaymentBatchMapper.toBatchTaxItemEntity(item, batchEntity.getId(), customerDto, inquiryResult);
                        govBatchItemEntityList.add(entity);
                    });
                    continue;
                }
            }
            GOVPaymentBatchItemEntity entity = govPaymentBatchMapper.toBatchTaxItemEntity(item, batchEntity.getId(), customerDto, null);
            govBatchItemEntityList.add(entity);
        }

        govPaymentBatchItemRepository.saveAll(getDistinctItemInquiryBatch(govBatchItemEntityList));
        govPaymentBatchRepository.updateStatusByBatchNo(BatchStatusEnum.CHECKED, null, batchEntity.getBatchNo(), batchEntity.getCusId());
        return null;
    }

    private Result<String> validateBatchItems(List<BatchImportItemDto> batchItems) {
        try {
            Result<CustomerDto> customerInfo = commonService.getCustomerInfo(AuthenticationUtils.getCurrentUser().getUser().getCif());
            if (!customerInfo.isSuccess()) {
                return Result.error(customerInfo.getCode(), customerInfo.getMessage());
            }
            String taxCode = customerInfo.getData().getTaxCode();

            // Validate common params for each item
            batchItems.forEach(BatchImportItemDto::validate);

            // Check phân quyền tài khoản trích nợ
            validateDebitAccNo(batchItems);
            // Check mã số thuế
            validateTaxCode(batchItems, taxCode);
            // Check giá trị các mã danh mục
            validateParamCode(batchItems);
            // Check tham số giá trị giao dịch theo sản phẩm
            validateTransactionValue(batchItems);
            // Check hạn mức khách hàng từng giao dịch; hạn mức tài khoản từng giao dịch
            validateTxn(batchItems);
            // Check tài khoản trích nợ (phục vụ cho bước tiếp theo)
            validateCoreAccount(batchItems);
            // Check xử lý giao dịch thủ công/Ngừng giao dịch
            validateTransactionFlow(batchItems, customerInfo.getData());
            // Validate TCC
            validateTcc(batchItems);
            // Validate duplicate row
            validateDuplicateRow(batchItems);

            return Result.success(null);
        } catch (ValidateException e) {
            log.error("Error validate batch items", e);
            return Result.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("Error validate batch items", e);
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    private void validateDebitAccNo(List<BatchImportItemDto> batchItems) {
        List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());

        for (BatchImportItemDto batchItem : batchItems) {
            BatchValidationStatusDto validationStatus = batchItem.getFieldValidationStatusAfterValidate();
            if (!validationStatus.isDebitAccNoValid())
                continue;

            Optional<AuthAccountDto> account = accounts.stream().filter(e -> e.getAccountNo().equals(batchItem.getDebitAccNo())).findFirst();
            if (!account.isPresent()) {
                batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, ResponseCode.DEBIT_ACCNO_INVALID.code());
            } else {
                // Kiểm tra loại tiền tệ giữa TK trích nợ và Loại tiền tệ của từng giao dịch
                if (validationStatus.isCcyValid() && !account.get().getCurrCode().equals(batchItem.getCcy())) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.CCY, ResponseCode.DEBIT_ACCNO_CCY_MISMATCH.code());
                }
            }
        }
    }

    private void validateTaxCode(List<BatchImportItemDto> batchItems, String taxCode) {
        for (BatchImportItemDto batchItem : batchItems) {
            BatchValidationStatusDto validationStatus = batchItem.getFieldValidationStatusAfterValidate();
            if (!validationStatus.isTaxCodeValid())
                continue;

            if (StringUtils.isBlank(batchItem.getAltTaxCode())) {
                if (!taxCode.equals(batchItem.getTaxCode())) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.TAX_CODE, ResponseCode.TAX_CODE_MISMATCH.code());
                }
            } else {
                if (!taxCode.equals(batchItem.getAltTaxCode())) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.ALT_TAX_CODE, ResponseCode.ALT_TAX_CODE_MISMATCH.code());
                }
                if (Objects.equals(batchItem.getTaxCode(), batchItem.getAltTaxCode())) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.TAX_CODE, ResponseCode.TAX_CODE_DUPLICATE.code());
                }
            }
        }
    }

    private void validateParamCode(List<BatchImportItemDto> batchItems) {
        Map<String, Set<String>> paramCodesMap = collectParamCodes(batchItems);

        List<TccDmSthueHqaCodeNameEntityDto> taxTypeList = tccDmSthueHqaRepository.findTaxTypeByCodes(paramCodesMap.get("taxType"));
        List<TccDmLoaitienhqaCodeNameEntityDto> ccList = tccDmLoaitienhqaRepository.findLoaitienhqaByCodes(paramCodesMap.get("cc"));
        List<TccDmLhxnkCodeNameEntityDto> eiTypeList = tccDmLhxnkRepository.findLhxnkByCodes(paramCodesMap.get("eiType"));
        List<TccDmNdktCodeNameEntityDto> ecList = tccDmNdktRepository.findNdktByCodes(paramCodesMap.get("ec"));
        List<TccDmChuongCodeNameEntityDto> chapterList = tccDmChuongRepository.findChuongByCodes(paramCodesMap.get("chapter"));
        List<TccDmDbhcCodeNameEntityDto> admAreaList = tccDmDbhcRepository.findDbhcByCodes(paramCodesMap.get("admArea"));
        List<TccDmCqthuCodeNameKhobacEntityDto> revAuthList = tccDmCqthuRepository.findCqthuByCodes(StatusEnum.ACTIVE.getValue(), paramCodesMap.get("revAuth"));
        List<TccDmTkNsnnStkEntityDto> revAccList = tccDmTkNsnnRepository.findTkNsnnByCodes(paramCodesMap.get("revAcc"));
        List<TccDmKhobacCodeNameEntityDto> treasuryList = tccDmKhobacRepository.findTreasuriesByCodes(StatusEnum.ACTIVE.getValue(), paramCodesMap.get(
                "treasury"));
        List<MappingTreasuryBenBankCodeDto> mappingTreasuryBenbank = paramGovService.getMappingTreasuryBbByCode(new ArrayList<>(paramCodesMap.get("treasury")));

        // Validate param code
        batchItems.forEach(batchItem -> {
            BatchValidationStatusDto validationStatus = batchItem.getFieldValidationStatusAfterValidate();

            if (validationStatus.isTreasuryCodeValid()) {
                Optional<TccDmKhobacCodeNameEntityDto> treasury = treasuryList.stream()
                        .filter(e -> e.getShkb().equals(batchItem.getTreasuryCode()))
                        .findFirst();

                if (!treasury.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.TREASURY_CODE, ResponseCode.TREASURY_CODE_INVALID_FORMAT.code());
                } else {
                    MappingTreasuryBenBankCodeDto treasuryInfo = mappingTreasuryBenbank.stream()
                            .filter(mapping -> mapping.getTreasuryCode().equals(batchItem.getTreasuryCode()))
                            .findFirst()
                            .orElse(new MappingTreasuryBenBankCodeDto());
                    batchItem.setTreasuryInfo(treasuryInfo);
                }
            }
            if (validationStatus.isRevAccCodeValid()) {
                Optional<TccDmTkNsnnStkEntityDto> revAcc = revAccList.stream()
                        .filter(e -> e.getTk().equals(batchItem.getRevAccCode()))
                        .findFirst();

                if (!revAcc.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.REV_ACC_CODE, ResponseCode.REV_ACC_CODE_INVALID_FORMAT.code());
                }
            }
            if (validationStatus.isRevAuthCodeValid()) {
                List<TccDmCqthuCodeNameKhobacEntityDto> revAuth = revAuthList.stream()
                        .filter(e -> e.getMaCqthu().equals(batchItem.getRevAuthCode()))
                        .toList();

                if (revAuth.isEmpty()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.REV_AUTH_CODE, ResponseCode.REV_AUTH_CODE_INVALID_FORMAT.code());
                } else {
                    Optional<TccDmCqthuCodeNameKhobacEntityDto> revAuthOptional = revAuth.stream()
                            .filter(e -> Objects.equals(e.getMaKhobac(), batchItem.getTreasuryCode()))
                            .findFirst();
                    if (!revAuthOptional.isPresent()) {
                        batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.REV_AUTH_CODE, ResponseCode.REV_AUTH_NOT_IN_TREASURY.code());
                    } else {
                        batchItem.setRevAuthName(revAuthOptional.get().getTenCqthu());
                    }
                }
            }
            if (validationStatus.isAdmAreaCodeValid()) {
                Optional<TccDmDbhcCodeNameEntityDto> admArea = admAreaList.stream()
                        .filter(e -> e.getMaDbhc().equals(batchItem.getAdmAreaCode()))
                        .findFirst();

                if (!admArea.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.ADM_AREA_CODE, ResponseCode.ADM_AREA_CODE_INVALID_FORMAT.code());
                }
            }
            if (validationStatus.isChapterCodeValid()) {
                Optional<TccDmChuongCodeNameEntityDto> chapter = chapterList.stream()
                        .filter(e -> e.getMaChuong().equals(batchItem.getChapterCode()))
                        .findFirst();

                if (!chapter.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.CHAPTER_CODE, ResponseCode.CHAPTER_CODE_INVALID_FORMAT.code());
                }
            }
            if (validationStatus.isEcCodeValid()) {
                Optional<TccDmNdktCodeNameEntityDto> ec = ecList.stream()
                        .filter(e -> e.getMaNdkt().equals(batchItem.getEcCode()))
                        .findFirst();

                if (!ec.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.EC_CODE, ResponseCode.EC_CODE_INVALID_FORMAT.code());
                }
            }
            if (validationStatus.isTaxTypeCodeValid()) {
                Optional<TccDmSthueHqaCodeNameEntityDto> taxType = taxTypeList.stream()
                        .filter(e -> e.getMaSthueHqa().equals(batchItem.getTaxTypeCode()))
                        .findFirst();

                if (!taxType.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.TAX_TYPE_CODE, ResponseCode.TAX_TYPE_CODE_INVALID_FORMAT.code());
                }
            }
            if (validationStatus.isCcCodeValid()) {
                Optional<TccDmLoaitienhqaCodeNameEntityDto> cc = ccList.stream()
                        .filter(e -> e.getMaLoaitienhqa().equals(batchItem.getCcCode()))
                        .findFirst();

                if (!cc.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.CC_CODE, ResponseCode.CC_CODE_INVALID_FORMAT.code());
                }
            }
            if (validationStatus.isEiTypeCodeValid()) {
                Optional<TccDmLhxnkCodeNameEntityDto> eiType = eiTypeList.stream()
                        .filter(e -> e.getMaLhxnk().equals(batchItem.getEiTypeCode()))
                        .findFirst();

                if (!eiType.isPresent()) {
                    batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.EI_TYPE_CODE, ResponseCode.EI_TYPE_CODE_INVALID_FORMAT.code());
                }
            }
        });
    }

    private void validateTransactionValue(List<BatchImportItemDto> batchItems) {
        batchItems = filterValidItems(batchItems);
        if (batchItems.isEmpty())
            return;

        batchItems.stream().collect(Collectors.groupingBy(x -> x.getDebitAccNo() + "|" + x.getTreasuryCode())).forEach((key, groupItems) -> {
            String ccy = groupItems.get(0).getCcy();
            String debitAccNo = groupItems.get(0).getDebitAccNo();
            String txnCode = groupItems.get(0).getTreasuryInfo().getTxnCode();

            Result<ParTransferTransDto> parTransData = commonService.getParTransData(CoreAccTypeEnum.DDA.name(), ccy, debitAccNo, txnCode);
            if (!parTransData.isSuccess()) {
                groupItems.forEach(item -> {
                    item.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, parTransData.getCode());
                });
                return;
            }

            ParTransValueDto parTransValue = parTransData.getData().getParTransValue();
            if (parTransValue == null)
                return;

            BigDecimal minValue = parTransValue.getMinVal();
            BigDecimal maxValue = parTransValue.getMaxVal();
            groupItems.forEach(item -> {
                BigDecimal transactionValue = new BigDecimal(item.getAmount());
                if (transactionValue.compareTo(minValue) < 0) {
                    item.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, ResponseCode.AMOUNT_BELOW_MIN_BALANCE.code());
                }
                if (transactionValue.compareTo(maxValue) > 0) {
                    item.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, ResponseCode.AMOUNT_EXCEED_MAX_BALANCE.code());
                }
            });
        });
    }

    private void validateTxn(List<BatchImportItemDto> batchItems) {
        batchItems = filterValidItems(batchItems);
        if (batchItems.isEmpty())
            return;

        Optional<ProductMapping> subProducts = ProductContext.findBy(ProductContext.ProductMappingCriteria.MAPPING_TYPE_PROD_TRANSCODE,
                TransactionCodeEnum.GOV01.code());
        if (subProducts.isEmpty()) {
            throw new ValidateException(ResponseCode.PROD_TRANSCODE_NOTFOUND);
        }

        // Filter valid items and prepare transaction info
        List<TransInfoDto> transInfos = batchItems.stream()
                .map(item -> {
                    item.setProductMapping(subProducts.get());

                    TransInfoDto transInfo = new TransInfoDto();
                    transInfo.setTxnId(String.valueOf(item.getRownum()));
                    transInfo.setTxnCode(TransactionCodeEnum.GOV01.code());
                    transInfo.setDebitAccount(item.getDebitAccNo());
                    transInfo.setCurrCode(item.getCcy());
                    transInfo.setDebitAmount(new BigDecimal(item.getAmount()));
                    transInfo.setLimitType(List.of(LIMIT_TYPE.LIMIT_ACC, LIMIT_TYPE.LIMIT_CIF));
                    transInfo.setProdCode(subProducts.get().getProductCode());
                    transInfo.setSubProdCode(subProducts.get().getSubProductCode());
                    return transInfo;
                })
                .collect(Collectors.toList());

        // Validate transactions
        UserInfo user = AuthenticationUtils.getCurrentUser().getUser();
        Result<ValidateTxnResponse> response = commonService.validateTxn(Long.valueOf(user.getUserId()), Long.valueOf(user.getCusId()), transInfos);
        if (!response.isSuccess()) {
            throw new ValidateException(response.getCode(), response.getMessage());
        }

        // Handle specific validation failures
        if (MapUtils.isNotEmpty(response.getData().getFailureResponse())) {
            batchItems.stream().forEach(item -> {
                ResponseError responseCode = response.getData().getFailureResponse().getOrDefault(String.valueOf(item.getRownum()), null);
                if (responseCode != null) {
                    item.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, responseCode.getErrorCode());
                }
            });
        }
    }

    private void validateCoreAccount(List<BatchImportItemDto> batchItems) {
        batchItems = filterValidItems(batchItems);
        if (batchItems.isEmpty())
            return;

        String cifNo = AuthenticationUtils.getCurrentUser().getUser().getCif();
        batchItems.stream().collect(Collectors.groupingBy(x -> x.getDebitAccNo())).forEach((debitAccNo, groupItems) -> {
            Result<CoreAccount> coreAccountResult = commonService.validateCoreAccount(debitAccNo, cifNo);
            if (!coreAccountResult.isSuccess()) {
                groupItems.forEach(item -> {
                    item.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, coreAccountResult.getCode());
                });
                return;
            }

            groupItems.forEach(item -> {
                item.setAccountInfo(coreAccountResult.getData());
            });
        });
    }

    private void validateTransactionFlow(List<BatchImportItemDto> batchItems, CustomerDto customerInfo) {
        batchItems = filterValidItems(batchItems);
        if (batchItems.isEmpty())
            return;

        ResultList<ParProcessTransactionDto> parProcessTransactionDtos = commonService.getProcessTransaction();
        if (!parProcessTransactionDtos.isSuccess()) {
            throw new ValidateException(parProcessTransactionDtos.getCode(), parProcessTransactionDtos.getMessage());
        }

        batchItems.forEach(batchItem -> {
            Result<ValidateTransactionFlowDto> validateFlowRes = commonService.validateTransactionFlowMain(
                    new BigDecimal(batchItem.getAmount()),
                    batchItem.getCcy(),
                    customerInfo,
                    batchItem.getAccountInfo(),
                    parProcessTransactionDtos.getData().getItems(),
                    ValidateTransFlowTypeEnum.DEBIT);

            if (!validateFlowRes.isSuccess()) {
                batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, validateFlowRes.getCode());
                return;
            }

            String processMethod = validateFlowRes.getData().getProcessMethod();
            if (StringUtils.equalsIgnoreCase(processMethod, TransProcessMethodResponse.USING_OVERDRAFT)
                    || StringUtils.equalsIgnoreCase(processMethod, TransProcessMethodResponse.AUTO)) {
                batchItem.setProcessFlow(processMethod);
            } else {
                batchItem.addFieldError(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, validateFlowRes.getData().getErrorCode());
            }
        });
    }

    private void validateTcc(List<BatchImportItemDto> batchItems) {
        batchItems.stream()
                .filter(BatchImportItemDto::isValidRow)
                .forEach(item -> {
                    TccValidateDocReq tccValidateDocReq = tccReqMapper.toValidateDocReq(item);
                    Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccValidateDocRes>>>> response = tccService.executeTccCall(
                            tccValidateDocReq, input -> integrateServiceFactory.getTccService().validateDoc(input));

                    if (!response.isSuccess()) {
                        if (response.getData() != null && response.getData().getBody() != null && response.getData().getBody().getMessageHeader() != null) {
                            EsbTccBaseBodyMessageHeader messageHeader = response.getData().getBody().getMessageHeader();
                            item.setTccErrCode(messageHeader.getErrCode());
                            item.setTccErrMsg(messageHeader.getErrDesc());
                        } else {
                            item.setTccErrCode(response.getCode());
                            item.setTccErrMsg(response.getMessage());
                        }
                    }
                });
    }

    private void validateDuplicateRow(List<BatchImportItemDto> batchItems) {
        Map<String, BatchImportItemDto> uniqueItems = new HashMap<>();

        batchItems.stream()
                .filter(BatchImportItemDto::isValidRow)
                .forEach(item -> {
                    // Create composite key from all ImportConfig fields
                    String uniqueKey = String.format("%s|%s|%s|%s|%s|%s|%s|%s|%s|%s",
                            item.getTaxCode(),
                            item.getDeclarationNo(),
                            item.getTreasuryCode(),
                            item.getRevAccCode(),
                            item.getRevAuthCode(),
                            item.getChapterCode(),
                            item.getEcCode(),
                            item.getTaxTypeCode(),
                            item.getCcCode(),
                            item.getEiTypeCode());

                    BatchImportItemDto existingItem = uniqueItems.get(uniqueKey);
                    if (existingItem != null) {
                        // Mark both current and existing items as duplicates
                        item.addFieldError(AppConstants.BATCH_FIELD_CODE.EXTRA_FIELD_CODE, ResponseCode.BATCH_DUPLICATE_RECORD.code());
                        existingItem.addFieldError(AppConstants.BATCH_FIELD_CODE.EXTRA_FIELD_CODE, ResponseCode.BATCH_DUPLICATE_RECORD.code());
                    } else {
                        uniqueItems.put(uniqueKey, item);
                    }
                });
    }

    private Map<String, Set<String>> collectParamCodes(List<BatchImportItemDto> batchItems) {
        Map<String, Set<String>> paramCodesMap = new HashMap<>();

        paramCodesMap.put("treasury", new HashSet<>());
        paramCodesMap.put("revAcc", new HashSet<>());
        paramCodesMap.put("revAuth", new HashSet<>());
        paramCodesMap.put("admArea", new HashSet<>());
        paramCodesMap.put("chapter", new HashSet<>());
        paramCodesMap.put("ec", new HashSet<>());
        paramCodesMap.put("taxType", new HashSet<>());
        paramCodesMap.put("cc", new HashSet<>());
        paramCodesMap.put("eiType", new HashSet<>());

        batchItems.forEach(batchItem -> {
            BatchValidationStatusDto validationStatus = batchItem.getFieldValidationStatusAfterValidate();
            if (validationStatus.isTreasuryCodeValid())
                paramCodesMap.get("treasury").add(batchItem.getTreasuryCode());
            if (validationStatus.isRevAccCodeValid())
                paramCodesMap.get("revAcc").add(batchItem.getRevAccCode());
            if (validationStatus.isRevAuthCodeValid())
                paramCodesMap.get("revAuth").add(batchItem.getRevAuthCode());
            if (validationStatus.isAdmAreaCodeValid())
                paramCodesMap.get("admArea").add(batchItem.getAdmAreaCode());
            if (validationStatus.isChapterCodeValid())
                paramCodesMap.get("chapter").add(batchItem.getChapterCode());
            if (validationStatus.isEcCodeValid())
                paramCodesMap.get("ec").add(batchItem.getEcCode());
            if (validationStatus.isTaxTypeCodeValid())
                paramCodesMap.get("taxType").add(batchItem.getTaxTypeCode());
            if (validationStatus.isCcCodeValid())
                paramCodesMap.get("cc").add(batchItem.getCcCode());
            if (validationStatus.isEiTypeCodeValid())
                paramCodesMap.get("eiType").add(batchItem.getEiTypeCode());
        });

        return paramCodesMap;
    }

    private void saveToDb(List<BatchImportItemDto> batchItems, GOVPaymentBatchEntity batchEntity) {
        List<GOVPaymentBatchItemEntity> govPaymentBatchItemEntityList = new ArrayList<>();
        for (BatchImportItemDto batchItem : batchItems) {
            GOVPaymentBatchItemEntity govPaymentBatchItemEntity = govPaymentBatchMapper.toItemEntity(batchItem, batchEntity.getId());
            govPaymentBatchItemEntityList.add(govPaymentBatchItemEntity);
        }
        govPaymentBatchItemRepository.saveAll(govPaymentBatchItemEntityList);
        govPaymentBatchRepository.updateStatusByBatchNo(BatchStatusEnum.CHECKED, null, batchEntity.getBatchNo(), batchEntity.getCusId());
    }

    private List<BatchImportItemDto> filterValidItems(List<BatchImportItemDto> batchItems) {
        return batchItems.stream()
                .filter(BatchImportItemDto::isValidDebitAccNoAmountCcyTreasuryRow)
                .collect(Collectors.toList());
    }

    private List<GOVPaymentBatchItemEntity> getDistinctItemInquiryBatch(List<GOVPaymentBatchItemEntity> govBatchItemEntityList) {
        List<GOVPaymentBatchItemEntity> validItemList = govBatchItemEntityList.stream()
                .filter(e -> Objects.equals(e.getStatus(), BatchItemStatusEnum.VALID))
                .toList();
        List<GOVPaymentBatchItemEntity> inValidItemList = govBatchItemEntityList.stream()
                .filter(e -> Objects.equals(e.getStatus(), BatchItemStatusEnum.INVALID))
                .toList();
        List<GOVPaymentBatchItemEntity> govBatchItemEntityDistinctList = ArrUtils.distinctList(validItemList,
                GOVPaymentBatchItemEntity::getAmount,
                GOVPaymentBatchItemEntity::getCcy,
                GOVPaymentBatchItemEntity::getDeclarationDate,
                GOVPaymentBatchItemEntity::getDeclarationNo,
                GOVPaymentBatchItemEntity::getMaChuong,
                GOVPaymentBatchItemEntity::getMaCqthu,
                GOVPaymentBatchItemEntity::getMaDbhc,
                GOVPaymentBatchItemEntity::getMaLh,
                GOVPaymentBatchItemEntity::getMaLthq,
                GOVPaymentBatchItemEntity::getMaNdkt,
                GOVPaymentBatchItemEntity::getMaSthue,
                GOVPaymentBatchItemEntity::getMaTk,
                GOVPaymentBatchItemEntity::getPayerName,
                GOVPaymentBatchItemEntity::getPayerAddr,
                GOVPaymentBatchItemEntity::getShkb,
                GOVPaymentBatchItemEntity::getTaxCode,
                GOVPaymentBatchItemEntity::getTransDesc,
                GOVPaymentBatchItemEntity::getPayerType);

        return ListUtils.union(govBatchItemEntityDistinctList, inValidItemList);
    }
}
