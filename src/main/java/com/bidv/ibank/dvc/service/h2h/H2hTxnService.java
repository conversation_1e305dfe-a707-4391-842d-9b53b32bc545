package com.bidv.ibank.dvc.service.h2h;

import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnListRes;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnDetailRes;
import com.bidv.ibank.framework.domain.response.Result;

public interface H2hTxnService {

    ResultList<H2hTxnListRes> list(TxnReportListReq req);

    Result<H2hTxnDetailRes> detail(TxnDetailReq req);

    Result<ExportFileRes> print(TxnPrintDocumentReq req);
}
