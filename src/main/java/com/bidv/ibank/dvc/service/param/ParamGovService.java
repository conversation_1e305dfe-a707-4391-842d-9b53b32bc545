package com.bidv.ibank.dvc.service.param;

import java.util.List;

import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.request.AdministrativeAreaReq;
import com.bidv.ibank.dvc.model.request.RevenueAuthorityReq;
import com.bidv.ibank.dvc.model.request.TreasuryDetailReq;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryDetailRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

public interface ParamGovService {

    ResultList<ChapterRes> listChapter();

    ResultList<EconomicContentRes> listEconomicContent();

    ResultList<TaxTypeRes> listTaxType();

    ResultList<CustomsCurrencyRes> listCustomsCurrency();

    ResultList<ExportImportType> listExportImportType();

    ResultList<TreasuryRes> listTreasury();

    ResultList<RevenueAccountRes> listRevenueAccount();

    ResultList<RevenueAuthorityRes> listRevenueAuthority(RevenueAuthorityReq req);

    ResultList<AdministrativeAreaRes> listAdministrativeArea(AdministrativeAreaReq req);

    List<MappingTreasuryBenBankCodeDto> getMappingTreasuryBb(List<TccDmKhobacEntity> treasuryEntityList);

    List<MappingTreasuryBenBankCodeDto> getMappingTreasuryBbByCode(List<String> treasuryCodes);

    Result<TreasuryDetailRes> detailTreasury(TreasuryDetailReq req);

    Result<TccDmCqthuEntity> checkRevAuthInTreasury(String revAuthCode, String treasuryCode);

    MappingTreasuryBenBankCodeDto getMappingTreasuryBenBankCode(String treasuryCode);
}