package com.bidv.ibank.dvc.service.common;

import com.bidv.ibank.dvc.model.dto.TxnTaxItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.framework.domain.response.Result;

import java.util.List;

public interface ValidationService {

    Result<String> validateAfterInitApproval(List<GOVPaymentTransactionEntity> txnEntityList);

    Result<String> validateInterruptTime(String subProdCode);

    Result<String> validateDebitAccount(GOVPaymentTransactionEntity txnEntity, boolean isBeforeAcct);

    Result<String> validateDuplicatePayments(List<TxnTaxItemDto> taxItems);

    Result<String> validateTaxCode(String taxCode, String altTaxCode);

    Result<String> validateTaxCode(String taxCode);
}
