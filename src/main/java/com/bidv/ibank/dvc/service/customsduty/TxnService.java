package com.bidv.ibank.dvc.service.customsduty;

import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.dvc.model.request.TxnSaveReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnListRes;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

public interface TxnService {

    ResultList<TxnPendingListRes> listPending(TxnPendingListReq req);

    Result<TxnDetailRes> detail(TxnDetailReq req);

    Result<String> save(TxnSaveReq req);

    ResultList<TxnListRes> list(TxnListReq req);

    Result<String> edit(TxnSaveReq req);

    Result<String> updateStatus(TxnDetailReq req);
}
