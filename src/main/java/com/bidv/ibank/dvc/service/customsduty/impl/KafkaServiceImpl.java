package com.bidv.ibank.dvc.service.customsduty.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.bidv.ibank.client.common.dto.transLimit.RevertTxnLimitRequest;
import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.dto.messagequeue.NotifyMessageAdditionalInfoDto;
import com.bidv.ibank.dvc.model.dto.messagequeue.NotifyMessageDto;
import com.bidv.ibank.dvc.model.dto.messagequeue.NotifyMessageInfoDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.customsduty.KafkaService;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.dvc.util.Utils;
import com.bidv.ibank.dvc.util.constant.CcyEnum;
import com.bidv.ibank.framework.kafka.service.IbankProducerKafka;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.framework.util.constants.Constants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaServiceImpl implements KafkaService {

    private final ObjectMapper objectMapper;
    private final IbankProducerKafka kafkaProducer;
    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;

    @Value("${bidv.kafka.ibank.topic.gov-accounting}")
    private String topic;

    @Value("${bidv.kafka.ibank.topic.notification}")
    private String notifyTopic;

    @Value("${bidv.kafka.ibank.topic.revert-limit-all}")
    private String revertAllTopic;

    @Override
    public void sendAccountingMessage(List<GOVPaymentTransactionEntity> txnEntityList) {
        for (GOVPaymentTransactionEntity txnEntity : txnEntityList) {
            try {
                if (TransactionStatusEnum.BANK_PROCESSING.name().equals(txnEntity.getStatus()) && txnEntity.isFinal()) {
                    kafkaProducer.send(txnEntity.getId(), topic);
                }
            } catch (Exception e) {
                log.error("Error sending message to Kafka: {}", e.getMessage());
                govPaymentTransactionRepository.updateState(List.of(txnEntity.getId()), TransactionStateEnum.KAFKA_FAIL.name());
            }
        }
    }

    @Override
    public void sendTxnNotification(String txnId) {
        try {
            GOVPaymentTransactionEntity txnEntity = govPaymentTransactionRepository.findById(txnId).orElse(null);
            if (txnEntity == null) {
                log.error("[SEND NOTIFICATION] Transaction not found: {}", txnId);
                return;
            }

            Map<String, String> params = new HashMap<>();
            Map<String, String> additionalInfoParams = new HashMap<>();

            String productVie = Translator.toLocale(AppConstants.LANGUAGE.TXN_TYPE + "." + txnEntity.getTxnType(), txnEntity.getTxnType(),
                    (Object[]) null, Locale.forLanguageTag(Constants.LOCALE_CODE_VI_VN));
            String productEng = Translator.toLocale(AppConstants.LANGUAGE.TXN_TYPE + "." + txnEntity.getTxnType(), txnEntity.getTxnType(),
                    (Object[]) null, Locale.forLanguageTag(Constants.LOCALE_CODE_EN_US));
            String txnStatusVie = Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + "." + txnEntity.getStatus(), txnEntity.getStatus(),
                    (Object[]) null, Locale.forLanguageTag(Constants.LOCALE_CODE_VI_VN));
            String txnStatusEng = Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + "." + txnEntity.getStatus(), txnEntity.getStatus(),
                    (Object[]) null, Locale.forLanguageTag(Constants.LOCALE_CODE_EN_US));
            String txnFeeOpt = Translator.toLocale(AppConstants.LANGUAGE.TXN_FEE_OPT + "." + txnEntity.getFeeOpt(), txnEntity.getFeeOpt(),
                    (Object[]) null, Locale.forLanguageTag(Constants.LOCALE_CODE_VI_VN));
            String amount = CcyEnum.VND.name().equalsIgnoreCase(txnEntity.getCcy()) ? StrUtils.formatWithThousandSeparator(txnEntity.getAmount())
                    : txnEntity.getAmount().toString();
            String approveDate = DateUtils.convertDateToString(txnEntity.getApprovedDate(), AppConstants.DATE_FORMAT_DD_MM_YYYY_HH_MM_SS);
            String pmtDate = DateUtils.convertDateToString(txnEntity.getPmtTime(), AppConstants.DATE_FORMAT_DD_MM_YYYY);
            String pmtTime = DateUtils.convertDateToString(txnEntity.getPmtTime(), AppConstants.DATE_FORMAT_HH_MM_SS);

            params.put("PRODUCT_VIE", productVie);
            params.put("PRODUCT_ENG", productEng);
            params.put("SUB_PRODUCT", txnEntity.getSubProdCode());
            params.put("MA_GIAO_DICH_IBANK", txnEntity.getId());
            params.put("TRANG_THAI", txnEntity.getStatus());
            params.put("THOI_GIAN_DUYET_GIAO_DICH", approveDate);
            params.put("NGAY_HACH_TOAN", pmtDate);
            params.put("THOI_GIAN_HACH_TOAN", pmtTime);
            params.put("CREATED_USER", txnEntity.getCreatedBy());
            params.put("SO_TAI_KHOAN_TRICH_NO", txnEntity.getDebitAccNo());
            params.put("TEN_TAI_KHOAN_TRICH_NO", txnEntity.getDebitAccName());
            params.put("SO_TIEN_PHI", txnEntity.getFeeAmount().toString());
            params.put("SO_TIEN", amount);
            params.put("LOAI_TIEN", txnEntity.getCcy());
            params.put("TRANG_THAI_VIE", txnStatusVie);
            params.put("LOAI_PHI", txnFeeOpt);
            params.put("TRANG_THAI_ENG", txnStatusEng);

            additionalInfoParams.put("transId", txnEntity.getId());

            List<Long> processUserIds = Utils.convetStringToListLong(txnEntity.getProcessUserIds());
            if ((long) processUserIds.size() > 1) {
                List<Long> makerUserIds = List.of(processUserIds.get(0));
                List<Long> checkerUserIds = processUserIds.subList(1, processUserIds.size());

                NotifyMessageDto makerMessage = getMakerMessage(params, additionalInfoParams, makerUserIds, txnEntity.getProdCode(), txnEntity
                        .getSubProdCode());
                NotifyMessageDto checkerMessage = getCheckerMessage(params, additionalInfoParams, checkerUserIds, txnEntity.getProdCode(),
                        txnEntity.getSubProdCode());

                kafkaProducer.send("maker-notification", objectMapper.writeValueAsString(makerMessage), notifyTopic);
                kafkaProducer.send("checker-notification", objectMapper.writeValueAsString(checkerMessage), notifyTopic);
            }
        } catch (Exception e) {
            log.debug("Error while sending notification message", e);
        }
    }

    @Override
    public void revertLimit(String txnId, String txnCode) {
        try {
            RevertTxnLimitRequest request = new RevertTxnLimitRequest();
            request.setTxnId(txnId);
            request.setTxnCode(txnCode);

            kafkaProducer.send("revert-all", objectMapper.writeValueAsString(request), revertAllTopic);
        } catch (Exception e) {
            log.debug("Error while sending revert limit message", e);
        }
    }

    private NotifyMessageDto getMakerMessage(Map<String, String> params, Map<String, String> additionalInfoParams, List<Long> recipients, String productCode,
            String subProductCode)
            throws JsonProcessingException {
        return getMessages(params, additionalInfoParams, recipients, productCode, subProductCode, "RESULT-AUTO-1-MKR");
    }

    private NotifyMessageDto getCheckerMessage(Map<String, String> params, Map<String, String> additionalInfoParams, List<Long> recipients, String productCode,
            String subProductCode)
            throws JsonProcessingException {
        return getMessages(params, additionalInfoParams, recipients, productCode, subProductCode, "RESULT-AUTO-1-CKR");
    }

    private NotifyMessageDto getMessages(Map<String, String> params, Map<String, String> additionalInfoParams, List<Long> recipients, String productCode,
            String subProductCode, String eventType)
            throws JsonProcessingException {
        return NotifyMessageDto.builder()
                .params(params)
                .attachments(List.of())
                .recipients(recipients)
                .info(NotifyMessageInfoDto.builder()
                        .productCode(productCode)
                        .subProductCode(subProductCode)
                        .eventType(eventType)
                        .build())
                .emails(List.of())
                .additionalInfo(objectMapper.writeValueAsString(NotifyMessageAdditionalInfoDto.builder()
                        .params(additionalInfoParams)
                        .build()))
                .build();
    }
}
