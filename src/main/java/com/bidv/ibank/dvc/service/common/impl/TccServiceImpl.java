package com.bidv.ibank.dvc.service.common.impl;

import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.result.ResponseStatus;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessageHeader;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseRequest;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class TccServiceImpl implements TccService {

    public <Q, S> Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<S>>> executeTccCall(Q body,
            Function<IntegrateBaseRequest<Q>, IntegrateBaseResponse<EsbTccBaseBodyMessage<S>>> tccFunction) {
        try {
            IntegrateBaseRequest<Q> request = new IntegrateBaseRequest<>();
            request.setHeader(IntegrateServiceFactory.createRequestHeader());
            request.setBody(body);
            IntegrateBaseResponse<EsbTccBaseBodyMessage<S>> response = tccFunction.apply(request);
            if (!response.isSuccess()) {
                return Result.response(ResponseStatus.ERROR, response.getHeader().getErrorCode(), response.getHeader().getErrorDesc(), response);
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("Error executing tcc call", e);
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    public <T, R> Result<R> getErrResult(Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<T>>>> response) {
        if (response.getData() != null && response.getData().getBody() != null && response.getData().getBody().getMessageHeader() != null) {
            EsbTccBaseBodyMessageHeader messageHeader = response.getData().getBody().getMessageHeader();
            return Result.error(messageHeader.getErrCode(), messageHeader.getErrDesc());
        }
        return Result.error(response.getCode(), response.getMessage());
    }

    public <T, R> ResultList<R> getErrResultList(Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<T>>>> response) {
        if (response.getData() != null && response.getData().getBody() != null && response.getData().getBody().getMessageHeader() != null) {
            EsbTccBaseBodyMessageHeader messageHeader = response.getData().getBody().getMessageHeader();
            return ResultList.error(messageHeader.getErrCode(), messageHeader.getErrDesc());
        }
        return ResultList.error(response.getCode(), response.getMessage());
    }
}
