package com.bidv.ibank.dvc.service.gov;

import com.bidv.ibank.dvc.model.request.TemplateDeleteReq;
import com.bidv.ibank.dvc.model.request.TemplateListReq;
import com.bidv.ibank.dvc.model.request.TemplateSaveReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

public interface GovTemplateService {

    Result<String> save(TemplateSaveReq request);

    ResultList<TemplateListRes> list(TemplateListReq request);

    Result<String> delete(TemplateDeleteReq request);
}
