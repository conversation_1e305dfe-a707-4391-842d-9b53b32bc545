package com.bidv.ibank.dvc.service.common;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Function;

import com.bidv.ibank.client.common.dto.fee.FeeResult;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.client.common.dto.masterdata.BalanceAccountDto;
import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.client.common.dto.masterdata.CustomerPmtSpecDto;
import com.bidv.ibank.client.common.dto.masterdata.ParInterruptTimeDto;
import com.bidv.ibank.client.common.dto.masterdata.ParProcessTransactionDto;
import com.bidv.ibank.client.common.dto.masterdata.ParTransferTransDto;
import com.bidv.ibank.client.common.dto.masterdata.ProductDto;
import com.bidv.ibank.client.common.dto.transLimit.TransInfoDto;
import com.bidv.ibank.client.common.dto.transLimit.ValidateTxnResponse;
import com.bidv.ibank.client.common.dto.workflow.CheckPermResponse;
import com.bidv.ibank.client.common.dto.workflow.WfTxnInfoResponse;
import com.bidv.ibank.common.txn.model.dto.ConfirmResDto;
import com.bidv.ibank.common.txn.model.dto.InitTransactionRes;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionResponse;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionFlowDto;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionRes;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.common.txn.util.constant.ValidateTransFlowTypeEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.account.CoreAccount;
import org.springframework.web.multipart.MultipartFile;

public interface CommonService {

    List<AuthAccountDto> getAuthorizedAccounts(String debitAccType, String grpType);

    List<CheckPermResponse> checkUserPermOnTxn(List<String> wfTxnIds);

    boolean checkAuthorizationAccount(String debitAccNo, String debitAccType, String grpType);

    Result<ValidateTransactionRes<GOVPaymentTransactionEntity>> validateTransaction(GOVPaymentTransactionEntity transactionEntity,
            TransactionActionEnum action, boolean isInBidv, boolean isCalcFee, boolean isGenerateTransKey,
            Function<GOVPaymentTransactionEntity, Result<GOVPaymentTransactionEntity>> function);

    Result<InitTransactionRes<GOVPaymentTransactionEntity>> initTransaction(String transKey, TransactionActionEnum action);

    Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> requestApprovalTransaction(List<String> txnIds);

    Result<ConfirmResDto<GOVPaymentTransactionEntity>> confirmAuthTransaction(String transKey, String confirmValue, TransactionActionEnum action);

    Result<BalanceAccountDto> getPmtAccountDetail(String debitAccNo, String transCode);

    Result<CustomerPmtSpecDto> getCusPmtSpec();

    Result<CustomerDto> getCustomerInfo(String cifNo);

    Result<ParTransferTransDto> getParTransData(String debitAccType, String ccy, String debitAccNo, String txnCode);

    Result<ValidateTxnResponse> validateTxn(Long userId, Long cusId, List<TransInfoDto> transInfos);

    Result<CoreAccount> validateCoreAccount(String debitAccNo, String cifNo);

    Result<ValidateTransactionFlowDto> validateTransactionFlowMain(BigDecimal amount, String ccy, CustomerDto customerDto, CoreAccount coreAccount,
            List<ParProcessTransactionDto> parProcessTransactionDtos, ValidateTransFlowTypeEnum validateTypeEnum);

    ResultList<ParProcessTransactionDto> getProcessTransaction();

    <T> T getCacheValue(String transKey, String cacheField, Class<T> clazz);

    Result<ProductDto> getProductUser(String txnCode);

    ResultList<FeeResult> calculateFeeBatch(List<GOVPaymentBatchItemEntity> batchItems);

    Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> initBulkTransactions(List<GOVPaymentTransactionEntity> entities,
            TransactionActionEnum txnAction);

    Result<ConfirmResDto<GOVPaymentTransactionEntity>> authConfirmRqApprovalBulk(String transKey, String confirmValue);

    Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> rejectTransaction(List<String> txnIds, String approvalNote);

    Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> approveTransaction(List<String> txnIds,
            Function<List<GOVPaymentTransactionEntity>, Result<String>> function);

    List<ParInterruptTimeDto> getInterruptTime();

    byte[] addDigitalSignature(String debitBrcd, byte[] fileContent, float marginBottom, float marginLeft);

    String generateChecksum(MultipartFile file);

    Result<LocalDate> validateEffDate(LocalDate effDate);

    ResultList<WfTxnInfoResponse> getTxnActivity(List<String> wfTxnIds);
}
