package com.bidv.ibank.dvc.service.common.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.client.common.dto.masterdata.ParTransferTransDto;
import com.bidv.ibank.client.common.dto.masterdata.ProductDto;
import com.bidv.ibank.client.common.dto.masterdata.TransferTransReqDto;
import com.bidv.ibank.client.common.dto.transLimit.TransInfoDto;
import com.bidv.ibank.client.common.dto.transLimit.ValidateRequest;
import com.bidv.ibank.client.common.dto.transLimit.ValidateTxnResponse;

import com.bidv.ibank.client.common.dto.workflow.WfTxnInfoResponse;
import com.bidv.ibank.integrate.entity.base.RequestHeader;
import com.bidv.ibank.integrate.entity.digisign.DisplayConfigDto;
import com.bidv.ibank.integrate.services.DigitalSigningService;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import org.springframework.stereotype.Service;

import com.bidv.ibank.client.common.api.MasterdataServiceClient;
import com.bidv.ibank.client.common.api.FeeServiceClient;
import com.bidv.ibank.client.common.api.TransLimitServiceClient;
import com.bidv.ibank.client.common.api.TransWorkflowClient;
import com.bidv.ibank.client.common.dto.fee.FeeReq;
import com.bidv.ibank.client.common.dto.fee.FeeReqItem;
import com.bidv.ibank.client.common.dto.fee.FeeResult;
import com.bidv.ibank.client.common.dto.masterdata.AccountDetailCriteriaDto;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.client.common.dto.masterdata.AuthorizedAccountListCriteriaDto;
import com.bidv.ibank.client.common.dto.masterdata.BalanceAccountDto;
import com.bidv.ibank.client.common.dto.masterdata.CheckAuthorizedAccountCriteriaDto;
import com.bidv.ibank.client.common.dto.masterdata.CustomerPmtSpecDto;
import com.bidv.ibank.client.common.dto.masterdata.InqCustomerCriteriaDto;
import com.bidv.ibank.client.common.dto.masterdata.ParInterruptTimeDto;
import com.bidv.ibank.client.common.dto.masterdata.ParProcessTransactionDto;
import com.bidv.ibank.client.common.dto.workflow.ApproveTransactionsRequestDto;
import com.bidv.ibank.client.common.dto.workflow.CheckPermResponse;
import com.bidv.ibank.common.txn.model.dto.BulkTransactionReq;
import com.bidv.ibank.client.common.util.FeeCodeEnum;
import com.bidv.ibank.common.txn.model.dto.ConfirmResDto;
import com.bidv.ibank.common.txn.model.dto.ConfirmTransactionRqDto;
import com.bidv.ibank.common.txn.model.dto.CusInfoDto;
import com.bidv.ibank.common.txn.model.dto.InitTransactionReq;
import com.bidv.ibank.common.txn.model.dto.InitTransactionRes;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionReq;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionResponse;
import com.bidv.ibank.common.txn.model.dto.ValidateAccountReq;
import com.bidv.ibank.common.txn.model.dto.ValidateTransFlowMainReq;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionFlowDto;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionReq;
import com.bidv.ibank.common.txn.model.dto.ValidateTransactionRes;
import com.bidv.ibank.common.txn.processor.BulkValidateTransactionsRes;
import com.bidv.ibank.common.txn.service.BulkTransactionService;
import com.bidv.ibank.common.txn.service.ProcessTransactionCacheService;
import com.bidv.ibank.common.txn.service.TransactionBaseService;
import com.bidv.ibank.common.txn.service.TransactionCommonService;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionCodeEnum;
import com.bidv.ibank.common.txn.util.constant.ValidateTransFlowTypeEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.integrate.entity.account.CoreAccount;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonServiceImpl implements CommonService {

    private final MasterdataServiceClient masterdataServiceClient;
    private final FeeServiceClient feeServiceClient;
    private final TransWorkflowClient transWorkflowClient;
    private final TransLimitServiceClient transLimitServiceClient;
    private final BulkTransactionService<GOVPaymentTransactionEntity> bulkTransactionService;
    private final TransactionBaseService<GOVPaymentTransactionEntity> transactionBaseService;
    private final TransactionCommonService transactionCommonService;
    private final ProcessTransactionCacheService processTransactionCacheService;
    private final IntegrateServiceFactory integrateServiceFactory;

    @Override
    public List<AuthAccountDto> getAuthorizedAccounts(String debitAccType, String grpType) {
        AuthorizedAccountListCriteriaDto dto = new AuthorizedAccountListCriteriaDto();
        dto.setAccType(debitAccType);
        dto.setGrpType(grpType);

        ResultList<AuthAccountDto> response = masterdataServiceClient.authorizedAccountList(dto);
        if (!response.isSuccess() || response.getData() == null) {
            log.error("Get authorized accounts failed: {}", response.getMessage());
            return Collections.emptyList();
        }

        return response.getData().getItems();
    }

    @Override
    public List<CheckPermResponse> checkUserPermOnTxn(List<String> wfTxnIds) {
        ApproveTransactionsRequestDto dto = new ApproveTransactionsRequestDto();
        dto.setWfTxnIds(wfTxnIds);
        ResultList<CheckPermResponse> response = transWorkflowClient.checkUserPermOnTxn(dto);

        if (!response.isSuccess() || response.getData() == null) {
            log.error("Check user perm on txn failed: {}", response.getMessage());
            return Collections.emptyList();
        }
        return response.getData().getItems();
    }

    @Override
    public boolean checkAuthorizationAccount(String debitAccNo, String debitAccType, String grpType) {
        CheckAuthorizedAccountCriteriaDto dto = new CheckAuthorizedAccountCriteriaDto();
        dto.setAccountNo(debitAccNo);
        dto.setAccType(debitAccType);
        dto.setGrpType(grpType);

        Result<Boolean> response = masterdataServiceClient.checkAuthorizationAccount(dto);
        if (!response.isSuccess() || response.getData() == null) {
            log.error("Check authorization account failed: {}", response.getMessage());
            return false;
        }
        return response.getData();
    }

    @Override
    public Result<ValidateTransactionRes<GOVPaymentTransactionEntity>> validateTransaction(
            GOVPaymentTransactionEntity transactionEntity,
            TransactionActionEnum action,
            boolean isInBidv,
            boolean isCalcFee,
            boolean isGenerateTransKey,
            Function<GOVPaymentTransactionEntity, Result<GOVPaymentTransactionEntity>> function) {
        Result<ValidateTransactionRes<GOVPaymentTransactionEntity>> response = transactionBaseService.validateTransaction(
                ValidateTransactionReq.<GOVPaymentTransactionEntity>builder()
                        .user(AuthenticationUtils.getCurrentUser().getUser())
                        .action(action)
                        .txnCode(TransactionCodeEnum.getByCode(isInBidv ? TransactionCodeEnum.GOV01.code() : TransactionCodeEnum.GOV02.code()))
                        .transaction(transactionEntity)
                        .calcFee(isCalcFee)
                        .noCache(!isGenerateTransKey)
                        .transEntityClass(GOVPaymentTransactionEntity.class)
                        .build(),
                e -> function.apply(e.getTransaction()));

        if (!response.isSuccess() || response.getData() == null) {
            log.error("Validate transaction failed: {}", response.getMessage());
            return Result.error(response.getCode(), response.getMessage());
        }

        return Result.success(response.getData());
    }

    @Override
    public Result<InitTransactionRes<GOVPaymentTransactionEntity>> initTransaction(String transKey, TransactionActionEnum action) {
        InitTransactionReq<GOVPaymentTransactionEntity> initTransactionReq = InitTransactionReq.<GOVPaymentTransactionEntity>builder()
                .transKey(transKey)
                .action(action)
                .user(AuthenticationUtils.getCurrentUser().getUser())
                .transEntityClass(GOVPaymentTransactionEntity.class)
                .build();

        Result<InitTransactionRes<GOVPaymentTransactionEntity>> response = transactionBaseService.initTransaction(initTransactionReq);

        if (!response.isSuccess() || response.getData() == null) {
            log.error("Init transaction failed: {}", response.getMessage());
            return Result.error(response.getCode(), response.getMessage());
        }

        return Result.success(response.getData());
    }

    @Override
    public Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> requestApprovalTransaction(List<String> txnIds) {
        ProcessTransactionReq<GOVPaymentTransactionEntity> request = ProcessTransactionReq.<GOVPaymentTransactionEntity>builder()
                .listTxnId(txnIds)
                .entityTransClass(GOVPaymentTransactionEntity.class)
                .build();

        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = transactionBaseService.requestApprovalTransaction(request);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Request approval transaction failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public Result<ConfirmResDto<GOVPaymentTransactionEntity>> confirmAuthTransaction(String transKey, String confirmValue, TransactionActionEnum action) {
        ConfirmTransactionRqDto<GOVPaymentTransactionEntity> dto = ConfirmTransactionRqDto.<GOVPaymentTransactionEntity>builder()
                .transKey(transKey)
                .reqValue(confirmValue)
                .transEntityClass(GOVPaymentTransactionEntity.class)
                .action(action)
                .build();

        Result<ConfirmResDto<GOVPaymentTransactionEntity>> result = transactionBaseService.confirmAuthTransaction(dto);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Confirm auth transaction failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public Result<BalanceAccountDto> getPmtAccountDetail(String debitAccNo, String transCode) {
        AccountDetailCriteriaDto dto = new AccountDetailCriteriaDto();
        dto.setAccountNo(debitAccNo);
        dto.setTransCode(transCode);

        Result<BalanceAccountDto> response = masterdataServiceClient.pmtAccountDetail(dto);
        if (!response.isSuccess() || response.getData() == null) {
            log.error("Get pmt account detail failed: {}", response.getMessage());
            return Result.error(response.getCode(), response.getMessage());
        }
        return Result.success(response.getData());
    }

    @Override
    public Result<CustomerPmtSpecDto> getCusPmtSpec() {
        CustomerPmtSpecDto pmtSpec = transactionBaseService.getTransactionProcessorService().getCusPmtSpec();
        if (pmtSpec == null) {
            log.error("Get pmt spec failed");
            return Result.error(ResponseCode.TIMEOUT_01);
        }
        return Result.success(pmtSpec);
    }

    @Override
    public Result<CustomerDto> getCustomerInfo(String cifNo) {
        InqCustomerCriteriaDto dto = new InqCustomerCriteriaDto();
        dto.setCifNo(cifNo);

        Result<CustomerDto> response = masterdataServiceClient.getCustomerInfo(dto);
        if (!response.isSuccess() || response.getData() == null) {
            log.error("Get customer info failed: {}", response.getMessage());
            return Result.error(response.getCode(), response.getMessage());
        }
        return Result.success(response.getData());
    }

    @Override
    public Result<ParTransferTransDto> getParTransData(String debitAccType, String ccy, String debitAccNo, String txnCode) {
        TransferTransReqDto dto = new TransferTransReqDto();
        dto.setDebitAccType(debitAccType);
        dto.setCcy(ccy);
        dto.setDebitAccNo(debitAccNo);
        dto.setTxnCode(txnCode);

        Result<ParTransferTransDto> response = masterdataServiceClient.parTransData(dto);
        if (!response.isSuccess() || response.getData() == null) {
            log.error("Get par trans data failed: {}", response.getMessage());
            return Result.error(response.getCode(), response.getMessage());
        }
        return Result.success(response.getData());
    }

    @Override
    public Result<ValidateTxnResponse> validateTxn(Long userId, Long cusId, List<TransInfoDto> transInfos) {
        ValidateRequest req = new ValidateRequest();
        req.setUserId(userId);
        req.setCusId(cusId);
        req.setTranInfos(transInfos);

        Result<ValidateTxnResponse> response = transLimitServiceClient.validateTxn(req);
        if (!response.isSuccess() || response.getData() == null) {
            log.error("Validate transaction failed: {}", response.getMessage());
            return Result.error(response.getCode(), response.getMessage());
        }
        return Result.success(response.getData());
    }

    @Override
    public Result<CoreAccount> validateCoreAccount(String debitAccNo, String cifNo) {
        ValidateAccountReq validateAccountReq = ValidateAccountReq.builder()
                .txnCode(TransactionCodeEnum.GOV01)
                .accountNo(debitAccNo)
                .accType(CoreAccTypeEnum.DDA.name())
                .cif(cifNo)
                .build();

        Result<CoreAccount> result = transactionCommonService.validateAccount(validateAccountReq);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Validate account failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public Result<ValidateTransactionFlowDto> validateTransactionFlowMain(
            BigDecimal amount,
            String ccy,
            CustomerDto customerDto,
            CoreAccount coreAccount,
            List<ParProcessTransactionDto> parProcessTransactionDtos,
            ValidateTransFlowTypeEnum validateTypeEnum) {
        CusInfoDto cusInfoDto = CusInfoDto.builder()
                .resident(customerDto.getResident())
                .nationality(customerDto.getCountry())
                .build();
        ValidateTransFlowMainReq req = ValidateTransFlowMainReq.builder()
                .ccy(ccy)
                .amount(amount)
                .debitAccount(coreAccount)
                .debitCusInfo(cusInfoDto)
                .parDtos(parProcessTransactionDtos)
                .validateType(validateTypeEnum)
                .usedSyncdata(true)
                .build();

        Result<ValidateTransactionFlowDto> result = transactionCommonService.validateTransactionFlowMain(req);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Validate transaction flow main failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public ResultList<ParProcessTransactionDto> getProcessTransaction() {
        ResultList<ParProcessTransactionDto> parProcessTransactionDtos = masterdataServiceClient.getProcessTransaction();
        if (!parProcessTransactionDtos.isSuccess() || parProcessTransactionDtos.getData() == null) {
            log.error("Get process transaction failed: {}", parProcessTransactionDtos.getMessage());
            return ResultList.error(parProcessTransactionDtos.getCode(), parProcessTransactionDtos.getMessage());
        }
        return ResultList.success(parProcessTransactionDtos.getData().getItems());
    }

    @Override
    public Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> initBulkTransactions(List<GOVPaymentTransactionEntity> entities,
            TransactionActionEnum txnAction) {
        BulkTransactionReq<GOVPaymentTransactionEntity> request = BulkTransactionReq.<GOVPaymentTransactionEntity>builder()
                .transactions(entities)
                .action(txnAction)
                .build();

        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = bulkTransactionService.initTransactionsBulk(request, null);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Init bulk transactions failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public Result<ConfirmResDto<GOVPaymentTransactionEntity>> authConfirmRqApprovalBulk(String transKey, String confirmValue) {
        Result<ConfirmResDto<GOVPaymentTransactionEntity>> result = bulkTransactionService.authConfirmRqApprovalBulk(
                transKey,
                confirmValue,
                GOVPaymentTransactionEntity.class);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Auth confirm rq approval bulk failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public <T> T getCacheValue(String transKey, String cacheField, Class<T> clazz) {
        return processTransactionCacheService.getValue(transKey, cacheField, clazz);
    }

    @Override
    public Result<ProductDto> getProductUser(String txnCode) {
        Result<ProductDto> response = masterdataServiceClient.getProductUser(txnCode);
        if (!response.isSuccess() || response.getData() == null) {
            log.error("Get product user failed: {}", response.getMessage());
            return Result.error(response.getCode(), response.getMessage());
        }
        return Result.success(response.getData());
    }

    @Override
    public ResultList<FeeResult> calculateFeeBatch(List<GOVPaymentBatchItemEntity> batchItems) {
        List<FeeReqItem> items = batchItems.stream()
                .map(item -> FeeReqItem.builder()
                        .id(item.getId())
                        .feeCode(Objects.equals(item.getTxnCode(), TransactionCodeEnum.GOV01.code()) ? FeeCodeEnum.GOV01.code() : FeeCodeEnum.GOV02.code())
                        .txnAmount(new BigDecimal(item.getAmount()))
                        .txnCcy(item.getCcy())
                        .feeCcy(item.getFeeCcy())
                        .build())
                .toList();

        FeeReq feeCalRequest = FeeReq.builder()
                .cusId(AuthenticationUtils.getCurrentUser().getUser().getCusId())
                .items(items)
                .build();

        ResultList<FeeResult> result = feeServiceClient.calculateFeeBatch(feeCalRequest);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Calculate fee failed: {}", result.getMessage());
            return ResultList.error(result.getCode(), result.getMessage());
        }
        return ResultList.success(result.getData().getItems());
    }

    @Override
    public Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> rejectTransaction(List<String> txnIds, String approvalNote) {
        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = transactionBaseService.rejectTransaction(txnIds, approvalNote,
                GOVPaymentTransactionEntity.class);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Reject transaction failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> approveTransaction(List<String> txnIds,
            Function<List<GOVPaymentTransactionEntity>, Result<String>> function) {
        ProcessTransactionReq<GOVPaymentTransactionEntity> processTransactionReq = ProcessTransactionReq.<GOVPaymentTransactionEntity>builder()
                .listTxnId(txnIds)
                .entityTransClass(GOVPaymentTransactionEntity.class)
                .domainValidator(e -> {
                    Result<String> result = function.apply(e);
                    if (!result.isSuccess()) {
                        return Result.error(result.getCode(), result.getMessage());
                    }
                    return Result.success(BulkValidateTransactionsRes.<GOVPaymentTransactionEntity>builder()
                            .success(e)
                            .build());
                })
                .build();

        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = transactionBaseService.approveTransaction(processTransactionReq);
        if (!result.isSuccess() || result.getData() == null) {
            log.error("Approve transaction failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(result.getData());
    }

    @Override
    public List<ParInterruptTimeDto> getInterruptTime() {
        ResultList<ParInterruptTimeDto> interruptTimeResult = masterdataServiceClient.getParInterruptTime();
        if (!interruptTimeResult.isSuccess() || interruptTimeResult.getData() == null) {
            log.error("Get interrupt time failed: {}", interruptTimeResult.getMessage());
            return Collections.emptyList();
        }
        return interruptTimeResult.getData().getItems();
    }

    @Override
    public byte[] addDigitalSignature(String debitBrcd, byte[] fileContent, float marginBottom, float marginLeft) {
        try {
            RequestHeader requestHeader = IntegrateServiceFactory.createRequestHeader();
            DigitalSigningService digitalSigningService = integrateServiceFactory.getDigitalSigningService();

            DisplayConfigDto displayConfigDto = new DisplayConfigDto();
            displayConfigDto.setMarginBottom(marginBottom);
            displayConfigDto.setMarginLeft(marginLeft);
            displayConfigDto.setLocateSign(0);

            return digitalSigningService.signingDocument(requestHeader, debitBrcd,
                    fileContent, displayConfigDto);
        } catch (Exception e) {
            log.error("Add digital signature failed: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public String generateChecksum(MultipartFile file) {
        try {
            return transactionCommonService.generateChecksumFile(file);
        } catch (Exception e) {
            log.error("Generate checksum failed: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public Result<LocalDate> validateEffDate(LocalDate effDate) {
        Result<LocalDate> result = transactionCommonService.validateEffDateForProcess(effDate);
        if (!result.isSuccess()) {
            log.error("Validate effective date failed: {}", result.getMessage());
            return Result.error(result.getCode(), result.getMessage());
        }
        return result;
    }

    @Override
    public ResultList<WfTxnInfoResponse> getTxnActivity(List<String> wfTxnIds) {
        ApproveTransactionsRequestDto dto = new ApproveTransactionsRequestDto();
        dto.setWfTxnIds(wfTxnIds);
        ResultList<WfTxnInfoResponse> result = transWorkflowClient.getTxnActivity(dto);
        if (!result.isSuccess()) {
            log.error("Get txn activity failed: {}", result.getMessage());
            return ResultList.error(result.getCode(), result.getMessage());
        }
        return result;
    }
}
