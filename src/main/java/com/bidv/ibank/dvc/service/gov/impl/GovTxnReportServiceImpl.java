package com.bidv.ibank.dvc.service.gov.impl;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.request.TxnExportReportReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.gov.GovTxnReportService;
import com.bidv.ibank.dvc.service.gov.GovTxnService;
import com.bidv.ibank.dvc.specification.TxnSpecifications;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class GovTxnReportServiceImpl implements GovTxnReportService {

    private final GovTxnService govTxnService;
    private final CommonService commonService;
    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;
    private final GOVPaymentTransactionMapper govPaymentTransactionMapper;

    @Override
    public ResultList<TxnReportListRes> listReport(TxnReportListReq req) {
        List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());
        Pageable pageable = QueryUtils.buildPageRequest(req, Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
        Page<GOVPaymentTransactionEntity> govTxnEntityList = govPaymentTransactionRepository.findAll(
                TxnSpecifications.createTxnReportListSpec(req)
                        .and(TxnSpecifications.debitAccNoIn(accounts.stream().map(AuthAccountDto::getAccountNo).toList())),
                pageable);

        return ResultList.success(govTxnEntityList.stream().map(govPaymentTransactionMapper::toReportListRes).toList(),
                govTxnEntityList.getTotalElements());
    }

    @Override
    public Result<ExportFileRes> export(TxnExportReportReq req) {
        return govTxnService.export(req);
    }
}
