package com.bidv.ibank.dvc.service.customsduty;

import org.springframework.web.multipart.MultipartFile;

import com.bidv.ibank.dvc.model.request.BatchConfirmReq;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchCalcFeeRes;
import com.bidv.ibank.dvc.model.request.BatchDetailEditReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.BatchProcessResultRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;

public interface BatchService {

    Result<ExportFileRes> downloadTemplate();

    Result<byte[]> downloadFile(BatchDetailReq req);

    Result<String> uploadFile(MultipartFile file);

    Result<String> delete(BatchDetailReq req);

    ResultList<BatchListRes> list(BatchListReq req);

    Result<ExportFileRes> downloadResult(BatchDetailReq req);

    Result<BatchDetailRes> detail(BatchDetailReq req);

    Result<BatchCalcFeeRes> calcFee(BatchDetailReq req);

    Result<String> editDetail(BatchDetailEditReq req);

    Result<ValidateCustomsDutyRes> validateDetail(ValidateCustomsDutyReq req);

    Result<TxnInitPushRes> initPush(BatchDetailReq req);

    Result<BatchProcessResultRes> confirmPush(BatchConfirmReq req);
}
