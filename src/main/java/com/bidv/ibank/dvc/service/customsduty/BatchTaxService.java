package com.bidv.ibank.dvc.service.customsduty;

import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.springframework.web.multipart.MultipartFile;

public interface BatchTaxService {

    Result<ExportFileRes> downloadTemplate();

    ResultList<BatchListRes> list(BatchListReq req);

    Result<ExportFileRes> downloadResult(BatchDetailReq req);

    Result<String> uploadFile(MultipartFile file);

    Result<ExportFileRes> exportInquiryResult(BatchDetailReq req);
}
