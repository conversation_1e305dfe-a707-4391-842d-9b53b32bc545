package com.bidv.ibank.dvc.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.tcc.TccReqMapper;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.repository.customsduty.GovPaymentTransactionCustomRepository;
import com.bidv.ibank.dvc.service.common.TccService;
import com.bidv.ibank.dvc.service.customsduty.KafkaService;
import com.bidv.ibank.dvc.service.gov.GovTxnAccountingService;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.context.AppContext;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.base.EsbTccBaseBody.EsbTccBaseBodyMessage;
import com.bidv.ibank.integrate.entity.tcc.TccMessageReq;
import com.bidv.ibank.integrate.entity.tcc.TccMessageRes;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoReq;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoRes;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoRow;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class JobService {

    private final TccService tccService;
    private final TccReqMapper tccReqMapper;
    private final KafkaService kafkaService;
    private final GovTxnAccountingService govTxnAccountingService;
    private final IntegrateServiceFactory integrateServiceFactory;
    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;
    private final GovPaymentTransactionCustomRepository govPaymentTransactionCustomRepository;

    public void updateTxnStatusUndefinedOrFailedState() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.minusMinutes(AppConstants.THRESHOLD_MINUTES);
        List<GOVPaymentTransactionEntity> txnEntities = new ArrayList<>();
        txnEntities.addAll(govPaymentTransactionRepository.findAllByStatus(TransactionStatusEnum.UNDEFINED.name(), threshold));
        txnEntities.addAll(govPaymentTransactionRepository.findAllByState(TransactionStatusEnum.SUCCESS.name(), TransactionStateEnum.FAIL_TRANS_TCC.name(), threshold));

        for (GOVPaymentTransactionEntity txnEntity : txnEntities) {
            try {
                LocalDateTime endOfDay = txnEntity.getPmtTime().toLocalDate().atTime(LocalTime.MAX);
                if (now.isAfter(endOfDay)) {
                    continue;
                }
                TccMessageReq<TccQueryTxnInfoReq> tccReq = new TccMessageReq<>(tccReqMapper.toQueryTxnInfoReq(txnEntity.getTccDocId()));
                Result<IntegrateBaseResponse<EsbTccBaseBodyMessage<TccMessageRes<TccQueryTxnInfoRes>>>> response = tccService.executeTccCall(
                        tccReq, input -> integrateServiceFactory.getTccService().queryTxnInfo(input));

                if (!response.isSuccess()) {
                    Result<String> errResult = tccService.getErrResult(response);
                    log.error("Error when calling updateTxnStatusUndefinedOrFailedState: {}", errResult.getMessage());
                    continue;
                }

                TccQueryTxnInfoRow tccQueryTxnInfoRes = response.getData().getBody().getMessageBody().getMessageResponse().getChungTu().getRow();

                if (TransactionStatusEnum.UNDEFINED.name().equals(txnEntity.getStatus())) {
                    boolean isRevertLimit = govPaymentTransactionCustomRepository.updateTxnStatusUndefined(txnEntity.getId(), tccQueryTxnInfoRes);
                    if (txnEntity.getApprovedDate().toLocalDate().isEqual(LocalDate.now()) && isRevertLimit) {
                        kafkaService.revertLimit(txnEntity.getId(), txnEntity.getTxnCode());
                    }
                } else {
                    govPaymentTransactionCustomRepository.updateTxnStateFailTccTrans(txnEntity.getId(), tccQueryTxnInfoRes);
                }
            } catch (Exception e) {
                log.error("Error when calling updateTxnStatusUndefinedOrFailedState: {}", e.getMessage());
            }
        }
    }

    public void retryUnderBalance() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime threshold = now.minusMinutes(AppConstants.THRESHOLD_MINUTES);
        int maxRetry = (int) AppContext.getProperty(AppConstants.DVC_JOB_RETRY_MAX, 3L);
        List<GOVPaymentTransactionEntity> txnEntities = govPaymentTransactionRepository.findAllByRetryInsfctBal(
                TransactionStatusEnum.BANK_PROCESSING.name(), TransactionStateEnum.UNDER_BALANCE.name(), AppConstants.UNDER_BALANCE_FLAG.YES, maxRetry,
                threshold);
        for (GOVPaymentTransactionEntity txnEntity : txnEntities) {
            try {
                LocalDateTime endOfDay = txnEntity.getApprovedDate().toLocalDate().atTime(LocalTime.MAX);
                if (now.isAfter(endOfDay)) {
                    continue;
                }
                Result<String> response = govTxnAccountingService.processAccounting(txnEntity.getId());

                Integer updRetryInsfctBalCnt = Optional.ofNullable(txnEntity.getRetryInsfctBalCnt()).orElse(0) + AppConstants.NUMBER.ONE;
                String underBalanceFlag = Optional.ofNullable(response.getData()).orElse(txnEntity.getRetryInsfctBal());

                if (AppConstants.UNDER_BALANCE_FLAG.YES.equals(underBalanceFlag) && updRetryInsfctBalCnt >= maxRetry) {
                    govPaymentTransactionRepository.updateRetryInsfctBalCntAndStatus(
                            txnEntity.getId(),
                            updRetryInsfctBalCnt,
                            TransactionStatusEnum.FAILED.name(),
                            TransactionStateEnum.FAILED.name());
                } else {
                    govPaymentTransactionRepository.updateRetryInsfctBalCnt(txnEntity.getId(), underBalanceFlag, updRetryInsfctBalCnt);
                }
            } catch (Exception e) {
                log.error("Error when calling retryUnderBalance: {}", e.getMessage());
            }
        }
    }
}
