package com.bidv.ibank.dvc.service.common.impl;

import com.bidv.ibank.dvc.model.dto.ValidateFileDto;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.util.FileUtils;
import com.bidv.ibank.framework.util.ObjectUtils;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseRequest;
import com.bidv.ibank.integrate.entity.base.IntegrateBaseResponse;
import com.bidv.ibank.integrate.entity.base.ResponseHeader;
import com.bidv.ibank.integrate.entity.file.DownloadFileReq;
import com.bidv.ibank.integrate.entity.file.DownloadFileResBody;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.integrate.entity.file.FileReqInfo;
import com.bidv.ibank.integrate.entity.file.UploadFileReq;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import com.bidv.ibank.util.excel.ExcelImporter;
import com.bidv.ibank.util.excel.Importable;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class FileServiceImpl implements FileService {

    private final IntegrateServiceFactory integrateServiceFactory;

    @Override
    public Result<FileInfo> uploadToS3(ByteArrayOutputStream byteArrayOutputStream, String fileName, boolean isGenerateUrl) {
        FileReqInfo fileReqInfo = new FileReqInfo(byteArrayOutputStream, fileName);
        UploadFileReq fileReqDto = new UploadFileReq();
        fileReqDto.setFiles(List.of(fileReqInfo));
        if (!isGenerateUrl)
            fileReqDto.setIsIgnoreUrl(true);

        IntegrateBaseRequest<UploadFileReq> uploadReq = new IntegrateBaseRequest<>();
        uploadReq.setBody(fileReqDto);
        uploadReq.setHeader(IntegrateServiceFactory.createRequestHeader());

        IntegrateBaseResponse<List<FileInfo>> uploadFileResponse = integrateServiceFactory.getFileService().uploadFile(uploadReq);

        if (!IntegrateServiceFactory.isSuccess(uploadFileResponse)) {
            ResponseHeader responseHeader = uploadFileResponse.getHeader();
            return Result.error(responseHeader.getErrorCode(), responseHeader.getErrorDesc());
        }

        return Result.success(getFileInfo(uploadFileResponse.getBody(), fileName));
    }

    @Override
    public Result<FileInfo> uploadToS3(String filePath, String fileName, String fileType, boolean isGenerateUrl) {
        ClassPathResource resource = new ClassPathResource(filePath);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            bos.write(resource.getInputStream().readAllBytes());
            return uploadToS3(bos, FileUtils.formatFileName(fileName, fileType), isGenerateUrl);
        } catch (IOException e) {
            log.error("Error reading file from classpath: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        } finally {
            ObjectUtils.close(bos);
        }
    }

    @Override
    public Result<FileInfo> uploadToS3(MultipartFile file, boolean isGenerateUrl) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            String fileName = FileUtils.formatFileName(file.getOriginalFilename(), extension);
            bos.write(file.getBytes());
            return uploadToS3(bos, fileName, isGenerateUrl);
        } catch (IOException e) {
            log.error("Error uploading file to S3: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        } finally {
            ObjectUtils.close(bos);
        }
    }

    @Override
    public Result<byte[]> downloadFile(String fileKey) {
        DownloadFileReq downloadFileReq = new DownloadFileReq();
        downloadFileReq.setFileKeys(new String[] { fileKey });

        IntegrateBaseRequest<DownloadFileReq> request = new IntegrateBaseRequest<>();
        request.setBody(downloadFileReq);
        request.setHeader(IntegrateServiceFactory.createRequestHeader());

        IntegrateBaseResponse<DownloadFileResBody> downloadFileResponse = integrateServiceFactory.getFileService().downloadFile(request);

        if (!IntegrateServiceFactory.isSuccess(downloadFileResponse)) {
            ResponseHeader responseHeader = downloadFileResponse.getHeader();
            return Result.error(responseHeader.getErrorCode(), responseHeader.getErrorDesc());
        }
        try (InputStream inputStream = downloadFileResponse.getBody().getFileContent()) {
            return Result.success(inputStream.readAllBytes());
        } catch (IOException e) {
            log.error("Error reading file from S3: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    @Override
    public <T extends Importable> Result<List<T>> validateAndReadExcelFile(MultipartFile file, ValidateFileDto<T> validateDto) {
        if (file.getSize() / (1024.0 * 1024.0) > validateDto.getMaxFileSize()) {
            return Result.error(ResponseCode.FILE_SIZE_EXCEEDED);
        }
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (!validateDto.getFileTypes().stream().anyMatch(fileType -> fileType.name().equalsIgnoreCase(extension))) {
            return Result.error(ResponseCode.FILE_UPLOAD_01);
        }
        String fileName = FilenameUtils.getBaseName(file.getOriginalFilename());
        if (!validateDto.getPattern().matcher(fileName).matches()) {
            return Result.error(ResponseCode.FILE_NAME_INVALID);
        }

        try {
            List<T> items = ExcelImporter.readExcel(file.getInputStream(), validateDto.getClazz(), (int) validateDto.getStartRow());
            if (items.isEmpty()) {
                return Result.error(ResponseCode.EMPTY_FILE);
            }
            if (items.size() > validateDto.getMaxItem()) {
                return Result.error(ResponseCode.ITEM_EXCEEDED, String.valueOf(validateDto.getMaxItem()));
            }
            return Result.success(items);
        } catch (Exception e) {
            log.error("Error validating and reading excel file: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    private FileInfo getFileInfo(List<FileInfo> fileInfos, String fileName) {
        return fileInfos.stream()
                .filter(fileInfo -> StringUtils.equalsIgnoreCase(fileInfo.getFilename(), fileName))
                .findFirst()
                .orElse(null);
    }
}
