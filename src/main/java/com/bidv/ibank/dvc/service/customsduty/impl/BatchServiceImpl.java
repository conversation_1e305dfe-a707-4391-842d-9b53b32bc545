package com.bidv.ibank.dvc.service.customsduty.impl;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentBatchMapper;
import com.bidv.ibank.dvc.model.request.BatchConfirmReq;
import com.bidv.ibank.dvc.model.request.BatchDetailEditReq;
import com.bidv.ibank.dvc.model.request.BatchDetailReq;
import com.bidv.ibank.dvc.model.request.BatchListReq;
import com.bidv.ibank.dvc.model.response.BatchCalcFeeRes;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.BatchProcessResultRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.FileTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.dvc.util.constant.StatusEnum;

import com.bidv.ibank.util.excel.ExcelExporter;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.bidv.ibank.dvc.model.dto.BatchItemExportDto;
import com.bidv.ibank.common.txn.model.dto.ConfirmResDto;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionResponse;
import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.client.common.dto.fee.FeeResult;
import com.bidv.ibank.client.common.dto.masterdata.CustomerPmtSpecDto;
import com.bidv.ibank.common.txn.util.constant.CacheField;
import com.bidv.ibank.common.txn.util.constant.ChargeFeeOpt;
import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import com.bidv.ibank.dvc.model.dto.TccDmChuongCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmCqthuCodeNameKhobacEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmDbhcCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmKhobacCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmLhxnkCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmLoaitienhqaCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmNdktCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmSthueHqaCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmTkNsnnStkEntityDto;
import com.bidv.ibank.dvc.model.dto.ValidateFileDto;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.ValidateCustomsDutyRes;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLhxnkRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentBatchRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GovPaymentBatchItemCustomRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.service.customsduty.AsyncService;
import com.bidv.ibank.dvc.service.customsduty.BatchService;
import com.bidv.ibank.dvc.service.customsduty.TaxService;
import com.bidv.ibank.dvc.specification.BatchItemSpecifications;
import com.bidv.ibank.dvc.specification.BatchSpecifications;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser.UserInfo;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.framework.context.AppContext;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.util.FileUtils;
import com.bidv.ibank.framework.util.ObjectUtils;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import com.bidv.ibank.util.excel.ExcelUtils;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class BatchServiceImpl implements BatchService {

    private final FileService fileService;
    private final AsyncService asyncService;
    private final CommonService commonService;
    private final GOVPaymentBatchMapper govPaymentBatchMapper;
    private final GOVPaymentBatchRepository govPaymentBatchRepository;
    private final GOVPaymentBatchItemRepository govPaymentBatchItemRepository;
    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;
    private final GOVPaymentItemRepository govPaymentItemRepository;
    private final GovPaymentBatchItemCustomRepository govPaymentBatchItemCustomRepository;
    private final TaxService taxService;
    private final TccDmKhobacRepository tccDmKhobacRepository;
    private final TccDmTkNsnnRepository tccDmTkNsnnRepository;
    private final TccDmCqthuRepository tccDmCqthuRepository;
    private final TccDmDbhcRepository tccDmDbhcRepository;
    private final TccDmChuongRepository tccDmChuongRepository;
    private final TccDmNdktRepository tccDmNdktRepository;
    private final TccDmLhxnkRepository tccDmLhxnkRepository;
    private final TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;
    private final TccDmSthueHqaRepository tccDmSthueHqaRepository;
    private final IntegrateServiceFactory integrateServiceFactory;

    @Override
    public Result<ExportFileRes> downloadTemplate() {
        Workbook templateWorkbook = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            // Create workbook from template file
            templateWorkbook = ExcelUtils.createWorkbookFromResource(AppConstants.BATCH_TXN_TEMPLATE_PATH);
            if (templateWorkbook == null) {
                log.error("Template file not found or cannot read: {}", AppConstants.BATCH_TXN_TEMPLATE_PATH);
                return Result.error(ResponseCode.TIMEOUT_01);
            }

            // Add categories to the workbook
            Result<Void> addCategoriesResult = addCategories(templateWorkbook);
            if (!addCategoriesResult.isSuccess()) {
                return Result.error(addCategoriesResult.getCode(), addCategoriesResult.getMessage());
            }
            byteArrayOutputStream = new ByteArrayOutputStream();
            ExcelUtils.writeAndClose(templateWorkbook, byteArrayOutputStream);

            // Upload to S3
            Result<FileInfo> uploadResult = fileService.uploadToS3(
                    byteArrayOutputStream,
                    FileUtils.formatExcelName(AppConstants.BATCH_IMPORT_TEMPLATE_FILE_NAME),
                    true);
            if (!uploadResult.isSuccess()) {
                return Result.error(uploadResult.getCode(), uploadResult.getMessage());
            }

            return Result.success(ExportFileRes.builder()
                    .url(uploadResult.getData().getFileUrl())
                    .build());
        } catch (IOException e) {
            log.error("Error convert from workbook to stream: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        } finally {
            ObjectUtils.close(byteArrayOutputStream);
            ObjectUtils.close(templateWorkbook);
        }
    }

    @Override
    public Result<byte[]> downloadFile(BatchDetailReq req) {
        try {
            GOVPaymentBatchEntity govBatchEntity = govPaymentBatchRepository.findByBatchNo(
                    req.getBatchNo(),
                    AuthenticationUtils.getCurrentUser().getUser().getUsername()).orElse(null);
            if (govBatchEntity == null) {
                return Result.error(ResponseCode.RESOURCE_NOTFOUND);
            }
            return fileService.downloadFile(govBatchEntity.getFileKey());
        } catch (Exception e) {
            log.error("Error downloading file: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    @Override
    @Transactional
    public Result<String> uploadFile(MultipartFile file) {
        long maxItem = AppContext.getProperty(AppConstants.DVC_UPLOAD_MAX_ITEM, 0L);
        ValidateFileDto<BatchImportItemDto> validateDto = ValidateFileDto.<BatchImportItemDto>builder()
                .pattern(AppConstants.BATCH_IMPORT_VALID_PATTERN)
                .startRow(AppConstants.BATCH_DOWNLOAD_RESULT_START_ROW)
                .maxItem(maxItem)
                .maxFileSize(AppContext.getProperty(AppConstants.DVC_UPLOAD_MAX_FILE_SIZE, 0L))
                .fileTypes(List.of(FileTypeEnum.XLSX, FileTypeEnum.XLS))
                .clazz(BatchImportItemDto.class)
                .build();

        Result<List<BatchImportItemDto>> validateResult = fileService.validateAndReadExcelFile(file, validateDto);
        if (!validateResult.isSuccess()) {
            return Result.error(validateResult.getCode(), validateResult.getMessage(), maxItem);
        }

        // Check sum
        String checksum = commonService.generateChecksum(file);
        if (StringUtils.isBlank(checksum)) {
            return Result.error(ResponseCode.TIMEOUT_01);
        }
        List<GOVPaymentBatchEntity> existingBatchs = govPaymentBatchRepository.findAll(BatchSpecifications.checkFileExistSpec(file, checksum,
                BatchTypeEnum.PAYMENT));
        if (!existingBatchs.isEmpty()) {
            return Result.error(ResponseCode.FILE_EXISTS);
        }

        // Upload file to S3
        Result<FileInfo> uploadResult = fileService.uploadToS3(file, false);
        if (!uploadResult.isSuccess()) {
            return Result.error(uploadResult.getCode(), uploadResult.getMessage());
        }

        GOVPaymentBatchEntity batchEntity = govPaymentBatchMapper.toEntity(
                file,
                checksum,
                uploadResult.getData().getFileKey(),
                BatchTypeEnum.PAYMENT);
        govPaymentBatchRepository.save(batchEntity);
        asyncService.handleBatchUpload(batchEntity.getId());

        return Result.success(batchEntity.getBatchNo());
    }

    @Override
    public ResultList<BatchListRes> list(BatchListReq req) {
        Pageable pageable = QueryUtils.buildPageRequest(req, Sort.Order.desc("createdDate"), Sort.Order.desc("batchNo"));

        Page<GOVPaymentBatchEntity> govBatchEntityList = govPaymentBatchRepository.findAll(
                BatchSpecifications.createBatchListSpec(req, BatchTypeEnum.PAYMENT),
                pageable);
        return ResultList.success(govBatchEntityList.stream().map(govPaymentBatchMapper::toListDto).toList(), govBatchEntityList.getTotalElements());
    }

    @Override
    @Transactional
    public Result<String> delete(BatchDetailReq req) {
        UserInfo userInfo = AuthenticationUtils.getCurrentUser().getUser();
        GOVPaymentBatchEntity batch = govPaymentBatchRepository.findByBatchNo(req.getBatchNo(), null).orElse(null);

        if (batch == null) {
            return Result.error(ResponseCode.RESOURCE_NOTFOUND);
        }
        if (!Objects.equals(batch.getStatus().name(), BatchStatusEnum.ERROR.name())
                && !Objects.equals(batch.getStatus().name(), BatchStatusEnum.CHECKED.name())) {
            return Result.error(ResponseCode.TRANS_STATUS);
        }
        if (!userInfo.getUsername().equals(batch.getCreatedBy())) {
            return Result.error(ResponseCode.TRANS_INFO);
        }
        govPaymentBatchRepository.updateStatusByBatchNo(BatchStatusEnum.DELETED, null, batch.getBatchNo(), userInfo.getCusId());
        return Result.success(null);
    }

    @Override
    public Result<ExportFileRes> downloadResult(BatchDetailReq req) {
        try {
            GOVPaymentBatchEntity govBatchEntity = govPaymentBatchRepository.findOne(
                    BatchSpecifications.createResultSpec(req.getBatchNo(), BatchTypeEnum.PAYMENT)).orElse(null);
            if (govBatchEntity == null) {
                return Result.error(ResponseCode.RESOURCE_NOTFOUND);
            }

            List<BatchItemExportDto> batchItems = govPaymentBatchMapper.toExportDto(govBatchEntity.getGovPaymentBatchItemList())
                    .stream().sorted(Comparator.comparing((BatchItemExportDto i) -> StringUtils.isBlank(i.getBatchOrder()))
                            .thenComparing(i -> StringUtils.defaultIfBlank(i.getBatchOrder(), ""))).toList();
            FileInfo fileInfo = ExcelExporter.exportExcel(
                    AppConstants.BATCH_RESULT_TEMPLATE_PATH,
                    AppConstants.BATCH_DOWNLOAD_RESULT_START_ROW,
                    AppConstants.BATCH_DOWNLOAD_RESULT_SHIFT_ROW,
                    null,
                    batchItems,
                    BatchItemExportDto.class,
                    true,
                    integrateServiceFactory.getFileService().createFileHubWriter(AppConstants.BATCH_DOWNLOAD_RESULT_FILE_NAME, true));

            return Result.success(ExportFileRes.builder()
                    .url(fileInfo.getFileUrl())
                    .build());
        } catch (Exception e) {
            log.error("Error downloading batch result: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    @Override
    public Result<BatchDetailRes> detail(BatchDetailReq req) {
        GOVPaymentBatchEntity govBatchEntity = govPaymentBatchRepository.findOne(
                Specification.where(BatchSpecifications.batchNoEq(req.getBatchNo()))
                        .and(BatchSpecifications.createdByEq(AuthenticationUtils.getCurrentUser().getUser().getUsername()))
                        .and(BatchSpecifications.statusIn(List.of(BatchStatusEnum.CHECKED.name())))).orElse(null);
        if (govBatchEntity == null) {
            return Result.error(ResponseCode.RESOURCE_NOTFOUND);
        }

        return Result.success(govPaymentBatchMapper.toDetailDto(govBatchEntity));
    }

    public Result<BatchCalcFeeRes> calcFee(BatchDetailReq req) {
        Result<List<GOVPaymentBatchItemEntity>> batchItemEntityListResult = getBatchItemEntityListByBatchNo(req.getBatchNo());
        if (!batchItemEntityListResult.isSuccess()) {
            return Result.error(batchItemEntityListResult.getCode(), batchItemEntityListResult.getMessage());
        }

        Result<List<GOVPaymentBatchItemEntity>> setFeeInfoResult = setFeeInfoByCusPmtSpec(batchItemEntityListResult.getData());
        if (!setFeeInfoResult.isSuccess()) {
            return Result.error(setFeeInfoResult.getCode(), setFeeInfoResult.getMessage());
        }

        ResultList<FeeResult> feeResult = commonService.calculateFeeBatch(batchItemEntityListResult.getData());
        if (!feeResult.isSuccess()) {
            return Result.error(feeResult.getCode(), feeResult.getMessage());
        }

        List<GOVPaymentBatchItemEntity> batchItemEntityListWithFee = govPaymentBatchMapper.toBatchItemEntitiesWithFee(
                batchItemEntityListResult.getData(),
                feeResult.getData().getItems());

        govPaymentBatchItemCustomRepository.update(batchItemEntityListWithFee);
        BatchCalcFeeRes batchCalcFeeRes = govPaymentBatchMapper.toBatchCalcFeeRes(batchItemEntityListWithFee);
        return Result.success(batchCalcFeeRes);
    }

    @Override
    @Transactional
    public Result<String> editDetail(BatchDetailEditReq req) {
        GOVPaymentTransactionEntity govTransactionEntity = commonService.getCacheValue(req.getTransKey(), CacheField.TRANSACTION_DATA.getKey(),
                GOVPaymentTransactionEntity.class);
        if (govTransactionEntity == null) {
            return Result.error(ResponseCode.RESOURCE_NOTFOUND);
        }
        if (!commonService.checkAuthorizationAccount(govTransactionEntity.getDebitAccNo(), CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name())) {
            return Result.error(ResponseCode.USER_ACCOUNT_INVALID);
        }

        GOVPaymentBatchItemEntity govBatchItemEntity = govPaymentBatchItemRepository.findById(req.getBatchItemId()).orElse(null);
        if (govBatchItemEntity == null || !Objects.equals(govBatchItemEntity.getGovPaymentBatchEntity().getBatchType(), BatchTypeEnum.PAYMENT)) {
            return Result.error(ResponseCode.RESOURCE_NOTFOUND);
        }
        if (!Objects.equals(govBatchItemEntity.getStatus(), BatchItemStatusEnum.INVALID)) {
            return Result.error(ResponseCode.TRANS_STATUS);
        }
        if (!Objects.equals(govBatchItemEntity.getCreatedBy(), AuthenticationUtils.getCurrentUser().getUser().getUsername())) {
            return Result.error(ResponseCode.TRANS_INFO);
        }

        GOVPaymentBatchItemEntity govBatchItemUpdatedEntity = govPaymentBatchMapper.toUpdatedEntity(govBatchItemEntity, govTransactionEntity);
        govPaymentBatchItemRepository.save(govBatchItemUpdatedEntity);

        List<String> duplicateBatchItemIds = getDuplicateBatchItemIds(govBatchItemEntity, govBatchItemUpdatedEntity);
        if (!duplicateBatchItemIds.isEmpty()) {
            duplicateBatchItemIds.add(govBatchItemEntity.getId());
            govPaymentBatchItemRepository.updateErrCode(duplicateBatchItemIds, ResponseCode.BATCH_DUPLICATE_RECORD.code());
        }
        return Result.success(null);
    }

    @Override
    public Result<ValidateCustomsDutyRes> validateDetail(ValidateCustomsDutyReq req) {
        req.setTxnId(null);
        req.setIsInBatch(true);
        return taxService.validate(req);
    }

    @Override
    public Result<TxnInitPushRes> initPush(BatchDetailReq request) {
        Result<List<GOVPaymentBatchItemEntity>> batchItemEntityListResult = getBatchItemEntityListByBatchNo(request.getBatchNo());
        if (!batchItemEntityListResult.isSuccess()) {
            return Result.error(batchItemEntityListResult.getCode(), batchItemEntityListResult.getMessage());
        }

        List<GOVPaymentTransactionEntity> txnEntities = govPaymentBatchMapper.toTransactionEntities(batchItemEntityListResult.getData());
        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = commonService.initBulkTransactions(
                txnEntities, TransactionActionEnum.REQUEST_APPROVAL);
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }

        TxnInitPushRes txnInitPushRes = TxnInitPushRes.builder()
                .transKey(result.getData().getTransKey())
                .requireAuth(result.getData().isRequireAuth())
                .transAuth(result.getData().getTransAuth())
                .build();
        return Result.success(txnInitPushRes);
    }

    @Override
    @Transactional
    public Result<BatchProcessResultRes> confirmPush(BatchConfirmReq request) {
        Result<ConfirmResDto<GOVPaymentTransactionEntity>> result = commonService.authConfirmRqApprovalBulk(request.getTransKey(), request.getConfirmValue());
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }

        List<GOVPaymentTransactionEntity> successTxns = result.getData().getSuccessfulTransactions();
        List<TransactionResDetail> failTxns = result.getData().getFailedTransactions();

        if (CollectionUtils.isNotEmpty(successTxns)) {
            try {
                long cusId = AuthenticationUtils.getCurrentUser().getUser().getCusId();
                govPaymentBatchRepository.updateStatusAndProcessedDateByBatchId(
                        BatchStatusEnum.PROCESSED,
                        LocalDateTime.now(),
                        successTxns.get(0).getBatchId(),
                        cusId);

                List<GOVPaymentItemEntity> govPaymentItemEntities = new ArrayList<>();
                successTxns.forEach(govPaymentTransactionEntity -> {
                    if (!govPaymentTransactionEntity.getPaymentItems().isEmpty()) {
                        govPaymentItemEntities.addAll(
                                govPaymentTransactionEntity.getPaymentItems()
                                        .stream()
                                        .map(e -> {
                                            e.setTxnId(govPaymentTransactionEntity.getId());
                                            return e;
                                        })
                                        .toList());
                    }
                });

                if (!govPaymentItemEntities.isEmpty()) {
                    govPaymentItemRepository.saveAll(govPaymentItemEntities);
                }
            } catch (Exception e) {
                log.error("Error saving transactions when confirm push batch: {}", e.getMessage());
                successTxns.forEach(item -> failTxns.add(TransactionResDetail.builder()
                        .txnId(item.getId())
                        .message(ResponseCode.TIMEOUT_01.message())
                        .build()));
                successTxns.clear();
            }
        }

        long total = (long) successTxns.size() + failTxns.size();
        BatchProcessResultRes batchProcessResultRes = BatchProcessResultRes.builder()
                .total(total)
                .totalSuccess(total - failTxns.size())
                .failTxns(failTxns)
                .build();
        return Result.success(batchProcessResultRes);
    }

    private List<String> getDuplicateBatchItemIds(GOVPaymentBatchItemEntity dbItem, GOVPaymentBatchItemEntity updateItem) {
        List<String> batchItemIds = new ArrayList<>();
        List<GOVPaymentBatchItemEntity> validItemList = govPaymentBatchItemRepository.findByBatchIdAndStatus(dbItem.getBatchId(), BatchItemStatusEnum.VALID);
        for (GOVPaymentBatchItemEntity item : validItemList) {
            if (item.getId().equals(updateItem.getId())) {
                continue;
            }
            if (Objects.equals(item.getTaxCode(), updateItem.getTaxCode())
                    && Objects.equals(item.getDeclarationNo(), updateItem.getDeclarationNo())
                    && Objects.equals(item.getShkb(), updateItem.getShkb())
                    && Objects.equals(item.getMaTk(), updateItem.getMaTk())
                    && Objects.equals(item.getMaCqthu(), updateItem.getMaCqthu())
                    && Objects.equals(item.getMaDbhc(), updateItem.getMaDbhc())
                    && Objects.equals(item.getMaChuong(), updateItem.getMaChuong())
                    && Objects.equals(item.getMaNdkt(), updateItem.getMaNdkt())
                    && Objects.equals(item.getMaSthue(), updateItem.getMaSthue())
                    && Objects.equals(item.getMaLthq(), updateItem.getMaLthq())
                    && Objects.equals(item.getMaLh(), updateItem.getMaLh())) {
                batchItemIds.add(item.getId());
            }
        }
        return batchItemIds;
    }

    private Result<List<GOVPaymentBatchItemEntity>> setFeeInfoByCusPmtSpec(List<GOVPaymentBatchItemEntity> batchItems) {
        Result<CustomerPmtSpecDto> pmtSpec = commonService.getCusPmtSpec();
        if (!pmtSpec.isSuccess() || pmtSpec.getData() == null) {
            return Result.error(pmtSpec.getCode(), pmtSpec.getMessage());
        }

        batchItems.forEach(item -> {
            item.setFeeAccNo(item.getDebitAccNo());
            if (!Objects.equals(pmtSpec.getData().getChargeFeeOpt(), ChargeFeeOpt.INST.code()))
                item.setFeeAccNo(pmtSpec.getData().getChargeAcc());
            item.setFeeCcy(Optional.ofNullable(pmtSpec.getData().getCurrCode()).orElse(item.getCcy()));
            item.setFeeOpt(pmtSpec.getData().getChargeFeeOpt());
            item.setFeeFreq(pmtSpec.getData().getChargeFreq());
        });
        return Result.success(batchItems);
    }

    private Result<List<GOVPaymentBatchItemEntity>> getBatchItemEntityListByBatchNo(String batchNo) {
        GOVPaymentBatchEntity batchEntity = govPaymentBatchRepository.findByBatchNo(batchNo, null).orElse(null);
        if (batchEntity == null || !Objects.equals(batchEntity.getBatchType(), BatchTypeEnum.PAYMENT)) {
            return Result.error(ResponseCode.RESOURCE_NOTFOUND);
        }

        if (!Objects.equals(batchEntity.getStatus(), BatchStatusEnum.CHECKED)) {
            return Result.error(ResponseCode.TRANS_STATUS);
        }
        if (!Objects.equals(batchEntity.getCreatedBy(), AuthenticationUtils.getCurrentUser().getUser().getUsername())) {
            return Result.error(ResponseCode.TRANS_INFO);
        }

        List<GOVPaymentBatchItemEntity> batchItemEntityList = govPaymentBatchItemRepository.findAll(
                Specification.where(BatchItemSpecifications.batchIdEq(batchEntity.getId()))
                        .and(BatchItemSpecifications.statusEq(BatchItemStatusEnum.VALID.name())));
        if (batchItemEntityList.isEmpty()) {
            return Result.error(ResponseCode.TRANS_STATUS);
        }

        return Result.success(batchItemEntityList);
    }

    private Result<Void> addCategories(Workbook templateWorkbook) {
        try {
            // Assuming the template has specific sheets for each category
            Sheet taxTypeSheet = templateWorkbook.getSheetAt(10);
            Sheet ccSheet = templateWorkbook.getSheetAt(9);
            Sheet eiTypeSheet = templateWorkbook.getSheetAt(8);
            Sheet ecSheet = templateWorkbook.getSheetAt(7);
            Sheet chapterSheet = templateWorkbook.getSheetAt(6);
            Sheet admAreaSheet = templateWorkbook.getSheetAt(5);
            Sheet revAuthSheet = templateWorkbook.getSheetAt(4);
            Sheet treasurySheet = templateWorkbook.getSheetAt(3);
            Sheet revAccSheet = templateWorkbook.getSheetAt(2);

            // Fetching data from repositories
            List<TccDmSthueHqaCodeNameEntityDto> taxTypeList = tccDmSthueHqaRepository.findTaxTypeByCodes(null);
            List<TccDmLoaitienhqaCodeNameEntityDto> ccList = tccDmLoaitienhqaRepository.findLoaitienhqaByCodes(null);
            ccList = ccList.stream().sorted().toList();

            List<TccDmLhxnkCodeNameEntityDto> eiTypeList = tccDmLhxnkRepository.findLhxnkByCodes(null);
            List<TccDmNdktCodeNameEntityDto> ecList = tccDmNdktRepository.findNdktByCodes(null);
            List<TccDmChuongCodeNameEntityDto> chapterList = tccDmChuongRepository.findChuongByCodes(null);
            List<TccDmDbhcCodeNameEntityDto> admAreaList = tccDmDbhcRepository.findDbhcByCodes(null);
            List<TccDmCqthuCodeNameKhobacEntityDto> revAuthList = tccDmCqthuRepository.findCqthuByCodes(StatusEnum.ACTIVE.getValue(), null);
            List<TccDmTkNsnnStkEntityDto> revAccList = tccDmTkNsnnRepository.findTkNsnnByCodes(null);
            List<TccDmKhobacCodeNameEntityDto> treasuryList = tccDmKhobacRepository.findTreasuriesByCodes(StatusEnum.ACTIVE.getValue(), null);

            // Exporting data to respective sheets

            ExcelExporter.exportExcelToSheet(taxTypeSheet, 1, 0, null, taxTypeList, TccDmSthueHqaCodeNameEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(ccSheet, 1, 0, null, ccList, TccDmLoaitienhqaCodeNameEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(eiTypeSheet, 1, 0, null, eiTypeList, TccDmLhxnkCodeNameEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(ecSheet, 1, 0, null, ecList, TccDmNdktCodeNameEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(chapterSheet, 1, 0, null, chapterList, TccDmChuongCodeNameEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(admAreaSheet, 1, 0, null, admAreaList, TccDmDbhcCodeNameEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(revAuthSheet, 1, 0, null, revAuthList, TccDmCqthuCodeNameKhobacEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(revAccSheet, 1, 0, null, revAccList, TccDmTkNsnnStkEntityDto.class, true);
            ExcelExporter.exportExcelToSheet(treasurySheet, 1, 0, null, treasuryList, TccDmKhobacCodeNameEntityDto.class, true);

            // Cleanup extra rows in each sheet
            cleanupExtraRows(taxTypeSheet, taxTypeList.size());
            cleanupExtraRows(ccSheet, ccList.size());
            cleanupExtraRows(eiTypeSheet, eiTypeList.size());
            cleanupExtraRows(ecSheet, ecList.size());
            cleanupExtraRows(chapterSheet, chapterList.size());
            cleanupExtraRows(admAreaSheet, admAreaList.size());
            cleanupExtraRows(revAuthSheet, revAuthList.size());
            cleanupExtraRows(revAccSheet, revAccList.size());
            cleanupExtraRows(treasurySheet, treasuryList.size());
            return Result.success(null);
        } catch (Exception e) {
            log.error("Error exporting data to sheets: " + e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    private void cleanupExtraRows(Sheet sheet, int dataSize) {
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum > dataSize) {
            for (int i = dataSize + 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    sheet.removeRow(row);
                }
            }
        }
    }
}
