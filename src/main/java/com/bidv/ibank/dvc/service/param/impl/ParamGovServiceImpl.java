package com.bidv.ibank.dvc.service.param.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.bidv.ibank.framework.context.AppContext;
import com.bidv.ibank.framework.domain.response.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.mapper.param.ParamMapper;
import com.bidv.ibank.dvc.model.request.AdministrativeAreaReq;
import com.bidv.ibank.dvc.model.request.RevenueAuthorityReq;
import com.bidv.ibank.dvc.model.request.TreasuryDetailReq;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryDetailRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.dvc.repository.param.TccDmChuongRepository;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKbnnNhtmRepository;
import com.bidv.ibank.dvc.repository.param.TccDmKhobacRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLhxnkRepository;
import com.bidv.ibank.dvc.repository.param.TccDmLoaitienhqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmNdktRepository;
import com.bidv.ibank.dvc.repository.param.TccDmSthueHqaRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.service.param.ParamGovService;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.StatusEnum;
import com.bidv.ibank.framework.domain.response.ResultList;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ParamGovServiceImpl implements ParamGovService {

    private final TccDmChuongRepository tccDmChuongRepository;
    private final TccDmNdktRepository tccDmNdktRepository;
    private final TccDmSthueHqaRepository tccDmSthueHqaRepository;
    private final TccDmLoaitienhqaRepository tccDmLoaitienhqaRepository;
    private final TccDmLhxnkRepository tccDmLhxnkRepository;
    private final TccDmKhobacRepository tccDmKhobacRepository;
    private final TccDmTkNsnnRepository tccDmTkNsnnRepository;
    private final TccDmCqthuRepository tccDmCqthuRepository;
    private final TccDmDbhcRepository tccDmDbhcRepository;
    private final TccDmKbnnNhtmRepository tccDmKbnnNhtmRepository;
    private final ParamMapper paramMapper;

    @Override
    @Transactional(readOnly = true)
    public ResultList<ChapterRes> listChapter() {
        List<TccDmChuongEntity> chapterList = tccDmChuongRepository.findAllByOrderByMaChuongAsc();
        return ResultList.success(chapterList.stream().map(paramMapper::toChapterRes).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<EconomicContentRes> listEconomicContent() {
        List<TccDmNdktEntity> ecList = tccDmNdktRepository.findAllByOrderByMaNdktAsc();
        return ResultList.success(ecList.stream().map(paramMapper::toEconomicContentRes).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<TaxTypeRes> listTaxType() {
        List<TccDmSthueHqaEntity> taxList = tccDmSthueHqaRepository.findAllByOrderByMaSthueAsc();
        return ResultList.success(taxList.stream().map(paramMapper::toTaxTypeRes).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<CustomsCurrencyRes> listCustomsCurrency() {
        List<TccDmLoaitienhqaEntity> ccList = tccDmLoaitienhqaRepository.findAllByOrderByMaLthqAsc();
        return ResultList.success(ccList.stream().map(paramMapper::toCustomsCurrencyRes).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<ExportImportType> listExportImportType() {
        List<TccDmLhxnkEntity> ieList = tccDmLhxnkRepository.findAllByOrderByMaLhAsc();
        return ResultList.success(ieList.stream().map(paramMapper::toExportImportType).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<TreasuryRes> listTreasury() {
        List<TccDmKhobacEntity> treasuryList = tccDmKhobacRepository.findAllByBk01OrderByShkbAsc(StatusEnum.ACTIVE.getValue());
        return ResultList.success(treasuryList.stream().map(paramMapper::toTreasuryRes).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<RevenueAccountRes> listRevenueAccount() {
        String priorityListString = AppContext.getProperty(AppConstants.PRIORITY_LIST_REV_ACC);
        List<String> priorityList = StringUtils.isNotBlank(priorityListString) ?
                Arrays.stream(priorityListString.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .toList() :
                List.of();
        List<TccDmTkNsnnEntity> accountList = tccDmTkNsnnRepository.findAllByOrderByMaTkAsc(priorityList);
        return ResultList.success(accountList.stream().map(paramMapper::toRevenueAccountRes).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<RevenueAuthorityRes> listRevenueAuthority(RevenueAuthorityReq req) {
        List<TccDmCqthuEntity> revAuthList = tccDmCqthuRepository.findAllByOrderByMaCqthuAsc(req.getTreasuryCode());
        return ResultList.success(revAuthList.stream().map(paramMapper::toRevenueAuthorityRes).toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ResultList<AdministrativeAreaRes> listAdministrativeArea(AdministrativeAreaReq req) {
        List<TccDmDbhcEntity> admAreaList = new ArrayList<>();
        if (StringUtils.isBlank(req.getTreasuryCode())) {
            admAreaList = tccDmDbhcRepository.findAllByOrderByMaDbhcAsc(null);
        } else {
            admAreaList = tccDmKhobacRepository.findByShkb(req.getTreasuryCode())
                    .map(TccDmKhobacEntity::getMaDbhc)
                    .map(maDbhc -> tccDmDbhcRepository.findAllByOrderByMaDbhcAsc(maDbhc))
                    .orElse(List.of());
        }

        return ResultList.success(admAreaList.stream()
                .map(paramMapper::toAdministrativeAreaRes)
                .toList());
    }

    @Override
    public Result<TreasuryDetailRes> detailTreasury(TreasuryDetailReq req) {
        List<MappingTreasuryBenBankCodeDto> mappingTreasuryBb = getMappingTreasuryBbByCode(List.of(req.getTreasuryCode()));
        MappingTreasuryBenBankCodeDto treasuryInfo = mappingTreasuryBb.stream()
                .filter(mapping -> mapping.getTreasuryCode().equals(req.getTreasuryCode()))
                .findFirst()
                .orElse(null);

        if (treasuryInfo == null) {
            return Result.error(ResponseCode.RESOURCE_NOTFOUND);
        }

        return Result.success(TreasuryDetailRes.builder()
                .treasuryCode(treasuryInfo.getTreasuryCode())
                .benBankCode(treasuryInfo.getBenBankCode())
                .benBankName(treasuryInfo.getBenBankName())
                .isInBidv(treasuryInfo.isInBidv())
                .build());
    }

    /**
     * Creates a mapping between treasury code (SHKB) and bank code (MA_NH).
     * The mapping is created using the following logic:
     * 1. First attempts to get MA_NH directly from TCC_DM_KHOBAC table for each SHKB
     * 2. For any SHKB where MA_NH is null, finds the first available MA_NH from TCC_DM_KBNN_NHTM table
     * where BANK_CODE is not 202 and MA_NH is not null
     *
     * @param treasuryEntityList List of treasury entities from TCC_DM_KHOBAC table
     * @return Map where key is treasury code (SHKB) and value is bank code (MA_NH)
     */
    @Override
    public List<MappingTreasuryBenBankCodeDto> getMappingTreasuryBb(List<TccDmKhobacEntity> treasuryEntityList) {
        List<MappingTreasuryBenBankCodeDto> treasuryBbList = new ArrayList<>();

        treasuryEntityList.forEach(entity -> {
            if (StringUtils.isNotBlank(entity.getMaNh())) {
                treasuryBbList.add(MappingTreasuryBenBankCodeDto.builder()
                        .treasuryCode(entity.getShkb())
                        .benBankCode(entity.getMaNh())
                        .benBankName(Optional.ofNullable(entity.getTccDmKbnnNhtmEntity()).map(TccDmKbnnNhtmEntity::getTenNh).orElse(null))
                        .debtAccCode(entity.getTknoNsnn())
                        .isInBidv(true)
                        .build());
            }
        });

        List<String> treasuriesWithoutBankCode = treasuryEntityList.stream()
                .filter(treasury -> StringUtils.isBlank(treasury.getMaNh()))
                .map(TccDmKhobacEntity::getShkb)
                .collect(Collectors.toList());

        if (!treasuriesWithoutBankCode.isEmpty()) {
            List<TccDmKbnnNhtmEntity> bankTreasuryRelations = tccDmKbnnNhtmRepository
                    .findActiveBankByShkbWithoutCode(treasuriesWithoutBankCode, AppConstants.BANK_CODE_202);

            bankTreasuryRelations.forEach(relation -> {
                MappingTreasuryBenBankCodeDto existingValue = treasuryBbList.stream()
                        .filter(treasury -> treasury.getTreasuryCode().equals(relation.getShkb()))
                        .findFirst()
                        .orElse(null);
                if (existingValue == null) {
                    String debtAccCode = treasuryEntityList.stream().filter(treasury -> treasury.getShkb().equals(relation.getShkb()))
                            .findFirst()
                            .map(TccDmKhobacEntity::getTknoNsnn)
                            .orElse(null);

                    treasuryBbList.add(MappingTreasuryBenBankCodeDto.builder()
                            .treasuryCode(relation.getShkb())
                            .benBankCode(relation.getMaNh())
                            .benBankName(relation.getTenNh())
                            .debtAccCode(debtAccCode)
                            .isInBidv(false)
                            .build());
                }
            });
        }

        return treasuryBbList;
    }

    @Override
    public List<MappingTreasuryBenBankCodeDto> getMappingTreasuryBbByCode(List<String> treasuryCodes) {
        List<TccDmKhobacEntity> treasuryList = tccDmKhobacRepository.findByShkbIn(treasuryCodes, StatusEnum.ACTIVE.getValue());
        return getMappingTreasuryBb(treasuryList);
    }

    @Override
    public Result<TccDmCqthuEntity> checkRevAuthInTreasury(String revAuthCode, String treasuryCode) {
        TccDmCqthuEntity tccDmCqthuEntity = tccDmCqthuRepository.findByMaCqthuAndShkb(revAuthCode, treasuryCode).orElse(null);
        if (tccDmCqthuEntity == null) {
            return Result.error(ResponseCode.REV_AUTH_NOT_IN_TREASURY);
        }
        return Result.success(tccDmCqthuEntity);
    }

    @Override
    public MappingTreasuryBenBankCodeDto getMappingTreasuryBenBankCode(String treasuryCode) {
        return getMappingTreasuryBbByCode(List.of(treasuryCode)).stream()
                .filter(mapping -> mapping.getTreasuryCode().equals(treasuryCode))
                .findFirst()
                .orElse(new MappingTreasuryBenBankCodeDto());
    }
}
