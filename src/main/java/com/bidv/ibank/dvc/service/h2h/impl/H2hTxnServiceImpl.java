package com.bidv.ibank.dvc.service.h2h.impl;

import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.service.gov.GovTxnService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnListRes;
import com.bidv.ibank.dvc.service.gov.GovTxnReportService;
import com.bidv.ibank.dvc.service.h2h.H2hTxnService;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.mapper.ModelMapperUtils;
import com.bidv.ibank.dvc.model.request.TxnDetailReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hTxnDetailRes;
import com.bidv.ibank.dvc.service.customsduty.TxnService;
import com.bidv.ibank.framework.domain.response.Result;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class H2hTxnServiceImpl implements H2hTxnService {

    private final GovTxnReportService govTxnReportService;
    private final GovTxnService govTxnService;
    private final TxnService txnService;

    @Override
    public ResultList<H2hTxnListRes> list(TxnReportListReq req) {
        ResultList<TxnReportListRes> txnResultList = govTxnReportService.listReport(req);
        if (!txnResultList.isSuccess()) {
            return ResultList.error(txnResultList.getCode(), txnResultList.getMessage());
        }

        return ResultList.success(txnResultList.getData().getItems().stream().map(txn -> ModelMapperUtils.map(txn, H2hTxnListRes.class)).toList(),
                txnResultList.getData().getTotal());
    }

    @Override
    public Result<H2hTxnDetailRes> detail(TxnDetailReq req) {
        Result<TxnDetailRes> txnDetailResult = txnService.detail(req);

        if (!txnDetailResult.isSuccess()) {
            return Result.error(txnDetailResult.getCode(), txnDetailResult.getMessage());
        }

        return Result.success(ModelMapperUtils.map(txnDetailResult.getData(), H2hTxnDetailRes.class));
    }

    @Override
    public Result<ExportFileRes> print(TxnPrintDocumentReq req) {
        return govTxnService.print(req);
    }
}
