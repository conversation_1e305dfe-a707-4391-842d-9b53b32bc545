package com.bidv.ibank.dvc.service.gov.impl;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;

import com.bidv.ibank.client.common.dto.workflow.WfTxnInfoResponse;
import com.bidv.ibank.dvc.model.entity.customsduty.VwTxnPendingApprovalEntity;
import com.bidv.ibank.dvc.model.request.TxnPendingApprovalListReq;
import com.bidv.ibank.dvc.model.request.TxnPrintDocumentReq;
import com.bidv.ibank.dvc.model.response.TxnPendingApprovalListRes;
import com.bidv.ibank.dvc.repository.customsduty.VwTxnPendingApprovalRepository;

import com.bidv.ibank.framework.context.AppContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import com.bidv.ibank.client.common.dto.masterdata.AuthAccountDto;
import com.bidv.ibank.common.txn.model.dto.ConfirmResDto;
import com.bidv.ibank.common.txn.model.dto.InitTransactionRes;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionResponse;
import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.common.txn.util.constant.TransactionActionEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.dto.TccDmCqthuCodeNameKhobacEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmDbhcCodeNameEntityDto;
import com.bidv.ibank.dvc.model.dto.TccDmTkNsnnStkEntityDto;
import com.bidv.ibank.dvc.model.dto.TxnExportReportDto;
import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentDto;
import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.mapper.customsduty.DocumentMapper;
import com.bidv.ibank.dvc.model.mapper.customsduty.GOVPaymentTransactionMapper;
import com.bidv.ibank.dvc.model.request.TxnRejectReq;
import com.bidv.ibank.dvc.model.request.TxnApproveReq;
import com.bidv.ibank.dvc.model.request.TxnConfirmReq;
import com.bidv.ibank.dvc.model.request.TxnInitPushReq;
import com.bidv.ibank.dvc.model.request.TxnDeleteReq;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.dvc.model.response.TxnRejectRes;
import com.bidv.ibank.dvc.model.response.ExportFileRes;
import com.bidv.ibank.dvc.model.response.TxnApprovalResultRes;
import com.bidv.ibank.dvc.model.response.TxnInitApproveRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.TxnProcessResultRes;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentItemRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTransactionRepository;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTxnTransferRepositoty;
import com.bidv.ibank.dvc.repository.param.TccDmCqthuRepository;
import com.bidv.ibank.dvc.repository.param.TccDmDbhcRepository;
import com.bidv.ibank.dvc.repository.param.TccDmTkNsnnRepository;
import com.bidv.ibank.dvc.service.common.CommonService;
import com.bidv.ibank.dvc.service.common.FileService;
import com.bidv.ibank.dvc.service.common.ValidationService;
import com.bidv.ibank.dvc.service.gov.GovTxnService;
import com.bidv.ibank.dvc.specification.BaseSpecifications;
import com.bidv.ibank.dvc.specification.TxnSpecifications;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.service.customsduty.KafkaService;
import com.bidv.ibank.dvc.service.gov.GovTxnAccountingService;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.ServiceGroupTypeEnum;
import com.bidv.ibank.dvc.util.constant.StatusEnum;
import com.bidv.ibank.dvc.util.constant.TxnPushTypeEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.domain.response.ResultList;
import com.bidv.ibank.framework.util.FileUtils;
import com.bidv.ibank.framework.util.ObjectUtils;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.integrate.entity.file.FileInfo;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;
import com.bidv.ibank.util.JasperReportUtils;
import com.bidv.ibank.util.excel.ExcelExporter;
import com.bidv.ibank.framework.util.mapper.ModelMapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@Validated
@RequiredArgsConstructor
public class GovTxnServiceImpl implements GovTxnService {

    private final CommonService commonService;
    private final FileService fileService;
    private final KafkaService kafkaService;
    private final ValidationService validationService;
    private final DocumentMapper documentMapper;
    private final GOVPaymentTxnTransferRepositoty govPaymentTxnTransferReposototy;
    private final IntegrateServiceFactory integrateServiceFactory;
    private final GovTxnAccountingService govTxnAccountingService;
    private final GOVPaymentTransactionRepository govPaymentTransactionRepository;
    private final GOVPaymentItemRepository govPaymentItemRepository;
    private final TccDmDbhcRepository tccDmDbhcRepository;
    private final TccDmCqthuRepository tccDmCqthuRepository;
    private final TccDmTkNsnnRepository tccDmTkNsnnRepository;
    private final GOVPaymentTransactionMapper govPaymentTransactionMapper;
    private final VwTxnPendingApprovalRepository vwTxnPendingApprovalRepository;

    @Override
    @Transactional
    public Result<String> delete(TxnDeleteReq req) {
        List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());
        List<GOVPaymentTransactionEntity> govTxnEntityList = new ArrayList<>();

        if (req.getTxnIds().size() == 1) {
            govTxnEntityList = govPaymentTransactionRepository.findAll(
                    Specification.where(BaseSpecifications.<GOVPaymentTransactionEntity>idIn(req.getTxnIds())));

            if (govTxnEntityList.isEmpty()) {
                return Result.error(ResponseCode.TXN_NOT_FOUND);
            }
            GOVPaymentTransactionEntity govTxnEntity = govTxnEntityList.get(0);
            if (!Objects.equals(govTxnEntity.getStatus(), TransactionStatusEnum.INIT.name()) &&
                    !Objects.equals(govTxnEntity.getStatus(), TransactionStatusEnum.REJECTED.name())) {
                return Result.error(ResponseCode.TRANS_STATUS);
            }
            if (!Objects.equals(govTxnEntity.getCreatedBy(), AuthenticationUtils.getCurrentUser().getUsername())) {
                return Result.error(ResponseCode.TRANS_INFO);
            }
            if (!accounts.stream().map(AuthAccountDto::getAccountNo).toList().contains(govTxnEntity.getDebitAccNo())) {
                return Result.error(ResponseCode.USER_ACCOUNT_INVALID);
            }
        } else {
            govTxnEntityList = govPaymentTransactionRepository.findAll(
                    Specification.where(BaseSpecifications.<GOVPaymentTransactionEntity>idIn(req.getTxnIds()))
                            .and(TxnSpecifications.statusIn(List.of(TransactionStatusEnum.INIT.name(), TransactionStatusEnum.REJECTED.name())))
                            .and(BaseSpecifications.<GOVPaymentTransactionEntity>createdByEq(AuthenticationUtils.getCurrentUser().getUsername()))
                            .and(TxnSpecifications.debitAccNoIn(accounts.stream().map(AuthAccountDto::getAccountNo).toList())));

            if (govTxnEntityList.size() != req.getTxnIds().size()) {
                return Result.error(ResponseCode.TIMEOUT_01);
            }
        }

        try {
            List<String> txnIds = govTxnEntityList.stream().map(GOVPaymentTransactionEntity::getId).toList();
            govPaymentTransactionRepository.updateStatus(txnIds, TransactionStatusEnum.DELETED.name(), AuthenticationUtils.getCurrentUser().getUsername());
        } catch (Exception e) {
            log.error("Error saving transactions: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }

        return Result.success(null);
    }

    @Override
    public Result<TxnInitPushRes> initPush(TxnInitPushReq request) {
        if (Objects.equals(request.getType(), TxnPushTypeEnum.PUSH_SAVE) || Objects.equals(request.getType(), TxnPushTypeEnum.PUSH_EDIT)) {
            Result<InitTransactionRes<GOVPaymentTransactionEntity>> result = commonService.initTransaction(request.getTransKey(),
                    TransactionActionEnum.REQUEST_APPROVAL);
            if (!result.isSuccess()) {
                return Result.error(result.getCode(), result.getMessage());
            }
            return Result.success(govPaymentTransactionMapper.toInitPushRes(result.getData()));
        }

        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = commonService.requestApprovalTransaction(request.getTxnIds());
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }
        return Result.success(govPaymentTransactionMapper.toInitPushRes(result.getData()));
    }

    @Override
    @Transactional
    public Result<TxnProcessResultRes> confirmPush(TxnConfirmReq request) {
        Result<ConfirmResDto<GOVPaymentTransactionEntity>> result = commonService.confirmAuthTransaction(
                request.getTransKey(),
                request.getConfirmValue(),
                TransactionActionEnum.REQUEST_APPROVAL);

        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }

        List<GOVPaymentTransactionEntity> successTxns = result.getData().getSuccessfulTransactions();
        List<TransactionResDetail> failTxns = result.getData().getFailedTransactions();

        if (CollectionUtils.isNotEmpty(successTxns)) {
            try {
                List<GOVPaymentItemEntity> govPaymentItemEntities = new ArrayList<>();
                successTxns.forEach(govPaymentTransactionEntity -> {
                    if (!govPaymentTransactionEntity.getPaymentItems().isEmpty()) {
                        govPaymentItemEntities.addAll(
                                govPaymentTransactionEntity.getPaymentItems()
                                        .stream()
                                        .map(e -> {
                                            e.setTxnId(govPaymentTransactionEntity.getId());
                                            return e;
                                        })
                                        .toList());
                    }
                });

                if (!govPaymentItemEntities.isEmpty()) {
                    govPaymentItemRepository.deleteByTxnIds(successTxns.stream().map(GOVPaymentTransactionEntity::getId).toList());
                    govPaymentItemRepository.saveAll(govPaymentItemEntities);
                }
            } catch (Exception e) {
                log.error("Error saving transactions when confirm push: {}", e.getMessage());
                successTxns.forEach(item -> failTxns.add(TransactionResDetail.builder()
                        .txnId(item.getId())
                        .message(ResponseCode.TIMEOUT_01.message())
                        .build()));
                successTxns.clear();
            }
        }

        return Result.success(getTxnProcessResultDtoResult(successTxns, failTxns, result.getData().getTransaction()));
    }

    @Override
    public Result<TxnRejectRes> reject(TxnRejectReq request) {
        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = commonService.rejectTransaction(request.getTxnIds(), request
                .getApprovalNote());
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }

        List<GOVPaymentTransactionEntity> safeSuccessTxns = new ArrayList<>(CollectionUtils.emptyIfNull(result.getData().getSuccessfulTransactions()));
        List<TransactionResDetail> safeFailTxns = new ArrayList<>(CollectionUtils.emptyIfNull(result.getData().getFailedTransactions()));

        TxnRejectRes rejectTxnRes = TxnRejectRes.builder()
                .total((long) (safeSuccessTxns.size() + safeFailTxns.size()))
                .totalSuccess((long) safeSuccessTxns.size())
                .failTxns(safeFailTxns)
                .build();

        if (rejectTxnRes.getTotal() == 1) {
            return Result.success(govPaymentTransactionMapper.toRejectDto(rejectTxnRes, result.getData().getTransaction()));
        }

        return Result.success(rejectTxnRes);
    }

    @Override
    public ResultList<TxnPendingApprovalListRes> listPendingApproval(TxnPendingApprovalListReq req) {
        List<VwTxnPendingApprovalEntity> vWTxnEntityList = vwTxnPendingApprovalRepository.findByAssignee(
                AuthenticationUtils.getCurrentUser().getUsername());
        if (CollectionUtils.isEmpty(vWTxnEntityList)) {
            return ResultList.success(Collections.emptyList(), 0L);
        }

        List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());
        Pageable pageable = QueryUtils.buildPageRequest(req, Sort.Order.desc("priority"), Sort.Order.desc("updatedDate"),
                Sort.Order.desc("createdDate"), Sort.Order.desc("id"));
        Page<GOVPaymentTransactionEntity> govTxnEntityList = govPaymentTransactionRepository.findAll(
                TxnSpecifications.createTxnPendingApprovalListSpec(req)
                        .and(TxnSpecifications.txnIdIn(vWTxnEntityList.stream().map(VwTxnPendingApprovalEntity::getTxnId).toList()))
                        .and(TxnSpecifications.debitAccNoIn(accounts.stream().map(AuthAccountDto::getAccountNo).toList())),
                pageable);

        return ResultList.success(govTxnEntityList.stream().map(govPaymentTransactionMapper::toPendingApprovalListRes).toList(),
                govTxnEntityList.getTotalElements());
    }

    @Override
    public Result<ExportFileRes> export(TxnExportReq req) {
        try {
            List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());
            Result<List<TxnExportReportDto>> result = govPaymentTxnTransferReposototy.findTxnSummary(
                    req,
                    AuthenticationUtils.getCurrentUser().getUser().getCusId(),
                    accounts.stream().map(AuthAccountDto::getAccountNo).toList(),
                    List.of(TransactionStatusEnum.DELETED.name()));
            if (!result.isSuccess()) {
                return Result.error(result.getCode(), result.getMessage());
            }

            List<TxnExportReportDto> finalList = formatTransactionExportData(result.getData());
            String rawName = AppConstants.TXN_EXPORT_FILE_NAME;
            String fileNameTranslator = Translator.toLocale(AppConstants.LANGUAGE.GOV_EXPORT_FILE_NAME + "." + rawName, rawName);
            String fileName = FileUtils.formatFileName(fileNameTranslator, FileUtils.EXCEL_EXTENSION);
            FileInfo fileInfo = ExcelExporter.exportExcel(
                    AppConstants.TXN_EXPORT_TEMPLATE_PATH,
                    AppConstants.TXN_EXPORT_START_ROW,
                    AppConstants.TXN_EXPORT_SHIFT_ROW,
                    govPaymentTransactionMapper.toExportParams(req),
                    finalList,
                    TxnExportReportDto.class,
                    true,
                    integrateServiceFactory.getFileService().createFileHubWriter(fileName, true));

            return Result.success(ExportFileRes.builder()
                    .url(fileInfo.getFileUrl())
                    .build());
        } catch (Exception e) {
            log.error("Error export transactions: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    @Override
    public Result<ExportFileRes> print(TxnPrintDocumentReq request) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            long maxItemPrint = AppContext.getProperty(AppConstants.MAX_PRINT, 9L);
            if (request.getTxnIds().size() > maxItemPrint) {
                return Result.error(ResponseCode.MAX_TXN_PRINT, String.valueOf(maxItemPrint));
            }
            List<AuthAccountDto> accounts = commonService.getAuthorizedAccounts(CoreAccTypeEnum.DDA.name(), ServiceGroupTypeEnum.ACC_FIN.name());
            List<TxnPrintDocumentDto> txnPrintDtos = govPaymentTransactionRepository.findDocumentsByIds(
                    request.getTxnIds(),
                    TransactionStatusEnum.DELETED.name(),
                    accounts.stream().map(AuthAccountDto::getAccountNo).toList(),
                    StatusEnum.ACTIVE.getValue());
            if (txnPrintDtos.isEmpty()) {
                return Result.error(ResponseCode.TXN_NOT_FOUND);
            }
            if (request.getTxnIds().size() > AppConstants.NUMBER.ONE) {
                Optional<TxnPrintDocumentDto> successDto = txnPrintDtos.stream()
                        .filter(txn -> txn.getStatus().equals(TransactionStatusEnum.SUCCESS.name()))
                        .findFirst();
                if (successDto.isPresent())
                    return Result.error(ResponseCode.STATUS_PRINT_INVALID);
            }
            ResultList<WfTxnInfoResponse> activityResult = commonService.getTxnActivity(
                    txnPrintDtos.stream().map(TxnPrintDocumentDto::getApprovalWfId).toList());
            if (!activityResult.isSuccess()) {
                return Result.error(activityResult.getCode(), activityResult.getMessage());
            }

            List<TxnPrintDocumentItemDto> txnPrintDocumentItemList = govPaymentItemRepository.findDocumentItemsByTxnIds(request.getTxnIds());
            List<JasperReportUtils.BatchExportParam> batchExportParams = documentMapper.toBatchExportParams(
                    txnPrintDtos, txnPrintDocumentItemList, activityResult.getData().getItems());
            JasperReportUtils.exportAsPdf(batchExportParams, Translator.getLocale(), outputStream);

            if (txnPrintDtos.size() == 1 && Objects.equals(TransactionStatusEnum.SUCCESS.name(), txnPrintDtos.get(0).getStatus())) {
                long totalElements = txnPrintDtos.get(0).getTxnPrintDocumentItems().size();
                byte[] signatureReport = commonService.addDigitalSignature(
                        AppConstants.HO_BRANCH_PREFIX,
                        outputStream.toByteArray(),
                        AppConstants.DOCUMENT_PARAM_NAME.SIGNATURE_MAX_HEIGHT -
                                (totalElements > 2 ?
                                        (totalElements - 2) * AppConstants.DOCUMENT_PARAM_NAME.DETAIL_ITEMS_HEIGHT + AppConstants.DOCUMENT_PARAM_NAME.TOTAL_AMOUNT_HEIGHT :
                                        0),
                        0);
                if (signatureReport == null)
                    return Result.error(ResponseCode.SIGNATURE_ERROR);
                outputStream.reset();
                outputStream.write(signatureReport);
            }
            Result<FileInfo> uploadResult = fileService.uploadToS3(
                    outputStream,
                    FileUtils.formatFileName(AppConstants.DOCUMENT_FILE_NAME, FileUtils.PDF_EXTENSION),
                    true);
            if (!uploadResult.isSuccess()) {
                return Result.error(uploadResult.getCode(), uploadResult.getMessage());
            }

            return Result.success(ExportFileRes
                    .builder()
                    .url(uploadResult.getData().getFileUrl())
                    .build());
        } catch (Exception e) {
            log.error("Error printing documents: {}", e.getMessage());
            return Result.error(ResponseCode.TIMEOUT_01);
        } finally {
            ObjectUtils.close(outputStream);
        }
    }

    @Override
    public Result<TxnInitApproveRes> initApproval(TxnApproveReq request) {
        Result<ProcessTransactionResponse<GOVPaymentTransactionEntity>> result = commonService
                .approveTransaction(request.getTxnIds(), validationService::validateAfterInitApproval);
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }

        ProcessTransactionResponse<GOVPaymentTransactionEntity> response = result.getData();
        return Result.success(TxnInitApproveRes.builder()
                .requireAuth(response.isRequireAuth())
                .transAuth(response.getTransAuth())
                .transKey(response.getTransKey())
                .build());
    }

    @Override
    public Result<TxnApprovalResultRes> confirmApproval(TxnConfirmReq request) {
        Result<ConfirmResDto<GOVPaymentTransactionEntity>> result = commonService.confirmAuthTransaction(
                request.getTransKey(),
                request.getConfirmValue(),
                TransactionActionEnum.APPROVAL);
        if (!result.isSuccess()) {
            return Result.error(result.getCode(), result.getMessage());
        }

        List<GOVPaymentTransactionEntity> successTxns = result.getData().getSuccessfulTransactions();
        List<TransactionResDetail> failTxns = new ArrayList<>(result.getData().getFailedTransactions());
        GOVPaymentTransactionEntity transaction = result.getData().getTransaction();

        TxnApprovalResultRes approvalResultRes = ModelMapperUtils.map(
                getTxnProcessResultDtoResult(successTxns, failTxns, transaction),
                TxnApprovalResultRes.class);
        if (transaction != null) {
            // Reload txn to retrieve approvedDate
            GOVPaymentTransactionEntity txnEntity = govPaymentTransactionRepository.findById(transaction.getId()).orElse(null);
            approvalResultRes.setApprovedDate(txnEntity != null ? txnEntity.getApprovedDate() : null);
            approvalResultRes.setIsFinal(transaction.isFinal());
        }

        return Result.success(processFinalApproval(approvalResultRes, successTxns, transaction));
    }

    private TxnProcessResultRes getTxnProcessResultDtoResult(
            List<GOVPaymentTransactionEntity> successTxns,
            List<TransactionResDetail> failTxns,
            GOVPaymentTransactionEntity transaction) {
        List<GOVPaymentTransactionEntity> safeSuccessTxns = new ArrayList<>(CollectionUtils.emptyIfNull(successTxns));
        List<TransactionResDetail> safeFailTxns = new ArrayList<>(CollectionUtils.emptyIfNull(failTxns));

        TxnProcessResultRes result = TxnProcessResultRes.builder()
                .total((long) (safeSuccessTxns.size() + safeFailTxns.size()))
                .totalSuccess((long) safeSuccessTxns.size())
                .failTxns(safeFailTxns)
                .build();

        if (result.getTotal() == 1) {
            List<TccDmDbhcCodeNameEntityDto> admAreaList = tccDmDbhcRepository.findDbhcByCodes(Optional.ofNullable(transaction.getMaDbhc())
                    .map(Collections::singletonList)
                    .orElse(Collections.emptyList()));

            List<TccDmCqthuCodeNameKhobacEntityDto> revAuthList = tccDmCqthuRepository.findCqthuByCodes(
                    StatusEnum.ACTIVE.getValue(),
                    Optional.ofNullable(transaction.getMaCqthu())
                            .map(Collections::singletonList)
                            .orElse(Collections.emptyList()));

            List<TccDmTkNsnnStkEntityDto> revAccList = tccDmTkNsnnRepository.findTkNsnnByCodes(
                    Optional.ofNullable(transaction.getMaTk())
                            .map(Collections::singletonList)
                            .orElse(Collections.emptyList()));

            result.setTreasuryName(revAuthList.isEmpty() ? transaction.getShkb() : revAuthList.get(0).getTenKhobac());
            result.setRevAuthName(revAuthList.isEmpty() ? transaction.getMaCqthu() : revAuthList.get(0).getTenCqthu());
            result.setRevAccName(revAccList.isEmpty() ? transaction.getMaTk() : revAccList.get(0).getTen());
            result.setAdmAreaName(admAreaList.isEmpty() ? transaction.getMaDbhc() : admAreaList.get(0).getTenDbhc());
            return govPaymentTransactionMapper.toProcessResultDto(result, transaction);
        }
        return result;
    }

    private TxnApprovalResultRes processFinalApproval(
            TxnApprovalResultRes txnApprovalResultRes,
            List<GOVPaymentTransactionEntity> successTxns,
            GOVPaymentTransactionEntity transaction) {

        List<GOVPaymentTransactionEntity> safeSuccessTxns = new ArrayList<>(CollectionUtils.emptyIfNull(successTxns));
        // Lấy ra các giao dịch với isFinal = true và trạng thái = BANK_PROCESSING
        List<GOVPaymentTransactionEntity> successFinalTxns = safeSuccessTxns.stream()
                .filter(e -> e.isFinal() && Objects.equals(e.getStatus(), TransactionStatusEnum.BANK_PROCESSING.name()))
                .toList();

        if (!successFinalTxns.isEmpty()) {
            if (txnApprovalResultRes.getTotal() == 1) {
                Result<String> result = govTxnAccountingService.processAccounting(transaction.getId());
                if (!result.isSuccess()) {
                    txnApprovalResultRes.setFailTxns(List.of(TransactionResDetail.builder()
                            .txnId(transaction.getId())
                            .code(result.getCode())
                            .message(result.getMessage())
                            .build()));
                    txnApprovalResultRes.setTotalSuccess(0L);
                } else {
                    txnApprovalResultRes.setUnderBalanceFlag(result.getData());
                }
            } else {
                kafkaService.sendAccountingMessage(successFinalTxns);
            }
        }
        return txnApprovalResultRes;
    }

    private List<TxnExportReportDto> formatTransactionExportData(List<TxnExportReportDto> transactions) {
        Map<String, AtomicInteger> txnCountMap = new HashMap<>();
        transactions.forEach(txn -> txnCountMap.merge(txn.getTxnId(), new AtomicInteger(1), (oldVal, newVal) -> {
            oldVal.incrementAndGet();
            return oldVal;
        }));
        Set<String> processedTxnIds = new HashSet<>();
        Map<String, AtomicInteger> subIndexMap = new HashMap<>();
        List<TxnExportReportDto> formattedList = new ArrayList<>();
        AtomicInteger mainIndex = new AtomicInteger(1);

        for (TxnExportReportDto txn : transactions) {
            String txnId = txn.getTxnId();
            int count = txnCountMap.get(txnId).get();

            if (count == AppConstants.NUMBER.ONE) {
                txn.setOrder(String.valueOf(mainIndex.getAndIncrement()));
                formattedList.add(txn);
                continue;
            }

            if (processedTxnIds.add(txnId)) {
                TxnExportReportDto parent = new TxnExportReportDto(txn);
                parent.setOrder(String.valueOf(mainIndex.getAndIncrement()));
                formattedList.add(parent);
            }

            int groupIndex = mainIndex.get() - AppConstants.NUMBER.ONE;
            int subIndex = subIndexMap.computeIfAbsent(txnId, id -> new AtomicInteger()).incrementAndGet();
            txn.setOrder(String.format("%d.%d", groupIndex, subIndex));
            txn.setFeeTotal(null);
            formattedList.add(txn);
        }

        return formattedList;
    }
}
