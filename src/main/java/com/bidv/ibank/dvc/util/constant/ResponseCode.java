package com.bidv.ibank.dvc.util.constant;

import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.framework.util.result.IResponseCode;

public enum ResponseCode implements IResponseCode {
    SUCCESS("0", "Success"),
    SESSION_TIMEOUT_01("GOV0001", "<PERSON><PERSON>n đăng nhập hết hiệu lực hoặc tài khoản đăng nhập thiết bị khác. Quý khách vui lòng thực hiện đăng nhập lại"),
    TIMEOUT_01("GOV0002", "<PERSON><PERSON> lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại"),
    INPUT_01("GOV0003", "Dữ liệu không hợp lệ"),
    RESOURCE_NOTFOUND("GOV0004", "Dữ liệu không tồn tại hoặc bị xóa"),

    USER_PRODUCT_INVALID("GOV0006", "<PERSON>u<PERSON> khách chưa được phân quyền sử dụng chức năng. <PERSON><PERSON> lòng kiểm tra lại"),
    DOC_TAX_EXIST("GOV0007", "Giao dịch đã tồn tại chứng từ thuế"),
    TXN_NOT_FOUND("GOV0008", "Không tìm thấy giao dịch. Vui lòng kiểm tra lại."),
    USER_ACCOUNT_INVALID("GOV0009", "Phân quyền tài khoản không hợp lệ để xử lý yêu cầu. Vui lòng kiểm tra lại."),
    TRANS_STATUS("GOV0010", "Thông tin và trạng thái giao dịch không hợp lệ. Vui lòng kiểm tra lại"),
    TRANS_INFO("GOV0011", "Thông tin giao dịch không hợp lệ để xử lý. Vui lòng kiểm tra lại."),
    REV_AUTH_NOT_IN_TREASURY("GOV0012", "Mã cơ quan thu không thuộc Kho bạc nhà nước."),
    CURRENCY_NOT_MATCH("GOV0013", "Quý khách vui lòng chọn tài khoản trích nợ có loại tiền giống với loại tiền nộp thuế."),
    FILE_UPLOAD_01("GOV0014", "File không đúng định dạng, vui lòng kiểm tra lại"),
    FILE_SIZE_EXCEEDED("GOV0015", "File upload quá dung lượng tối đa cho phép"),
    ITEM_EXCEEDED("GOV0016", "File upload không được vượt quá {0} bản ghi"),
    FILE_NAME_INVALID("GOV0017", "Tên file không được chứa ký tự đặc biệt trừ ký tự _ -.()"),
    FILE_EXISTS("GOV0018", "Tệp đính kèm đã tồn tại. Quý khách vui lòng kiểm tra lại"),
    EMPTY_FILE("GOV0019", "Không có nội dung giao dịch trong file. Quý khách vui lòng thử lại"),
    LIMIT_CIF("GOV0021", "Vượt hạn mức khách hàng từng giao dịch"),
    LIMIT_ACC("GOV0022", "Vượt hạn mức tài khoản từng giao dịch"),
    PROD_TRANSCODE_NOTFOUND("GOV0023", "Không tìm thấy sản phẩm"),
    UNDEFINED("GOV0024",
            "Giao dịch chưa xác định trạng thái. Quý khách vui lòng kiểm tra biến động số dư tài khoản và báo cáo giao dịch trước khi thực hiện lại. Hoặc liên hệ hotline 19009248 để được hỗ trợ."),
    UPD_STATUS_RETRY_EXCEEDED("GOV0025", "Quý khách đã sử dụng chức năng cập nhật quá 3 lần cho phép. Vui lòng liên hệ Ngân hàng để được hỗ trợ"),
    UPD_STATUS_RETRY_EXCEEDED_01("GOV0026", "Đã cập nhật trạng thái. Quý khách còn {0} lần cập nhật"),
    INVALID_APPROVE_INTERRUPT_TIME("GOV0027", "Giao dịch đang trong giờ gián đoạn. Vui lòng thử lại sau."),
    INVALID_APPROVE_EFF_DATE("GOV0028", "Quá ngày được phép xử lý giao dịch"),
    INVALID_APPROVE_OVER_DRAFT("GOV0029", "Tài khoản không được phép sử dụng nguồn thấu chi. Vui lòng kiểm tra lại"),
    INSUFFICIENT_MIN_BALANCE("GOV0030", "Tài khoản không đủ số dư tối thiểu. Vui lòng kiểm tra lại"),
    INSUFFICIENT_BALANCE("GOV0031", "Tài khoản không đủ số dư. Vui lòng kiểm tra lại"),
    TEMPLATE_EXISTS("GOV0032", "Mẫu giao dịch đã tồn tại trong hệ thống. Vui lòng kiểm tra lại"),
    NOT_FOUND_01("GOV0033", "Không tìm thấy mẫu. Vui lòng kiểm tra lại"),
    TEMP_INFO_01("GOV0034", "Thông tin mẫu không hợp lệ để xử lý yêu cầu. Vui lòng kiểm tra lại"),
    SC_PURPOSE_CODE_INVALID("GOV0035", "Tài khoản có mã sản phẩm/mã mục đích không hợp lệ"),
    DEBIT_ACC_RELATION_INVALID("GOV0036", "Mã quan hệ tài khoản trích nợ không hợp lệ"),
    EXIST_INVALID_APPROVE_EFF_DATE("GOV0037", "Tồn tại giao dịch quá ngày được phép xử lý giao dịch"),
    MAX_TAX_ITEMS_EXCEED("GOV0038", "Quý khách chỉ được chọn tối đa 5 khoản nộp trong 1 giao dịch"),
    TAX_ITEMS_DUPLICATED("GOV0039", "Tồn tại khoản nộp bị trùng. Quý khách vui lòng kiểm tra lại"),
    TAX_CODE_MISMATCH_01("GOV0040", "MST vấn tin không khớp đúng với thông tin của doanh nghiệp. Vui lòng kiểm tra lại"),
    SIGNATURE_ERROR("GOV0041", "Có lỗi trong quá trình ký số trên chứng từ. Vui lòng thử lại"),
    STATUS_PRINT_INVALID("GOV0042", "Quý khách chỉ được in 1 giao dịch ở trạng thái thành công"),
    MAX_TXN_PRINT("GOV0043", "Số lượng giao dịch tối đa được phép xử lý là {0}"),

    // Error code for validate batch
    DEBIT_ACCNO_REQUIRED("GOV0100", "Trường tài khoản chuyển không được để trống"),
    DEBIT_ACCNO_INVALID("GOV0101", "Tài khoản trích nợ không hợp lệ"),
    DEBIT_ACCNO_CCY_MISMATCH("GOV0102", "Quý khách vui lòng chọn tài khoản trích nợ có loại tiền giống với loại tiền nộp thuế"),
    DEBIT_ACCNO_EXCEED_MAX_LENGTH("GOV0103", "Trường tài khoản chuyển quá số ký tự cho phép. Số ký tự cho phép là 14"),
    DEBIT_ACCNO_INVALID_FORMAT("GOV0104", "Trường tài khoản chuyển không cho phép ký tự đặc biệt"),

    TAX_CODE_REQUIRED("GOV0105", "Trường mã số thuế người nộp thuế không được để trống"),
    TAX_CODE_EXCEED_MAX_LENGTH("GOV0106", "Trường mã số thuế người nộp thuế quá số ký tự cho phép. Số ký tự cho phép là 20"),
    TAX_CODE_INVALID_FORMAT("GOV0107", "Trường mã số thuế người nộp thuế không cho phép ký tự đặc biệt trừ @&()-._,/"),
    TAX_CODE_MISMATCH("GOV0108", "Mã số thuế người nộp thuế không khớp"),
    TAX_CODE_DUPLICATE("GOV0109", "Mã số thuế người nộp thuế không được giống mã số thuế người nộp thay"),

    PAYER_NAME_REQUIRED("GOV0110", "Trường họ tên người nộp thuế không được để trống"),
    PAYER_NAME_EXCEED_MAX_LENGTH("GOV0111", "Trường họ tên người nộp thuế quá số ký tự cho phép. Số ký tự cho phép là 140"),
    PAYER_NAME_INVALID_FORMAT("GOV0112", "Trường họ tên người nộp thuế không cho phép ký tự đặc biệt trừ @&()-._,/"),

    PAYER_ADDR_REQUIRED("GOV0113", "Trường địa chỉ người nộp thuế không được để trống"),
    PAYER_ADDR_EXCEED_MAX_LENGTH("GOV0114", "Trường địa chỉ người nộp thuế quá số ký tự cho phép. Số ký tự cho phép là 140"),
    PAYER_ADDR_INVALID_FORMAT("GOV0115", "Trường địa chỉ người nộp thuế không cho phép ký tự đặc biệt trừ @&()-._,/"),

    ALT_TAX_CODE_EXCEED_MAX_LENGTH("GOV0116", "Trường mã số thuế người nộp thay quá số ký tự cho phép. Số ký tự cho phép là 20"),
    ALT_TAX_CODE_INVALID_FORMAT("GOV0117", "Trường mã số thuế người nộp thay không cho phép ký tự đặc biệt trừ @&()-._,/"),
    ALT_TAX_CODE_MISMATCH("GOV0118", "Mã số thuế người nộp thay không khớp"),

    ALT_PAYER_NAME_REQUIRED("GOV0119", "Trường họ tên người nộp thay không được để trống"),
    ALT_PAYER_NAME_EXCEED_MAX_LENGTH("GOV0120", "Trường họ tên người nộp thay quá số ký tự cho phép. Số ký tự cho phép là 140"),
    ALT_PAYER_NAME_INVALID_FORMAT("GOV0121", "Trường họ tên người nộp thay không cho phép ký tự đặc biệt trừ @&()-._,/"),

    ALT_PAYER_ADDR_REQUIRED("GOV0122", "Trường địa chỉ người nộp thay không được để trống"),
    ALT_PAYER_ADDR_EXCEED_MAX_LENGTH("GOV0123", "Trường địa chỉ người nộp thay quá số ký tự cho phép. Số ký tự cho phép là 140"),
    ALT_PAYER_ADDR_INVALID_FORMAT("GOV0124", "Trường địa chỉ người nộp thay không cho phép ký tự đặc biệt trừ @&()-._,/"),

    DECLARATION_NO_REQUIRED("GOV0125", "Trường số tờ khai/số quyết định/số thông báo không được để trống"),
    DECLARATION_NO_EXCEED_MAX_LENGTH("GOV0126", "Trường số tờ khai/số quyết định/số thông báo quá số ký tự cho phép. Số ký tự cho phép là 30"),
    DECLARATION_NO_INVALID_FORMAT("GOV0127", "Trường số tờ khai/số quyết định/số thông báo không cho phép ký tự đặc biệt trừ @&()-._,/"),

    DECLARATION_DATE_REQUIRED("GOV0128", "Trường ngày quyết định/ngày đăng kí/ngày thông báo không được để trống"),
    DECLARATION_DATE_EXCEED_MAX_LENGTH("GOV0129", "Trường ngày quyết định/ngày đăng kí/ngày thông báo quá số ký tự cho phép. Số ký tự cho phép là 10"),
    DECLARATION_DATE_INVALID_FORMAT("GOV0130", "Trường ngày quyết định/ngày đăng kí/ngày thông báo nhập sai định dạng"),
    DECLARATION_DATE_INVALID_DATE("GOV0131", "Không được nhập ngày tờ khai là ngày tương lai"),

    TREASURY_CODE_REQUIRED("GOV0132", "Trường mã - tên kho bạc không được để trống"),
    TREASURY_CODE_EXCEED_MAX_LENGTH("GOV0133", "Trường mã - tên kho bạc quá số ký tự cho phép. Số ký tự cho phép là 4"),
    TREASURY_CODE_INVALID_FORMAT("GOV0134", "Trường mã - tên kho bạc nhập sai định dạng"),

    REV_ACC_CODE_REQUIRED("GOV0135", "Trường số TK thu NSNN không được để trống"),
    REV_ACC_CODE_EXCEED_MAX_LENGTH("GOV0136", "Trường số TK thu NSNN quá số ký tự cho phép. Số ký tự cho phép là 50"),
    REV_ACC_CODE_INVALID_FORMAT("GOV0137", "Trường số TK thu NSNN nhập sai định dạng"),

    REV_AUTH_CODE_REQUIRED("GOV0138", "Trường mã - tên cơ quan thu không được để trống"),
    REV_AUTH_CODE_EXCEED_MAX_LENGTH("GOV0139", "Trường mã - tên cơ quan thu quá số ký tự cho phép. Số ký tự cho phép là 7"),
    REV_AUTH_CODE_INVALID_FORMAT("GOV0140", "Trường mã - tên cơ quan thu nhập sai định dạng"),
    REV_AUTH_MISMATCH_TREASURY("GOV0141", "Mã cơ quan thu không thuộc Kho bạc nhà nước"),

    ADM_AREA_CODE_REQUIRED("GOV0142", "Trường mã - tên địa bàn hành chính không được để trống"),
    ADM_AREA_CODE_EXCEED_MAX_LENGTH("GOV0143", "Trường mã - tên địa bàn hành chính quá số ký tự cho phép. Số ký tự cho phép là 5"),
    ADM_AREA_CODE_INVALID_FORMAT("GOV0144", "Trường mã - tên địa bàn hành chính nhập sai định dạng"),

    CHAPTER_CODE_REQUIRED("GOV0145", "Trường mã chương không được để trống"),
    CHAPTER_CODE_EXCEED_MAX_LENGTH("GOV0146", "Trường mã chương quá số ký tự cho phép. Số ký tự cho phép là 3"),
    CHAPTER_CODE_INVALID_FORMAT("GOV0147", "Trường mã chương nhập sai định dạng"),

    EC_CODE_REQUIRED("GOV0148", "Trường mã nội dung kinh tế không được để trống"),
    EC_CODE_EXCEED_MAX_LENGTH("GOV0149", "Trường mã nội dung kinh tế quá số ký tự cho phép. Số ký tự cho phép là 4"),
    EC_CODE_INVALID_FORMAT("GOV0150", "Trường mã nội dung kinh tế nhập sai định dạng"),

    TAX_TYPE_CODE_REQUIRED("GOV0151", "Trường sắc thuế không được để trống"),
    TAX_TYPE_CODE_EXCEED_MAX_LENGTH("GOV0152", "Trường sắc thuế quá số ký tự cho phép. Số ký tự cho phép là 10"),
    TAX_TYPE_CODE_INVALID_FORMAT("GOV0153", "Trường sắc thuế nhập sai định dạng"),

    CC_CODE_REQUIRED("GOV0154", "Trường loại tiền HQ không được để trống"),
    CC_CODE_EXCEED_MAX_LENGTH("GOV0155", "Trường loại tiền HQ quá số ký tự cho phép. Số ký tự cho phép là 20"),
    CC_CODE_INVALID_FORMAT("GOV0156", "Trường loại tiền HQ nhập sai định dạng"),

    EI_TYPE_CODE_REQUIRED("GOV0157", "Trường loại hình XNK không được để trống"),
    EI_TYPE_CODE_EXCEED_MAX_LENGTH("GOV0158", "Trường loại hình XNK quá số ký tự cho phép. Số ký tự cho phép là 20"),
    EI_TYPE_CODE_INVALID_FORMAT("GOV0159", "Trường loại hình XNK nhập sai định dạng"),

    PAYER_TYPE_REQUIRED("GOV0160", "Trường mã loại hình người nộp thuế không được để trống"),
    PAYER_TYPE_EXCEED_MAX_LENGTH("GOV0161", "Trường mã loại hình người nộp thuế quá số ký tự cho phép. Số ký tự cho phép là 1"),
    PAYER_TYPE_INVALID_FORMAT("GOV0162", "Trường mã loại hình người nộp thuế nhập sai định dạng"),

    AMOUNT_REQUIRED("GOV0163", "Trường số tiền không được để trống"),
    AMOUNT_EXCEED_MAX_LENGTH("GOV0164", "Trường số tiền quá số ký tự cho phép. Số ký tự cho phép là 15"),
    AMOUNT_INVALID_FORMAT("GOV0165", "Sai định dạng số tiền chuyển"),
    AMOUNT_CANNOT_BE_ZERO("GOV0166", "Số tiền không được bằng 0"),
    AMOUNT_EXCEED_MAX_BALANCE("GOV0167", "Số tiền giao dịch vượt giá trị giao dịch tối đa được phép của sản phẩm. Vui lòng nhập lại"),
    AMOUNT_BELOW_MIN_BALANCE("GOV0168", "Số tiền giao dịch nhỏ hơn giá trị giao dịch tối thiểu được phép của sản phẩm. Vui lòng nhập lại"),

    CCY_REQUIRED("GOV0169", "Trường loại tiền không được để trống"),
    CCY_MUST_BE_VND("GOV0170", "Đơn vị tiền phải là VNĐ"),

    TRANS_DESC_REQUIRED("GOV0171", "Trường diễn giải giao dịch không được để trống"),
    TRANS_DESC_EXCEED_MAX_LENGTH("GOV0172", "Trường diễn giải giao dịch quá số ký tự cho phép. Số ký tự cho phép là 210"),
    TRANS_DESC_INVALID_FORMAT("GOV0173", "Trường diễn giải giao dịch không cho phép ký tự đặc biệt trừ @&()-._,/"),

    BATCH_DUPLICATE_RECORD("GOV0174", "Có bản ghi trùng"),

    DECLARATION_YEAR_EXCEED_MAX_LENGTH("GOV0175", "Trường năm đăng ký quá số ký tự cho phép. Số ký tự cho phép là 4"),
    DECLARATION_YEAR_INVALID_FORMAT("GOV0176", "Trường năm đăng ký sai định dạng"),
    DECLARATION_YEAR_INVALID_YEAR("GOV0177", "Không được nhập năm đăng ký là năm tương lai"),

    ORG_ID_EXCEED_MAX_LENGTH("GOV0178", "Trường mã giao dịch khách hàng quá số ký tự cho phép. Số ký tự cho phép là 30"),
    ORG_ID_INVALID_FORMAT("GOV0179", "Trường mã giao dịch khách hàng không cho phép ký tự đặc biệt trừ -_"),

    AMOUNT_WITH_DOT_EXCEED_MAX_LENGTH("GOV0180", "Trường số tiền quá số ký tự cho phép. Số ký tự cho phép là 16"),
    INQUIRY_DECLARATION_NO_REQUIRED("GOV0181", "Trường số tờ khai hải quan không được để trống"),
    INQUIRY_DECLARATION_NO_EXCEED_MAX_LENGTH("GOV0182", "Trường số tờ khai hải quan quá số ký tự cho phép. Số ký tự cho phép là 30"),
    INQUIRY_DECLARATION_NO_INVALID_FORMAT("GOV0183", "Trường số tờ khai hải quan không cho phép ký tự đặc biệt trừ @&()-.+_,/");

    private final String code;

    ResponseCode(String code, String message) {
        this.code = code;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String message() {
        return Translator.toLocale("response.code." + code);
    }
}
