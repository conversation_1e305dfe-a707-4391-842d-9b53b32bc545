package com.bidv.ibank.dvc.util.excel.converter;

import com.bidv.ibank.util.excel.converter.Converter;
import com.bidv.ibank.framework.util.Utils;

public class ExtractCodeFromTextConverter implements Converter<String, String> {
   public ExtractCodeFromTextConverter() {
   }

   public String convert(String t) throws Exception {
      if (Utils.isEmpty(t)) {
         return null;
      } else {
         String[] arr = t.split("-");
         return arr[0].trim();
      }
   }
}
