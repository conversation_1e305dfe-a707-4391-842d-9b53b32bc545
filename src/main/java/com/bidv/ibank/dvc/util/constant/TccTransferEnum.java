package com.bidv.ibank.dvc.util.constant;

import java.util.Arrays;

public enum TccTransferEnum {
    SUCCESS("0"),
    FAILED("1");

    private final String value;

    TccTransferEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static String fromValue(String value) {
        return Arrays.stream(TccTransferEnum.values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(FAILED).name();
    }
}
