package com.bidv.ibank.dvc.util.excel.converter;

import com.bidv.ibank.util.excel.converter.Converter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class FormatNumberExcelConverter implements Converter<String, String> {

    @Override
    public String convert(String value) {
        if (value == null) {
            return null;
        }
        
        // Remove quotes and trim
        value = StringUtils.strip(value.trim(), "\"");
        
        try {
            // Handle parentheses for negative numbers
            boolean isNegative = value.startsWith("(") && value.endsWith(")");
            if (isNegative) {
                value = "-" + value.substring(1, value.length() - 1);
            }
            
            // Remove commas and any other non-numeric characters except decimal point and minus sign
            String cleanedValue = value.replaceAll("[^\\d.-]", "");
            
            // Parse as BigDecimal
            BigDecimal number = new BigDecimal(cleanedValue);
            
            // Handle zero case
            if (number.compareTo(BigDecimal.ZERO) == 0) {
                return "0";
            }
            
            // Format the number without trailing zeros
            return number.stripTrailingZeros().toPlainString();
            
        } catch (NumberFormatException e) {
            return value.trim();
        }
    }

}
