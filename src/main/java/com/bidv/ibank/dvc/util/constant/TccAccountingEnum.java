package com.bidv.ibank.dvc.util.constant;

import java.util.Arrays;

public enum TccAccountingEnum {
    SUCCESS("0"),
    UNDEFINED("8"),
    FAILED("9");

    private final String value;

    TccAccountingEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static String fromValue(String value) {
        return Arrays.stream(TccAccountingEnum.values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(UNDEFINED).name();
    }
}
