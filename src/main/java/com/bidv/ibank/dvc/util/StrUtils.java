package com.bidv.ibank.dvc.util;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;

public class StrUtils {

    public static String combineCodeName(String code, String name) {
        if (StringUtils.isBlank(code) && StringUtils.isBlank(name)) {
            return "";
        }
        if (StringUtils.isBlank(code)) {
            return StringUtils.defaultString(name);
        }
        if (StringUtils.isBlank(name)) {
            return StringUtils.defaultString(code);
        }
        return StringUtils.defaultString(code) + " - " + StringUtils.defaultString(name);
    }

    public static String formatWithThousandSeparator(String value) {
        if (StringUtils.isBlank(value)) return "";
        try {
            BigDecimal decimalValue = new BigDecimal(value);
            return formatWithThousandSeparator(decimalValue);
        } catch (NumberFormatException e) {
            return value;
        }
    }

    public static String formatWithThousandSeparator(BigDecimal value) {
        if (value == null) {
            return "";
        }
        NumberFormat formatter = NumberFormat.getNumberInstance(Locale.US); // You can specify the locale
        return formatter.format(value);
    }

    public static String concatNonBlank(String... strings) {
        return Stream.of(strings)
                .filter(s -> s != null && !s.trim().isEmpty())
                .collect(Collectors.joining(";"));
    }

    public static List<String> splitAndTrim(String str, String delimiter, List<String> defaultValues) {
        if (StringUtils.isBlank(str)) {
            return defaultValues;
        }
        return Stream.of(str.split(delimiter))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }
}