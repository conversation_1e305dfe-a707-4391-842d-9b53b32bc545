package com.bidv.ibank.dvc.util;

import com.bidv.ibank.dvc.util.constant.ResponseCode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AppConstants {

    public static final int THRESHOLD_MINUTES = 30;
    public static final String DATE_FORMAT_DD_MM_YYYY = "dd/MM/yyyy";
    public static final String DATE_FORMAT_DD_MM_YYYY_HH_MM_SS = "dd/MM/yyyy HH:mm:ss";
    public static final String DATE_FORMAT_HH_MM_SS_DD_MM_YYYY = "HH:mm:ss dd/MM/yyyy";
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DATE_FORMAT_HH_MM_SS = "HH:mm:ss";
    public static final String DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS";

    public static final String DVC_UPLOAD_MAX_ITEM = "dvc.upload.file.item.max";
    public static final String DVC_UPLOAD_MAX_FILE_SIZE = "dvc.upload.file.size.max";
    public static final String DVC_JOB_RETRY_MAX = "dvc.job.retry.max";
    public static final String ACCOUNT_DEBIT_PURPOSE = "txn.account.debit.purpose";
    public static final String ACCOUNT_ALLOW_ACCT_RELATION = "txn.account.allow.debit.acctRelation";
    public static final String PURPOSE_CODE_NOT_CHECK = "txn.purpose.uncheck";
    public static final String PRIORITY_LIST_REV_ACC = "bo.dvc.priority.list.acc";
    public static final String MAX_PRINT = "max.num.records.report.export";
    public static final List<String> PURPOSE_CODE_NOT_CHECK_LIST = List.of("BP1", "BP4");

    public static final String HO_BRANCH_PREFIX = "990";

    public static final int TXN_EXPORT_START_ROW = 14;
    public static final int TXN_EXPORT_SHIFT_ROW = 1;

    public static final int BATCH_DOWNLOAD_RESULT_START_ROW = 4;
    public static final int BATCH_DOWNLOAD_RESULT_SHIFT_ROW = 0;

    public static final int BATCH_TAX_RESULT_START_ROW = 5;
    public static final int BATCH_TAX_INQUIRY_RESULT_START_ROW = 4;
    public static final int BATCH_TAX_FILE_START_ROW = 5;

    public static final String TXN_EXPORT_TEMPLATE_PATH = "templates/txn_report.xlsx";
    public static final String BATCH_TXN_TEMPLATE_PATH = "templates/batch_txn.xlsx";
    public static final String BATCH_RESULT_TEMPLATE_PATH = "templates/batch_result.xlsx";
    public static final String BATCH_TAX_TEMPLATE_PATH = "templates/batch_tax.xlsx";
    public static final String BATCH_TAX_RESULT_TEMPLATE_PATH = "templates/batch_tax_result.xlsx";
    public static final String BATCH_TAX_INQUIRY_RESULT_TEMPLATE_PATH = "templates/batch_tax_inquiry_result.xlsx";

    public static final String BATCH_TAX_TEMPLATE_FILE_NAME = "TEMP_VAN_TIN_THUE_XNK_INQUIRE_TAX";
    public static final String BATCH_IMPORT_TEMPLATE_FILE_NAME = "TEMP_IMPORT_THUE_XNK";

    public static final String TXN_EXPORT_FILE_NAME = "fileName";
    public static final String BATCH_DOWNLOAD_RESULT_FILE_NAME = "KET_QUA_KIEM_TRA_BK NOP THUE XNK_Import  tax file validation.xlsx";
    public static final String BATCH_TAX_RESULT_SUFFIX_FILE_NAME = "_KHONG_THANH_CONG.xlsx";
    public static final String BATCH_TAX_INQUIRY_RESULT_FILE_NAME = "KET_QUA_VAN_TIN_CUSTOM_TAX_INFORMATION_RESULT.xlsx";

    public static final Pattern BATCH_IMPORT_VALID_PATTERN = Pattern.compile("^[A-zA-Z0-9À-ỹ_.\\-() ]+$");

    public static class LANGUAGE {
        public static final String TAX_PAYER_TYPE = "tax.payer.type";
        public static final String RESPONSE_CODE = "response.code";
        public static final String TXN_STATUS = "txn.status";
        public static final String BATCH_STATUS = "batch.status";
        public static final String BATCH_ITEM_STATUS = "batch.item.status";
        public static final String BATCH_TAX_CHECKED_STATUS = "batch.tax.checked.status";
        public static final String TXN_TYPE = "txn.type";
        public static final String TXN_FEE_OPT = "txn.fee.opt";
        public static final String ACCOUNTING_STATUS = "accounting.status";
        public static final String CUSTOMS_CONN_STATUS = "customs.conn.status";
        public static final String GOV_EXPORT_FILE_NAME = "gov.export";
    }

    public static class REGEX_VALIDATION {
        public static final String NUMBER_AND_DOT = "^(?:\\d+(?:\\.\\d{1,2})?|\\d+)$";
        public static final String NUMBER_AND_LETTER = "^[A-Za-z0-9]*$";
        public static final String NUMBER_AND_LETTER_AND_COMMA_AND_DOT = "^[A-Za-z0-9,.]*$";
        public static final String NUMBER_AND_LETTER_AND_LINE = "^[A-Za-z0-9-]*$";
        public static final String NUMBER_AND_LETTER_AND_LINE_AND_DOT = "^[A-Za-z0-9.-]*$";
        public static final String NUMBER_AND_LETTER_AND_LINE_AND_DOT_SPACE = "^[A-Za-z0-9.\\- ]*$";
        public static final String NUMBER_AND_LETTER_AND_LINE_AND_UNDERSCORE_SPACE = "^[A-Za-z0-9-_ ]*$";
        public static final String NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE = "^[a-zA-Z0-9@&-.()_,/]*$";
        public static final String NUMBER_AND_LETTER_AND_SPEC_CHAR_SPACE = "^[a-zA-Z0-9@&-.()_,/ ]*$";
        public static final String NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE = "^[a-zA-Z0-9À-ỹ\\s\\-_() ]*$";
        public static final String NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED = "^[a-zA-Z0-9À-ỹ\\s@&().\\-_,/ ]*$";
        public static final String NUMBER = "^[0-9]*$";
        public static final String LETTER = "^[A-Za-z]*$";
        public static final String DAY_MONTH_YEAR = "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/([0-9]{4})$";
        public static final String YEAR_PATTERN = "^\\d{4}$";
    }

    public static final String BANK_CODE_202 = "202";

    public static class NUMBER {
        public static final int ZERO = 0;
        public static final int ONE = 1;
        public static final int THREE = 3;
        public static final int FIVE = 5;
    }

    public static class UNDER_BALANCE_FLAG {
        public static final String YES = "Y";
        public static final String NO = "N";
    }

    public static class TEMPLATE_STATUS {
        public static final String ACTIVE = "ACTIVE";
        public static final String INACTIVE = "INACTIVE";
    }

    public static class BATCH_FIELD_LENGTH {
        public static final int MAX_DEBIT_ACC_NO_LENGTH = 14;
        public static final int MAX_TAX_CODE_LENGTH = 20;
        public static final int MAX_PAYER_NAME_LENGTH = 140;
        public static final int MAX_PAYER_ADDR_LENGTH = 140;
        public static final int MAX_DECLARATION_NO_LENGTH = 30;
        public static final int MAX_DECLARATION_DATE_LENGTH = 10;
        public static final int MAX_DECLARATION_YEAR_LENGTH = 4;
        public static final int MAX_AMOUNT_LENGTH = 15;
        public static final int MAX_AMOUNT_WITH_DOT_LENGTH = 16;
        public static final int MAX_TRANS_DESC_LENGTH = 210;
        public static final int MAX_TREASURY_CODE_LENGTH = 4;
        public static final int MAX_REV_ACC_CODE_LENGTH = 50;
        public static final int MAX_REV_AUTH_CODE_LENGTH = 7;
        public static final int MAX_ADM_AREA_CODE_LENGTH = 5;
        public static final int MAX_CHAPTER_CODE_LENGTH = 3;
        public static final int MAX_EC_CODE_LENGTH = 4;
        public static final int MAX_TAX_TYPE_CODE_LENGTH = 10;
        public static final int MAX_CC_CODE_LENGTH = 20;
        public static final int MAX_EI_TYPE_CODE_LENGTH = 20;
        public static final int MAX_CCY_LENGTH = 3;
        public static final int MAX_PAYER_TYPE_LENGTH = 1;
        public static final int MAX_ORG_ID_LENGTH = 30;
        public static final int MAX_BEN_BANK_CODE_LENGTH = 8;

        public static final int MAX_SEARCH_LENGTH = 70;
        public static final int MAX_SEARCH_BATCH_NO_LENGTH = 50;
        public static final int MAX_RA_NOTE_LENGTH = 100;
        public static final int MAX_TEMPLATE_NAME_LENGTH = 40;
        public static final int MAX_BATCH_NO_LENGTH = 20;
        public static final int MAX_AMOUNT_RANGE_LENGTH = 18;
        public static final int MAX_BATCH_NAME_LENGTH = 30;
        public static final int MAX_APPROVAL_NOTE_LENGTH = 100;
        public static final int MAX_PAYMENT_ITEM_LENGTH = 30;
        public static final int MAX_UUID_LENGTH = 36;
        public static final int MAX_TXN_ID_LENGTH = 50;
        public static final int MAX_TRANS_KEY_LENGTH = 150;
        public static final int MAX_TXN_TYPE_LENGTH = 2;
        public static final int MAX_TXN_ITEM_ID_LENGTH = 30;
        public static final int MAX_TCC_REF_NO_LENGTH = 35;
    }

    public static final String DOCUMENT_JASPER_FIRST_PAGE_PATH = "templates/doc_first_page.jasper";
    public static final String DOCUMENT_JASPER_SECOND_PAGE_PATH = "templates/doc_second_page.jasper";
    public static final String DOCUMENT_LOGO = "/templates/logo.png";
    public static final String TICK_MARK = "/templates/tick.png";
    public static final String DOCUMENT_FILE_NAME = "Giay nop tien vao Ngan sach Nha nuoc";

    public static class DOCUMENT_PARAM_NAME {
        public static final String TICK = "tick";
        public static final String TCC_DOC_SIGN = "tccDocSign";
        public static final String TCC_DOC_NO = "tccDocNo";
        public static final String REF_NUMBER = "refNumber";
        public static final String PAYER_NAME = "payerName";
        public static final String TAX_CODE = "taxCode";
        public static final String PAYER_ADDR = "payerAddr";
        public static final String ALT_TAX_CODE = "altTaxCode";
        public static final String ALT_PAYER_NAME = "altPayerName";
        public static final String ALT_PAYER_ADDR = "altPayerAddr";
        public static final String DEBIT_ACC_NO = "debitAccNo";
        public static final String REV_AUTH_NAME = "revAuthName";
        public static final String REV_ACC_CODE = "revAccCode";
        public static final String REV_ACC_NAME = "revAccName";
        public static final String LOGO_URL = "logoUrl";
        public static final String TXN_PRINT_DOCUMENT_ITEM = "txnPrintDocumentItem";
        public static final String AMOUNT_TEXT = "amountText";
        public static final String REV_AUTH_CODE = "revAuthCode";
        public static final String PAYMENT_DATE = "paymentDate";
        public static final String PMT_TIME = "pmtTime";
        public static final String TOTAL_AMOUNT = "totalAmount";
        public static final String DEBIT_AMOUNT = "debitAmount";
        public static final String FEE_TOTAL = "feeTotal";
        public static final String FEE_VAT = "feeVat";
        public static final String PAYMENT_MONTH = "paymentMonth";
        public static final String PAYMENT_YEAR = "paymentYear";
        public static final String TAX_AMOUNT = "taxAmount";
        public static final String TREASURY_NAME = "treasuryName";
        public static final String BEN_BANK_NAME = "benBankName";
        public static final String ADM_AREA_CODE = "admAreaCode";
        public static final String STATUS_TXN_TEXT = "statusTxnText";
        public static final String ITEM_TABLE = "itemTable";
        public static final String DEBIT_ACC = "debitAcc";
        public static final String CHIEF_ACCT = "chiefAcct";
        public static final String OWNER_ACCT = "ownerAcct";
        public static final String STATUS_TEXT = "Giao dịch chưa được hoàn tất xử lý";
        public static final String PAYER_TEXT = "NGƯỜI NỘP TIỀN";
        public static final String CHIEF_ACCT_TEXT = "KẾ TOÁN TRƯỞNG";
        public static final String OWNER_ACCT_TEXT = "THỦ TRƯỞNG ĐƠN VỊ";
        public static final String TEXT_TCC_DOC_NO = "Số/No: ";
        public static final long DETAIL_ITEMS_HEIGHT = 52;
        public static final long TOTAL_AMOUNT_HEIGHT = 50;
        public static final long SIGNATURE_MAX_HEIGHT = 455;
    }

    public static class BATCH_FIELD_CODE {
        public static final String DEBIT_ACC_NO = "debitAccNo";
        public static final String TAX_CODE = "taxCode";
        public static final String ALT_TAX_CODE = "altTaxCode";
        public static final String PAYER_NAME = "payerName";
        public static final String ALT_PAYER_NAME = "altPayerName";
        public static final String PAYER_ADDR = "payerAddr";
        public static final String ALT_PAYER_ADDR = "altPayerAddr";
        public static final String DECLARATION_NO = "declarationNo";
        public static final String DECLARATION_DATE = "declarationDate";
        public static final String TREASURY_CODE = "treasuryCode";
        public static final String REV_ACC_CODE = "revAccCode";
        public static final String REV_AUTH_CODE = "revAuthCode";
        public static final String ADM_AREA_CODE = "admAreaCode";
        public static final String CHAPTER_CODE = "chapterCode";
        public static final String EC_CODE = "ecCode";
        public static final String AMOUNT = "amount";
        public static final String CCY = "ccy";
        public static final String TRANS_DESC = "transDesc";
        public static final String PAYER_TYPE = "payerType";
        public static final String TAX_TYPE_CODE = "taxTypeCode";
        public static final String CC_CODE = "ccCode";
        public static final String EI_TYPE_CODE = "eiTypeCode";
        public static final String DECLARATION_YEAR = "declarationYear";
        public static final String ORG_ID = "orgId";

        // Use for saving code for valid row
        public static final String EXTRA_FIELD_CODE = "extraFieldCode";
    }

    public static class TCC_VALIDATE_DOC {
        public static final String IBANK = "IBANK";
        public static final String BDR = "BDR";
        public static final String CKCA = "CKCA";
        public static final String VOSTRO = "VOSTRO";
        public static final String OL4 = "OL4";
        public static final String _990BDR = "990BDR";
        public static final String BANK_CODE = "********";
        public static final String SUBMISSION_METHOD_CODE = "02";
        public static final String YES = "Y";
        public static final String NO = "N";
    }

    public static class TXN_PUSH_TYPE {
        public static final String PUSH_SAVE = "PUSH_SAVE";
        public static final String PUSH_EDIT = "PUSH_EDIT";
        public static final String PUSH = "PUSH";
    }

    public static class TCC_ACCOUNTING {
        public static final String _990SMB = "990SMB";
        public static final String BRCD = "BRCD";
        public static final String INQ_CODE_ACCOUNTING = "001013";
        public static final String INQ_NAME_ACCOUNTING = "Hach toan";
        public static final String INQ_CODE_RESEND = "000013";
        public static final String INQ_NAME_RESEND = "GUI LAI CHUNG TU";
        public static final String DATA_TYPE = "CTU";
    }

    public static class TAX_INQUIRY_BATCH_FIELD_LENGTH {
        public static final int MAX_DECLARATION_NO_LENGTH = 30;
        public static final int MAX_DECLARATION_YEAR_LENGTH = 4;
    }

    public static final Map<String, String> RESPONSE_CODE_FIELD_CODE_MAP = Map.<String, String>ofEntries(
            Map.entry(ResponseCode.DEBIT_ACCNO_REQUIRED.code(), BATCH_FIELD_CODE.DEBIT_ACC_NO),
            Map.entry(ResponseCode.DEBIT_ACCNO_INVALID.code(), BATCH_FIELD_CODE.DEBIT_ACC_NO),
            Map.entry(ResponseCode.DEBIT_ACCNO_CCY_MISMATCH.code(), BATCH_FIELD_CODE.DEBIT_ACC_NO),
            Map.entry(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.DEBIT_ACC_NO),
            Map.entry(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT.code(), BATCH_FIELD_CODE.DEBIT_ACC_NO),
            Map.entry(ResponseCode.TAX_CODE_REQUIRED.code(), BATCH_FIELD_CODE.TAX_CODE),
            Map.entry(ResponseCode.TAX_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.TAX_CODE),
            Map.entry(ResponseCode.TAX_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.TAX_CODE),
            Map.entry(ResponseCode.TAX_CODE_MISMATCH.code(), BATCH_FIELD_CODE.TAX_CODE),
            Map.entry(ResponseCode.TAX_CODE_DUPLICATE.code(), BATCH_FIELD_CODE.TAX_CODE),
            Map.entry(ResponseCode.PAYER_NAME_REQUIRED.code(), BATCH_FIELD_CODE.PAYER_NAME),
            Map.entry(ResponseCode.PAYER_NAME_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.PAYER_NAME),
            Map.entry(ResponseCode.PAYER_NAME_INVALID_FORMAT.code(), BATCH_FIELD_CODE.PAYER_NAME),
            Map.entry(ResponseCode.PAYER_ADDR_REQUIRED.code(), BATCH_FIELD_CODE.PAYER_ADDR),
            Map.entry(ResponseCode.PAYER_ADDR_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.PAYER_ADDR),
            Map.entry(ResponseCode.PAYER_ADDR_INVALID_FORMAT.code(), BATCH_FIELD_CODE.PAYER_ADDR),
            Map.entry(ResponseCode.ALT_TAX_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.ALT_TAX_CODE),
            Map.entry(ResponseCode.ALT_TAX_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.ALT_TAX_CODE),
            Map.entry(ResponseCode.ALT_TAX_CODE_MISMATCH.code(), BATCH_FIELD_CODE.ALT_TAX_CODE),
            Map.entry(ResponseCode.ALT_PAYER_NAME_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.ALT_PAYER_NAME),
            Map.entry(ResponseCode.ALT_PAYER_NAME_INVALID_FORMAT.code(), BATCH_FIELD_CODE.ALT_PAYER_NAME),
            Map.entry(ResponseCode.ALT_PAYER_NAME_REQUIRED.code(), BATCH_FIELD_CODE.ALT_PAYER_NAME),
            Map.entry(ResponseCode.ALT_PAYER_ADDR_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.ALT_PAYER_ADDR),
            Map.entry(ResponseCode.ALT_PAYER_ADDR_INVALID_FORMAT.code(), BATCH_FIELD_CODE.ALT_PAYER_ADDR),
            Map.entry(ResponseCode.ALT_PAYER_ADDR_REQUIRED.code(), BATCH_FIELD_CODE.ALT_PAYER_ADDR),
            Map.entry(ResponseCode.DECLARATION_NO_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.DECLARATION_NO),
            Map.entry(ResponseCode.DECLARATION_NO_INVALID_FORMAT.code(), BATCH_FIELD_CODE.DECLARATION_NO),
            Map.entry(ResponseCode.DECLARATION_NO_REQUIRED.code(), BATCH_FIELD_CODE.DECLARATION_NO),
            Map.entry(ResponseCode.DECLARATION_DATE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.DECLARATION_DATE),
            Map.entry(ResponseCode.DECLARATION_DATE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.DECLARATION_DATE),
            Map.entry(ResponseCode.DECLARATION_DATE_REQUIRED.code(), BATCH_FIELD_CODE.DECLARATION_DATE),
            Map.entry(ResponseCode.DECLARATION_DATE_INVALID_DATE.code(), BATCH_FIELD_CODE.DECLARATION_DATE),
            Map.entry(ResponseCode.TREASURY_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.TREASURY_CODE),
            Map.entry(ResponseCode.TREASURY_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.TREASURY_CODE),
            Map.entry(ResponseCode.TREASURY_CODE_REQUIRED.code(), BATCH_FIELD_CODE.TREASURY_CODE),
            Map.entry(ResponseCode.REV_ACC_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.REV_ACC_CODE),
            Map.entry(ResponseCode.REV_ACC_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.REV_ACC_CODE),
            Map.entry(ResponseCode.REV_ACC_CODE_REQUIRED.code(), BATCH_FIELD_CODE.REV_ACC_CODE),
            Map.entry(ResponseCode.REV_AUTH_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.REV_AUTH_CODE),
            Map.entry(ResponseCode.REV_AUTH_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.REV_AUTH_CODE),
            Map.entry(ResponseCode.REV_AUTH_CODE_REQUIRED.code(), BATCH_FIELD_CODE.REV_AUTH_CODE),
            Map.entry(ResponseCode.REV_AUTH_MISMATCH_TREASURY.code(), BATCH_FIELD_CODE.REV_AUTH_CODE),
            Map.entry(ResponseCode.ADM_AREA_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.ADM_AREA_CODE),
            Map.entry(ResponseCode.ADM_AREA_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.ADM_AREA_CODE),
            Map.entry(ResponseCode.ADM_AREA_CODE_REQUIRED.code(), BATCH_FIELD_CODE.ADM_AREA_CODE),
            Map.entry(ResponseCode.CHAPTER_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.CHAPTER_CODE),
            Map.entry(ResponseCode.CHAPTER_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.CHAPTER_CODE),
            Map.entry(ResponseCode.CHAPTER_CODE_REQUIRED.code(), BATCH_FIELD_CODE.CHAPTER_CODE),
            Map.entry(ResponseCode.EC_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.EC_CODE),
            Map.entry(ResponseCode.EC_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.EC_CODE),
            Map.entry(ResponseCode.EC_CODE_REQUIRED.code(), BATCH_FIELD_CODE.EC_CODE),
            Map.entry(ResponseCode.AMOUNT_REQUIRED.code(), BATCH_FIELD_CODE.AMOUNT),
            Map.entry(ResponseCode.AMOUNT_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.AMOUNT),
            Map.entry(ResponseCode.AMOUNT_INVALID_FORMAT.code(), BATCH_FIELD_CODE.AMOUNT),
            Map.entry(ResponseCode.AMOUNT_CANNOT_BE_ZERO.code(), BATCH_FIELD_CODE.AMOUNT),
            Map.entry(ResponseCode.AMOUNT_EXCEED_MAX_BALANCE.code(), BATCH_FIELD_CODE.AMOUNT),
            Map.entry(ResponseCode.AMOUNT_BELOW_MIN_BALANCE.code(), BATCH_FIELD_CODE.AMOUNT),
            Map.entry(ResponseCode.CCY_REQUIRED.code(), BATCH_FIELD_CODE.CCY),
            Map.entry(ResponseCode.CCY_MUST_BE_VND.code(), BATCH_FIELD_CODE.CCY),
            Map.entry(ResponseCode.TAX_TYPE_CODE_REQUIRED.code(), BATCH_FIELD_CODE.TAX_TYPE_CODE),
            Map.entry(ResponseCode.TAX_TYPE_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.TAX_TYPE_CODE),
            Map.entry(ResponseCode.TAX_TYPE_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.TAX_TYPE_CODE),
            Map.entry(ResponseCode.CC_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.CC_CODE),
            Map.entry(ResponseCode.CC_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.CC_CODE),
            Map.entry(ResponseCode.CC_CODE_REQUIRED.code(), BATCH_FIELD_CODE.CC_CODE),
            Map.entry(ResponseCode.EI_TYPE_CODE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.EI_TYPE_CODE),
            Map.entry(ResponseCode.EI_TYPE_CODE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.EI_TYPE_CODE),
            Map.entry(ResponseCode.EI_TYPE_CODE_REQUIRED.code(), BATCH_FIELD_CODE.EI_TYPE_CODE),
            Map.entry(ResponseCode.PAYER_TYPE_REQUIRED.code(), BATCH_FIELD_CODE.PAYER_TYPE),
            Map.entry(ResponseCode.PAYER_TYPE_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.PAYER_TYPE),
            Map.entry(ResponseCode.PAYER_TYPE_INVALID_FORMAT.code(), BATCH_FIELD_CODE.PAYER_TYPE),
            Map.entry(ResponseCode.TRANS_DESC_REQUIRED.code(), BATCH_FIELD_CODE.TRANS_DESC),
            Map.entry(ResponseCode.TRANS_DESC_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.TRANS_DESC),
            Map.entry(ResponseCode.TRANS_DESC_INVALID_FORMAT.code(), BATCH_FIELD_CODE.TRANS_DESC),
            Map.entry(ResponseCode.DECLARATION_YEAR_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.DECLARATION_YEAR),
            Map.entry(ResponseCode.DECLARATION_YEAR_INVALID_FORMAT.code(), BATCH_FIELD_CODE.DECLARATION_YEAR),
            Map.entry(ResponseCode.ORG_ID_EXCEED_MAX_LENGTH.code(), BATCH_FIELD_CODE.ORG_ID),
            Map.entry(ResponseCode.ORG_ID_INVALID_FORMAT.code(), BATCH_FIELD_CODE.ORG_ID));
}
