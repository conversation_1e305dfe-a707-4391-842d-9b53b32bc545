package com.bidv.ibank.dvc.util;

import java.util.List;

import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;

public class ParamUtils {

    public static String getEcName(List<TccDmNdktEntity> ecList, String ecCode) {
        return ecList.stream().filter(e -> e.getMaNdkt().equals(ecCode)).findFirst().map(TccDmNdktEntity::getTen).orElse(null);
    }

    public static String getCcName(List<TccDmLoaitienhqaEntity> ccList, String ccCode) {
        return ccList.stream().filter(e -> e.getMaLthq().equals(ccCode)).findFirst().map(TccDmLoaitienhqaEntity::getTenLthq).orElse(null);
    }

    public static String getChapterName(List<TccDmChuongEntity> chapterList, String chapterCode) {
        return chapterList.stream().filter(e -> e.getMaChuong().equals(chapterCode)).findFirst().map(TccDmChuongEntity::getTen).orElse(null);
    }

    public static String getRevAccName(List<TccDmTkNsnnEntity> revAccList, String revAccCode) {
        return revAccList.stream().filter(e -> e.getMaTk().equals(revAccCode)).findFirst().map(TccDmTkNsnnEntity::getTen).orElse(null);
    }

    public static String getTaxTypeName(List<TccDmSthueHqaEntity> taxTypeList, String taxTypeCode) {
        return taxTypeList.stream().filter(e -> e.getMaSthue().equals(taxTypeCode)).findFirst().map(TccDmSthueHqaEntity::getTenSthue).orElse(null);
    }

    public static String getAdmAreaCode(List<TccDmKhobacEntity> treasuryList, String treasuryCode) {
        return treasuryList.stream()
                .filter(e -> e.getShkb().equals(treasuryCode))
                .findFirst()
                .map(TccDmKhobacEntity::getTccDmDbhcEntity)
                .map(dbhc -> dbhc.getMaDbhc())
                .orElse(null);
    }

    public static String getAdmAreaName(List<TccDmKhobacEntity> treasuryList, String treasuryCode) {
        return treasuryList.stream()
                .filter(e -> e.getShkb().equals(treasuryCode))
                .findFirst()
                .map(TccDmKhobacEntity::getTccDmDbhcEntity)
                .map(dbhc -> dbhc.getTen())
                .orElse(null);
    }

    public static String getBenBankCodeByTreasuryCode(List<MappingTreasuryBenBankCodeDto> mappingTreasuryBb, String treasuryCode) {
        return mappingTreasuryBb.stream()
                .filter(e -> e.getTreasuryCode().equals(treasuryCode))
                .findFirst()
                .map(x -> x.getBenBankCode())
                .orElse(null);
    }

    public static String getBenBankNameByTreasuryCode(List<MappingTreasuryBenBankCodeDto> mappingTreasuryBb, String treasuryCode) {
        return mappingTreasuryBb.stream()
                .filter(e -> e.getTreasuryCode().equals(treasuryCode))
                .findFirst()
                .map(x -> x.getBenBankName())
                .orElse(null);
    }
}
