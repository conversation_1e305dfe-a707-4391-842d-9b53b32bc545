package com.bidv.ibank.dvc.util;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ArrUtils {

    /**
     * Distinct a list based on multiple fields.
     */
    @SafeVarargs
    public static <T> List<T> distinctList(List<T> list, Function<? super T, ?>... keyExtractors) {
        final Set<List<?>> seen = ConcurrentHashMap.newKeySet();
        return list.stream()
                .filter(t -> {
                    List<?> keys = Arrays.stream(keyExtractors)
                            .map(ke -> ke.apply(t))
                            .collect(Collectors.toList());
                    return seen.add(keys);
                })
                .collect(Collectors.toList());
    }
}