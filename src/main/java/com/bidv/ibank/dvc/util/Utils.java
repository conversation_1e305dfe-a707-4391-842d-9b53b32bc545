package com.bidv.ibank.dvc.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Utils {

    public static List<Long> convetStringToListLong(String str) {
        if (StringUtils.isBlank(str))
            return new ArrayList<>();

        return Arrays.stream(str.split(","))
                .map(e -> {
                    try {
                        return Long.parseLong(e.trim());
                    } catch (NumberFormatException ex) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();
    }
}
