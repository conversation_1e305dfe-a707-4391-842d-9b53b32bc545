package com.bidv.ibank.dvc.util;

import java.util.Set;

import com.bidv.ibank.dvc.util.constant.CcyEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;

public class CcyUtils {

    private final static Set<String> ccyList = Set.of(
            CcyEnum.VND.name(),
            CcyEnum.CLP.name(),
            CcyEnum.JYP.name(),
            CcyEnum.KRW.name());

    public static String getCodeByCcy(String ccy) {
        if (ccy == null) return "";
        String upperCcy = ccy.toUpperCase();
        return ccyList.contains(upperCcy) ? upperCcy : "";
    }

    public static boolean isValidStringAmount(String amount, String ccy) {
        if (amount == null)
            return true;

        String regex = getRegexAmountByCcy(ccy);
        if (!amount.matches(regex))
            return false;
        return true;
    }

    public static boolean isValidMaxLengthAmount(String amount, String ccy) {
        if (amount == null)
            return true;

        int maxLength = getMaxLengthAmountByCcy(ccy);
        if (amount.length() > maxLength)
            return false;
        return true;
    }

    public static int getMaxLengthAmountByCcy(String ccy) {
        if (ccy != null && ccyList.contains(ccy)) {
            return AppConstants.BATCH_FIELD_LENGTH.MAX_AMOUNT_LENGTH;
        }
        return AppConstants.BATCH_FIELD_LENGTH.MAX_AMOUNT_WITH_DOT_LENGTH;
    }

    public static String getRegexAmountByCcy(String ccy) {
        if (ccy != null && ccyList.contains(ccy)) {
            return AppConstants.REGEX_VALIDATION.NUMBER;
        }
        return AppConstants.REGEX_VALIDATION.NUMBER_AND_DOT;
    }

    public static ResponseCode getResponseCodeByCcy(String ccy) {
        int maxLength = getMaxLengthAmountByCcy(ccy);
        if (maxLength == AppConstants.BATCH_FIELD_LENGTH.MAX_AMOUNT_LENGTH)
            return ResponseCode.AMOUNT_EXCEED_MAX_LENGTH;
        return ResponseCode.AMOUNT_WITH_DOT_EXCEED_MAX_LENGTH;
    }
}