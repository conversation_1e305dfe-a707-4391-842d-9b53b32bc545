package com.bidv.ibank.dvc.util.constant;

public enum BatchItemFieldEnum implements FieldEnum {
    FEE_ACCNO("feeAccNo"),
    FEE_CCY("feeCcy"),
    FEE_OPT("feeOpt"),
    FEE_FREQ("feeFreq"),
    FEE_METHOD("feeMethod"),
    FEE_BRCD("feeBrcd"),
    FEE_TOTAL("feeTotal"),
    FEE_CODE("feeCode"),
    FEE_AMOUNT("feeAmount"),
    FEE_VAT("feeVAT"),
    VAT_RATE("vatRate"),
    FEE_ORIGINAL("feeOriginal");

    private final String fieldCode;

    BatchItemFieldEnum(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    @Override
    public String fieldCode() {
        return fieldCode;
    }
}
