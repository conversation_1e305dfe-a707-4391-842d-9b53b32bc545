package com.bidv.ibank.dvc.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Slf4j
@RequiredArgsConstructor
public class DateUtils {

    public static LocalDate minusWorkingDays(LocalDate date, int workingDays) {
        LocalDate result = date;
        int daysSubtracted = 0;

        while (daysSubtracted < workingDays) {
            result = result.minusDays(1);
            if (!(result.getDayOfWeek() == DayOfWeek.SATURDAY || result.getDayOfWeek() == DayOfWeek.SUNDAY)) {
                daysSubtracted++;
            }
        }

        return result;
    }

    public static LocalDate convertStringToDate(String date, String dateFormat) {
        if (date == null || date.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            return LocalDate.parse(date, formatter);
        } catch (DateTimeParseException e) {
            log.error("convertStringToDate ParseException: {}", e.getMessage());
            return null;
        }
    }

    public static LocalDateTime convertStringToDateTime(String date, String dateFormat) {
        if (date == null || date.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            return LocalDateTime.parse(date, formatter);
        } catch (DateTimeParseException e) {
            log.error("convertStringToDate ParseException: {}", e.getMessage());
            return null;
        }
    }

    public static String convertDateToString(LocalDateTime dateTime, String dateFormat) {
        if (dateTime == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        return formatter.format(dateTime);
    }

    public static String convertDateToString(LocalDateTime dateTime) {
        return convertDateToString(dateTime, AppConstants.DATE_FORMAT_DD_MM_YYYY_HH_MM_SS);
    }

    public static String convertDateToString(LocalDate date, String dateFormat) {
        if (date == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
        return formatter.format(date);
    }

    public static String convertDateToString(LocalDate date) {
        return convertDateToString(date, AppConstants.DATE_FORMAT_DD_MM_YYYY);
    }

    public static Duration parseDuration(String time) {
        if (time == null || time.isEmpty()) {
            return Duration.ZERO;
        }
        try {
            String[] parts = time.split(":");
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);
            return Duration.ofHours(hours).plusMinutes(minutes).plusSeconds(seconds);
        } catch (DateTimeParseException | NumberFormatException | ArrayIndexOutOfBoundsException e) {
            throw new IllegalArgumentException("Invalid time format. Please provide the time in HH:MM:SS format.");
        }
    }

    // Validate date format dd/MM/yyyy
    public static boolean isValidDateFormat(String dateStr) {
        if (dateStr == null) return false;
        // Check format by regex: dd/mm/yyyy (dd: 01-31, mm: 01-12, yyyy: 4 chars)
        if (!dateStr.matches("^\\d{2}/\\d{2}/\\d{4}$")) {
            return false;
        }

        // Check valid date (Date exists)
        SimpleDateFormat sdf = new SimpleDateFormat(AppConstants.DATE_FORMAT_DD_MM_YYYY);
        sdf.setLenient(false); // Không cho phép các giá trị như 32/13/2020
        try {
            sdf.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    public static boolean isValidTxnEffDate(LocalDate txnDate, long maxEffDate) {
        LocalDate currentDate = LocalDate.now();
        if (txnDate.isAfter(currentDate) || txnDate.plusDays(maxEffDate).isBefore(currentDate)) {
            return false;
        }
        return true;
    }
}