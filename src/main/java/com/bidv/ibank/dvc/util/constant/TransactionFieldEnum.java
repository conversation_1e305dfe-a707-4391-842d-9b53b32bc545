package com.bidv.ibank.dvc.util.constant;

import lombok.Getter;

@Getter
public enum TransactionFieldEnum implements FieldEnum {
    TCC_ERR_CODE("tccErrCode"),
    TCC_ERR_DESC("tccErrDesc"),
    STATUS("status"),
    STATE("state"),
    PMT_TIME("pmtTime"),
    FINISH_DATE("finishDate"),
    TCC_DOC_ID("tccDocId"),
    TCC_DOC_SIGN("tccDocSign"),
    TCC_DOC_NO("tccDocNo"),
    TCC_ID_CORE("tccIdCore"),
    CORE_REF("coreRef"),
    TCC_RM_NO("tccRmNo"),
    RETRY_INSFCT_BAL("retryInsfctBal");

    private final String fieldCode;

    TransactionFieldEnum(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    @Override
    public String fieldCode() {
        return fieldCode;
    }
}
