package com.bidv.ibank.dvc.repository.customsduty.impl;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.repository.BaseRepository;
import com.bidv.ibank.dvc.repository.customsduty.GovPaymentBatchItemCustomRepository;
import com.bidv.ibank.dvc.util.constant.BatchItemFieldEnum;
import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class GovPaymentBatchItemCustomRepositoryImpl extends BaseRepository implements GovPaymentBatchItemCustomRepository {

    public GovPaymentBatchItemCustomRepositoryImpl(EntityManager entityManager) {
        super(entityManager);
    }

    @Override
    @Transactional
    public void update(List<GOVPaymentBatchItemEntity> batchItemEntityList) {
        Map<BatchItemFieldEnum, Object> params = new HashMap<>();

        for (GOVPaymentBatchItemEntity batchItemEntity : batchItemEntityList) {
            putIfNotNull(params, BatchItemFieldEnum.FEE_ACCNO, batchItemEntity.getFeeAccNo());
            putIfNotNull(params, BatchItemFieldEnum.FEE_CCY, batchItemEntity.getFeeCcy());
            putIfNotNull(params, BatchItemFieldEnum.FEE_OPT, batchItemEntity.getFeeOpt());
            putIfNotNull(params, BatchItemFieldEnum.FEE_FREQ, batchItemEntity.getFeeFreq());
            putIfNotNull(params, BatchItemFieldEnum.FEE_METHOD, batchItemEntity.getFeeMethod());
            putIfNotNull(params, BatchItemFieldEnum.FEE_BRCD, batchItemEntity.getFeeBrcd());
            putIfNotNull(params, BatchItemFieldEnum.FEE_TOTAL, batchItemEntity.getFeeTotal());
            putIfNotNull(params, BatchItemFieldEnum.FEE_CODE, batchItemEntity.getFeeCode());
            putIfNotNull(params, BatchItemFieldEnum.FEE_AMOUNT, batchItemEntity.getFeeAmount());
            putIfNotNull(params, BatchItemFieldEnum.FEE_VAT, batchItemEntity.getFeeVAT());
            putIfNotNull(params, BatchItemFieldEnum.VAT_RATE, batchItemEntity.getVatRate());
            putIfNotNull(params, BatchItemFieldEnum.FEE_ORIGINAL, batchItemEntity.getFeeOriginal());
            updateByColumn("id", batchItemEntity.getId(), params, GOVPaymentBatchItemEntity.class);
        }
    }
}
