package com.bidv.ibank.dvc.repository.param;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import com.bidv.ibank.dvc.model.dto.TccDmCqthuCodeNameKhobacEntityDto;
import feign.Param;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;

@Repository
public interface TccDmCqthuRepository extends JpaRepository<TccDmCqthuEntity, String> {

    @Query("SELECT t FROM TccDmCqthuEntity t WHERE t.shkb = :treasuryCode ORDER BY t.maCqthu ASC")
    List<TccDmCqthuEntity> findAllByOrderByMaCqthuAsc(@Param("treasuryCode") String treasuryCode);

    Optional<TccDmCqthuEntity> findByMaCqthuAndShkb(String maCqthu, String shkb);

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmCqthuCodeNameKhobacEntityDto(t.shkb, k.ten, t.maCqthu, t.ten) " +
            "FROM TccDmCqthuEntity t " +
            "JOIN t.tccDmKhobacEntity k " +
            "WHERE k.bk01 = :status AND :revAuthCodes IS NULL OR t.maCqthu IN :revAuthCodes" +
            " ORDER BY t.shkb, t.maCqthu ASC")
    List<TccDmCqthuCodeNameKhobacEntityDto> findCqthuByCodes(@Param("status") String status, @Param("revAuthCodes") Collection<String> revAuthCodes);
}