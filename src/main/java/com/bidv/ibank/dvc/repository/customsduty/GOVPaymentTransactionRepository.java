package com.bidv.ibank.dvc.repository.customsduty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentDto;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;

@Repository
public interface GOVPaymentTransactionRepository extends JpaRepository<GOVPaymentTransactionEntity, String>,
                JpaSpecificationExecutor<GOVPaymentTransactionEntity> {

        @Override
        @NonNull
        @EntityGraph(attributePaths = { "tccDmKhobacEntity", "tccDmKhobacEntity.tccDmDbhcEntity", "tccDmKhobacEntity.tccDmKbnnNhtmEntity", "tccDmDbhcEntity",
                        "tccDmCqthuEntity", "tccDmTkNsnnEntity", "tccDmKbnnNhtmEntity",
                        "govPaymentItemList" })
        Page<GOVPaymentTransactionEntity> findAll(@NonNull Specification<GOVPaymentTransactionEntity> spec, @NonNull Pageable pageable);

        @Override
        @NonNull
        @EntityGraph(attributePaths = { "tccDmKhobacEntity", "tccDmDbhcEntity", "tccDmCqthuEntity", "tccDmTkNsnnEntity", "govPaymentItemList",
                        "govPaymentItemList.tccDmNdktEntity", "govPaymentItemList.tccDmChuongEntity", "govPaymentItemList.tccDmSthueHqaEntity",
                        "govPaymentItemList.tccDmLhxnkEntity", "govPaymentItemList.tccDmLoaitienhqaEntity" })
        Optional<GOVPaymentTransactionEntity> findOne(@NonNull Specification<GOVPaymentTransactionEntity> spec);

        @Override
        @NonNull
        @EntityGraph(attributePaths = { "tccDmKhobacEntity", "tccDmDbhcEntity", "tccDmCqthuEntity", "tccDmTkNsnnEntity", "govPaymentItemList" })
        List<GOVPaymentTransactionEntity> findAll(@NonNull Specification<GOVPaymentTransactionEntity> spec);

        @Override
        @NonNull
        @EntityGraph(attributePaths = { "tccDmKhobacEntity", "tccDmDbhcEntity", "tccDmCqthuEntity", "tccDmTkNsnnEntity", "govPaymentItemList" })
        Optional<GOVPaymentTransactionEntity> findById(@NonNull String txnId);

        @Modifying
        @Query("UPDATE GOVPaymentTransactionEntity gt SET gt.status = :status WHERE gt.id IN :txnIds AND gt.createdBy = :createdBy")
        void updateStatus(List<String> txnIds, String status, String createdBy);

        @Query("""
                        SELECT new com.bidv.ibank.dvc.model.dto.TxnPrintDocumentDto(
                            e.id, e.txnCode, e.status, e.maDbhc, e.tccDocSign,
                            e.tccDocNo, e.coreRef, e.tccRmNo, null, e.payerName, e.taxCode, e.payerAddr,
                            e.altTaxCode, e.altPayerName, e.altPayerAddr, e.debitAccNo, e.maCqthu,
                            cq.ten, e.maTk, tk.ten,
                            TO_CHAR(e.pmtTime, 'DD'), TO_CHAR(e.pmtTime, 'MM'), TO_CHAR(e.pmtTime, 'YYYY'),
                            TO_CHAR(e.pmtTime, 'DD/MM/YYYY'), TO_CHAR(COALESCE(e.amount, 0)),
                            TO_CHAR(COALESCE(e.amount, 0) + COALESCE(e.feeTotal, 0)), TO_CHAR(COALESCE(e.feeTotal, 0)),
                            TO_CHAR(COALESCE(e.feeVAT, 0)), e.ccy, kb.ten, nh.tenNh, e.approvalWfId, kb.taiKhoan ,null)
                        FROM GOVPaymentTransactionEntity e
                        LEFT JOIN e.tccDmKhobacEntity kb on kb.shkb = e.shkb and kb.bk01 = :bk01
                        LEFT JOIN e.tccDmKbnnNhtmEntity nh on nh.shkb = e.shkb and e.maNh = nh.maNh
                        LEFT JOIN e.tccDmCqthuEntity cq on cq.maCqthu = e.maCqthu and e.shkb = cq.shkb
                        LEFT JOIN e.tccDmTkNsnnEntity tk on tk.maTk = e.maTk
                        WHERE e.id in :txnIds AND e.status <> :status AND e.debitAccNo in :debitAccNos
                        """)
        List<TxnPrintDocumentDto> findDocumentsByIds(
                        @Param("txnIds") List<String> txnIds,
                        @Param("status") String status,
                        @Param("debitAccNos") List<String> debitAccNos,
                        @Param("bk01") String bk01);

        @Modifying
        @Transactional
        @Query("UPDATE GOVPaymentTransactionEntity gt SET gt.state = :state WHERE gt.id IN :txnIds")
        void updateState(List<String> txnIds, String state);

        @Modifying
        @Query("UPDATE GOVPaymentTransactionEntity gt SET gt.updStatusCnt = :updStatusCnt WHERE gt.id = :txnId")
        void updateRetryStatusCount(String txnId, Integer updStatusCnt);

        @Modifying
        @Query("UPDATE GOVPaymentTransactionEntity gt SET gt.updStatusCnt = :updStatusCnt, gt.tccErrCode = :errCode, gt.tccErrDesc = :errDesc WHERE gt.id = :txnId")
        void updateRetryStatusCountAndErrCode(String txnId, Integer updStatusCnt, String errCode, String errDesc);

        @Query("SELECT gt FROM GOVPaymentTransactionEntity gt WHERE gt.status = :status AND gt.pmtTime <= :threshold")
        List<GOVPaymentTransactionEntity> findAllByStatus(
                        @Param("status") String status,
                        @Param("threshold") LocalDateTime threshold);

        @Query("SELECT gt FROM GOVPaymentTransactionEntity gt WHERE gt.status = :status AND gt.state = :state AND gt.pmtTime <= :threshold")
        List<GOVPaymentTransactionEntity> findAllByState(
                        @Param("status") String status,
                        @Param("state") String state,
                        @Param("threshold") LocalDateTime threshold);

        @Query("SELECT gt FROM GOVPaymentTransactionEntity gt WHERE gt.status = :status AND gt.state = :state AND gt.retryInsfctBal = :retryInsfctBal AND (gt.retryInsfctBalCnt < :retryInsfctBalCnt OR gt.retryInsfctBalCnt IS NULL) AND gt.approvedDate <= :threshold")
        List<GOVPaymentTransactionEntity> findAllByRetryInsfctBal(
                        @Param("status") String status,
                        @Param("state") String state,
                        @Param("retryInsfctBal") String retryInsfctBal,
                        @Param("retryInsfctBalCnt") Integer retryInsfctBalCnt,
                        @Param("threshold") LocalDateTime threshold);

        @Modifying
        @Transactional(propagation = Propagation.REQUIRES_NEW)
        @Query("UPDATE GOVPaymentTransactionEntity gt SET gt.retryInsfctBalCnt = :retryInsfctBalCnt, gt.retryInsfctBal = :retryInsfctBal WHERE gt.id = :txnId")
        void updateRetryInsfctBalCnt(
                        @Param("txnId") String txnId,
                        @Param("retryInsfctBal") String retryInsfctBal,
                        @Param("retryInsfctBalCnt") Integer retryInsfctBalCnt);

        @Modifying
        @Transactional(propagation = Propagation.REQUIRES_NEW)
        @Query("UPDATE GOVPaymentTransactionEntity gt SET gt.retryInsfctBalCnt = :retryInsfctBalCnt, gt.status = :status, gt.state = :state WHERE gt.id = :txnId")
        void updateRetryInsfctBalCntAndStatus(
                        @Param("txnId") String txnId,
                        @Param("retryInsfctBalCnt") Integer retryInsfctBalCnt,
                        @Param("status") String status,
                        @Param("state") String state);

        @Query("SELECT gt FROM GOVPaymentTransactionEntity gt WHERE gt.status = :status AND gt.state = :state AND gt.retryInsfctBal = :retryInsfctBal")
        List<GOVPaymentTransactionEntity> findAllByRetryInsfctBal(@Param("status") String status, @Param("state") String state,
                        @Param("retryInsfctBal") String retryInsfctBal);

        List<GOVPaymentTransactionEntity> findByBatchNo(String batchNo);

        Optional<GOVPaymentTransactionEntity> findFirstByCusIdAndOrgIdAndStatusNotIn(Long cusId, String orgId, List<String> statuses);

        Optional<GOVPaymentTransactionEntity> findFirstByCusIdAndOrgId(Long cusId, String orgId);
}