package com.bidv.ibank.dvc.repository.param;

import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TccDmKbnnNhtmRepository extends JpaRepository<TccDmKbnnNhtmEntity, Long> {

    @Query("SELECT t FROM TccDmKbnnNhtmEntity t WHERE t.shkb IN :treasuryCodes AND t.bankCode <> :bankCode AND t.maNh IS NOT NULL ORDER BY t.shkb, t.maNh")
    List<TccDmKbnnNhtmEntity> findActiveBankByShkbWithoutCode(
            @Param("treasuryCodes") List<String> treasuryCodes,
            @Param("bankCode") String bankCode);
}