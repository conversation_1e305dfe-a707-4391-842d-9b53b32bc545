package com.bidv.ibank.dvc.repository.param;

import java.util.Collection;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.TccDmLhxnkCodeNameEntityDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;

import feign.Param;

@Repository
public interface TccDmLhxnkRepository extends JpaRepository<TccDmLhxnkEntity, String> {

    List<TccDmLhxnkEntity> findAllByOrderByMaLhAsc();

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmLhxnkCodeNameEntityDto(t.maLh, t.ten) FROM TccDmLhxnkEntity t WHERE :eiTypeCodes IS NULL OR t.maLh IN :eiTypeCodes ORDER BY t.maLh ASC")
    List<TccDmLhxnkCodeNameEntityDto> findLhxnkByCodes(@Param("eiTypeCodes") Collection<String> eiTypeCodes);
}