package com.bidv.ibank.dvc.repository.param;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import com.bidv.ibank.dvc.model.dto.TccDmKhobacCodeNameEntityDto;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;

@Repository
public interface TccDmKhobacRepository extends JpaRepository<TccDmKhobacEntity, String> {

    @EntityGraph(attributePaths = { "tccDmDbhcEntity", "tccDmKbnnNhtmEntity" })
    @Query("SELECT t FROM TccDmKhobacEntity t WHERE t.bk01 = :status ORDER BY t.shkb ASC")
    List<TccDmKhobacEntity> findAllByBk01OrderByShkbAsc(@Param("status") String status);

    Optional<TccDmKhobacEntity> findByShkb(String treasuryCode);

    @EntityGraph(attributePaths = { "tccDmDbhcEntity", "tccDmKbnnNhtmEntity" })
    @Query("SELECT t FROM TccDmKhobacEntity t WHERE t.shkb IN :treasuryCodes AND t.bk01 = :status")
    List<TccDmKhobacEntity> findByShkbIn(
            @Param("treasuryCodes") List<String> treasuryCodes,
            @Param("status") String status);

    @Query(value = "select new com.bidv.ibank.dvc.model.dto.TccDmKhobacCodeNameEntityDto(t.shkb, t.ten) from TccDmKhobacEntity t WHERE t.bk01 = :status AND (:treasuryCodes IS NULL OR t.shkb IN :treasuryCodes) ORDER BY t.shkb ASC")
    List<TccDmKhobacCodeNameEntityDto> findTreasuriesByCodes(@Param("status") String status, @Param("treasuryCodes") Collection<String> treasuryCodes);
}