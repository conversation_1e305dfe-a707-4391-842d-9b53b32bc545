package com.bidv.ibank.dvc.repository.customsduty;

import java.util.List;

import com.bidv.ibank.dvc.model.dto.TxnExportReportDto;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.framework.domain.response.Result;

public interface GOVPaymentTxnTransferRepositoty {

    Result<List<TxnExportReportDto>> findTxnSummary(
            TxnExportReq req,
            Long cusId,
            List<String> debitAccNos,
            List<String> statuses);
}