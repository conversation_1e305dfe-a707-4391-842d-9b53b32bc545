package com.bidv.ibank.dvc.repository.customsduty;

import org.springframework.stereotype.Repository;

import com.bidv.ibank.integrate.entity.tcc.TccAccountingRes;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocResult;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoRow;

@Repository
public interface GovPaymentTransactionCustomRepository {

    void updateAccountingFailed(String txnId, String errCode, String errDesc);

    void updateAccountingUnderBalance(String txnId);

    void updateAccountingUndefined(String txnId);

    void updatePmtTime(String txnId);

    boolean updateAccountingResult(String txnId, TccCreateDocResult tccCreateDocResult, TccAccountingRes tccAccountingRes);

    void updateTxnStateFailTccTrans(String txnId, TccQueryTxnInfoRow infoRow);

    boolean updateTxnStatusUndefined(String txnId, TccQueryTxnInfoRow infoRow);
}