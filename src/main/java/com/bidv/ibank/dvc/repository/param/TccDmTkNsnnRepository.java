package com.bidv.ibank.dvc.repository.param;

import java.util.Collection;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.TccDmTkNsnnStkEntityDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;

import feign.Param;

@Repository
public interface TccDmTkNsnnRepository extends JpaRepository<TccDmTkNsnnEntity, String> {

    @Query("""
        SELECT t FROM TccDmTkNsnnEntity t
        ORDER BY
            CASE
                WHEN t.maTk IN :priorityList THEN 0
                ELSE 1
            END,
            t.maTk ASC
        """)
    List<TccDmTkNsnnEntity> findAllByOrderByMaTkAsc(@Param("priorityList") List<String> priorityList);

    List<TccDmTkNsnnEntity> findByMaTkIn(List<String> revAccCodes);

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmTkNsnnStkEntityDto(t.tk, t.ten) FROM TccDmTkNsnnEntity t WHERE :revAccCodes IS NULL OR t.tk IN :revAccCodes ORDER BY t.tk ASC")
    List<TccDmTkNsnnStkEntityDto> findTkNsnnByCodes(@Param("revAccCodes") Collection<String> revAccCodes);
}