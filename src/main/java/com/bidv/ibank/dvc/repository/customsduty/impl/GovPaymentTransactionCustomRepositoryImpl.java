package com.bidv.ibank.dvc.repository.customsduty.impl;

import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.repository.BaseRepository;
import com.bidv.ibank.dvc.repository.customsduty.GovPaymentTransactionCustomRepository;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.constant.TccAccountingEnum;
import com.bidv.ibank.dvc.util.constant.TccQueryTxnStatusEnum;
import com.bidv.ibank.dvc.util.constant.TccTransferEnum;
import com.bidv.ibank.dvc.util.constant.TransactionFieldEnum;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingRes;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingResultSibsInfo;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingResultTransferInfo;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocResult;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoRow;

import jakarta.persistence.EntityManager;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Repository
public class GovPaymentTransactionCustomRepositoryImpl extends BaseRepository implements GovPaymentTransactionCustomRepository {

    public GovPaymentTransactionCustomRepositoryImpl(EntityManager entityManager) {
        super(entityManager);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateAccountingFailed(String txnId, String errCode, String errDesc) {
        Map<TransactionFieldEnum, Object> params = new HashMap<>();
        putIfNotNull(params, TransactionFieldEnum.STATUS, TransactionStatusEnum.FAILED.name());
        putIfNotNull(params, TransactionFieldEnum.STATE, TransactionStateEnum.FAILED.name());
        putIfNotNull(params, TransactionFieldEnum.TCC_ERR_CODE, errCode);
        putIfNotNull(params, TransactionFieldEnum.TCC_ERR_DESC, errDesc);
        updateByColumn("id", txnId, params, GOVPaymentTransactionEntity.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateAccountingUnderBalance(String txnId) {
        Map<TransactionFieldEnum, Object> params = new HashMap<>();
        putIfNotNull(params, TransactionFieldEnum.STATUS, TransactionStatusEnum.BANK_PROCESSING.name());
        putIfNotNull(params, TransactionFieldEnum.STATE, TransactionStateEnum.UNDER_BALANCE.name());
        updateByColumn("id", txnId, params, GOVPaymentTransactionEntity.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateAccountingUndefined(String txnId) {
        Map<TransactionFieldEnum, Object> params = new HashMap<>();
        putIfNotNull(params, TransactionFieldEnum.STATUS, TransactionStatusEnum.UNDEFINED.name());
        putIfNotNull(params, TransactionFieldEnum.STATE, TransactionStateEnum.UNDEFINED.name());
        updateByColumn("id", txnId, params, GOVPaymentTransactionEntity.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updatePmtTime(String txnId) {
        Map<TransactionFieldEnum, Object> params = new HashMap<>();
        putIfNotNull(params, TransactionFieldEnum.PMT_TIME, LocalDateTime.now());
        updateByColumn("id", txnId, params, GOVPaymentTransactionEntity.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean updateAccountingResult(String txnId, TccCreateDocResult tccCreateDocResult, TccAccountingRes tccAccountingRes) {
        boolean isRevertLimit = false;
        TccAccountingResultSibsInfo sibsInfo = tccAccountingRes.getResult().getSibsInfo();
        TccAccountingResultTransferInfo transferInfo = tccAccountingRes.getResult().getTransferInfo();

        Map<TransactionFieldEnum, Object> params = new HashMap<>();
        putIfNotNull(params, TransactionFieldEnum.TCC_DOC_ID, tccCreateDocResult.getIdCtuHdr());
        putIfNotNull(params, TransactionFieldEnum.TCC_DOC_SIGN, tccCreateDocResult.getKyhieuCt());
        putIfNotNull(params, TransactionFieldEnum.TCC_DOC_NO, tccCreateDocResult.getSoCt());
        putIfNotNull(params, TransactionFieldEnum.CORE_REF, sibsInfo.getRefNo());
        putIfNotNull(params, TransactionFieldEnum.TCC_RM_NO, sibsInfo.getRmNo());
        putIfNotNull(params, TransactionFieldEnum.TCC_ID_CORE, sibsInfo.getIdCore());

        // Hạch toán thành công
        if (TccAccountingEnum.SUCCESS.getValue().equals(sibsInfo.getStatus())) {
            setTxnStatus(params, TransactionStatusEnum.SUCCESS, TransactionStateEnum.SUCCESS);
            setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, null, null);
            // Truyền nhận thất bại
            if (!TccTransferEnum.SUCCESS.getValue().equals(transferInfo.getStatus())) {
                putIfNotNull(params, TransactionFieldEnum.STATE, TransactionStateEnum.FAIL_TRANS_TCC.name());
                setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, transferInfo.getErrCode(), transferInfo.getErrDesc());
            }
        }
        // Hạch toán thất bại
        else if (TccAccountingEnum.FAILED.getValue().equals(sibsInfo.getStatus())) {
            isRevertLimit = true;
            setTxnStatus(params, TransactionStatusEnum.FAILED, TransactionStateEnum.FAILED);
            setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, sibsInfo.getErrCode(), sibsInfo.getErrDesc());
        }
        // Hạch toán không xác định
        else if (TccAccountingEnum.UNDEFINED.getValue().equals(sibsInfo.getStatus())) {
            setTxnStatus(params, TransactionStatusEnum.UNDEFINED, TransactionStateEnum.UNDEFINED);
        }
        updateByColumn("id", txnId, params, GOVPaymentTransactionEntity.class);
        return isRevertLimit;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTxnStateFailTccTrans(String txnId, TccQueryTxnInfoRow infoRow) {
        Map<TransactionFieldEnum, Object> params = new HashMap<>();
        String statusCtu = infoRow.getStatusCtu();

        if (TccQueryTxnStatusEnum.SUCCESS_CTU.getValue().equals(statusCtu)) {
            putIfNotNull(params, TransactionFieldEnum.STATE, TransactionStateEnum.SUCCESS.name());
            setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, null, null);
            setStateTransfer(params, infoRow);
            updateByColumn("id", txnId, params, GOVPaymentTransactionEntity.class);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean updateTxnStatusUndefined(String txnId, TccQueryTxnInfoRow infoRow) {
        boolean isRevertLimit = false;
        Map<TransactionFieldEnum, Object> params = new HashMap<>();
        String statusCtu = infoRow.getStatusCtu();

        if (TccQueryTxnStatusEnum.UNDEFINED_CTU.getValue().equals(statusCtu)) {
            setTxnStatus(params, TransactionStatusEnum.UNDEFINED, TransactionStateEnum.UNDEFINED);
            setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, infoRow.getErrorcodeCtu(), infoRow.getErrordescCtu());
        } else if (TccQueryTxnStatusEnum.FAIL_CTU.getValue().equals(statusCtu)) {
            isRevertLimit = true;
            setTxnStatus(params, TransactionStatusEnum.FAILED, TransactionStateEnum.FAILED);
            setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, infoRow.getErrorcodeCtu(), infoRow.getErrordescCtu());
        } else if (TccQueryTxnStatusEnum.SUCCESS_CTU.getValue().equals(statusCtu)) {
            setTxnStatus(params, TransactionStatusEnum.SUCCESS, TransactionStateEnum.SUCCESS);
            setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, null, null);

            putIfNotNull(params, TransactionFieldEnum.FINISH_DATE, DateUtils.convertStringToDateTime(infoRow.getTgKy(), AppConstants.DATE_FORMAT_HH_MM_SS_DD_MM_YYYY));
            putIfNotNull(params, TransactionFieldEnum.TCC_DOC_ID, infoRow.getIdCtHdr());
            putIfNotNull(params, TransactionFieldEnum.TCC_DOC_SIGN, infoRow.getKyHieuCt());
            putIfNotNull(params, TransactionFieldEnum.TCC_DOC_NO, infoRow.getSoCtu());
            putIfNotNull(params, TransactionFieldEnum.CORE_REF, infoRow.getRefcore());
            putIfNotNull(params, TransactionFieldEnum.TCC_RM_NO, infoRow.getRmNo());
            putIfNotNull(params, TransactionFieldEnum.TCC_ID_CORE, infoRow.getIdCore());

            // Trạng thái hạch toán != 11
            if (!TccQueryTxnStatusEnum.SUCCESS.getValue().equals(infoRow.getStatusCore())) {
                isRevertLimit = true;
                setTxnStatus(params, TransactionStatusEnum.FAILED, TransactionStateEnum.FAILED);
                setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, infoRow.getErrcodeCore(), infoRow.getErrdescCore());
            }
            // Trạng thái truyền nhận != 11
            else if (!TccQueryTxnStatusEnum.SUCCESS.getValue().equals(infoRow.getTtHqa())) {
                setStateTransfer(params, infoRow);
            }
        }
        updateByColumn("id", txnId, params, GOVPaymentTransactionEntity.class);
        return isRevertLimit;
    }

    private void setStateTransfer(Map<TransactionFieldEnum, Object> params, TccQueryTxnInfoRow infoRow) {
        if (!TccQueryTxnStatusEnum.SUCCESS.getValue().equals(infoRow.getTtHqa())) {
            putIfNotNull(params, TransactionFieldEnum.STATE, TransactionStateEnum.FAIL_TRANS_TCC.name());
            setError(params, TransactionFieldEnum.TCC_ERR_CODE, TransactionFieldEnum.TCC_ERR_DESC, infoRow.getErrcodeHqa(), infoRow.getErrdescHqa());
        }
    }

    private void setTxnStatus(Map<TransactionFieldEnum, Object> params, TransactionStatusEnum status, TransactionStateEnum state) {
        putIfNotNull(params, TransactionFieldEnum.STATUS, status.name());
        putIfNotNull(params, TransactionFieldEnum.STATE, state.name());
    }

    private void setError(Map<TransactionFieldEnum, Object> params, TransactionFieldEnum codeField, TransactionFieldEnum descField, String code, String desc) {
        putIfNotNull(params, codeField, code);
        putIfNotNull(params, descField, desc);
    }
}
