package com.bidv.ibank.dvc.repository.customsduty;

import java.util.List;

import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

@Repository
public interface GOVPaymentBatchItemRepository extends JpaRepository<GOVPaymentBatchItemEntity, String>,
        JpaSpecificationExecutor<GOVPaymentBatchItemEntity> {

    @Override
    @NonNull
    @EntityGraph(attributePaths = { "govPaymentBatchEntity", "tccDmNdktEntity", "tccDmChuongEntity", "tccDmSthueHqaEntity", "tccDmLhxnkEntity",
            "tccDmLoaitienhqaEntity", "tccDmKhobacEntity", "tccDmKhobacEntity.tccDmDbhcEntity", "tccDmKhobacEntity.tccDmKbnnNhtmEntity",
            "tccDmDbhcEntity", "tccDmCqthuEntity",
            "tccDmTkNsnnEntity" })
    List<GOVPaymentBatchItemEntity> findAll(@NonNull Specification<GOVPaymentBatchItemEntity> spec);

    @Query("SELECT g FROM GOVPaymentBatchItemEntity g WHERE g.batchId = :batchId AND (:status IS NULL OR g.status = :status)")
    List<GOVPaymentBatchItemEntity> findByBatchIdAndStatus(@Param("batchId") String batchId, @Param("status") BatchItemStatusEnum status);

    @Modifying
    @Query("UPDATE GOVPaymentBatchItemEntity g SET g.errCode = :errCode WHERE g.id IN :batchItemIds")
    void updateErrCode(@Param("batchItemIds") List<String> batchItemIds, @Param("errCode") String errCode);

    @Modifying
    @Query("UPDATE GOVPaymentBatchItemEntity g SET g.status = :status WHERE g.id IN :batchItemIds")
    void updateStatus(@Param("batchItemIds") List<String> batchItemIds, @Param("status") BatchItemStatusEnum status);

}
