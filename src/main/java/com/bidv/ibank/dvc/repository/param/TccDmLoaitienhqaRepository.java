package com.bidv.ibank.dvc.repository.param;

import java.util.Collection;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.TccDmLoaitienhqaCodeNameEntityDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;

import feign.Param;

@Repository
public interface TccDmLoaitienhqaRepository extends JpaRepository<TccDmLoaitienhqaEntity, String> {

    List<TccDmLoaitienhqaEntity> findAllByOrderByMaLthqAsc();

    List<TccDmLoaitienhqaEntity> findByMaLthqIn(List<String> ccNodes);

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmLoaitienhqaCodeNameEntityDto(t.maLthq, t.tenLthq) FROM TccDmLoaitienhqaEntity t WHERE :ccNodes IS NULL OR t.maLthq IN :ccNodes ORDER BY t.maLthq ASC")
    List<TccDmLoaitienhqaCodeNameEntityDto> findLoaitienhqaByCodes(@Param("ccNodes") Collection<String> ccNodes);
}