package com.bidv.ibank.dvc.repository.customsduty;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateEntity;

@Repository
public interface GOVPaymentTemplateRepository extends JpaRepository<GOVPaymentTemplateEntity, String>,
        JpaSpecificationExecutor<GOVPaymentTemplateEntity> {

    @Override
    @NonNull
    @EntityGraph(attributePaths = { "tccDmKhobacEntity", "tccDmDbhcEntity", "tccDmCqthuEntity", "tccDmTkNsnnEntity", "govPaymentTemplateItemList",
            "govPaymentTemplateItemList.tccDmNdktEntity", "govPaymentTemplateItemList.tccDmChuongEntity", "govPaymentTemplateItemList.tccDmSthueHqaEntity",
            "govPaymentTemplateItemList.tccDmLhxnkEntity", "govPaymentTemplateItemList.tccDmLoaitienhqaEntity" })
    Page<GOVPaymentTemplateEntity> findAll(@NonNull Specification<GOVPaymentTemplateEntity> spec, @NonNull Pageable pageable);

    @Override
    @NonNull
    @EntityGraph(attributePaths = { "tccDmKhobacEntity", "tccDmDbhcEntity", "tccDmCqthuEntity", "tccDmTkNsnnEntity", "govPaymentTemplateItemList" })
    List<GOVPaymentTemplateEntity> findAll(@NonNull Specification<GOVPaymentTemplateEntity> spec);

    @Modifying
    @Query("UPDATE GOVPaymentTemplateEntity gt SET gt.status = :status WHERE gt.id IN :templateIds AND gt.cifNo = :cifNo")
    void updateStatus(@Param("templateIds") List<String> templateIds, @Param("status") String status, @Param("cifNo") String cifNo);
}