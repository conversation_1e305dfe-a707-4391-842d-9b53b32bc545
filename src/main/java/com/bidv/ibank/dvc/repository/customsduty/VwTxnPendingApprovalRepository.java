package com.bidv.ibank.dvc.repository.customsduty;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.customsduty.VwTxnPendingApprovalEntity;

import java.util.List;

@Repository
public interface VwTxnPendingApprovalRepository extends JpaRepository<VwTxnPendingApprovalEntity, String> {

    List<VwTxnPendingApprovalEntity> findByAssignee(String assignee);

}
