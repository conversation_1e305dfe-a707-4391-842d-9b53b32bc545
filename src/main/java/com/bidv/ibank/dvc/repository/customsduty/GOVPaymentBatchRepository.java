package com.bidv.ibank.dvc.repository.customsduty;

import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import org.springframework.lang.NonNull;

@Repository
public interface GOVPaymentBatchRepository extends JpaRepository<GOVPaymentBatchEntity, String>, JpaSpecificationExecutor<GOVPaymentBatchEntity> {

    @Override
    @NonNull
    @EntityGraph(attributePaths = { "govPaymentBatchItemList",
            "govPaymentBatchItemList.tccDmKhobacEntity", "govPaymentBatchItemList.tccDmDbhcEntity", "govPaymentBatchItemList.tccDmCqthuEntity",
            "govPaymentBatchItemList.tccDmTkNsnnEntity", "govPaymentBatchItemList.tccDmNdktEntity", "govPaymentBatchItemList.tccDmChuongEntity",
            "govPaymentBatchItemList.tccDmSthueHqaEntity", "govPaymentBatchItemList.tccDmLhxnkEntity",
            "govPaymentBatchItemList.tccDmLoaitienhqaEntity" })
    Optional<GOVPaymentBatchEntity> findOne(@NonNull Specification<GOVPaymentBatchEntity> spec);

    @Query("SELECT b FROM GOVPaymentBatchEntity b WHERE b.batchNo = :batchNo AND (:createdBy IS NULL OR b.createdBy = :createdBy)")
    Optional<GOVPaymentBatchEntity> findByBatchNo(@Param("batchNo") String batchNo, @Param("createdBy") String createdBy);

    @Modifying
    @Query("update GOVPaymentBatchEntity b set b.status = :status, b.errCode = :errCode where b.batchNo = :batchNo AND b.cusId = :cusId")
    void updateStatusByBatchNo(
            @Param("status") BatchStatusEnum status,
            @Param("errCode") String errCode,
            @Param("batchNo") String batchNo,
            @Param("cusId") Long cusId);

    @Modifying
    @Query("update GOVPaymentBatchEntity b set b.status = :status, b.processedDate = :processedDate where b.cusId = :cusId AND b.id = :batchId")
    void updateStatusAndProcessedDateByBatchId(
            @Param("status") BatchStatusEnum status,
            @Param("processedDate") LocalDateTime processedDate,
            @Param("batchId") String batchId,
            @Param("cusId") Long cusId);
}
