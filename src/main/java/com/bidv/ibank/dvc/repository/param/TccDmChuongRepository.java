package com.bidv.ibank.dvc.repository.param;

import com.bidv.ibank.dvc.model.dto.TccDmChuongCodeNameEntityDto;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;

import feign.Param;

import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TccDmChuongRepository extends JpaRepository<TccDmChuongEntity, String> {

    List<TccDmChuongEntity> findAllByOrderByMaChuongAsc();

    List<TccDmChuongEntity> findByMaChuongIn(List<String> chapterCodes);

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmChuongCodeNameEntityDto(t.ma<PERSON>huong, t.ten) FROM TccDmChuongEntity t WHERE :chapterCodes IS NULL OR t.maChuong IN :chapterCodes ORDER BY t.maChuong ASC")
    List<TccDmChuongCodeNameEntityDto> findChuongByCodes(@Param("chapterCodes") Collection<String> chapterCodes);
}