package com.bidv.ibank.dvc.repository;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.util.constant.FieldEnum;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public abstract class BaseRepository {

    private final EntityManager entityManager;

    public <T extends FieldEnum, S> void updateByColumn(String columnName, String columnValue, Map<T, Object> params, Class<S> entityClass) {
        if (StringUtils.isBlank(columnValue) || MapUtils.isEmpty(params)) {
            return;
        }

        StringBuilder query = new StringBuilder();
        query.append("UPDATE ").append(entityClass.getSimpleName()).append(" t SET ");
        params.forEach((k, v) -> query.append("t.")
                .append(k.fieldCode())
                .append(" = :")
                .append(k.fieldCode())
                .append(", "));

        if (query.toString().endsWith(", ")) {
            query.setLength(query.length() - 2);
            query.append(" WHERE ");
            query.append(columnName);
            query.append(" = :columnValue");

            Query q = entityManager.createQuery(query.toString());
            q.setParameter("columnValue", columnValue);
            params.forEach((k, v) -> q.setParameter(k.fieldCode(), v));
            q.executeUpdate();
        }
    }

    public <T> void putIfNotNull(Map<T, Object> map, T key, Object value) {
        if (value != null) {
            map.put(key, value);
        }
    }
}
