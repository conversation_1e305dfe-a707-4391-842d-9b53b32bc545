package com.bidv.ibank.dvc.repository.customsduty;

import java.util.List;

import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentItemDto;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;

@Repository
public interface GOVPaymentItemRepository extends JpaRepository<GOVPaymentItemEntity, String>, JpaSpecificationExecutor<GOVPaymentItemEntity> {

    @Modifying
    @Query("DELETE FROM GOVPaymentItemEntity WHERE txnId = :txnId")
    void deleteByTxnId(String txnId);

    @Modifying
    @Query("DELETE FROM GOVPaymentItemEntity WHERE txnId IN :txnIds")
    void deleteByTxnIds(List<String> txnIds);

    @Query("""
            SELECT new com.bidv.ibank.dvc.model.dto.TxnPrintDocumentItemDto(
                i.txnId, null, i.declarationNo, TO_CHAR(i.declarationDate, 'DD/MM/YYYY'), i.transDesc,
                TO_CHAR(COALESCE(i.amount, 0)), TO_CHAR(COALESCE(i.amount, 0)),
                i.maChuong, i.maNdkt)
            FROM GOVPaymentItemEntity i WHERE i.txnId in :txnIds
            """)
    List<TxnPrintDocumentItemDto> findDocumentItemsByTxnIds(@Param("txnIds") List<String> txnIds);
}