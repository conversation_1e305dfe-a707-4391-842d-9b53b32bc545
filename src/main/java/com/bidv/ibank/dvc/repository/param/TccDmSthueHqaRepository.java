package com.bidv.ibank.dvc.repository.param;

import com.bidv.ibank.dvc.model.dto.TccDmSthueHqaCodeNameEntityDto;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;

import feign.Param;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TccDmSthueHqaRepository extends JpaRepository<TccDmSthueHqaEntity, String> {

    List<TccDmSthueHqaEntity> findAllByOrderByMaSthueAsc();

    List<TccDmSthueHqaEntity> findByMaSthueIn(List<String> taxTypeCodes);

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmSthueHqaCodeNameEntityDto(t.maSthue, t.tenSthue) FROM TccDmSthueHqaEntity t WHERE :taxTypeCodes IS NULL OR t.maSthue IN :taxTypeCodes ORDER BY t.maSthue ASC")
    List<TccDmSthueHqaCodeNameEntityDto> findTaxTypeByCodes(@Param("taxTypeCodes") Collection<String> taxTypeCodes);
}