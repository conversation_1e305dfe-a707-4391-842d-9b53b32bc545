package com.bidv.ibank.dvc.repository.param;

import com.bidv.ibank.dvc.model.dto.TccDmNdktCodeNameEntityDto;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;

import feign.Param;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TccDmNdktRepository extends JpaRepository<TccDmNdktEntity, String> {

    List<TccDmNdktEntity> findAllByOrderByMaNdktAsc();

    List<TccDmNdktEntity> findByMaNdktIn(List<String> ecCodes);

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmNdktCodeNameEntityDto(t.maNdkt, t.ten) FROM TccDmNdktEntity t WHERE :ecCodes IS NULL OR t.maNdkt IN :ecCodes ORDER BY t.maNdkt ASC")
    List<TccDmNdktCodeNameEntityDto> findNdktByCodes(@Param("ecCodes") Collection<String> ecCodes);
}