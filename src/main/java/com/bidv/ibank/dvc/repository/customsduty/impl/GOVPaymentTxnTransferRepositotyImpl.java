package com.bidv.ibank.dvc.repository.customsduty.impl;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.dto.TxnExportReportDto;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.dvc.repository.customsduty.GOVPaymentTxnTransferRepositoty;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.database.query.QueryUtils;
import com.bidv.ibank.framework.domain.response.Result;
import com.bidv.ibank.framework.util.VNCharacterUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalTime;
import java.util.List;

@Slf4j
@Repository
@RequiredArgsConstructor
public class GOVPaymentTxnTransferRepositotyImpl implements GOVPaymentTxnTransferRepositoty {

    private final EntityManager entityManager;

    @Override
    public Result<List<TxnExportReportDto>> findTxnSummary(
            TxnExportReq req,
            Long cusId,
            List<String> debitAccNos,
            List<String> notInStatuses) {

        String selectPrefix = """
                    SELECT NEW com.bidv.ibank.dvc.model.dto.TxnExportReportDto(
                        /* 2 */ TO_CHAR(e.createdDate, 'DD/MM/YYYY') AS createdDate,
                        /* 3 */ e.status AS status,
                        /* 5 */ e.id AS txnId,
                        /* 6 */ e.txnType AS txnType,
                        /* 7 */ e.debitAccNo AS debitAccNo,
                        /* 8 */ e.taxCode AS taxCode,
                        /* 9 */ e.payerName AS payerName,
                        /* 10 */ item.declarationNo AS declarationNo,
                        /* 12 */ TO_CHAR(item.declarationDate, 'DD/MM/YYYY') AS declarationDate,

                        /* 34 */ e.shkb AS treasuryCode,
                        /* 35 */ khobac.ten AS treasuryName,
                        /* 36 */ kbnn.maNh AS benBankCode,
                        /* 37 */ kbnn.tenNh AS benBankName,
                        /* 38 */ e.maTk AS revAccCode,
                        /* 39 */ tknsnn.ten AS revAccName,
                        /* 40 */ e.maCqthu AS revAuthCode,
                        /* 41 */ cqthu.ten AS revAuthName,
                        /* 42 */ e.maDbhc AS admAreaCode,
                        /* 43 */ dbhc.ten AS admAreaName,
                        /* 44 */ item.maChuong AS chapterCode,
                        /* 45 */ chuong.ten AS chapterName,
                        /* 46 */ item.maNdkt AS ecCode,
                        /* 47 */ ndkt.ten AS ecName,
                        /* 48 */ TO_CHAR(e.amount) AS parAmount,
                        /* 49 */ TO_CHAR(item.amount) AS itemAmount,
                        /* 50 */ e.tccErrDesc AS tccErrDesc,
                        /* 51 */ e.tccSibsErrDesc AS tccSibsErrDesc,
                        /* 52 */ e.tccTransErrDesc AS tccTransErrDesc,

                        /* 20 */ item.transDesc AS transDesc,
                        /* 22 */ e.ccy AS ccy,
                        /* 23 */ e.batchNo AS batchNo,
                        /* 24 */ e.createdBy AS createdBy,
                        /* 25 */ e.approvalUsers AS approvalUsers,
                        /* 26 */ TO_CHAR(e.approvedDate, 'DD/MM/YYYY') AS approvedDate,
                        /* 27 */ TO_CHAR(e.feeTotal) AS feeTotal,
                        /* 28 */ e.state AS state,
                        /* 29 */ e.tccIdCore AS tccIdCore,
                        /* 30 */ e.coreRef AS tccRefNo,
                        /* 31 */ e.channel AS channel,
                        /* 32 */ e.orgId AS orgId
                    )
                """;
        StringBuilder jpql = new StringBuilder(selectPrefix)
                .append(renderSqlExport(req) + "\n")
                .append("""
                        GROUP BY
                            e.createdDate, e.status, e.id, e.txnType, e.debitAccNo, e.taxCode, e.payerName, item.declarationNo, item.declarationDate,
                            e.shkb, khobac.ten,kbnn.maNh, kbnn.tenNh, e.maTk, tknsnn.ten, e.maCqthu, cqthu.ten, e.maDbhc, dbhc.ten, item.maChuong, chuong.ten,
                            item.maNdkt, ndkt.ten, item.transDesc, e.amount, e.ccy, e.batchNo, e.createdBy, e.approvalUsers, e.approvedDate, e.feeTotal,
                            e.state, e.tccIdCore, e.coreRef, e.channel, e.orgId, item.amount, e.tccErrDesc, e.tccSibsErrDesc,
                            e.tccTransErrDesc
                        """)
                .append("\n ORDER BY e.createdDate DESC, e.id DESC");
        try {
            TypedQuery<TxnExportReportDto> query = entityManager.createQuery(jpql.toString(), TxnExportReportDto.class);

            setParameter(query, req, cusId, debitAccNos, notInStatuses);

            List<TxnExportReportDto> entities = query.getResultList();

            return Result.success(entities);
        } catch (Exception e) {
            log.error("queryCustomTxnTransfers Exception {}, {}", e.getMessage(), e);
            return Result.error(ResponseCode.TIMEOUT_01);
        }
    }

    public StringBuilder renderSqlExport(TxnExportReq req) {

        StringBuilder sql = new StringBuilder("FROM GOVPaymentTransactionEntity e ");

        sql.append("LEFT JOIN e.govPaymentItemList item ON e.id = item.txnId ");
        sql.append("LEFT JOIN e.tccDmKbnnNhtmEntity kbnn ");
        sql.append("LEFT JOIN e.tccDmKhobacEntity khobac ");
        sql.append("LEFT JOIN e.tccDmDbhcEntity dbhc ");
        sql.append("LEFT JOIN e.tccDmCqthuEntity cqthu ");
        sql.append("LEFT JOIN e.tccDmTkNsnnEntity tknsnn ");
        sql.append("LEFT JOIN item.tccDmNdktEntity ndkt ");
        sql.append("LEFT JOIN item.tccDmChuongEntity chuong \n");

        sql.append("WHERE e.cusId = :cusId ");
        sql.append("AND e.status NOT IN (:notInStatuses) ");
        sql.append("AND e.debitAccNo IN :debitAccNos");

        addCommonFilters(sql, req);

        return sql;
    }

    public void setParameter(Query query, TxnExportReq req, Long cusId, List<String> debitAccNos, List<String> notInStatuses) {
        query.setParameter("cusId", cusId);
        query.setParameter("debitAccNos", debitAccNos);
        query.setParameter("notInStatuses", notInStatuses);

        setCommonParameters(query, req);
    }

    private void setCommonParameters(Query query, TxnExportReq req) {
        setSearchParam(query, req);
        setDateParams(query, req);
        setAmountParams(query, req);
        setCcyAndStatusParams(query, req);
        setSearchLikeParams(query, req);
        setAdditionalParams(query, req);
    }

    private void setSearchParam(Query query, TxnExportReq req) {
        if (StringUtils.isNotBlank(req.getSearch())) {
            String search = QueryUtils.escapeLikeString(
                    VNCharacterUtils.removeAccent(req.getSearch().trim().toLowerCase()));
            query.setParameter("search", String.format("%%%s%%", search));
        }
    }

    private void setDateParams(Query query, TxnExportReq req) {
        if (req.getStartDate() != null) {
            query.setParameter("startDate", req.getStartDate().atStartOfDay());
        }
        if (req.getEndDate() != null) {
            query.setParameter("endDate", req.getEndDate().atTime(LocalTime.MAX));
        }
    }

    private void setAmountParams(Query query, TxnExportReq req) {
        if (req.getMinAmount() != null) {
            query.setParameter("minAmount", req.getMinAmount());
        }
        if (req.getMaxAmount() != null) {
            query.setParameter("maxAmount", req.getMaxAmount());
        }
    }

    private void setCcyAndStatusParams(Query query, TxnExportReq req) {
        if (CollectionUtils.isNotEmpty(req.getCcys())) {
            query.setParameter("ccys", req.getCcys());
        }
        if (CollectionUtils.isNotEmpty(req.getStatuses())) {
            List<String> statuses = req.getStatuses().stream()
                    .filter(s -> !s.equals(TransactionStatusEnum.DELETED.name()))
                    .toList();
            query.setParameter("statuses", statuses);
        }
    }

    private void setSearchLikeParams(Query query, TxnExportReq req) {
        if (StringUtils.isNotBlank(req.getDebitAccNo())) {
            query.setParameter("debitAccNo", String.format("%%%s%%", req.getDebitAccNo()));
        }
        if (StringUtils.isNotBlank(req.getTaxCode())) {
            query.setParameter("taxCode", String.format("%%%s%%", req.getTaxCode()));
        }
        if (StringUtils.isNotBlank(req.getDeclarationNo())) {
            query.setParameter("declarationNo", String.format("%%%s%%", req.getDeclarationNo()));
        }
        if (StringUtils.isNotBlank(req.getBatchNo())) {
            query.setParameter("batchNo", String.format("%%%s%%", req.getBatchNo()));
        }
        if (StringUtils.isNotBlank(req.getTccRefNo())) {
            query.setParameter("tccRefNo", String.format("%%%s%%", req.getTccRefNo()));
        }
    }

    private void setAdditionalParams(Query query, TxnExportReq req) {
        if (CollectionUtils.isNotEmpty(req.getTxnTypes())) {
            query.setParameter("txnTypes", req.getTxnTypes());
        }
        if (CollectionUtils.isNotEmpty(req.getChannels())) {
            query.setParameter("channels", req.getChannels().stream()
                    .map(String::toUpperCase).toList());
        }
    }

    private void addCommonFilters(StringBuilder sql, TxnExportReq req) {
        addSearchFilter(sql, req);
        addDateFilters(sql, req);
        addAmountFilters(sql, req);
        addListFilters(sql, req);
        addStringFilters(sql, req);
    }

    private void addSearchFilter(StringBuilder sql, TxnExportReq req) {
        if (StringUtils.isNotBlank(req.getSearch())) {
            sql.append("""
                        AND (
                            lower(e.id) LIKE :search
                            OR TO_CHAR(e.amount) LIKE :search
                            OR lower(e.debitAccNo) LIKE :search
                            OR lower(e.orgId) LIKE :search
                        )
                    """);
        }
    }

    private void addDateFilters(StringBuilder sql, TxnExportReq req) {
        if (req.getStartDate() != null) {
            sql.append(" AND e.createdDate >= :startDate");
        }
        if (req.getEndDate() != null) {
            sql.append(" AND e.createdDate <= :endDate");
        }
    }

    private void addAmountFilters(StringBuilder sql, TxnExportReq req) {
        if (req.getMinAmount() != null) {
            sql.append(" AND e.amount >= :minAmount");
        }
        if (req.getMaxAmount() != null) {
            sql.append(" AND e.amount <= :maxAmount");
        }
    }

    private void addListFilters(StringBuilder sql, TxnExportReq req) {
        if (CollectionUtils.isNotEmpty(req.getCcys())) {
            sql.append(" AND e.ccy IN :ccys");
        }
        if (CollectionUtils.isNotEmpty(req.getStatuses())) {
            sql.append(" AND e.status IN :statuses");
        }
        if (CollectionUtils.isNotEmpty(req.getTxnTypes())) {
            sql.append(" AND e.txnType IN :txnTypes");
        }
        if (CollectionUtils.isNotEmpty(req.getChannels())) {
            sql.append(" AND e.channel IN :channels");
        }
    }

    private void addStringFilters(StringBuilder sql, TxnExportReq req) {
        if (StringUtils.isNotBlank(req.getDebitAccNo())) {
            sql.append(" AND e.debitAccNo LIKE :debitAccNo");
        }
        if (StringUtils.isNotBlank(req.getTaxCode())) {
            sql.append(" AND e.taxCode LIKE :taxCode");
        }
        if (StringUtils.isNotBlank(req.getDeclarationNo())) {
            sql.append(" AND item.declarationNo LIKE :declarationNo");
        }
        if (StringUtils.isNotBlank(req.getBatchNo())) {
            sql.append(" AND e.batchNo LIKE :batchNo");
        }
        if (StringUtils.isNotBlank(req.getTccRefNo())) {
            sql.append(" AND e.coreRef LIKE :tccRefNo");
        }
        if (StringUtils.isNotBlank(req.getTxnItemId())) {
            // TODO: txnItemId chỉ dùng cho thuế nội địa, tạm thời disjunction trong giai đoạn thuế hải quan
            sql.append(" AND 1 = 0");
        }
    }

}