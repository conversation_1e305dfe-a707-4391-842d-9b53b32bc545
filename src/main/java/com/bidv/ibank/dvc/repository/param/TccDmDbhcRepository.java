package com.bidv.ibank.dvc.repository.param;

import java.util.Collection;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.TccDmDbhcCodeNameEntityDto;
import feign.Param;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;

@Repository
public interface TccDmDbhcRepository extends JpaRepository<TccDmDbhcEntity, String> {

    @Query("SELECT t FROM TccDmDbhcEntity t WHERE TRIM(:admAreaCode) IS NULL OR t.maDbhc = :admAreaCode ORDER BY t.maDbhc ASC")
    List<TccDmDbhcEntity> findAllByOrderByMaDbhcAsc(@Param("admAreaCode") String admAreaCode);

    @Query(value = "SELECT new com.bidv.ibank.dvc.model.dto.TccDmDbhcCodeNameEntityDto(t.maDbhc, t.ten) FROM TccDmDbhcEntity t WHERE :admAreaCodes IS NULL OR t.maDbhc IN :admAreaCodes ORDER BY t.maDbhc ASC")
    List<TccDmDbhcCodeNameEntityDto> findDbhcByCodes(@Param("admAreaCodes") Collection<String> admAreaCodes);
}