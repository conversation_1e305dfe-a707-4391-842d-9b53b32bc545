package com.bidv.ibank.dvc.specification;

import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;

public class TxnItemSpecifications extends BaseSpecifications {

    public static Specification<GOVPaymentItemEntity> declarationNoLike(String declarationNo) {
        return columnLike("declarationNo", declarationNo);
    }

    public static Specification<GOVPaymentItemEntity> txnItemIdLike(String txnItemId) {
        // TODO: txnItemId chỉ dùng cho thuế nội địa, tạm thời disjunction trong giai đoạn thuế hải quan
        return (root, query, cb) -> cb.disjunction();
    }
}
