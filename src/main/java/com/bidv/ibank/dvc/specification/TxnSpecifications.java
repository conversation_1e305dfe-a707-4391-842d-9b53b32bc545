package com.bidv.ibank.dvc.specification;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.model.dto.TxnSearchBaseDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.request.TxnListReq;
import com.bidv.ibank.dvc.model.request.TxnPendingApprovalListReq;
import com.bidv.ibank.dvc.model.request.TxnPendingListReq;
import com.bidv.ibank.dvc.model.request.TxnReportListReq;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;

public class TxnSpecifications extends BaseSpecifications {

    private static final List<String> SEARCHABLE_FIELDS = Arrays.asList(
            "id",
            "amount",
            "debitAccNo",
            "orgId");

    public static <T> Specification<T> approvalUsersLike(String userName) {
        return columnContains("approvalUsers", userName);
    }

    public static Specification<GOVPaymentTransactionEntity> cusIdEq(Long cusId) {
        return columnEq("cusId", cusId);
    }

    public static Specification<GOVPaymentTransactionEntity> txnIdIn(List<String> txnIds) {
        return columnIn("id", txnIds);
    }

    public static Specification<GOVPaymentTransactionEntity> ccyIn(List<String> ccys) {
        return columnIn("ccy", ccys);
    }

    public static Specification<GOVPaymentTransactionEntity> debitAccNoEq(String debitAccNo) {
        return columnEq("debitAccNo", debitAccNo);
    }

    public static Specification<GOVPaymentTransactionEntity> debitAccNoLike(String debitAccNo) {
        return columnLike("debitAccNo", debitAccNo);
    }

    public static Specification<GOVPaymentTransactionEntity> debitAccNoIn(List<String> debitAccNos) {
        return columnIn("debitAccNo", debitAccNos);
    }

    public static Specification<GOVPaymentTransactionEntity> batchItemIdIn(List<String> batchItemIds) {
        return columnIn("batchItemId", batchItemIds);
    }

    public static Specification<GOVPaymentTransactionEntity> taxCodeEq(String taxCode) {
        return columnEq("taxCode", taxCode);
    }

    public static Specification<GOVPaymentTransactionEntity> taxCodeLike(String taxCode) {
        return columnLike("taxCode", taxCode);
    }

    public static Specification<GOVPaymentTransactionEntity> batchNoEq(String batchNo) {
        return columnEq("batchNo", batchNo);
    }

    public static Specification<GOVPaymentTransactionEntity> batchNoLike(String batchNo) {
        return columnLike("batchNo", batchNo);
    }

    public static Specification<GOVPaymentTransactionEntity> minAmountGte(BigDecimal minAmount) {
        return columnGte("amount", minAmount);
    }

    public static Specification<GOVPaymentTransactionEntity> maxAmountLte(BigDecimal maxAmount) {
        return columnLte("amount", maxAmount);
    }

    public static Specification<GOVPaymentTransactionEntity> statusIn(List<String> statuses) {
        return columnIn("status", statuses);
    }

    public static Specification<GOVPaymentTransactionEntity> statusNotIn(List<String> statuses) {
        return columnNotIn("status", statuses);
    }

    public static Specification<GOVPaymentTransactionEntity> statusEq(String status) {
        return columnEq("status", status);
    }

    public static Specification<GOVPaymentTransactionEntity> txnTypeIn(List<String> txnTypes) {
        return columnIn("txnType", txnTypes);
    }

    public static Specification<GOVPaymentTransactionEntity> coreRefLike(String coreRef) {
        return columnLike("coreRef", coreRef);
    }

    public static Specification<GOVPaymentTransactionEntity> channelIn(List<String> channels) {
        return columnIn("channel", channels);
    }

    public static Specification<GOVPaymentTransactionEntity> searchLike(String search) {
        return columnsLike(SEARCHABLE_FIELDS, search);
    }

    public static Specification<GOVPaymentTransactionEntity> declarationNoExists(String declarationNo) {
        if (StringUtils.isBlank(declarationNo)) {
            return cbConjunction();
        }
        return columnExists(GOVPaymentItemEntity.class, "govPaymentTransactionEntity",
                TxnItemSpecifications.declarationNoLike(declarationNo));
    }

    public static Specification<GOVPaymentTransactionEntity> txnItemIdExists(String txnItemId) {
        if (StringUtils.isBlank(txnItemId)) {
            return cbConjunction();
        }
        return columnExists(GOVPaymentItemEntity.class, "govPaymentTransactionEntity",
                TxnItemSpecifications.txnItemIdLike(txnItemId));
    }

    public static Specification<GOVPaymentTransactionEntity> createTxnPendingListSpec(TxnPendingListReq request) {
        return createBaseTxnListSpec(request)
                .and(statusIn(request.getStatuses()))
                .and(statusIn(List.of(TransactionStatusEnum.INIT.name(), TransactionStatusEnum.REJECTED.name())));
    }

    public static Specification<GOVPaymentTransactionEntity> createTxnPendingApprovalListSpec(TxnPendingApprovalListReq request) {
        return createBaseTxnListSpec(request)
                .and(statusIn(List.of(TransactionStatusEnum.PENDING_APPROVAL.name())))
                .and(channelIn(request.getChannels()))
                .and(txnTypeIn(request.getTxnTypes()))
                .and(txnItemIdExists(request.getTxnItemId()));
    }

    public static Specification<GOVPaymentTransactionEntity> createTxnListSpec(TxnListReq request) {
        return createBaseTxnListSpec(request)
                .and(statusIn(request.getStatuses()))
                .and(statusNotIn(List.of(TransactionStatusEnum.DELETED.name())));
    }

    public static Specification<GOVPaymentTransactionEntity> createTxnReportListSpec(TxnReportListReq request) {
        return createBaseTxnListSpec(request)
                .and(statusIn(request.getStatuses()))
                .and(coreRefLike(request.getTccRefNo()))
                .and(channelIn(request.getChannels()))
                .and(txnTypeIn(request.getTxnTypes()))
                .and(txnItemIdExists(request.getTxnItemId()))
                .and(statusNotIn(List.of(TransactionStatusEnum.DELETED.name())));
    }

    private static Specification<GOVPaymentTransactionEntity> createBaseTxnListSpec(TxnSearchBaseDto request) {
        return Specification.where(cusIdEq(AuthenticationUtils.getCurrentUser().getUser().getCusId()))
                .and(searchLike(request.getSearch())
                        .or(declarationNoExists(request.getSearch())))
                .and(startDateGte(request.getStartDate()))
                .and(endDateLte(request.getEndDate()))
                .and(minAmountGte(request.getMinAmount()))
                .and(maxAmountLte(request.getMaxAmount()))
                .and(taxCodeLike(request.getTaxCode()))
                .and(batchNoLike(request.getBatchNo()))
                .and(debitAccNoLike(request.getDebitAccNo()))
                .and(ccyIn(request.getCcys()))
                .and(declarationNoExists(request.getDeclarationNo()));
    }
}
