package com.bidv.ibank.dvc.specification;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;

public class BaseSpecifications {

    public static <T> Specification<T> createdByEq(String userName) {
        return columnEq("createdBy", userName);
    }

    public static <T> Specification<T> idEq(String id) {
        return columnEq("id", id);
    }

    public static <T> Specification<T> idIn(List<String> ids) {
        return columnIn("id", ids);
    }

    public static <T> Specification<T> startDateGte(LocalDate startDate) {
        return columnGte("createdDate", startDate);
    }

    public static <T> Specification<T> endDateLte(LocalDate endDate) {
        return columnLte("createdDate", endDate);
    }

    protected static <T> Specification<T> columnEq(String columnName, String value) {
        if (StringUtils.isBlank(value))
            return cbConjunction();
        return (root, query, cb) -> cb.equal(root.get(columnName), value.trim());
    }

    protected static <T> Specification<T> columnEq(String columnName, Boolean value) {
        if (value == null)
            return cbConjunction();
        return (root, query, cb) -> cb.equal(root.get(columnName), value);
    }

    protected static <T> Specification<T> columnEq(String columnName, Long value) {
        if (value == null)
            return cbConjunction();
        return (root, query, cb) -> cb.equal(root.get(columnName), value);
    }

    protected static <T> Specification<T> columnIn(String columnName, List<String> values) {
        if (CollectionUtils.isEmpty(values))
            return cbConjunction();
        return (root, query, cb) -> {
            return root.get(columnName).in(values);
        };
    }

    protected static <T> Specification<T> columnNotIn(String columnName, List<String> values) {
        if (CollectionUtils.isEmpty(values))
            return cbConjunction();
        return (root, query, cb) -> cb.not(root.get(columnName).in(values));
    }

    protected static <T> Specification<T> columnGte(String columnName, LocalDate value) {
        if (value == null)
            return cbConjunction();
        return (root, query, cb) -> cb.greaterThanOrEqualTo(root.get(columnName), value.atStartOfDay());
    }

    protected static <T> Specification<T> columnGte(String columnName, BigDecimal value) {
        if (value == null)
            return cbConjunction();
        return (root, query, cb) -> cb.greaterThanOrEqualTo(root.get(columnName), value);
    }

    protected static <T> Specification<T> columnLte(String columnName, LocalDate value) {
        if (value == null)
            return cbConjunction();
        return (root, query, cb) -> cb.lessThanOrEqualTo(root.get(columnName), value.atTime(LocalTime.MAX));
    }

    protected static <T> Specification<T> columnLte(String columnName, BigDecimal value) {
        if (value == null)
            return cbConjunction();
        return (root, query, cb) -> cb.lessThanOrEqualTo(root.get(columnName), value);
    }

    protected static <T> Specification<T> columnLike(String columnName, String value) {
        if (StringUtils.isBlank(value))
            return cbConjunction();
        return (root, query, cb) -> {
            String searchPattern = "%" + value.trim().toLowerCase().replace("\\", "\\\\").replace("%", "\\%") + "%";
            return cb.like(cb.lower(root.get(columnName).as(String.class)), searchPattern, '\\');
        };
    }

    protected static <T> Specification<T> columnContains(String columnName, String value) {
        if (StringUtils.isBlank(value))
            return cbConjunction();

        return (root, query, cb) -> {
            String v = value.trim().toLowerCase()
                    .replace("\\", "\\\\")
                    .replace("%", "\\%");

            return cb.or(
                    cb.equal(cb.lower(root.get(columnName).as(String.class)), v),
                    cb.like(cb.lower(root.get(columnName).as(String.class)), v + ",%", '\\'),
                    cb.like(cb.lower(root.get(columnName).as(String.class)), "%," + v + ",%", '\\'),
                    cb.like(cb.lower(root.get(columnName).as(String.class)), "%," + v, '\\'));
        };
    }

    protected static <T> Specification<T> columnLike(String columnName, String value, String relation, JoinType joinType) {
        if (StringUtils.isBlank(value))
            return cbConjunction();
        return (root, query, cb) -> {
            Join<T, ?> join = root.join(relation, joinType);
            String searchPattern = "%" + value.trim().toLowerCase().replace("\\", "\\\\").replace("%", "\\%") + "%";
            return cb.like(cb.lower(join.get(columnName).as(String.class)), searchPattern, '\\');
        };
    }

    protected static <T> Specification<T> columnsLike(List<String> columnNames, String value) {
        if (StringUtils.isBlank(value)) {
            return cbConjunction();
        }

        return (root, query, cb) -> {
            List<Predicate> predicates = columnNames.stream()
                    .map(field -> {
                        Specification<T> spec = columnLike(field, value);
                        return spec.toPredicate(root, query, cb);
                    })
                    .collect(Collectors.toList());

            return cb.or(predicates.toArray(new Predicate[0]));
        };
    }

    protected static <T, U> Specification<T> columnExists(Class<U> subEntityClass, String subEntityRelationToMainEntity,
            Specification<U> subQuerySpecification) {
        return (root, query, cb) -> { // root is Root<T> for the main query
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<U> subEntityRoot = subquery.from(subEntityClass); // Root<U> for the sub-query
            subquery.select(cb.literal(1L)); // Or cb.count(subEntityRoot)

            // 1. Create the join predicate: links sub-entity back to the main entity
            Predicate joinPredicate = cb.equal(subEntityRoot.get(subEntityRelationToMainEntity), root);

            // 2. Create the filter predicate from the provided sub-query specification
            Predicate filterPredicate = null;
            if (subQuerySpecification != null) {
                // Important: Use the subEntityRoot and the main query's CriteriaQuery and CriteriaBuilder
                // for converting the subQuerySpecification to a Predicate.
                filterPredicate = subQuerySpecification.toPredicate(subEntityRoot, query, cb);
            }

            // 3. Combine predicates for the subquery's WHERE clause
            if (filterPredicate != null) {
                subquery.where(cb.and(joinPredicate, filterPredicate));
            } else {
                subquery.where(joinPredicate);
            }

            return cb.exists(subquery);
        };
    }

    protected static <T> Specification<T> cbConjunction() {
        return (root, query, cb) -> cb.conjunction();
    }
}