package com.bidv.ibank.dvc.specification;

import org.springframework.data.jpa.domain.Specification;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;

public class BatchItemSpecifications extends BaseSpecifications {

    public static Specification<GOVPaymentBatchItemEntity> batchIdEq(String batchId) {
        return columnEq("batchId", batchId);
    }

    public static Specification<GOVPaymentBatchItemEntity> statusEq(String status) {
        return columnEq("status", status);
    }
}
