package com.bidv.ibank.dvc.specification;

import java.util.Arrays;
import java.util.List;

import org.springframework.data.jpa.domain.Specification;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateEntity;
import com.bidv.ibank.dvc.model.request.TemplateListReq;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;

import jakarta.persistence.criteria.JoinType;

public class TemplateSpecifications extends BaseSpecifications {

    private static final List<String> SEARCHABLE_FIELDS = Arrays.asList(
            "name",
            "payerName",
            "amount");

    public static Specification<GOVPaymentTemplateEntity> searchLike(String search) {
        return columnsLike(SEARCHABLE_FIELDS, search);
    }

    public static Specification<GOVPaymentTemplateEntity> cusIdEq(Long cusId) {
        return columnEq("cusId", cusId);
    }

    public static Specification<GOVPaymentTemplateEntity> isPublicEq(Boolean isPublic) {
        return columnEq("isPublic", isPublic);
    }

    public static Specification<GOVPaymentTemplateEntity> treasuryNameLike(String treasuryName) {
        return columnLike("ten", treasuryName, "tccDmKhobacEntity", JoinType.LEFT);
    }

    public static Specification<GOVPaymentTemplateEntity> revAuthNameLike(String revAuthName) {
        return columnLike("ten", revAuthName, "tccDmCqthuEntity", JoinType.LEFT);
    }

    public static Specification<GOVPaymentTemplateEntity> cifNoEq(String cifNo) {
        return columnEq("cifNo", cifNo);
    }

    public static Specification<GOVPaymentTemplateEntity> templateNameEq(String templateName) {
        return columnEq("name", templateName);
    }

    public static Specification<GOVPaymentTemplateEntity> statusIn(List<String> status) {
        return columnIn("status", status);
    }

    public static Specification<GOVPaymentTemplateEntity> debitAccNoIn(List<String> debitAccNos) {
        return columnIn("debitAccNo", debitAccNos);
    }

    public static Specification<GOVPaymentTemplateEntity> statusEq(String status) {
        return columnEq("status", status);
    }

    public static Specification<GOVPaymentTemplateEntity> txnTypeEq(String txnType) {
        return columnEq("txnType", txnType);
    }

    public static Specification<GOVPaymentTemplateEntity> createTxnTemplateListSpec(TemplateListReq request) {
        return Specification.where(cusIdEq(AuthenticationUtils.getCurrentUser().getUser().getCusId()))
                .and((searchLike(request.getSearch())
                        .or(revAuthNameLike(request.getSearch())))
                        .and(txnTypeEq(request.getTxnType())));
    }

    public static Specification<GOVPaymentTemplateEntity> checkExistingTemplate(String templateName, boolean isPublic, String cifNo, String createdBy) {
        Specification<GOVPaymentTemplateEntity> spec = Specification.where(templateNameEq(templateName));
        if (isPublic) {
            spec = spec.and(cifNoEq(cifNo));
        } else {
            spec = spec.and(createdByEq(createdBy));
        }
        return spec;
    }

}
