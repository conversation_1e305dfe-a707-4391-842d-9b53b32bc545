package com.bidv.ibank.dvc.specification;

import java.util.Arrays;
import java.util.List;

import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;

import org.apache.commons.io.FilenameUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.multipart.MultipartFile;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.dvc.model.request.BatchListReq;

public class BatchSpecifications extends BaseSpecifications {
    private static final List<String> SEARCHABLE_FIELDS = Arrays.asList(
            "name",
            "batchNo");

    public static Specification<GOVPaymentBatchEntity> batchNoEq(String batchNo) {
        return columnEq("batchNo", batchNo);
    }

    public static Specification<GOVPaymentBatchEntity> cusIdEq(Long cusId) {
        return columnEq("cusId", cusId);
    }

    public static Specification<GOVPaymentBatchEntity> searchLike(String search) {
        return columnsLike(SEARCHABLE_FIELDS, search);
    }

    public static Specification<GOVPaymentBatchEntity> statusIn(List<String> statuses) {
        return columnIn("status", statuses);
    }

    public static Specification<GOVPaymentBatchEntity> statusNotIn(List<String> statuses) {
        return columnNotIn("status", statuses);
    }

    public static Specification<GOVPaymentBatchEntity> checksumEq(String checksum) {
        return columnEq("checksum", checksum);
    }

    public static Specification<GOVPaymentBatchEntity> batchTypeEq(String batchType) {
        return columnEq("batchType", batchType);
    }

    public static Specification<GOVPaymentBatchEntity> batchNoLike(String batchNo) {
        return columnLike("batchNo", batchNo);
    }

    public static Specification<GOVPaymentBatchEntity> batchNameLike(String batchName) {
        return columnLike("name", batchName);
    }

    public static Specification<GOVPaymentBatchEntity> batchNameEq(String batchName) {
        return columnEq("name", batchName);
    }

    public static Specification<GOVPaymentBatchEntity> cifNoEq(String cifNo) {
        return columnEq("cifNo", cifNo);
    }

    public static Specification<GOVPaymentBatchEntity> createBatchListSpec(BatchListReq request, BatchTypeEnum batchType) {
        return Specification.where(cusIdEq(AuthenticationUtils.getCurrentUser().getUser().getCusId()))
                .and(searchLike(request.getSearch()))
                .and(startDateGte(request.getStartDate()))
                .and(endDateLte(request.getEndDate()))
                .and(statusIn(request.getStatuses()))
                .and(batchNoLike(request.getBatchNo()))
                .and(batchNameLike(request.getBatchName()))
                .and(batchTypeEq(batchType.name()))
                .and(createdByEq(AuthenticationUtils.getCurrentUser().getUser().getUsername()))
                .and(statusNotIn(List.of(BatchStatusEnum.DELETED.name())));
    }

    public static Specification<GOVPaymentBatchEntity> createResultSpec(String batchNo, BatchTypeEnum batchType) {
        return Specification.where(batchNoEq(batchNo))
                .and(statusIn(List.of(BatchStatusEnum.PROCESSED.name(), BatchStatusEnum.CHECKED.name())))
                .and(createdByEq(AuthenticationUtils.getCurrentUser().getUser().getUsername()))
                .and(batchTypeEq(batchType.name()));
    }

    public static Specification<GOVPaymentBatchEntity> checkFileExistSpec(MultipartFile file, String checksum, BatchTypeEnum batchType) {
        String fileName = FilenameUtils.getBaseName(file.getOriginalFilename());

        return Specification.where(batchNameEq(fileName).or(checksumEq(checksum)))
                .and(statusIn(List.of(BatchStatusEnum.PROCESSED.name(), BatchStatusEnum.CHECKED.name(), BatchStatusEnum.PROCESSING.name())))
                .and(batchTypeEq(batchType.name()))
                .and(cifNoEq(AuthenticationUtils.getCurrentUser().getUser().getCif()));
    }
}
