package com.bidv.ibank.dvc.validation;

import com.bidv.ibank.dvc.model.validation.FieldValidator;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.constant.CcyEnum;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.dvc.util.constant.ResponseCode;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BatchImportValidators {
    private static final Map<String, FieldValidator> FIELD_VALIDATORS = createValidators();

    private static Map<String, FieldValidator> createValidators() {
        Map<String, FieldValidator> validators = new HashMap<>();

        validators.put(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO, createDebitAccNoValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.TAX_CODE, createTaxCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.PAYER_NAME, createPayerNameValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.PAYER_ADDR, createPayerAddrValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.ALT_TAX_CODE, createAltTaxCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_NAME, createAltPayerNameValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_ADDR, createAltPayerAddrValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.DECLARATION_NO, createDeclarationNoValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.DECLARATION_DATE, createDeclarationDateValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.TREASURY_CODE, createTreasuryCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.REV_ACC_CODE, createRevAccCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.REV_AUTH_CODE, createRevAuthCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.ADM_AREA_CODE, createAdmAreaCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.CHAPTER_CODE, createChapterCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.EC_CODE, createEcCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.AMOUNT, createAmountValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.CCY, createCurrencyValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.TRANS_DESC, createTransDescValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.TAX_TYPE_CODE, createTaxTypeCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.CC_CODE, createCcCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.EI_TYPE_CODE, createEiTypeCodeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.PAYER_TYPE, createPayerTypeValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.ORG_ID, createOrgIdValidator());

        return validators;
    }

    private static FieldValidator createDebitAccNoValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_DEBIT_ACC_NO_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
                .requiredErrorCode(ResponseCode.DEBIT_ACCNO_REQUIRED)
                .maxLengthErrorCode(ResponseCode.DEBIT_ACCNO_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.DEBIT_ACCNO_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createTaxCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.TAX_CODE)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
                .requiredErrorCode(ResponseCode.TAX_CODE_REQUIRED)
                .maxLengthErrorCode(ResponseCode.TAX_CODE_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.TAX_CODE_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createPayerNameValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.PAYER_NAME)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_NAME_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
                .requiredErrorCode(ResponseCode.PAYER_NAME_REQUIRED)
                .maxLengthErrorCode(ResponseCode.PAYER_NAME_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.PAYER_NAME_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createPayerAddrValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.PAYER_ADDR)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_ADDR_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
                .requiredErrorCode(ResponseCode.PAYER_ADDR_REQUIRED)
                .maxLengthErrorCode(ResponseCode.PAYER_ADDR_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.PAYER_ADDR_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createAltTaxCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.ALT_TAX_CODE)
                .isRequired(false)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
                .maxLengthErrorCode(ResponseCode.ALT_TAX_CODE_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.ALT_TAX_CODE_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createAltPayerNameValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_NAME)
                .isRequired(false)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_NAME_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
                .maxLengthErrorCode(ResponseCode.ALT_PAYER_NAME_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.ALT_PAYER_NAME_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createAltPayerAddrValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_ADDR)
                .isRequired(false)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_ADDR_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
                .maxLengthErrorCode(ResponseCode.ALT_PAYER_ADDR_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.ALT_PAYER_ADDR_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createDeclarationNoValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.DECLARATION_NO)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
                .requiredErrorCode(ResponseCode.DECLARATION_NO_REQUIRED)
                .maxLengthErrorCode(ResponseCode.DECLARATION_NO_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.DECLARATION_NO_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createDeclarationDateValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.DECLARATION_DATE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.DECLARATION_DATE_REQUIRED)
                .pattern(AppConstants.REGEX_VALIDATION.DAY_MONTH_YEAR)
                .patternErrorCode(ResponseCode.DECLARATION_DATE_INVALID_FORMAT)
                .rules(List.of(
                        (value, errors) -> {
                            if (!DateUtils.isValidDateFormat(value)) {
                                errors.add(ResponseCode.DECLARATION_DATE_INVALID_FORMAT);
                            } else {
                                LocalDate date = DateUtils.convertStringToDate(value, AppConstants.DATE_FORMAT_DD_MM_YYYY);
                                if (date.isAfter(LocalDate.now())) {
                                    errors.add(ResponseCode.DECLARATION_DATE_INVALID_DATE);
                                }
                            }
                        }))
                .build();
    }

    private static FieldValidator createTreasuryCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.TREASURY_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.TREASURY_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createRevAccCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.REV_ACC_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.REV_ACC_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createRevAuthCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.REV_AUTH_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.REV_AUTH_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createAdmAreaCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.ADM_AREA_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.ADM_AREA_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createChapterCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.CHAPTER_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.CHAPTER_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createEcCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.EC_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.EC_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createAmountValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.AMOUNT)
                .isRequired(true)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER)
                .requiredErrorCode(ResponseCode.AMOUNT_REQUIRED)
                .patternErrorCode(ResponseCode.AMOUNT_INVALID_FORMAT)
                .rules(List.of(
                        (value, errors) -> {
                            if (value.equals("0")) {
                                errors.add(ResponseCode.AMOUNT_CANNOT_BE_ZERO);
                            }
                        }))
                .build();
    }

    private static FieldValidator createCurrencyValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.CCY)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_CCY_LENGTH)
                .requiredErrorCode(ResponseCode.CCY_REQUIRED)
                .rules(List.of(
                        (value, errors) -> {
                            if (!value.equals(CcyEnum.VND.name())) {
                                errors.add(ResponseCode.CCY_MUST_BE_VND);
                            }
                        }))
                .build();
    }

    private static FieldValidator createTransDescValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.TRANS_DESC)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_TRANS_DESC_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
                .patternErrorCode(ResponseCode.TRANS_DESC_INVALID_FORMAT)
                .requiredErrorCode(ResponseCode.TRANS_DESC_REQUIRED)
                .maxLengthErrorCode(ResponseCode.TRANS_DESC_EXCEED_MAX_LENGTH)
                .build();
    }

    private static FieldValidator createTaxTypeCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.TAX_TYPE_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.TAX_TYPE_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createCcCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.CC_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.CC_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createEiTypeCodeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.EI_TYPE_CODE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.EI_TYPE_CODE_REQUIRED)
                .build();
    }

    private static FieldValidator createPayerTypeValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.PAYER_TYPE)
                .isRequired(true)
                .requiredErrorCode(ResponseCode.PAYER_TYPE_REQUIRED)
                .rules(List.of(
                        (value, errors) -> {
                            if (Arrays.stream(PayerTypeEnum.values())
                                    .noneMatch(type -> type.getValue().toString().equals(value))) {
                                errors.add(ResponseCode.PAYER_TYPE_INVALID_FORMAT);
                            }
                        }))
                .build();
    }

    private static FieldValidator createOrgIdValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.ORG_ID)
                .isRequired(false)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_ORG_ID_LENGTH)
                .maxLengthErrorCode(ResponseCode.ORG_ID_EXCEED_MAX_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_LINE_AND_UNDERSCORE_SPACE)
                .patternErrorCode(ResponseCode.ORG_ID_INVALID_FORMAT)
                .build();
    }

    public static Map<String, FieldValidator> getValidators() {
        return FIELD_VALIDATORS;
    }
}