package com.bidv.ibank.dvc.validation;

import com.bidv.ibank.dvc.model.validation.FieldValidator;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;

import java.time.Year;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BatchTaxImportValidators {
    private static final Map<String, FieldValidator> FIELD_VALIDATORS = createValidators();

    private static Map<String, FieldValidator> createValidators() {
        Map<String, FieldValidator> validators = new HashMap<>();

        validators.put(AppConstants.BATCH_FIELD_CODE.DECLARATION_NO, createDeclarationNoValidator());
        validators.put(AppConstants.BATCH_FIELD_CODE.DECLARATION_YEAR, createDeclarationYearValidator());

        return validators;
    }

    private static FieldValidator createDeclarationNoValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.DECLARATION_NO)
                .isRequired(true)
                .maxLength(AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH)
                .pattern(AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
                .requiredErrorCode(ResponseCode.INQUIRY_DECLARATION_NO_REQUIRED)
                .maxLengthErrorCode(ResponseCode.INQUIRY_DECLARATION_NO_EXCEED_MAX_LENGTH)
                .patternErrorCode(ResponseCode.INQUIRY_DECLARATION_NO_INVALID_FORMAT)
                .build();
    }

    private static FieldValidator createDeclarationYearValidator() {
        return FieldValidator.builder()
                .fieldName(AppConstants.BATCH_FIELD_CODE.DECLARATION_YEAR)
                .isRequired(false)
                .pattern(AppConstants.REGEX_VALIDATION.YEAR_PATTERN)
                .patternErrorCode(ResponseCode.DECLARATION_YEAR_INVALID_FORMAT)
                .rules(List.of(
                        (value, errors) -> {
                            if (value.matches(AppConstants.REGEX_VALIDATION.YEAR_PATTERN)) {
                                int currentYear = Year.now().getValue();
                                if (Integer.valueOf(value) > currentYear) {
                                    errors.add(ResponseCode.DECLARATION_YEAR_INVALID_YEAR);
                                }
                            }
                        }))
                .build();
    }

    public static Map<String, FieldValidator> getValidators() {
        return FIELD_VALIDATORS;
    }
}