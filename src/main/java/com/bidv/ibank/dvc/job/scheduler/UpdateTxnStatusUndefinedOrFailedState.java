package com.bidv.ibank.dvc.job.scheduler;

import com.bidv.ibank.dvc.service.JobService;
import com.bidv.quartz.job.BaseJobBean;
import org.quartz.JobExecutionContext;

public class UpdateTxnStatusUndefinedOrFailedState extends BaseJobBean {

    private JobService jobService;

    public UpdateTxnStatusUndefinedOrFailedState(JobService jobService) {
        super("UpdateTxnStatusUndefinedOrFailedState");
        this.jobService = jobService;
    }

    @Override
    public void executeJob(JobExecutionContext context) {
        jobService.updateTxnStatusUndefinedOrFailedState();
    }
}