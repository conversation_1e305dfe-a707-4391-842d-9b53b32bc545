package com.bidv.ibank.dvc.job.scheduler;

import com.bidv.ibank.dvc.service.JobService;
import com.bidv.quartz.job.BaseJobBean;
import org.quartz.JobExecutionContext;

public class RetryUnderBalance extends BaseJobBean {

    private JobService jobService;

    public RetryUnderBalance(JobService jobService) {
        super("RetryUnderBalance");
        this.jobService = jobService;
    }

    @Override
    public void executeJob(JobExecutionContext context) {
        jobService.retryUnderBalance();
    }
}