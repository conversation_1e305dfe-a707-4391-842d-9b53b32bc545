package com.bidv.ibank.dvc.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TxnPrintDocumentReq {

    @NotEmpty
    @Schema(example = "['*******************', '*******************']", description = "Mã giao dịch")
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ID_LENGTH) @NotBlank @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER) String> txnIds;
}
