package com.bidv.ibank.dvc.model.entity.customsduty;

import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;

import com.bidv.ibank.common.txn.model.entity.TransactionBaseEntityWithFee;
import com.bidv.ibank.common.txn.processor.CustOTAndRetry;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "GOV_PAYMENT_TRANSACTION")
public class GOVPaymentTransactionEntity extends TransactionBaseEntityWithFee implements ItfGOVPaymentBaseEntity, CustOTAndRetry {

    @NotNull
    @Size(max = 20)
    @Column(name = "TAX_CODE", length = 20)
    private String taxCode;

    @Size(max = 20)
    @Column(name = "ALT_TAX_CODE", length = 20)
    private String altTaxCode;

    @NotNull
    @Size(max = 140)
    @Column(name = "PAYER_NAME", length = 140)
    private String payerName;

    @Size(max = 140)
    @Column(name = "ALT_PAYER_NAME", length = 140)
    private String altPayerName;

    @NotNull
    @Size(max = 140)
    @Column(name = "PAYER_ADDR", length = 140)
    private String payerAddr;

    @Size(max = 140)
    @Column(name = "ALT_PAYER_ADDR", length = 140)
    private String altPayerAddr;

    @NotNull
    @Column(name = "PAYER_TYPE")
    @Enumerated(EnumType.ORDINAL)
    private PayerTypeEnum payerType;

    @Size(max = 20)
    @Column(name = "BATCH_NO", length = 20)
    private String batchNo;

    @NotNull
    @Size(max = 4)
    @Column(name = "SHKB", length = 4)
    private String shkb;

    @NotNull
    @Size(max = 5)
    @Column(name = "MA_DBHC", length = 5)
    private String maDbhc;

    @NotNull
    @Size(max = 7)
    @Column(name = "MA_CQTHU", length = 7)
    private String maCqthu;

    @NotNull
    @Size(max = 50)
    @Column(name = "MA_TK", length = 50)
    private String maTk;

    @NotNull
    @Size(max = 8)
    @Column(name = "MA_NH", length = 8)
    private String maNh;

    @Size(max = 30)
    @Column(name = "TCC_DOC_ID", length = 30)
    private String tccDocId;

    @Size(max = 30)
    @Column(name = "TCC_DOC_SIGN", length = 30)
    private String tccDocSign;

    @Size(max = 30)
    @Column(name = "TCC_DOC_NO", length = 30)
    private String tccDocNo;

    @Size(max = 50)
    @Column(name = "TCC_ERR_CODE", length = 50)
    private String tccErrCode;

    @Size(max = 1000)
    @Column(name = "TCC_ERR_DESC", length = 1000)
    private String tccErrDesc;

    @Size(max = 30)
    @Column(name = "TCC_RM_NO", length = 30)
    private String tccRmNo;

    @Size(max = 50)
    @Column(name = "TCC_ID_CORE", length = 50)
    private String tccIdCore;

    @Column(name = "UPD_STATUS_CNT")
    private Integer updStatusCnt;

    @Size(max = 30)
    @Column(name = "CUST_OVER_COT", length = 30)
    private String custOverCot;

    @Size(max = 1)
    @Column(name = "RETRY_INSFCT_BAL", length = 1)
    private String retryInsfctBal;

    @Column(name = "RETRY_INSFCT_BAL_CNT")
    private Integer retryInsfctBalCnt;

    @Size(max = 36)
    @Column(name = "BATCH_ITEM_ID", length = 36)
    private String batchItemId;

    @Column(name = "FINISH_DATE")
    private LocalDateTime finishDate;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "govPaymentTransactionEntity")
    @Builder.Default
    @OrderBy("createdDate")
    private Set<GOVPaymentItemEntity> govPaymentItemList = new LinkedHashSet<>();

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    private TccDmKhobacEntity tccDmKhobacEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_DBHC", referencedColumnName = "MA_DBHC", insertable = false, updatable = false)
    private TccDmDbhcEntity tccDmDbhcEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_CQTHU", referencedColumnName = "MA_CQTHU", insertable = false, updatable = false)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    private TccDmCqthuEntity tccDmCqthuEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_TK", referencedColumnName = "MA_TK", insertable = false, updatable = false)
    private TccDmTkNsnnEntity tccDmTkNsnnEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    @JoinColumn(name = "MA_NH", referencedColumnName = "MA_NH", insertable = false, updatable = false)
    private TccDmKbnnNhtmEntity tccDmKbnnNhtmEntity;

    @Transient
    @Builder.Default
    private Set<GOVPaymentItemEntity> paymentItems = new LinkedHashSet<>();

    @Transient
    private String batchId;
}
