package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmKhobacCodeNameEntityDto implements Exportable {
    private String shkb;
    private String tenKhobac;

    @ExportConfig(colIndex = 1)
    public String getShkb() {
        return shkb;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenKhobac() {
        return StrUtils.combineCodeName(shkb, tenKhobac);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
