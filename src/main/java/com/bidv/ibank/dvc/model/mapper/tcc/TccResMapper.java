package com.bidv.ibank.dvc.model.mapper.tcc;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Component;

import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.constant.CcyEnum;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.util.mapper.EntityDtoMapper;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationItem;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationItemDebtDetail;

@Component
public class TccResMapper implements EntityDtoMapper<Object, Object> {

    @Override
    public Object toDto(Object entity) {
        throw new UnsupportedOperationException("Unimplemented method 'toDto'");
    }

    @Override
    public Object toEntity(Object dto) {
        throw new UnsupportedOperationException("Unimplemented method 'toEntity'");
    }

    public List<InquiryCustomsDutyRes> toInquiryCustomsDutyRes(TccInquiryDeclarationItem item) {
        List<InquiryCustomsDutyRes> result = new ArrayList<>();
        for (TccInquiryDeclarationItemDebtDetail itemDetail : item.getCtNo()) {
            if (itemDetail.getDuNo() == null || new BigDecimal(itemDetail.getDuNo()).compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            String revAccCode = Optional.ofNullable(item.getTkkb())
                    .map(s -> s.substring(0, Math.min(4, s.length())))
                    .orElse(null);
            result.add(InquiryCustomsDutyRes.builder()
                    .declarationNo(item.getSoTk())
                    .declarationDate(DateUtils.convertStringToDate(item.getNgayDk(), AppConstants.DATE_FORMAT_YYYY_MM_DD))
                    .chapterCode(item.getMaChuong())
                    .ecCode(itemDetail.getTieuMuc())
                    .revAuthCode(item.getMaHqCqt())
                    .revAuthName(item.getTenHqPh())
                    .revAccCode(revAccCode)
                    .taxTypeCode(itemDetail.getLoaiThue())
                    .eiTypeCode(item.getMaLh())
                    .eiTypeName(item.getTenLh())
                    .ccCode(item.getMaLt())
                    .treasuryCode(item.getMaKb())
                    .treasuryName(item.getTenKb())
                    .amount(itemDetail.getDuNo())
                    .payerType(PayerTypeEnum.BUSINESS.getValue())
                    .ccy(CcyEnum.VND.name())
                    .build());
        }

        return result;
    }
}
