package com.bidv.ibank.dvc.model.dto.contracts;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.bidv.ibank.dvc.model.dto.PayerTypeDto;
import com.bidv.ibank.dvc.model.dto.TxnGeneralInfoDto;
import com.bidv.ibank.dvc.model.dto.TxnParamBenBankDto;
import com.bidv.ibank.dvc.model.dto.TxnParamCodeDto;
import com.bidv.ibank.dvc.model.dto.TxnParamNameDto;
import com.bidv.ibank.dvc.model.dto.TxnDetailStatusDto;
import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;
import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class TxnDetailDto implements TxnParamCodeDto, TxnParamNameDto, TxnGeneralInfoDto, PayerTypeDto, TxnParamBenBankDto, TxnDetailStatusDto {

    private String taxCode;
    private String altTaxCode;
    private String payerName;
    private String altPayerName;
    private String payerAddr;
    private String altPayerAddr;
    private String treasuryCode;
    private String treasuryName;
    private String admAreaCode;
    private String admAreaName;
    private String revAccCode;
    private String revAccName;
    private String revAuthCode;
    private String revAuthName;
    private String benBankCode;
    private String benBankName;
    private Integer payerType;

    @Schema(example = "**********", description = "Tài khoản trích nợ")
    private String debitAccNo;

    @Schema(example = "TXN001", description = "Mã giao dịch")
    private String txnId;

    @Schema(example = "Công ty TNHH A", description = " Tên tài khoản trích nợ")
    private String debitAccName;

    @Schema(example = "*********", description = "Số tiền")
    private BigDecimal amount;

    @Schema(example = "VND", description = "Loại tiền tệ")
    private String ccy;

    @Schema(example = "BATCH001", description = "Mã lô")
    private String batchNo;

    @Schema(example = "SUCCESS", description = "Trạng thái giao dịch")
    private String status;

    @Schema(example = "2021-01-01 12:00:00", description = "Ngày tạo")
    private LocalDateTime createdDate;

    @Schema(example = "2021-01-01 12:00:00", description = "Ngày cập nhật")
    private LocalDateTime updatedDate;

    @Schema(example = "2021-01-01", description = "Ngày hiệu lực")
    private LocalDate effDate;

    @Schema(example = "88060ktv", description = "Người tạo")
    private String createdBy;

    @Schema(example = "88060ktv", description = "Số ref TCC")
    private String tccRefNo;

    @Schema(example = "10000", description = "Phí dịch vụ (bao gồm VAT)")
    private BigDecimal feeTotal;

    @Schema(example = "Phí khoán", description = "Hình thức thu phí")
    private String feeOpt;

    @Schema(example = "VND", description = "Loại tiền tệ phí")
    private String feeCcy;

    @Schema(example = "235947523904", description = "Mã giao dịch khách hàng")
    private String orgId;

    @Schema(example = "Mô tả", description = "Mô tả")
    private String description;

    @Schema(example = "WEB", description = "Kênh giao dịch")
    private String channel;

    @Schema(example = "", description = "Danh sách thông tin khoản nộp")
    private List<TxnTaxFullItemDto> taxItems;

    @JsonIgnore
    private String state;

    @Schema(example = "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi", description = "Số tiền bằng chữ")
    public String getAmountText() {
        return ReadNumber.formatAmountToText(Translator.getLocale().toLanguageTag().toLowerCase(), this.getAmount().toString(), this.getCcy());
    }

    @Override
    public String getTreasuryName() {
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    @Override
    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }

    @Override
    public String getRevAccName() {
        return Translator.toLocale(revAccCode, revAccName);
    }

    @Override
    public String getRevAuthName() {
        return Translator.toLocale(revAuthCode, revAuthName);
    }

    @Override
    public String getBenBankName() {
        return StringUtils.defaultString(benBankName);
    }

}