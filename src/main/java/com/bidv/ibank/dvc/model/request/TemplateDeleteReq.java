package com.bidv.ibank.dvc.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.bidv.ibank.dvc.util.AppConstants;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateDeleteReq {

    @Schema(example = "['26CE7FAC-29E1-4F33-9DE7-0830BFDB0122']", description = "Mã mẫu thanh toán")
    @NotEmpty
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_UUID_LENGTH) @NotBlank @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_LINE) String> templateIds;
}
