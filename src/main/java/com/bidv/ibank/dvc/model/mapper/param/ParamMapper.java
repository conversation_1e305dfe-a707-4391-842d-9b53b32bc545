package com.bidv.ibank.dvc.model.mapper.param;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.response.AdministrativeAreaRes;
import com.bidv.ibank.dvc.model.response.ChapterRes;
import com.bidv.ibank.dvc.model.response.CustomsCurrencyRes;
import com.bidv.ibank.dvc.model.response.EconomicContentRes;
import com.bidv.ibank.dvc.model.response.ExportImportType;
import com.bidv.ibank.dvc.model.response.RevenueAccountRes;
import com.bidv.ibank.dvc.model.response.RevenueAuthorityRes;
import com.bidv.ibank.dvc.model.response.TaxTypeRes;
import com.bidv.ibank.dvc.model.response.TreasuryRes;
import com.bidv.ibank.framework.util.mapper.EntityDtoMapper;

@Component
public class ParamMapper implements EntityDtoMapper<Object, Object> {

    @Override
    public Object toDto(Object e) {
        throw new UnsupportedOperationException("Unimplemented method 'toDto'");
    }

    @Override
    public Object toEntity(Object dto) {
        throw new UnsupportedOperationException("Unimplemented method 'toEntity'");
    }

    public ChapterRes toChapterRes(TccDmChuongEntity chapter) {
        return ChapterRes.builder()
                .chapterCode(chapter.getMaChuong())
                .chapterName(chapter.getTen())
                .build();
    }

    public EconomicContentRes toEconomicContentRes(TccDmNdktEntity ec) {
        return EconomicContentRes.builder()
                .ecCode(ec.getMaNdkt())
                .ecName(ec.getTen())
                .build();
    }

    public TaxTypeRes toTaxTypeRes(TccDmSthueHqaEntity tax) {
        return TaxTypeRes.builder()
                .taxTypeCode(tax.getMaSthue())
                .taxTypeName(tax.getTenSthue())
                .build();
    }

    public CustomsCurrencyRes toCustomsCurrencyRes(TccDmLoaitienhqaEntity cc) {
        return CustomsCurrencyRes.builder()
                .ccCode(cc.getMaLthq())
                .ccName(cc.getTenLthq())
                .build();
    }

    public ExportImportType toExportImportType(TccDmLhxnkEntity ie) {
        return ExportImportType.builder()
                .eiTypeCode(ie.getMaLh())
                .eiTypeName(ie.getTen())
                .build();
    }

    public TreasuryRes toTreasuryRes(TccDmKhobacEntity treasury) {
        return TreasuryRes.builder()
                .treasuryCode(treasury.getShkb())
                .treasuryName(treasury.getTen())
                .admAreaCode(Optional.ofNullable(treasury.getTccDmDbhcEntity()).map(TccDmDbhcEntity::getMaDbhc).orElse(null))
                .admAreaName(Optional.ofNullable(treasury.getTccDmDbhcEntity()).map(TccDmDbhcEntity::getTen).orElse(null))
                .build();
    }

    public TreasuryRes toTreasuryRes(TccDmKbnnNhtmEntity treasury) {
        return TreasuryRes.builder()
                .treasuryCode(treasury.getShkb())
                .treasuryName(treasury.getTenKb())
                .build();
    }

    public RevenueAccountRes toRevenueAccountRes(TccDmTkNsnnEntity account) {
        return RevenueAccountRes.builder()
                .revAccCode(account.getMaTk())
                .revAccName(account.getTen())
                .build();
    }

    public RevenueAuthorityRes toRevenueAuthorityRes(TccDmCqthuEntity tca) {
        return RevenueAuthorityRes.builder()
                .revAuthCode(tca.getMaCqthu())
                .revAuthName(tca.getTen())
                .build();
    }

    public AdministrativeAreaRes toAdministrativeAreaRes(TccDmDbhcEntity admArea) {
        return AdministrativeAreaRes.builder()
                .admAreaCode(admArea.getMaDbhc())
                .admAreaName(admArea.getTen())
                .build();
    }
}