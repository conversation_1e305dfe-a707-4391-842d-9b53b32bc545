package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.common.txn.util.constant.TransactionCodeEnum;
import com.bidv.ibank.dvc.util.AppConstants;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MappingTreasuryBenBankCodeDto {

    private String treasuryCode;
    private String benBankCode;
    private String benBankName;
    private boolean isInBidv;
    private String debtAccCode;

    public String getTxnCode() {
        return isInBidv ? TransactionCodeEnum.GOV01.code() : TransactionCodeEnum.GOV02.code();
    }

    public String getTccChannel() {
        return isInBidv ? AppConstants.TCC_VALIDATE_DOC.VOSTRO : AppConstants.TCC_VALIDATE_DOC.OL4;
    }
}
