package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.model.validation.FieldValidator;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.CcyUtils;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.excel.converter.ExtractCodeFromTextConverter;
import com.bidv.ibank.dvc.util.excel.converter.FormatStringExcelConverter;
import com.bidv.ibank.dvc.validation.BatchImportValidators;
import com.bidv.ibank.framework.remote.config.database.entity.ProductMapping;
import com.bidv.ibank.integrate.entity.account.CoreAccount;
import com.bidv.ibank.util.excel.ImportConfig;
import com.bidv.ibank.util.excel.Importable;

import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchImportItemDto implements Importable, Comparable<BatchImportItemDto>, Serializable {

    @Setter
    @ImportConfig(colIndex = 0, converter = FormatStringExcelConverter.class)
    private String order;

    @ImportConfig(colIndex = 1, converter = FormatStringExcelConverter.class)
    private String debitAccNo;

    @ImportConfig(colIndex = 2, converter = FormatStringExcelConverter.class)
    private String taxCode;

    @ImportConfig(colIndex = 3, converter = FormatStringExcelConverter.class)
    private String payerName;

    @ImportConfig(colIndex = 4, converter = FormatStringExcelConverter.class)
    private String payerAddr;

    @ImportConfig(colIndex = 5, converter = FormatStringExcelConverter.class)
    private String altTaxCode;

    @ImportConfig(colIndex = 6, converter = FormatStringExcelConverter.class)
    private String altPayerName;

    @ImportConfig(colIndex = 7, converter = FormatStringExcelConverter.class)
    private String altPayerAddr;

    @ImportConfig(colIndex = 8, converter = FormatStringExcelConverter.class)
    private String declarationNo;

    @ImportConfig(colIndex = 9, converter = FormatStringExcelConverter.class)
    private String declarationDate;

    @ImportConfig(colIndex = 10, converter = ExtractCodeFromTextConverter.class)
    private String treasuryCode;

    @ImportConfig(colIndex = 11, converter = ExtractCodeFromTextConverter.class)
    private String revAccCode;

    @ImportConfig(colIndex = 12, converter = ExtractCodeFromTextConverter.class)
    private String revAuthCode;

    @ImportConfig(colIndex = 13, converter = ExtractCodeFromTextConverter.class)
    private String admAreaCode;

    @ImportConfig(colIndex = 14, converter = ExtractCodeFromTextConverter.class)
    private String chapterCode;

    @ImportConfig(colIndex = 15, converter = ExtractCodeFromTextConverter.class)
    private String ecCode;

    @ImportConfig(colIndex = 16, converter = FormatStringExcelConverter.class)
    private String amount;

    @ImportConfig(colIndex = 17, converter = FormatStringExcelConverter.class)
    private String ccy;

    @ImportConfig(colIndex = 18, converter = FormatStringExcelConverter.class)
    private String transDesc;

    @ImportConfig(colIndex = 19, converter = ExtractCodeFromTextConverter.class)
    private String taxTypeCode;

    @ImportConfig(colIndex = 20, converter = ExtractCodeFromTextConverter.class)
    private String ccCode;

    @ImportConfig(colIndex = 21, converter = ExtractCodeFromTextConverter.class)
    private String eiTypeCode;

    @ImportConfig(colIndex = 22, converter = ExtractCodeFromTextConverter.class)
    private String payerType;

    @ImportConfig(colIndex = 23, converter = FormatStringExcelConverter.class)
    private String orgId;

    private int rowNum;
    private int index;

    private String revAuthName;
    private MappingTreasuryBenBankCodeDto treasuryInfo;
    private CoreAccount accountInfo;
    private String processFlow;
    private ProductMapping productMapping;
    private String tccErrCode;
    private String tccErrMsg;
    @Builder.Default
    private Map<String, Set<String>> fieldErrors = new HashMap<>();

    @Override
    public int getRownum() {
        return rowNum;
    }

    @Override
    public void setRownum(int rowNum) {
        this.rowNum = rowNum;
    }

    @Override
    public int getIndex() {
        return index;
    }

    @Override
    public void setIndex(int index) {
        this.index = index;
    }

    @Override
    public int compareTo(BatchImportItemDto o) {
        return Integer.compare(this.getRownum(), o.getRownum());
    }

    public void validate() {
        fieldErrors.clear();
        tccErrCode = null;
        tccErrMsg = null;

        // Get validators from the dedicated validator class
        Map<String, FieldValidator> validators = BatchImportValidators.getValidators();

        // Validate each field using validators
        validators.forEach((fieldName, validator) -> {
            try {
                String value = (String) this.getClass().getDeclaredField(fieldName).get(this);
                List<ResponseCode> errors = validator.validate(value);
                if (!errors.isEmpty()) {
                    fieldErrors.put(fieldName, errors.stream().map(ResponseCode::code).collect(Collectors.toSet()));
                }
            } catch (Exception e) {
                fieldErrors.put(fieldName, Set.of(ResponseCode.TIMEOUT_01.code()));
            }
        });

        if (StringUtils.isNotBlank(altTaxCode)) {
            if (StringUtils.isBlank(altPayerName)) {
                addFieldError(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_NAME, ResponseCode.ALT_PAYER_NAME_REQUIRED.code());
            }
            if (StringUtils.isBlank(altPayerAddr)) {
                addFieldError(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_ADDR, ResponseCode.ALT_PAYER_ADDR_REQUIRED.code());
            }
        }
        if (StringUtils.isNotBlank(amount)) {
            if (!CcyUtils.isValidMaxLengthAmount(amount, ccy)) {
                addFieldError(AppConstants.BATCH_FIELD_CODE.AMOUNT, CcyUtils.getResponseCodeByCcy(ccy).code());
            }
            if (!CcyUtils.isValidStringAmount(amount, ccy)) {
                addFieldError(AppConstants.BATCH_FIELD_CODE.AMOUNT, ResponseCode.AMOUNT_INVALID_FORMAT.code());
            }
        }
    }

    public BatchValidationStatusDto getFieldValidationStatusAfterValidate() {
        return BatchValidationStatusDto.builder()
                .debitAccNoValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO))
                .taxCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.TAX_CODE))
                .payerNameValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.PAYER_NAME))
                .payerAddrValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.PAYER_ADDR))
                .altTaxCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.ALT_TAX_CODE))
                .altPayerNameValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_NAME))
                .altPayerAddrValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.ALT_PAYER_ADDR))
                .declarationNoValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.DECLARATION_NO))
                .declarationDateValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.DECLARATION_DATE))
                .treasuryCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.TREASURY_CODE))
                .revAccCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.REV_ACC_CODE))
                .revAuthCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.REV_AUTH_CODE))
                .admAreaCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.ADM_AREA_CODE))
                .chapterCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.CHAPTER_CODE))
                .ecCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.EC_CODE))
                .amountValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.AMOUNT))
                .ccyValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.CCY))
                .transDescValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.TRANS_DESC))
                .taxTypeCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.TAX_TYPE_CODE))
                .ccCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.CC_CODE))
                .eiTypeCodeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.EI_TYPE_CODE))
                .payerTypeValid(!hasErrorsForField(AppConstants.BATCH_FIELD_CODE.PAYER_TYPE))
                .build();
    }

    public boolean isValidRow() {
        return (MapUtils.isEmpty(fieldErrors) ||
                (fieldErrors.size() == 1 && fieldErrors.containsKey(AppConstants.BATCH_FIELD_CODE.EXTRA_FIELD_CODE)))
                && StringUtils.isBlank(tccErrCode)
                && StringUtils.isBlank(tccErrMsg);
    }

    public boolean isValidDebitAccNoAmountCcyTreasuryRow() {
        return !hasErrorsForField(AppConstants.BATCH_FIELD_CODE.DEBIT_ACC_NO)
                && !hasErrorsForField(AppConstants.BATCH_FIELD_CODE.AMOUNT)
                && !hasErrorsForField(AppConstants.BATCH_FIELD_CODE.CCY)
                && !hasErrorsForField(AppConstants.BATCH_FIELD_CODE.TREASURY_CODE);
    }

    public void addFieldError(String fieldName, String errorCode) {
        fieldErrors.computeIfAbsent(fieldName, k -> new HashSet<>()).add(errorCode);
    }

    private boolean hasErrorsForField(String fieldName) {
        return fieldErrors.containsKey(fieldName) && !fieldErrors.get(fieldName).isEmpty();
    }
}
