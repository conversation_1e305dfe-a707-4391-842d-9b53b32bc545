package com.bidv.ibank.dvc.model.entity.param;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TCC_DM_NDKT")
public class TccDmNdktEntity {

    @Id
    @Size(max = 4)
    @Column(name = "MA_NDKT", length = 4)
    private String maNdkt;

    @Nullable
    @Size(max = 500)
    @Column(name = "TEN", length = 500)
    private String ten;
}