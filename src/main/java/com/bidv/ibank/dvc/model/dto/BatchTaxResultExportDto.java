package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaxResultExportDto implements Exportable {
    @ExportConfig(colIndex = 0)
    private String status;

    @ExportConfig(colIndex = 1)
    private String description;

    @ExportConfig(colIndex = 2)
    private String declarationNo;

    @ExportConfig(colIndex = 3)
    private String declarationYear;

    private String errCodes;
    private String tccErrMsg;

    public String getStatus() {
        return Translator.toLocale(AppConstants.LANGUAGE.BATCH_TAX_CHECKED_STATUS + "." + status);
    }

    public String getDescription() {
        if (StringUtils.isBlank(errCodes)) {
            return tccErrMsg;
        }
        String errCodeStr = Arrays.stream(errCodes.split(","))
                .map(errCode -> Translator.toLocale(AppConstants.LANGUAGE.RESPONSE_CODE + "." + errCode, errCode))
                .collect(Collectors.joining(";"));
        return StringUtils.isBlank(tccErrMsg) ? errCodeStr : errCodeStr + ";" + tccErrMsg;
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
