package com.bidv.ibank.dvc.model.dto;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class BatchValidationStatusDto {
    private boolean debitAccNoValid;
    private boolean taxCodeValid;
    private boolean payerNameValid;
    private boolean payerAddrValid;
    private boolean altTaxCodeValid;
    private boolean altPayerNameValid;
    private boolean altPayerAddrValid;
    private boolean declarationNoValid;
    private boolean declarationDateValid;
    private boolean treasuryCodeValid;
    private boolean revAccCodeValid;
    private boolean revAuthCodeValid;
    private boolean admAreaCodeValid;
    private boolean chapterCodeValid;
    private boolean ecCodeValid;
    private boolean amountValid;
    private boolean ccyValid;
    private boolean transDescValid;
    private boolean taxTypeCodeValid;
    private boolean ccCodeValid;
    private boolean eiTypeCodeValid;
    private boolean payerTypeValid;
}