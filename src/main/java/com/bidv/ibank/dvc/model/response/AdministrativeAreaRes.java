package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdministrativeAreaRes {

    private String admAreaCode;
    private String admAreaName;

    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }
}