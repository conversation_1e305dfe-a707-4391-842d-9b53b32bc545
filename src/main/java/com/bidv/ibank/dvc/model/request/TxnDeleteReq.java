package com.bidv.ibank.dvc.model.request;

import java.util.List;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TxnDeleteReq {

    @NotEmpty
    @Schema(example = "[\"12345\", \"67890\"]", description = "Các mã giao dịch")
    private List<@NotBlank @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ID_LENGTH) @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER) String> txnIds;

}
