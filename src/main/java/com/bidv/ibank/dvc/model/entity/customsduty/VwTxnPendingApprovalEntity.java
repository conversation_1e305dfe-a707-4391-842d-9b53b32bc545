package com.bidv.ibank.dvc.model.entity.customsduty;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Immutable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Immutable
@Table(name = "VW_TXN_PENDING_APPROVAL")
public class VwTxnPendingApprovalEntity {

    @Id
    @Size(max = 40)
    @Column(name = "ID", length = 40)
    private String id;

    @Size(max = 30)
    @Column(name = "ASSIGNEE", length = 30)
    private String assignee;

    @Size(max = 50)
    @Column(name = "TXN_ID", length = 50)
    private String txnId;

    @Size(max = 20)
    @Column(name = "TXN_CODE", length = 20)
    private String txnCode;

    @Size(max = 50)
    @Column(name = "PROD_CODE", length = 50)
    private String prodCode;

    @Size(max = 50)
    @Column(name = "SUB_PROD_CODE", length = 50)
    private String subProdCode;

    @Column(name = "CUS_ID")
    private Long cusId;

    @Size(max = 20)
    @Column(name = "TXN_STATUS", length = 20)
    private String txnStatus;
}
