package com.bidv.ibank.dvc.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.bidv.ibank.dvc.annotation.ValidTxnPushParams;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.TxnPushTypeEnum;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ValidTxnPushParams
public class TxnInitPushReq {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TRANS_KEY_LENGTH)
    @Schema(example = "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86", description = "Key giao dịch")
    private String transKey;

    @Schema(example = "['*******************', '*******************']", description = "Mã giao dịch")
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ID_LENGTH) @NotBlank @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER) String> txnIds;

    @NotNull
    @Schema(example = "PUSH", description = "Loại đẩy duyệt")
    private TxnPushTypeEnum type;
}
