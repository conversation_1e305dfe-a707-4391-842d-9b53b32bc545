package com.bidv.ibank.dvc.model.request;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class TxnSaveReq {

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TRANS_KEY_LENGTH)
    @Schema(example = "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86", description = "Key giao dịch")
    private String transKey;
}
