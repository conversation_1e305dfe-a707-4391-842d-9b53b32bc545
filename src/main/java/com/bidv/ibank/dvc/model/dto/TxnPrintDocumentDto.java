package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.client.common.dto.workflow.WfTxnInfoResponse;
import com.bidv.ibank.common.txn.util.constant.TransactionCodeEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.dvc.util.constant.UserTitleEnum;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;
import lombok.Builder;
import lombok.Data;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@Builder
public class TxnPrintDocumentDto {
    private String txnId;
    private String txnCode;
    private String status;
    private String admAreaCode;
    private String tccDocSign;
    private String tccDocNo;
    private String coreRef;
    private String tccRmNo;
    private String refNumber;
    private String payerName;
    private String taxCode;
    private String payerAddr;
    private String altTaxCode;
    private String altPayerName;
    private String altPayerAddr;
    private String debitAccNo;
    private String revAuthCode;
    private String revAuthName;
    private String revAccCode;
    private String revAccName;
    private String paymentDate;
    private String paymentMonth;
    private String paymentYear;
    private String pmtTime;
    private String amount;
    private String debitAmount;
    private String feeTotal;
    private String feeVat;
    private String ccy;
    private String treasuryName;
    private String benBankName;
    private String approvalWfId;
    private String debitAcc;
    private List<TxnPrintDocumentItemDto> txnPrintDocumentItems;

    public String getAmountText() {
        return ReadNumber.formatAmountToText(Translator.getLocale().toLanguageTag().toLowerCase(), amount, ccy);
    }

    public Map<String, Object> getParamFirstPage() {
        Map<String, Object> param = new HashMap<>();
        int totalItem = txnPrintDocumentItems != null ? txnPrintDocumentItems.size() : 0;
        param.put(AppConstants.DOCUMENT_PARAM_NAME.LOGO_URL,
                StringUtils.defaultString(Objects.requireNonNull(getClass().getResource(AppConstants.DOCUMENT_LOGO)).toString()));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.TICK,
                StringUtils.defaultString(Objects.requireNonNull(getClass().getResource(AppConstants.TICK_MARK)).toString()));
        if (Objects.equals(TransactionStatusEnum.SUCCESS.name(), status)) {
            param.put(AppConstants.DOCUMENT_PARAM_NAME.TCC_DOC_SIGN, StringUtils.defaultString(tccDocSign));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.TCC_DOC_NO, AppConstants.DOCUMENT_PARAM_NAME.TEXT_TCC_DOC_NO + StringUtils.defaultString(tccDocNo));
            if (txnCode.equals(TransactionCodeEnum.GOV01.code()))
                refNumber = coreRef;
            else
                refNumber = tccRmNo;
            param.put(AppConstants.DOCUMENT_PARAM_NAME.REF_NUMBER, StringUtils.defaultString(refNumber));
        } else if (!Objects.equals(TransactionStatusEnum.FAILED.name(), status)) {
            param.put(AppConstants.DOCUMENT_PARAM_NAME.STATUS_TXN_TEXT, AppConstants.DOCUMENT_PARAM_NAME.STATUS_TEXT);
        }
        param.put(AppConstants.DOCUMENT_PARAM_NAME.PAYER_NAME, StringUtils.defaultString(payerName));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.TAX_CODE, StringUtils.defaultString(taxCode));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.PAYER_ADDR, StringUtils.defaultString(payerAddr));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.ALT_TAX_CODE, StringUtils.defaultString(altTaxCode));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.ALT_PAYER_NAME, StringUtils.defaultString(altPayerName));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.ALT_PAYER_ADDR, StringUtils.defaultString(altPayerAddr));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC_NO, StringUtils.defaultString(debitAccNo));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.REV_AUTH_NAME, StringUtils.defaultString(revAuthName));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.REV_ACC_CODE, StringUtils.defaultString(revAccCode));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.REV_ACC_NAME, StringUtils.defaultString(revAccName));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.TREASURY_NAME, StringUtils.defaultString(treasuryName));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.BEN_BANK_NAME, StringUtils.defaultString(benBankName));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.ITEM_TABLE, totalItem);
        param.put(AppConstants.DOCUMENT_PARAM_NAME.AMOUNT_TEXT, StringUtils.defaultString(getAmountText()));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.TOTAL_AMOUNT, StringUtils.defaultString(StrUtils.formatWithThousandSeparator(amount)));
        if (totalItem > 0 && txnPrintDocumentItems != null) {
            List<TxnPrintDocumentItemDto> itemFirstPage = txnPrintDocumentItems.subList(0, Math.min(totalItem, 2));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.TXN_PRINT_DOCUMENT_ITEM,
                    new JRBeanCollectionDataSource(CollectionUtils.emptyIfNull(itemFirstPage)));
        }
        return param;
    }

    public Map<String, Object> getParamSecondPage(WfTxnInfoResponse txnInfo) {
        Map<String, Object> param = new HashMap<>();
        int totalItem = txnPrintDocumentItems != null ? txnPrintDocumentItems.size() : 0;
        param.put(AppConstants.DOCUMENT_PARAM_NAME.ITEM_TABLE, totalItem);
        if (totalItem > 2) {
            List<TxnPrintDocumentItemDto> itemSecondPage = txnPrintDocumentItems.subList(2, totalItem);
            param.put(AppConstants.DOCUMENT_PARAM_NAME.TXN_PRINT_DOCUMENT_ITEM,
                    new JRBeanCollectionDataSource(CollectionUtils.emptyIfNull(itemSecondPage)));
        }
        param.put(AppConstants.DOCUMENT_PARAM_NAME.AMOUNT_TEXT, StringUtils.defaultString(getAmountText()));
        param.put(AppConstants.DOCUMENT_PARAM_NAME.TOTAL_AMOUNT, StringUtils.defaultString(StrUtils.formatWithThousandSeparator(amount)));
        if (Objects.equals(TransactionStatusEnum.SUCCESS.name(), status)) {
            param.put(AppConstants.DOCUMENT_PARAM_NAME.REV_AUTH_CODE, StringUtils.defaultString(revAuthCode));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC_NO, StringUtils.defaultString(debitAccNo));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.PAYMENT_DATE, StringUtils.defaultString(paymentDate));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.PAYMENT_MONTH, StringUtils.defaultString(paymentMonth));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.PAYMENT_YEAR, StringUtils.defaultString(paymentYear));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.PMT_TIME, StringUtils.defaultString(pmtTime));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_AMOUNT, StringUtils.defaultString(StrUtils.formatWithThousandSeparator(debitAmount)));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.FEE_TOTAL, StringUtils.defaultString(StrUtils.formatWithThousandSeparator(feeTotal)));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.FEE_VAT, StringUtils.defaultString(StrUtils.formatWithThousandSeparator(feeVat)));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.REV_ACC_CODE, StringUtils.defaultString(revAccCode));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.TAX_AMOUNT, StringUtils.defaultString(StrUtils.formatWithThousandSeparator(amount)));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.ADM_AREA_CODE, StringUtils.defaultString(admAreaCode));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.REF_NUMBER, StringUtils.defaultString(refNumber));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.DEBIT_ACC, StringUtils.defaultString(debitAcc));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.PAYER_NAME, StringUtils.defaultIfEmpty(payerName.toUpperCase(), AppConstants.DOCUMENT_PARAM_NAME.PAYER_TEXT));
            param.put(AppConstants.DOCUMENT_PARAM_NAME.CHIEF_ACCT, AppConstants.DOCUMENT_PARAM_NAME.CHIEF_ACCT_TEXT);
            param.put(AppConstants.DOCUMENT_PARAM_NAME.OWNER_ACCT, AppConstants.DOCUMENT_PARAM_NAME.OWNER_ACCT_TEXT);
            if (txnInfo != null) {
                txnInfo.getWfTxnActivityDtoList().forEach(activity -> {
                    if (UserTitleEnum.CHIEF_ACCT.name().equals(activity.getTitle())) {
                        param.put(AppConstants.DOCUMENT_PARAM_NAME.CHIEF_ACCT,
                                StringUtils.isBlank(activity.getName()) ? AppConstants.DOCUMENT_PARAM_NAME.CHIEF_ACCT_TEXT : activity.getName().toUpperCase());
                    } else if (UserTitleEnum.OWNER_ACCT.name().equals(activity.getTitle())) {
                        param.put(AppConstants.DOCUMENT_PARAM_NAME.OWNER_ACCT,
                                StringUtils.isBlank(activity.getName()) ? AppConstants.DOCUMENT_PARAM_NAME.OWNER_ACCT_TEXT : activity.getName().toUpperCase());
                    }
                });
            }
        }
        return param;
    }
}
