package com.bidv.ibank.dvc.model.dto;

import java.util.List;

import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonPropertyOrder(alphabetic = true)
public class TxnExportReportDto implements Exportable, TxnParamCodeDto, TxnParamNameDto, TxnParamBenBankDto {

    @ExportConfig(colIndex = 0, name = "")
    private String blankCol;

    @ExportConfig(colIndex = 1, name = "STT")
    private String order;

    @ExportConfig(colIndex = 2, name = "Ngày tạo")
    private String createdDate;

    @ExportConfig(colIndex = 3, name = "Trạng thái")
    private String status;

    @ExportConfig(colIndex = 4, name = "Mô tả")
    private String description;

    @ExportConfig(colIndex = 5, name = "Mã giao dịch")
    private String txnId;

    @ExportConfig(colIndex = 6, name = "Loại thuế phí")
    private String txnType;

    @ExportConfig(colIndex = 7, name = "Tài khoản trích nợ")
    private String debitAccNo;

    @ExportConfig(colIndex = 8, name = "Mã số thuế người nộp thuế")
    private String taxCode;

    @ExportConfig(colIndex = 9, name = "Tên người nộp thuế")
    private String payerName;

    @ExportConfig(colIndex = 10, name = "Số tờ khai")
    private String declarationNo;

    @ExportConfig(colIndex = 11, name = "Id khoản nộp (thuế nội địa)")
    private String txnItemId;

    @ExportConfig(colIndex = 12, name = "Ngày tờ khai")
    private String declarationDate;

    @ExportConfig(colIndex = 13, name = "Mã - tên kho bạc")
    private String treasuryCodeName;

    @ExportConfig(colIndex = 14, name = "Mã - tên ngân hàng hưởng")
    private String benBankCodeName;

    @ExportConfig(colIndex = 15, name = "Số TK thu NSNN")
    private String revAccCode;

    @ExportConfig(colIndex = 16, name = "Mã - tên cơ quan thu")
    private String revAuthCodeName;

    @ExportConfig(colIndex = 17, name = "Mã - tên dbhc")
    private String admAreaCodeName;

    @ExportConfig(colIndex = 18, name = "Mã - tên chương")
    private String chapterCodeName;

    @ExportConfig(colIndex = 19, name = "Mã - tên ndkt")
    private String ecCodeName;

    @ExportConfig(colIndex = 20, name = "Diễn giải giao dịch")
    private String transDesc;

    @ExportConfig(colIndex = 21, name = "Số tiền")
    private String amount;

    @ExportConfig(colIndex = 22, name = "Loại tiền")
    private String ccy;

    @ExportConfig(colIndex = 23, name = "Số tham chiếu lô")
    private String batchNo;

    @ExportConfig(colIndex = 24, name = "Người tạo")
    private String createdBy;

    @ExportConfig(colIndex = 25, name = "Người duyệt")
    private String approvalUsers;

    @ExportConfig(colIndex = 26, name = "Ngày phê duyệt cuối cùng")
    private String approvedDate;

    @ExportConfig(colIndex = 27, name = "Phí giao dịch (bao gồm VAT)")
    private String feeTotal;

    @ExportConfig(colIndex = 28, name = "Trạng thái kết nối hải quan")
    private String tccTransStatus;

    @ExportConfig(colIndex = 29, name = "Số hạch toán")
    private String tccIdCore;

    @ExportConfig(colIndex = 30, name = "Số tham chiếu TCC")
    private String tccRefNo;

    @ExportConfig(colIndex = 31, name = "Kênh giao dịch")
    private String channel;

    @ExportConfig(colIndex = 32, name = "Mã giao dịch khách hàng")
    private String orgId;

    private String treasuryCode;
    private String treasuryName;
    private String benBankCode;
    private String benBankName;
    private String revAccName;
    private String revAuthCode;
    private String revAuthName;
    private String admAreaCode;
    private String admAreaName;
    private String chapterCode;
    private String chapterName;
    private String ecCode;
    private String ecName;
    private String parAmount;
    private String itemAmount;
    private String tccErrDesc;
    private String tccSibsErrDesc;
    private String tccTransErrDesc;
    private String state;

    @Builder.Default
    private boolean isParent = false;

    @Override
    public String getTreasuryName() {
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    @Override
    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }

    @Override
    public String getRevAuthName() {
        return Translator.toLocale(revAuthCode, revAuthName);
    }

    @Override
    public String getRevAccName() {
        return Translator.toLocale(revAccCode, revAccName);
    }

    public String getBenBankName() {
        return Translator.toLocale(benBankCode, benBankName);
    }

    public String getChapterName() {
        return Translator.toLocale(chapterCode, chapterName);
    }

    public String getEcName() {
        return Translator.toLocale(ecCode, ecName);
    }

    public String getTccTransStatus() {
        List<String> finalStates = List.of(
                TransactionStateEnum.SUCCESS.name(),
                TransactionStateEnum.FAIL_TRANS_TCC.name());
        String transStatus = state != null && finalStates.contains(state)
                ? (state.equals(TransactionStateEnum.SUCCESS.name()) ? TransactionStateEnum.SUCCESS.name() : TransactionStateEnum.FAILED.name())
                : null;
        return transStatus != null
                ? Translator.toLocale(AppConstants.LANGUAGE.CUSTOMS_CONN_STATUS + "." + transStatus, transStatus)
                : null;
    }

    public String getAmount() {
        return isParent ? parAmount : itemAmount;
    }

    public String getTxnType() {
        return Translator.toLocale(AppConstants.LANGUAGE.TXN_TYPE + "." + txnType, txnType);
    }

    public String getStatus() {
        return Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + "." + status, status);
    }

    public String getBenBankCodeName() {
        return benBankCodeName != null ? benBankCodeName : StrUtils.combineCodeName(benBankCode, benBankName);
    }

    public String getTreasuryCodeName() {
        return treasuryCodeName != null ? treasuryCodeName : StrUtils.combineCodeName(treasuryCode, treasuryName);
    }

    public String getRevAuthCodeName() {
        return revAuthCodeName != null ? revAuthCodeName : StrUtils.combineCodeName(revAuthCode, revAuthName);
    }

    public String getAdmAreaCodeName() {
        return admAreaCodeName != null ? admAreaCodeName : StrUtils.combineCodeName(admAreaCode, admAreaName);
    }

    public String getChapterCodeName() {
        return chapterCodeName != null ? chapterCodeName : StrUtils.combineCodeName(chapterCode, chapterName);
    }

    public String getEcCodeName() {
        return ecCodeName != null ? ecCodeName : StrUtils.combineCodeName(ecCode, ecName);
    }

    public String getDescription() {
        return StrUtils.concatNonBlank(tccErrDesc, tccSibsErrDesc, tccTransErrDesc);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }

    public TxnExportReportDto(TxnExportReportDto source) {
        this.createdDate = source.getCreatedDate();
        this.status = source.getStatus();
        this.description = source.getDescription();
        this.txnId = source.getTxnId();
        this.txnType = source.getTxnType();
        this.debitAccNo = source.getDebitAccNo();
        this.taxCode = source.getTaxCode();
        this.payerName = source.getPayerName();
        this.declarationNo = null;
        this.txnItemId = source.getTxnItemId();
        this.declarationDate = null;
        this.treasuryCodeName = source.getTreasuryCodeName();
        this.benBankCodeName = source.getBenBankCodeName();
        this.revAccCode = source.getRevAccCode();
        this.revAuthCodeName = source.getRevAuthCodeName();
        this.admAreaCodeName = source.getAdmAreaCodeName();
        this.chapterCodeName = null;
        this.ecCodeName = null;
        this.transDesc = null;
        this.amount = source.getAmount();
        this.ccy = source.getCcy();
        this.batchNo = source.getBatchNo();
        this.createdBy = source.getCreatedBy();
        this.approvalUsers = source.getApprovalUsers();
        this.approvedDate = source.getApprovedDate();
        this.feeTotal = source.getFeeTotal();
        this.tccTransStatus = source.getTccTransStatus();
        this.tccIdCore = source.getTccIdCore();
        this.tccRefNo = source.getTccRefNo();
        this.channel = source.getChannel();
        this.orgId = source.getOrgId();
        this.parAmount = source.getParAmount();
        this.itemAmount = source.getItemAmount();
        this.tccErrDesc = source.getTccErrDesc();
        this.tccSibsErrDesc = source.getTccSibsErrDesc();
        this.tccTransErrDesc = source.getTccTransErrDesc();
        this.isParent = true;
    }
}
