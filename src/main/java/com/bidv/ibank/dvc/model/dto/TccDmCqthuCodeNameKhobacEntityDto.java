package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmCqthuCodeNameKhobacEntityDto implements Exportable {
    private String maKhobac;
    private String tenKhobac;
    private String maCqthu;
    private String tenCqthu;

    @ExportConfig(colIndex = 1)
    public String getMaKhobac() {
        return maKhobac;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenKhobac() {
        return StrUtils.combineCodeName(maKhobac, tenKhobac);
    }

    @ExportConfig(colIndex = 3)
    public String getMaTenCqthu() {
        return StrUtils.combineCodeName(maCqthu, tenCqthu);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
