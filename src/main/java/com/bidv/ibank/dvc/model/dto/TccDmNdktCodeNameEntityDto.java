package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmNdktCodeNameEntityDto implements Exportable {
    private String maNdkt;
    private String tenNdkt;

    @ExportConfig(colIndex = 1)
    public String getMaNdkt() {
        return maNdkt;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenNdkt() {
        return StrUtils.combineCodeName(maNdkt, tenNdkt);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
