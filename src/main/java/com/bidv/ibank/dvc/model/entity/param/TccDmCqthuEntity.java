package com.bidv.ibank.dvc.model.entity.param;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TCC_DM_CQTHU")
public class TccDmCqthuEntity {

    @Id
    @Size(max = 7)
    @Column(name = "MA_CQTHU", length = 7)
    private String maCqthu;

    @Nullable
    @Size(max = 4)
    @Column(name = "SHKB", length = 4)
    private String shkb;

    @Nullable
    @Size(max = 200)
    @Column(name = "TEN", length = 200)
    private String ten;

    @OneToOne
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    private TccDmKhobacEntity tccDmKhobacEntity;
}