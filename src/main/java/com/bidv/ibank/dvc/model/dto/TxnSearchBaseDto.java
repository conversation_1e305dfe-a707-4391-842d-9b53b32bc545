package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.model.filter.AmountRangeFilter;
import com.bidv.ibank.dvc.model.filter.DateRangeFilter;
import com.bidv.ibank.dvc.model.filter.Searchable;
import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

public interface TxnSearchBaseDto extends Searchable, DateRangeFilter, AmountRangeFilter {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_DEBIT_ACC_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    @Schema(example = "**********", description = "Tài khoản trích nợ")
    String getDebitAccNo();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "9581856", description = "Mã số thuế")
    String getTaxCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "**********", description = "Số tờ khai hải quan")
    String getDeclarationNo();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_BATCH_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    @Schema(example = "1234567890", description = "Số tham chiếu lô")
    String getBatchNo();
}
