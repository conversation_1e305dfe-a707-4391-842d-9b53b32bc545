package com.bidv.ibank.dvc.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

public interface TxnParamNameDto {

    @Schema(example = "Kho bạc Nhà nước Hà Nội", description = "Tên kho bạc")
    String getTreasuryName();

    @Schema(example = "Hà Nội", description = "Tên địa bàn hành chính")
    String getAdmAreaName();

    @Schema(example = "Tài khoản thu 1898", description = "Tên tài khoản thu")
    String getRevAccName();

    @Schema(example = "Cơ quan thu 1420", description = "Tên cơ quan thu")
    String getRevAuthName();
}
