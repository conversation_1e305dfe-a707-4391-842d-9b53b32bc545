package com.bidv.ibank.dvc.model.mapper.customsduty;

import org.springframework.stereotype.Component;

import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTemplateItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.request.TemplateSaveReq;
import com.bidv.ibank.dvc.model.response.TemplateListRes;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.mapper.EntityDtoMapper;

@Component
public class GOVPaymentTemplateMapper extends GOVPaymentBaseMapper implements EntityDtoMapper<Object, Object> {

    @Override
    public Object toDto(Object e) {
        throw new UnsupportedOperationException("Unimplemented method 'toDto'");
    }

    @Override
    public Object toEntity(Object dto) {
        throw new UnsupportedOperationException("Unimplemented method 'toEntity'");
    }

    public GOVPaymentTemplateEntity toEntity(GOVPaymentTransactionEntity txnEntity, TemplateSaveReq request) {
        return GOVPaymentTemplateEntity.builder()
                .name(request.getTemplateName())
                .debitAccNo(txnEntity.getDebitAccNo())
                .debitAccName(txnEntity.getDebitAccName())
                .taxCode(txnEntity.getTaxCode())
                .altTaxCode(txnEntity.getAltTaxCode())
                .payerName(txnEntity.getPayerName())
                .altPayerName(txnEntity.getAltPayerName())
                .payerAddr(txnEntity.getPayerAddr())
                .altPayerAddr(txnEntity.getAltPayerAddr())
                .payerType(txnEntity.getPayerType())
                .shkb(txnEntity.getShkb())
                .maDbhc(txnEntity.getMaDbhc())
                .maCqthu(txnEntity.getMaCqthu())
                .maTk(txnEntity.getMaTk())
                .maNh(txnEntity.getMaNh())
                .isPublic(request.getIsPublic())
                .cifNo(txnEntity.getCifNo())
                .cusId(txnEntity.getCusId())
                .status(AppConstants.TEMPLATE_STATUS.ACTIVE)
                .txnType(txnEntity.getTxnType())
                .amount(txnEntity.getAmount())
                .ccy(txnEntity.getCcy())
                .feeTotal(txnEntity.getFeeTotal())
                .feeOpt(txnEntity.getFeeOpt())
                .feeCcy(txnEntity.getFeeCcy())
                .channel(txnEntity.getChannel())
                .orgId(txnEntity.getOrgId())
                .build();
    }

    public GOVPaymentTemplateItemEntity toItemEntity(GOVPaymentItemEntity txnItemEntity, String templateId) {
        return GOVPaymentTemplateItemEntity.builder()
                .templateId(templateId)
                .declarationNo(txnItemEntity.getDeclarationNo())
                .declarationDate(txnItemEntity.getDeclarationDate())
                .maNdkt(txnItemEntity.getMaNdkt())
                .maChuong(txnItemEntity.getMaChuong())
                .maSthue(txnItemEntity.getMaSthue())
                .maLh(txnItemEntity.getMaLh())
                .maLthq(txnItemEntity.getMaLthq())
                .amount(txnItemEntity.getAmount())
                .ccy(txnItemEntity.getCcy())
                .transDesc(txnItemEntity.getTransDesc())
                .build();
    }

    public TemplateListRes toListDto(GOVPaymentTemplateEntity entity) {
        return TemplateListRes.builder()
                .templateId(entity.getId())
                .templateName(entity.getName())
                .debitAccNo(entity.getDebitAccNo())
                .debitAccName(entity.getDebitAccName())
                .taxCode(entity.getTaxCode())
                .altTaxCode(entity.getAltTaxCode())
                .payerName(entity.getPayerName())
                .altPayerName(entity.getAltPayerName())
                .payerAddr(entity.getPayerAddr())
                .altPayerAddr(entity.getAltPayerAddr())
                .payerType(entity.getPayerType().getValue())
                .createdDate(entity.getCreatedDate())
                .treasuryCode(entity.getShkb())
                .treasuryName(getTreasuryName(entity))
                .admAreaCode(entity.getMaDbhc())
                .admAreaName(getAdmAreaName(entity))
                .revAccCode(entity.getMaTk())
                .revAccName(getRevAccName(entity))
                .revAuthCode(entity.getMaCqthu())
                .revAuthName(getRevAuthName(entity))
                .benBankCode(entity.getMaNh())
                .benBankName(getBenBankName(entity))
                .txnType(entity.getTxnType())
                .amount(entity.getAmount())
                .ccy(entity.getCcy())
                .feeTotal(entity.getFeeTotal())
                .feeOpt(entity.getFeeOpt())
                .feeCcy(entity.getFeeCcy())
                .channel(entity.getChannel())
                .orgId(entity.getOrgId())
                .isPublic(entity.getIsPublic())
                .taxItems(mapTaxItems(entity, entity.getGovPaymentTemplateItemList()))
                .build();
    }
}
