package com.bidv.ibank.dvc.model.entity.param;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TCC_DM_STHUE_HQA")
public class TccDmSthueHqaEntity {

    @Id
    @Size(max = 10)
    @Column(name = "MA_STHUE", length = 10)
    private String maSthue;

    @Nullable
    @Size(max = 500)
    @Column(name = "TEN_STHUE", length = 500)
    private String tenSthue;
}