package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RevenueAccountRes {

    private String revAccCode;
    private String revAccName;

    public String getRevAccName() {
        return Translator.toLocale(revAccCode, revAccName);
    }
}