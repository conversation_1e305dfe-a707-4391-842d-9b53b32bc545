package com.bidv.ibank.dvc.model.request;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class TxnConfirmReq {

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TRANS_KEY_LENGTH)
    @Schema(example = "transKey", description = "Giá trị trả về từ API đẩy duyệt (phục vụ truy vấn dữ liệu từ cache)")
    private String transKey;

    @NotBlank
    @Schema(example = "confirmValue", description = "Gi<PERSON> trị xác thực")
    private String confirmValue;
}
