package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrMsgDto {

    private String code;
    private String field;
    private String message;

    public String getMessage() {
        return this.message != null ? this.message : Translator.toLocale(AppConstants.LANGUAGE.RESPONSE_CODE + "." + code, code);
    }
}
