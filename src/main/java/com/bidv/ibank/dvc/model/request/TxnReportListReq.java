package com.bidv.ibank.dvc.model.request;

import java.util.List;

import com.bidv.ibank.dvc.model.filter.StatusesFilter;

import com.bidv.ibank.dvc.util.AppConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class TxnReportListReq extends TxnPendingApprovalListReq implements StatusesFilter {

    private List<String> statuses;

    @Schema(example = "*********", description = "Số tham chiếu thuế")
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TCC_REF_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    private String tccRefNo;
}
