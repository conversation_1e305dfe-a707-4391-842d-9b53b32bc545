package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.dvc.model.dto.BaseTotalDto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class BatchProcessResultRes extends BaseTotalDto {

    private List<TransactionResDetail> failTxns;
}
