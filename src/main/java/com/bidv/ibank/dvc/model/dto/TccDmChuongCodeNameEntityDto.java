package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmChuongCodeNameEntityDto implements Exportable {
    private String maChuong;
    private String tenChuong;

    @ExportConfig(colIndex = 1)
    public String getMaChuong() {
        return maChuong;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenChuong() {
        return StrUtils.combineCodeName(maChuong, tenChuong);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
