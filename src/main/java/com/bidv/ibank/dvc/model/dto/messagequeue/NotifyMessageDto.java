package com.bidv.ibank.dvc.model.dto.messagequeue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotifyMessageDto implements Serializable {

    private Map<String, String> params;
    private List<String> attachments;
    private List<Long> recipients;
    private NotifyMessageInfoDto info;
    private List<String> emails;
    private String additionalInfo;
}
