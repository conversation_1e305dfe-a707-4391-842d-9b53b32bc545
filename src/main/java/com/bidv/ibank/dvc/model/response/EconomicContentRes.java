package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EconomicContentRes {

    private String ecCode;
    private String ecName;

    public String getEcName() {
        return Translator.toLocale(ecCode, ecName);
    }
}
