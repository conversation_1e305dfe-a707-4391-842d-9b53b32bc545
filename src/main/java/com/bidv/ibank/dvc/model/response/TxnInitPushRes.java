package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.common.txn.model.dto.TransAuth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TxnInitPushRes {

    private String transKey;
    private boolean requireAuth;
    private TransAuth transAuth;
}
