package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomsCurrencyRes {

    private String ccCode;
    private String ccName;

    public String getCcName() {
        return Translator.toLocale(ccCode, ccName);
    }
}