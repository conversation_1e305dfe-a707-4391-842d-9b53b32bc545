package com.bidv.ibank.dvc.model.entity.param;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TCC_DM_LHXNK")
public class TccDmLhxnkEntity {

    @Id
    @Size(max = 20)
    @Column(name = "MA_LH", length = 20)
    private String maLh;

    @Nullable
    @Size(max = 20)
    @Column(name = "TEN", length = 20)
    private String ten;
}