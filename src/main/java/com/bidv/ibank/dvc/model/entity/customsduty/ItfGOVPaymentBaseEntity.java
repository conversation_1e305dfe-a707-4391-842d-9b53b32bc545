package com.bidv.ibank.dvc.model.entity.customsduty;

import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;

public interface ItfGOVPaymentBaseEntity {

    String getTaxCode();

    String getAltTaxCode();

    String getPayerName();

    String getAltPayerName();

    String getPayerAddr();

    String getAltPayerAddr();

    PayerTypeEnum getPayerType();

    String getShkb();

    String getMaDbhc();

    String getMaCqthu();

    String getMaTk();

    String getMaNh();

    TccDmKhobacEntity getTccDmKhobacEntity();

    TccDmDbhcEntity getTccDmDbhcEntity();

    TccDmCqthuEntity getTccDmCqthuEntity();

    TccDmTkNsnnEntity getTccDmTkNsnnEntity();

    TccDmKbnnNhtmEntity getTccDmKbnnNhtmEntity();
}