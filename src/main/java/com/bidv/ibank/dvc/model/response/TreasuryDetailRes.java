package com.bidv.ibank.dvc.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreasuryDetailRes {

    private String treasuryCode;
    private String benBankCode;
    private String benBankName;
    @JsonProperty("isInBidv")
    private boolean isInBidv;

    @JsonIgnore
    public boolean isInBidv() {
        return this.isInBidv;
    }
}
