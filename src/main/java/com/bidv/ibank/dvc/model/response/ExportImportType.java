package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportImportType {

    private String eiTypeCode;
    private String eiTypeName;

    public String getEiTypeName() {
        return Translator.toLocale(eiTypeCode, eiTypeName);
    }
}