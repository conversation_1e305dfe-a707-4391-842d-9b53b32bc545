package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

public interface TxnItemParamCodeDto {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_CC_CODE_LENGTH)
    @Schema(example = "1", description = "Mã loại tiền hải quan")
    String getCcCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_CHAPTER_CODE_LENGTH)
    @Schema(example = "755", description = "Mã chương")
    String getChapterCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_EC_CODE_LENGTH)
    @Schema(example = "3063", description = "Mã nội dung kinh tế")
    String getEcCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_EI_TYPE_CODE_LENGTH)
    @Schema(example = "A11", description = "Mã loại hình xuất nhập khẩu")
    String getEiTypeCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_TYPE_CODE_LENGTH)
    @Schema(example = "VA", description = "Mã sắc thuế")
    String getTaxTypeCode();
}
