package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmTkNsnnStkEntityDto implements Exportable {
    private String tk;
    private String ten;

    @ExportConfig(colIndex = 1)
    public String getTk() {
        return tk;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenTkNsnn() {
        return StrUtils.combineCodeName(tk, ten);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
