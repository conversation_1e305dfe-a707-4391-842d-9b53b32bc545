package com.bidv.ibank.dvc.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

public interface TxnItemParamNameDto {

    @Schema(example = "Thuế xuất nhập khẩu", description = "Tên loại tiền hải quan")
    String getCcName();

    @Schema(example = "Kinh tế tư nhân", description = "Tên chương")
    String getChapterName();

    @Schema(example = "Lệ phí cấp giấy phép quy hoạch", description = "Tên nội dung kinh tế")
    String getEcName();

    @Schema(example = "Nhập kinh doanh tiêu dùng", description = "Tên loại hình xuất nhập khẩu")
    String getEiTypeName();

    @Schema(example = "Thuế giá trị gia tăng", description = "Tê<PERSON> sắc thuế")
    String getTaxTypeName();
}
