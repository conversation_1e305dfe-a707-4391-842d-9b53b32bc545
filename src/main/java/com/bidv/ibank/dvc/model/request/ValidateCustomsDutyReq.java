package com.bidv.ibank.dvc.model.request;

import java.util.List;

import com.bidv.ibank.dvc.model.dto.AmountDto;
import com.bidv.ibank.dvc.model.dto.TxnGeneralInfoDto;
import com.bidv.ibank.dvc.model.dto.TxnParamCodeDto;
import com.bidv.ibank.dvc.model.dto.TxnTaxItemDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ValidateCustomsDutyReq implements AmountDto, TxnParamCodeDto, TxnGeneralInfoDto {

    @NotBlank
    private String amount;
    @NotBlank
    private String ccy;
    @NotBlank
    private String treasuryCode;
    @NotBlank
    private String admAreaCode;
    @NotBlank
    private String revAuthCode;
    @NotBlank
    private String revAccCode;
    @NotBlank
    private String taxCode;
    @NotBlank
    private String payerName;
    @NotBlank
    private String payerAddr;
    private String altTaxCode;
    private String altPayerName;
    private String altPayerAddr;

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_DEBIT_ACC_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    @Schema(example = "1200046752", description = "Tài khoản trích nợ")
    private String debitAccNo;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ID_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    @Schema(example = "DVC01704202411252339", description = "Mã giao dịch (bắt buộc khi trong luồng chỉnh sửa giao dịch)")
    private String txnId;

    @NotNull
    @Schema(example = "1", description = "Loại hình người nộp thuế: 0 - Không xác định, 1 - doanh nghiệp, 2 - cá nhân")
    private PayerTypeEnum payerType;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_RA_NOTE_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_LINE_AND_UNDERSCORE_SPACE)
    @Schema(example = "Nộp thuế doanh nghiệp hàng tháng", description = "Ghi chú tới người duyệt")
    private String raNote;

    @NotEmpty
    @Valid
    @Schema(description = "Danh sách thông tin khoản nộp")
    private List<TxnTaxItemDto> taxItems;

    @Schema(example = "true", description = "Giao dịch ưu tiên")
    private boolean priority;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_ORG_ID_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_LINE_AND_UNDERSCORE_SPACE)
    @Schema(example = "235947523904", description = "Mã giao dịch khách hàng")
    private String orgId;

    @JsonIgnore
    @Schema(example = "false", description = "Validate giao dịch đơn hay lô")
    @Builder.Default
    private Boolean isInBatch = false;
}
