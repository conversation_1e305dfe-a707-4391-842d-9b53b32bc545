package com.bidv.ibank.dvc.model.mapper.customsduty;

import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.ItfGOVPaymentBaseEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.ItfGOVPaymentItemBaseEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public abstract class GOVPaymentBaseMapper {

    protected String getAdmAreaName(ItfGOVPaymentBaseEntity entity) {
        return Optional.ofNullable(entity.getTccDmDbhcEntity())
                .map(TccDmDbhcEntity::getTen)
                .orElse(null);
    }

    protected String getRevAccName(ItfGOVPaymentBaseEntity entity) {
        return Optional.ofNullable(entity.getTccDmTkNsnnEntity())
                .map(TccDmTkNsnnEntity::getTen)
                .orElse(null);
    }

    protected String getRevAuthName(ItfGOVPaymentBaseEntity entity) {
        return Optional.ofNullable(entity.getTccDmCqthuEntity())
                .map(TccDmCqthuEntity::getTen)
                .orElse(null);
    }

    protected String getTreasuryName(ItfGOVPaymentBaseEntity entity) {
        return Optional.ofNullable(entity.getTccDmKhobacEntity())
                .map(TccDmKhobacEntity::getTen)
                .orElse(null);
    }

    protected String getBenBankName(ItfGOVPaymentBaseEntity entity) {
        return Optional.ofNullable(entity.getTccDmKbnnNhtmEntity())
                .map(TccDmKbnnNhtmEntity::getTenNh)
                .orElse(null);
    }

    protected List<TxnTaxFullItemDto> mapTaxItems(ItfGOVPaymentBaseEntity entity, Set<? extends ItfGOVPaymentItemBaseEntity> items) {
        String admAreaName = getAdmAreaName(entity);
        String revAccName = getRevAccName(entity);
        String revAuthName = getRevAuthName(entity);
        String treasuryName = getTreasuryName(entity);
        String benBankName = getBenBankName(entity);

        return items.stream()
                .<TxnTaxFullItemDto>map(item -> TxnTaxFullItemDto.builder()
                        .admAreaCode(entity.getMaDbhc())
                        .admAreaName(admAreaName)
                        .ccCode(item.getMaLthq())
                        .ccy(item.getCcy())
                        .ccName(Optional.ofNullable(item.getTccDmLoaitienhqaEntity()).map(TccDmLoaitienhqaEntity::getTenLthq).orElse(null))
                        .chapterCode(item.getMaChuong())
                        .chapterName(Optional.ofNullable(item.getTccDmChuongEntity()).map(TccDmChuongEntity::getTen).orElse(null))
                        .declarationDate(item.getDeclarationDate())
                        .declarationNo(item.getDeclarationNo())
                        .ecCode(item.getMaNdkt())
                        .ecName(Optional.ofNullable(item.getTccDmNdktEntity()).map(TccDmNdktEntity::getTen).orElse(null))
                        .eiTypeCode(item.getMaLh())
                        .eiTypeName(Optional.ofNullable(item.getTccDmLhxnkEntity()).map(TccDmLhxnkEntity::getTen).orElse(null))
                        .revAccCode(entity.getMaTk())
                        .revAccName(revAccName)
                        .revAuthCode(entity.getMaCqthu())
                        .revAuthName(revAuthName)
                        .payerType(entity.getPayerType().getValue())
                        .taxTypeCode(item.getMaSthue())
                        .taxTypeName(Optional.ofNullable(item.getTccDmSthueHqaEntity()).map(TccDmSthueHqaEntity::getTenSthue).orElse(null))
                        .transDesc(item.getTransDesc())
                        .treasuryCode(entity.getShkb())
                        .treasuryName(treasuryName)
                        .benBankCode(entity.getMaNh())
                        .benBankName(benBankName)
                        .amount(item.getAmount().toString())
                        .build())
                .toList();
    }
}