package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.dvc.model.dto.contracts.TxnBaseListDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TxnPendingListRes extends TxnBaseListDto {

    @Schema(example = "true", description = "Giao dịch ưu tiên")
    private boolean priority;
}
