package com.bidv.ibank.dvc.model.filter;

import java.math.BigDecimal;
import java.util.List;

import com.bidv.ibank.dvc.annotation.ValidAmountRange;
import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;

@ValidAmountRange
public interface AmountRangeFilter {

    @Digits(integer = AppConstants.BATCH_FIELD_LENGTH.MAX_AMOUNT_LENGTH, fraction = 2)
    @PositiveOrZero
    @Schema(example = "100000", description = "Số tiền tối thiểu")
    BigDecimal getMinAmount();

    @Digits(integer = AppConstants.BATCH_FIELD_LENGTH.MAX_AMOUNT_LENGTH, fraction = 2)
    @PositiveOrZero
    @Schema(example = "1000000", description = "Số tiền tối đa")
    BigDecimal getMaxAmount();

    @Schema(example = "[\"VND\", \"USD\"]", description = "Danh sách loại tiền tệ")
    List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_CCY_LENGTH) @Pattern(regexp = AppConstants.REGEX_VALIDATION.LETTER) String> getCcys();
}
