package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@Schema(description = "Thông tin tra cứu thuế hải quan")
public class InquiryCustomsDutyRes extends TxnTaxFullItemDto {

}
