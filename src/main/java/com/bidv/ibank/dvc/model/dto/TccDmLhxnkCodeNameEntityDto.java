package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmLhxnkCodeNameEntityDto implements Exportable {
    private String maLhxnk;
    private String tenLhxnk;

    @ExportConfig(colIndex = 1)
    public String getMaLhxnk() {
        return maLhxnk;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenLhxnk() {
        return StrUtils.combineCodeName(maLhxnk, tenLhxnk);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
