package com.bidv.ibank.dvc.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.bidv.ibank.dvc.util.AppConstants;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TxnRejectReq {

    @NotEmpty
    @Schema(example = "['GOV231952', 'GOV931164']", description = "Mã giao dịch ")
    public List<@NotBlank @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ID_LENGTH) @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER) String> txnIds;

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_APPROVAL_NOTE_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_LINE_AND_UNDERSCORE_SPACE)
    @Schema(example = "Sai thong tin giao dich", description = "Lý do từ chối")
    public String approvalNote;
}
