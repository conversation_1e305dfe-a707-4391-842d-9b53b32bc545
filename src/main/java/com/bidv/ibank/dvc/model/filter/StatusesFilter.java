package com.bidv.ibank.dvc.model.filter;

import java.util.List;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

public interface StatusesFilter {

    @Schema(example = "[\"INIT\"]", description = "Trạng thái giao dịch")
    List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH) String> getStatuses();
}
