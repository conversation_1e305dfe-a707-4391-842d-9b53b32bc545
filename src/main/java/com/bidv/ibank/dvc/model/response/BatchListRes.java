package com.bidv.ibank.dvc.model.response;

import java.time.LocalDateTime;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class BatchListRes {

    @Schema(example = "F2B4067F550441739FE10B63B9235715", description = "Id lô")
    private String batchId;

    @JsonFormat(pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS)
    @Schema(example = "2025-05-29T09:01:45.146761", description = "<PERSON><PERSON>y tạo")
    private LocalDateTime createdDate;

    @Schema(example = "PROCESSING", description = "Trạng thái")
    private String status;

    @Schema(example = "Thanh toán nghĩa vụ thuế", description = "Tên bảng kê")
    private String batchName;

    @Schema(example = "**********", description = "Số tham chiếu lô")
    private String batchNo;

    @JsonFormat(pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS)
    @Schema(example = "2025-06-24T16:58:44.006902", description = "Ngày xử lý")
    private LocalDateTime processedDate;

    @Schema(example = "APP", description = "Kênh giao dịch")
    private String channel;

    @Schema(example = "Đang kiểm tra", description = "Tên trạng thái")
    public String getStatusName() {
        return Translator.toLocale(AppConstants.LANGUAGE.BATCH_STATUS + "." + status);
    }
}
