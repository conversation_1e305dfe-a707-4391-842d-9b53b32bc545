package com.bidv.ibank.dvc.model.response;

import java.math.BigDecimal;

import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ValidateCustomsDutyRes {

    @Schema(example = "TXNKEY_REQUEST_APPROVAL_297system_180208d2-4109-4ce3-85ca-b686abd84f86", description = "Key giao dịch")
    private String transKey;

    @Schema(example = "10000", description = "<PERSON><PERSON> dịch vụ (bao gồm VAT)")
    private BigDecimal feeTotal;

    @Schema(example = "VND", description = "Loại tiền tệ")
    private String feeCcy;

    @Schema(example = "<PERSON><PERSON> k<PERSON>án", description = "<PERSON><PERSON><PERSON> thức thu phí")
    private String feeOpt;

    @Schema(example = "*********", description = "Số tiền")
    private BigDecimal amount;

    @Schema(example = "VND", description = "Loại tiền tệ")
    private String ccy;

    @Schema(example = "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi", description = "Số tiền bằng chữ")
    public String getAmountText() {
        return ReadNumber.formatAmountToText(Translator.getLocale().toLanguageTag().toLowerCase(), this.getAmount().toString(), this.getCcy());
    }
}
