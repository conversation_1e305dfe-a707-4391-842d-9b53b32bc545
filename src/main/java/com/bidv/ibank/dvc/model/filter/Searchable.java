package com.bidv.ibank.dvc.model.filter;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

public interface Searchable {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH)
    @Schema(example = "Nop thue", description = "Giá trị tìm kiếm theo: Số tờ khai || Mã giao dịch || Số tiền || Số tài khoản trích nợ")
    String getSearch();
}
