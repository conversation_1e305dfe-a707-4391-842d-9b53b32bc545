package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.annotation.ValidAmount;
import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

@ValidAmount
public interface AmountDto {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_CCY_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.LETTER)
    @Schema(example = "VND", description = "Loại tiền tệ của số tiền nhập")
    String getCcy();

    @Schema(example = "100000", description = "Số tiền")
    String getAmount();

    void setCcy(String ccy);

    void setAmount(String amount);
}
