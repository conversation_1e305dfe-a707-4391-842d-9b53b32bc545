package com.bidv.ibank.dvc.model.response;

import java.time.LocalDateTime;
import java.util.Objects;

import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.dvc.model.dto.contracts.TxnDetailDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonPropertyOrder(alphabetic = true)
@EqualsAndHashCode(callSuper = true)
public class TxnDetailRes extends TxnDetailDto {

    @Schema(example = "88060ktv", description = "Người duyệt")
    private String approvalUsers;

    @Schema(example = "Ghi chú tới người duyệt", description = "Ghi chú tới người duyệt")
    private String raNote;

    @Schema(example = "Lý do từ chối", description = "Lý do từ chối")
    private String approvalNote;

    @Schema(example = "true", description = "Giao dịch ưu tiên")
    private boolean priority;

    @JsonFormat(pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS)
    @Schema(example = "2025-06-24T16:58:44.006902", description = "Thời gian hạch toán")
    private LocalDateTime pmtTime;

    public String getApprovalNote() {
        if (Objects.equals(TransactionStateEnum.REJECTED.name(), getStatus())) {
            return approvalNote;
        }
        return null;
    }

}