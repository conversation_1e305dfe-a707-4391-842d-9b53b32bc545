package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RevenueAuthorityRes {

    private String revAuthCode;
    private String revAuthName;

    public String getRevAuthName() {
        return Translator.toLocale(revAuthCode, revAuthName);
    }
}