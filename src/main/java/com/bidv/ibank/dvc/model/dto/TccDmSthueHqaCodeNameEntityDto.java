package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmSthueHqaCodeNameEntityDto implements Exportable {
    private String maSthueHqa;
    private String tenSthueHqa;

    @ExportConfig(colIndex = 1)
    public String getMaSthueHqa() {
        return maSthueHqa;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenSthueHqa() {
        return StrUtils.combineCodeName(maSthueHqa, tenSthueHqa);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
