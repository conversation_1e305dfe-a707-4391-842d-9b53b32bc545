package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.annotation.ValidGeneralInfo;
import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;

@ValidGeneralInfo
public interface TxnGeneralInfoDto {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "**********", description = "Mã số thuế người nộp")
    String getTaxCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_NAME_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
    @Schema(example = "Nguyen Van A", description = "Tên người nộp thuế")
    String getPayerName();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_ADDR_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
    @Schema(example = "Hà Nội", description = "Địa chỉ người nộp thuế")
    String getPayerAddr();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "**********", description = "Mã số thuế người nộp thay")
    String getAltTaxCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_NAME_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
    @Schema(example = "Nguyen Van B", description = "Tên người nộp thay")
    String getAltPayerName();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_ADDR_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE_ADVANCED)
    @Schema(example = "Hà Nội", description = "Địa chỉ người nộp thay")
    String getAltPayerAddr();

    void setAltPayerName(String altPayerName);

    void setAltPayerAddr(String altPayerAddr);
}
