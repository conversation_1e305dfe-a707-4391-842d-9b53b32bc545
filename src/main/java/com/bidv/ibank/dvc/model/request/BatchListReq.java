package com.bidv.ibank.dvc.model.request;

import java.time.LocalDate;
import java.util.List;

import com.bidv.ibank.dvc.model.filter.DateRangeFilter;
import com.bidv.ibank.dvc.model.filter.Searchable;
import com.bidv.ibank.dvc.model.filter.StatusesFilter;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.domain.request.PagingQueryRequest;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class BatchListReq extends PagingQueryRequest implements Searchable, DateRangeFilter, StatusesFilter {

    @Schema(example = "**********", description = "Tên bảng kê | Số tham chiếu lô")
    private String search;

    private LocalDate startDate;
    private LocalDate endDate;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_BATCH_NAME_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_ACCENT_AND_SPEC_CHAR_SPACE)
    @Schema(example = "Thanh toán nghĩa vụ thuế", description = "Tên bảng kê")
    private String batchName;

    @Schema(example = "PROCESSING", description = "Trạng thái")
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH) String> statuses;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_BATCH_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    @Schema(example = "1234567890", description = "Số tham chiếu lô")
    private String batchNo;
}
