package com.bidv.ibank.dvc.model.entity.param;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TCC_DM_KHOBAC")
public class TccDmKhobacEntity {

    @Id
    @Size(max = 4)
    @Column(name = "SHKB", length = 4)
    private String shkb;

    @Nullable
    @Size(max = 200)
    @Column(name = "TEN", length = 200)
    private String ten;

    @Nullable
    @Size(max = 5)
    @Column(name = "MA_DBHC", length = 5)
    private String maDbhc;

    @Nullable
    @Size(max = 10)
    @Column(name = "MA_NH", length = 10)
    private String maNh;

    @Nullable
    @Size(max = 10)
    @Column(name = "TKNO_NSNN", length = 10)
    private String tknoNsnn;

    @Nullable
    @Size(max = 100)
    @Column(name = "BK01", length = 100)
    private String bk01;

    @Nullable
    @Size(max = 20)
    @Column(name = "TAI_KHOAN", length = 20)
    private String taiKhoan;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_DBHC", referencedColumnName = "MA_DBHC", insertable = false, updatable = false)
    private TccDmDbhcEntity tccDmDbhcEntity;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    @JoinColumn(name = "MA_NH", referencedColumnName = "MA_NH", insertable = false, updatable = false)
    private TccDmKbnnNhtmEntity tccDmKbnnNhtmEntity;
}