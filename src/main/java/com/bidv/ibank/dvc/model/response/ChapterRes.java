package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChapterRes {

    private String chapterCode;
    private String chapterName;

    public String getChapterName() {
        return Translator.toLocale(chapterCode, chapterName);
    }
}
