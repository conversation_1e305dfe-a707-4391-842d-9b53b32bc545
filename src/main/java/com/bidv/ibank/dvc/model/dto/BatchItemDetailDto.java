package com.bidv.ibank.dvc.model.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonPropertyOrder(alphabetic = true)
public class BatchItemDetailDto implements TxnParamCodeDto, TxnParamNameDto, TxnItemParamCodeDto, TxnItemParamNameDto, TxnGeneralInfoDto, AmountDto,
        TxnParamBenBankDto {

    private String taxCode;
    private String altTaxCode;
    private String payerName;
    private String altPayerName;
    private String payerAddr;
    private String altPayerAddr;
    private String treasuryCode;
    private String treasuryName;
    private String revAccCode;
    private String revAccName;
    private String revAuthCode;
    private String revAuthName;
    private String benBankCode;
    private String benBankName;
    private String admAreaCode;
    private String admAreaName;
    private String ccCode;
    private String ccName;
    private String chapterCode;
    private String chapterName;
    private String ecCode;
    private String ecName;
    private String eiTypeCode;
    private String eiTypeName;
    private String taxTypeCode;
    private String taxTypeName;
    private String amount;
    private String ccy;
    private String orgId;

    @Schema(example = "a614d77b-9ee6-481f-b321-838ed39ddba8", description = "ID của item trong batch")
    private String batchItemId;

    @Schema(example = "***********", description = "Số tờ khai")
    private String declarationNo;

    @Schema(example = "24/05/2024", description = "Ngày tờ khai")
    private String declarationDate;

    @Schema(example = "2024", description = "Năm tờ khai")
    private String declarationYear;

    @Schema(example = "**********", description = "Tài khoản trích nợ")
    private String debitAccNo;

    @Schema(example = "1", description = " Loại hình người nộp thuế: 0 - Không xác định, 1 - doanh nghiệp, 2 - cá nhân")
    private String payerType;

    @Schema(example = "2021-01-01 12:00:00", description = "Ngày tạo")
    private LocalDateTime createdDate;

    @Schema(example = "Tờ khai thuế GTGT", description = "Diễn giải giao dịch")
    private String transDesc;

    @Schema(example = "10000", description = "Phí dịch vụ (bao gồm VAT)")
    private BigDecimal feeTotal;

    @Schema(example = "VND", description = "Loại tiền tệ phí")
    private String feeCcy;

    @JsonIgnore
    private String errCode;
    @JsonIgnore
    private String tccErrCode;
    @JsonIgnore
    private String tccErrMsg;

    @Schema(example = "GOV0001, GOV0002", description = "Danh sách lỗi")
    private List<ErrMsgDto> errors;

    @Schema(example = "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi", description = "Số tiền bằng chữ")
    public String getAmountText() {
        List<String> amountCodeErrors = List.of(
                ResponseCode.AMOUNT_REQUIRED.code(),
                ResponseCode.AMOUNT_EXCEED_MAX_LENGTH.code(),
                ResponseCode.AMOUNT_INVALID_FORMAT.code());
        if (getErrors().stream().anyMatch(err -> amountCodeErrors.contains(err.getCode()))) {
            return null;
        }
        return ReadNumber.formatAmountToText(Translator.getLocale().toLanguageTag().toLowerCase(), this.getAmount(), this.getCcy());
    }

    @Override
    public String getTreasuryName() {
        if (errCode != null && errCode.contains(ResponseCode.TREASURY_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    @Override
    public String getAdmAreaName() {
        if (errCode != null && errCode.contains(ResponseCode.ADM_AREA_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(admAreaCode, admAreaName);
    }

    @Override
    public String getRevAccName() {
        if (errCode != null && errCode.contains(ResponseCode.REV_ACC_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(revAccCode, revAccName);
    }

    @Override
    public String getRevAuthName() {
        if (errCode != null && errCode.contains(ResponseCode.REV_AUTH_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(revAuthCode, revAuthName);
    }

    @Override
    public String getBenBankName() {
        if (errCode != null) {
            return StringUtils.EMPTY;
        }
        return StringUtils.defaultString(benBankName);
    }

    @Override
    public String getCcName() {
        if (errCode != null && errCode.contains(ResponseCode.CC_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(ccCode, ccName);
    }

    @Override
    public String getChapterName() {
        if (errCode != null && errCode.contains(ResponseCode.CHAPTER_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(chapterCode, chapterName);
    }

    @Override
    public String getEcName() {
        if (errCode != null && errCode.contains(ResponseCode.EC_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(ecCode, ecName);
    }

    @Override
    public String getEiTypeName() {
        if (errCode != null && errCode.contains(ResponseCode.EI_TYPE_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(eiTypeCode, eiTypeName);
    }

    @Override
    public String getTaxTypeName() {
        if (errCode != null && errCode.contains(ResponseCode.TAX_TYPE_CODE_INVALID_FORMAT.code())) {
            return StringUtils.EMPTY;
        }
        return Translator.toLocale(taxTypeCode, taxTypeName);
    }

    @Schema(example = "Doanh nghiệp", description = "Loại người nộp thuế")
    public String getPayerTypeName() {
        try {
            int payerTypeEnum = Integer.parseInt(payerType);
            return Translator.toLocale(AppConstants.LANGUAGE.TAX_PAYER_TYPE + "." + payerTypeEnum, String.valueOf(payerTypeEnum));
        } catch (NumberFormatException e) {
            return StringUtils.EMPTY;
        }
    }

    public List<ErrMsgDto> getErrors() {
        List<ErrMsgDto> errors = new ArrayList<>();

        if (StringUtils.isNotBlank(tccErrCode) && StringUtils.isNotBlank(tccErrMsg)) {
            errors.add(ErrMsgDto.builder()
                    .code(tccErrCode)
                    .message(tccErrMsg)
                    .build());
        }

        Optional.ofNullable(errCode)
                .filter(StringUtils::isNotBlank)
                .ifPresent(code -> errors.addAll(
                        Arrays.stream(code.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotEmpty)
                                .map(err -> ErrMsgDto.builder()
                                        .code(err)
                                        .field(AppConstants.RESPONSE_CODE_FIELD_CODE_MAP.getOrDefault(err, null))
                                        .build())
                                .toList()));

        return errors;
    }
}
