package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreasuryRes {

    private String treasuryCode;
    private String treasuryName;
    private String admAreaCode;
    private String admAreaName;

    public String getTreasuryName() {
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }
}