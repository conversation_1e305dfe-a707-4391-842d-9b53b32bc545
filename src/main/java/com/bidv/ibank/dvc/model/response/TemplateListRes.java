package com.bidv.ibank.dvc.model.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.bidv.ibank.dvc.model.dto.PayerTypeDto;
import com.bidv.ibank.dvc.model.dto.TxnGeneralInfoDto;
import com.bidv.ibank.dvc.model.dto.TxnParamBenBankDto;
import com.bidv.ibank.dvc.model.dto.TxnParamCodeDto;
import com.bidv.ibank.dvc.model.dto.TxnParamNameDto;
import com.bidv.ibank.dvc.model.dto.TxnTaxFullItemDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TemplateListRes implements TxnParamCodeDto, TxnParamNameDto, TxnGeneralInfoDto, PayerTypeDto, TxnParamBenBankDto {

    private String taxCode;
    private String altTaxCode;
    private String payerName;
    private String altPayerName;
    private String payerAddr;
    private String altPayerAddr;
    private String treasuryCode;
    private String treasuryName;
    private String admAreaCode;
    private String admAreaName;
    private String revAccCode;
    private String revAccName;
    private String revAuthCode;
    private String revAuthName;
    private String benBankCode;
    private String benBankName;
    private Integer payerType;

    @Schema(example = "**********", description = "Tài khoản trích nợ")
    private String debitAccNo;

    @Schema(example = "Công ty TNHH A", description = " Tên tài khoản trích nợ")
    private String debitAccName;

    @Schema(example = "26CE7FAC-29E1-4F33-9DE7-0830BFDB0122", description = "Mã mẫu giao dịch")
    private String templateId;

    @Schema(example = "Mẫu giao dịch 1", description = "Tên mẫu giao dịch")
    private String templateName;

    @JsonFormat(pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS)
    @Schema(example = "2025-06-24T16:58:44.006902", description = "Ngày tạo")
    private LocalDateTime createdDate;

    @Schema(example = "", description = "Danh sách thông tin khoản nộp")
    private List<TxnTaxFullItemDto> taxItems;

    @Schema(example = "04", description = "Loại giao dịch: 01 là thuế nội địa. 03 là phí hạ tầng cảng biển. 04 là thuế hải quan")
    private String txnType;

    @Schema(example = "*********", description = "Số tiền")
    private BigDecimal amount;

    @Schema(example = "VND", description = "Loại tiền tệ")
    private String ccy;

    @Schema(example = "10000", description = "Phí dịch vụ (bao gồm VAT)")
    private BigDecimal feeTotal;

    @Schema(example = "Phí khoán", description = "Hình thức thu phí")
    private String feeOpt;

    @Schema(example = "VND", description = "Loại tiền tệ phí")
    private String feeCcy;

    @Schema(example = "APP", description = "Kênh giao dịch")
    private String channel;

    @Schema(example = "235947523904", description = "Mã giao dịch khách hàng")
    private String orgId;

    @JsonProperty("isPublic")
    @Schema(example = "true", description = "Lưu mẫu công khai")
    private boolean isPublic;

    @Override
    public String getTreasuryName() {
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    @Override
    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }

    @Override
    public String getRevAccName() {
        return Translator.toLocale(revAccCode, revAccName);
    }

    @Override
    public String getRevAuthName() {
        return Translator.toLocale(revAuthCode, revAuthName);
    }

    @Override
    public String getBenBankName() {
        return StringUtils.defaultString(benBankName);
    }

    public String getTxnTypeName() {
        return txnType != null ? Translator.toLocale(AppConstants.LANGUAGE.TXN_TYPE + "." + txnType, txnType) : null;
    }

    @Schema(example = "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi", description = "Số tiền bằng chữ")
    public String getAmountText() {
        return ReadNumber.formatAmountToText(Translator.getLocale().toLanguageTag().toLowerCase(), this.getAmount().toString(), this.getCcy());
    }
}
