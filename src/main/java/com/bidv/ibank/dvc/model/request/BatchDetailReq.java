package com.bidv.ibank.dvc.model.request;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchDetailReq {

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_BATCH_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    @Schema(example = "BDR20250529172533579", description = "Mã lô")
    private String batchNo;

}
