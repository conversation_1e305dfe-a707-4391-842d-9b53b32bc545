package com.bidv.ibank.dvc.model.entity.customsduty;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.framework.domain.entity.AuditEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "GOV_PAYMENT_ITEM")
public class GOVPaymentItemEntity extends AuditEntity<String> implements ItfGOVPaymentItemBaseEntity {

    @Id
    @Column(name = "ID", nullable = false)
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "TXN_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    private GOVPaymentTransactionEntity govPaymentTransactionEntity;

    @Size(max = 50)
    @Column(name = "TXN_ID", length = 50)
    private String txnId;

    @Size(max = 30)
    @Column(name = "DECLARATION_NO", length = 30)
    private String declarationNo;

    @Column(name = "DECLARATION_DATE")
    private LocalDate declarationDate;

    @NotNull
    @Size(max = 4)
    @Column(name = "MA_NDKT", length = 4)
    private String maNdkt;

    @NotNull
    @Size(max = 3)
    @Column(name = "MA_CHUONG", length = 3)
    private String maChuong;

    @NotNull
    @Size(max = 10)
    @Column(name = "MA_STHUE", length = 10)
    private String maSthue;

    @NotNull
    @Size(max = 20)
    @Column(name = "MA_LH", length = 20)
    private String maLh;

    @NotNull
    @Size(max = 20)
    @Column(name = "MA_LTHQ", length = 20)
    private String maLthq;

    @NotNull
    @Column(name = "AMOUNT")
    private BigDecimal amount;

    @NotNull
    @Size(max = 3)
    @Column(name = "CCY", length = 3)
    private String ccy;

    @Size(max = 1000)
    @Column(name = "TRANS_DESC", length = 1000)
    private String transDesc;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_NDKT", referencedColumnName = "MA_NDKT", insertable = false, updatable = false)
    private TccDmNdktEntity tccDmNdktEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_CHUONG", referencedColumnName = "MA_CHUONG", insertable = false, updatable = false)
    private TccDmChuongEntity tccDmChuongEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_STHUE", referencedColumnName = "MA_STHUE", insertable = false, updatable = false)
    private TccDmSthueHqaEntity tccDmSthueHqaEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_LH", referencedColumnName = "MA_LH", insertable = false, updatable = false)
    private TccDmLhxnkEntity tccDmLhxnkEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_LTHQ", referencedColumnName = "MA_LTHQ", insertable = false, updatable = false)
    private TccDmLoaitienhqaEntity tccDmLoaitienhqaEntity;

}
