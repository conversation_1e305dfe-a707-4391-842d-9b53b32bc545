package com.bidv.ibank.dvc.model.mapper.customsduty;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import com.bidv.ibank.client.common.dto.workflow.WfTxnInfoResponse;
import com.bidv.ibank.dvc.util.StrUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentDto;
import com.bidv.ibank.dvc.model.dto.TxnPrintDocumentItemDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.util.JasperReportUtils;

import net.sf.jasperreports.engine.JREmptyDataSource;

@Component
public class DocumentMapper {
    public List<JasperReportUtils.BatchExportParam> toBatchExportParams(List<TxnPrintDocumentDto> txnPrintDtos,
            List<TxnPrintDocumentItemDto> txnPrintDocumentItemList,
            List<WfTxnInfoResponse> wfTxnInfoResponses) {
        txnPrintDtos.forEach(txnPrintDto -> {
            AtomicInteger order = new AtomicInteger(1);
            List<TxnPrintDocumentItemDto> txnPrintDocumentItemDataList = txnPrintDocumentItemList.stream()
                    .filter(data -> data.getTxnId().equals(txnPrintDto.getTxnId()))
                    .peek(data -> {
                        data.setOrder(((Integer) order.getAndIncrement()).toString());
                        data.setAmount(StringUtils.defaultString(StrUtils.formatWithThousandSeparator(data.getAmount())));
                        data.setAmountVnd(StringUtils.defaultString(StrUtils.formatWithThousandSeparator(data.getAmountVnd())));
                    })
                    .toList();
            txnPrintDto.setTxnPrintDocumentItems(txnPrintDocumentItemDataList);
        });

        List<JasperReportUtils.BatchExportParam> batchExportParams = new ArrayList<>();
        for (TxnPrintDocumentDto txnPrintDto : txnPrintDtos) {
            WfTxnInfoResponse txnInfo = wfTxnInfoResponses.stream()
                    .filter(info -> info.getId().equals(txnPrintDto.getApprovalWfId()))
                    .findFirst()
                    .orElse(null);
            JasperReportUtils.BatchExportParam firstPageParam = JasperReportUtils.BatchExportParam.builder()
                    .templateFile(AppConstants.DOCUMENT_JASPER_FIRST_PAGE_PATH)
                    .dataSource(new JREmptyDataSource())
                    .inputParams(txnPrintDto.getParamFirstPage())
                    .build();
            JasperReportUtils.BatchExportParam secondPageParam = JasperReportUtils.BatchExportParam.builder()
                    .templateFile(AppConstants.DOCUMENT_JASPER_SECOND_PAGE_PATH)
                    .dataSource(new JREmptyDataSource())
                    .inputParams(txnPrintDto.getParamSecondPage(txnInfo))
                    .build();
            batchExportParams.add(firstPageParam);
            batchExportParams.add(secondPageParam);
        }
        return batchExportParams;
    }
}
