package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaxTypeRes {

    private String taxTypeCode;
    private String taxTypeName;

    public String getTaxTypeName() {
        return Translator.toLocale(taxTypeCode, taxTypeName);
    }
}
