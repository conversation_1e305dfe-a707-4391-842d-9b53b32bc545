package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmDbhcCodeNameEntityDto implements Exportable {
    private String maDbhc;
    private String tenDbhc;

    @ExportConfig(colIndex = 1)
    public String getMaDbhc() {
        return maDbhc;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenDbhc() {
        return StrUtils.combineCodeName(maDbhc, tenDbhc);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
