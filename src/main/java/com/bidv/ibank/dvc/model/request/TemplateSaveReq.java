package com.bidv.ibank.dvc.model.request;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TemplateSaveReq {

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ID_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER)
    @Schema(example = "DVC01704202411252339", description = "Mã giao dịch")
    private String txnId;

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TEMPLATE_NAME_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_LINE_AND_DOT_SPACE)
    @Schema(example = "Mẫu nộp thuế 1", description = "Tên template")
    private String templateName;

    @Schema(example = "true", description = "Lưu mẫu công khai")
    @Builder.Default
    private Boolean isPublic = false;
}
