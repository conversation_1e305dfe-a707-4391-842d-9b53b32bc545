package com.bidv.ibank.dvc.model.dto;

import java.util.List;

import com.bidv.ibank.common.txn.util.constant.TransactionStateEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

import io.swagger.v3.oas.annotations.media.Schema;

public interface TxnDetailStatusDto {

    @Schema(example = "SUCCESS", description = "Trạng thái giao dịch")
    String getStatus();

    String getState();

    @Schema(example = "Thành công", description = "Tên trạng thái")
    default String getStatusName() {
        return Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + "." + getStatus(), getStatus());
    }

    @Schema(example = "SUCCESS", description = "Trạng thái hạch toán: (SUCCESS, FAILED, UNDEFINED)")
    default String getAccountingStatus() {
        List<String> finalStatuses = List.of(
                TransactionStatusEnum.SUCCESS.name(),
                TransactionStatusEnum.FAILED.name(),
                TransactionStatusEnum.UNDEFINED.name(),
                TransactionStatusEnum.BANK_PROCESSING.name());
        return getStatus() != null && finalStatuses.contains(getStatus()) ? getStatus() : null;
    }

    @Schema(example = "SUCCESS", description = "Trạng thái kết nối hải quan: (SUCCESS, FAILED)")
    default String getCustomsConnStatus() {
        List<String> finalStates = List.of(
                TransactionStateEnum.SUCCESS.name(),
                TransactionStateEnum.FAIL_TRANS_TCC.name());
        return getState() != null && finalStates.contains(getState())
                ? (getState().equals(TransactionStateEnum.SUCCESS.name()) ? TransactionStateEnum.SUCCESS.name() : TransactionStateEnum.FAILED.name())
                : null;
    }

    @Schema(example = "Thành công", description = "Tên trạng thái hạch toán")
    default String getAccountingStatusName() {
        return getAccountingStatus() != null
                ? Translator.toLocale(AppConstants.LANGUAGE.ACCOUNTING_STATUS + "." + getAccountingStatus(), getAccountingStatus())
                : null;
    }

    @Schema(example = "Thành công", description = "Tên trạng thái hải quan")
    default String getCustomsConnStatusName() {
        return getCustomsConnStatus() != null
                ? Translator.toLocale(AppConstants.LANGUAGE.CUSTOMS_CONN_STATUS + "." + getCustomsConnStatus(), getCustomsConnStatus())
                : null;
    }
}
