package com.bidv.ibank.dvc.model.entity.customsduty;

import java.math.BigDecimal;
import java.util.LinkedHashSet;
import java.util.Set;

import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.framework.domain.entity.AuditEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "GOV_PAYMENT_TEMPLATE")
public class GOVPaymentTemplateEntity extends AuditEntity<String> implements ItfGOVPaymentBaseEntity {

    @Id
    @Column(name = "ID", nullable = false)
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    @NotNull
    @Size(max = 100)
    @Column(name = "NAME", length = 100)
    private String name;

    @NotNull
    @Size(max = 20)
    @Column(name = "DEBIT_ACCNO", length = 20)
    private String debitAccNo;

    @Size(max = 500)
    @Column(name = "DEBIT_ACCNAME", length = 500)
    private String debitAccName;

    @NotNull
    @Size(max = 20)
    @Column(name = "TAX_CODE", length = 20)
    private String taxCode;

    @Size(max = 20)
    @Column(name = "ALT_TAX_CODE", length = 20)
    private String altTaxCode;

    @NotNull
    @Column(name = "PAYER_TYPE")
    @Enumerated(EnumType.ORDINAL)
    private PayerTypeEnum payerType;

    @NotNull
    @Size(max = 4)
    @Column(name = "SHKB", length = 4)
    private String shkb;

    @NotNull
    @Size(max = 5)
    @Column(name = "MA_DBHC", length = 5)
    private String maDbhc;

    @NotNull
    @Size(max = 7)
    @Column(name = "MA_CQTHU", length = 7)
    private String maCqthu;

    @NotNull
    @Size(max = 50)
    @Column(name = "MA_TK", length = 50)
    private String maTk;

    @NotNull
    @Size(max = 8)
    @Column(name = "MA_NH", length = 8)
    private String maNh;

    @NotNull
    @Size(max = 140)
    @Column(name = "PAYER_NAME", length = 140)
    private String payerName;

    @Size(max = 140)
    @Column(name = "ALT_PAYER_NAME", length = 140)
    private String altPayerName;

    @NotNull
    @Size(max = 140)
    @Column(name = "PAYER_ADDR", length = 140)
    private String payerAddr;

    @Size(max = 140)
    @Column(name = "ALT_PAYER_ADDR", length = 140)
    private String altPayerAddr;

    @Column(name = "IS_PUBLIC")
    private Boolean isPublic;

    @NotNull
    @Size(max = 30)
    @Column(name = "CIF_NO", length = 30)
    private String cifNo;

    @Column(name = "CUS_ID")
    private Long cusId;

    @NotNull
    @Size(max = 10)
    @Column(name = "STATUS", length = 10)
    private String status;

    @NotNull
    @Size(max = 2)
    @Column(name = "TXN_TYPE", length = 2)
    private String txnType;

    @Column(name = "AMOUNT")
    private BigDecimal amount;

    @Size(max = 3)
    @Column(name = "CCY", length = 3)
    private String ccy;

    @Column(name = "FEE_TOTAL")
    private BigDecimal feeTotal;

    @Column(name = "FEE_OPT", length = 30)
    private String feeOpt;

    @Column(name = "FEE_CCY", length = 3)
    private String feeCcy;

    @NotNull
    @Size(max = 30)
    @Column(name = "CHANNEL", length = 30)
    private String channel;

    @Size(max = 30)
    @Column(name = "ORG_ID", length = 30)
    private String orgId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "govPaymentTemplateEntity")
    @Builder.Default
    @OrderBy("createdDate")
    private Set<GOVPaymentTemplateItemEntity> govPaymentTemplateItemList = new LinkedHashSet<>();

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    private TccDmKhobacEntity tccDmKhobacEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_DBHC", referencedColumnName = "MA_DBHC", insertable = false, updatable = false)
    private TccDmDbhcEntity tccDmDbhcEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_CQTHU", referencedColumnName = "MA_CQTHU", insertable = false, updatable = false)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    private TccDmCqthuEntity tccDmCqthuEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_TK", referencedColumnName = "MA_TK", insertable = false, updatable = false)
    private TccDmTkNsnnEntity tccDmTkNsnnEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    @JoinColumn(name = "MA_NH", referencedColumnName = "MA_NH", insertable = false, updatable = false)
    private TccDmKbnnNhtmEntity tccDmKbnnNhtmEntity;
}
