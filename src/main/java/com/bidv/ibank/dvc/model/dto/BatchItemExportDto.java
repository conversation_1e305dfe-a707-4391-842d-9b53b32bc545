package com.bidv.ibank.dvc.model.dto;

import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonPropertyOrder(alphabetic = true)
public class BatchItemExportDto extends BatchItemDetailDto implements Exportable {

    @ExportConfig(colIndex = 0)
    private String batchOrder;

    @ExportConfig(colIndex = 1)
    private String status;

    @ExportConfig(colIndex = 2)
    private String errorDetail;

    @ExportConfig(colIndex = 3)
    @Schema(example = "**********", description = "Tài khoản trích nợ")
    private String debitAccNo;

    @ExportConfig(colIndex = 4)
    private String taxCode;

    @ExportConfig(colIndex = 5)
    private String payerName;

    @ExportConfig(colIndex = 6)
    private String payerAddr;

    @ExportConfig(colIndex = 7)
    private String altTaxCode;

    @ExportConfig(colIndex = 8)
    private String altPayerName;

    @ExportConfig(colIndex = 9)
    private String altPayerAddr;

    @ExportConfig(colIndex = 10)
    @Schema(example = "30644771632", description = "Số tờ khai")
    private String declarationNo;

    @ExportConfig(colIndex = 11)
    @Schema(example = "24/05/2024", description = "Ngày tờ khai")
    private String declarationDate;

    @ExportConfig(colIndex = 12)
    private String treasuryCodeName;

    @ExportConfig(colIndex = 13)
    private String revAccCode;

    @ExportConfig(colIndex = 14)
    private String revAuthCodeName;

    @ExportConfig(colIndex = 15)
    private String admAreaCodeName;

    @ExportConfig(colIndex = 16)
    private String chapterCodeName;

    @ExportConfig(colIndex = 17)
    private String ecCodeName;

    @ExportConfig(colIndex = 18)
    @Schema(example = "*********", description = "Số tiền")
    private String amount;

    @ExportConfig(colIndex = 19)
    @Schema(example = "VND", description = "Loại tiền tệ")
    private String ccy;

    @ExportConfig(colIndex = 20)
    @Schema(example = "Tờ khai thuế GTGT", description = "Diễn giải giao dịch")
    private String transDesc;

    @ExportConfig(colIndex = 21)
    @Schema(example = "VA", description = "Mã sắc thuế")
    private String taxTypeCode;

    @ExportConfig(colIndex = 22)
    @Schema(example = "11", description = "Mã loại tiền hải quan")
    private String ccCode;

    @ExportConfig(colIndex = 23)
    private String eiTypeCodeName;

    @ExportConfig(colIndex = 24)
    @Schema(example = "1", description = " Loại hình người nộp thuế: 0 - Không xác định, 1 - doanh nghiệp, 2 - cá nhân")
    private String payerType;

    @ExportConfig(colIndex = 25)
    @Schema(example = "12345678", description = "Mã khách hàng")
    private String orgId;

    public String getErrorDetail() {
        if (CollectionUtils.isEmpty(super.getErrors())) {
            return super.getTccErrMsg();
        }
        String errCodeStr = super.getErrors().stream()
                .map(ErrMsgDto::getMessage)
                .collect(Collectors.joining(";"));
        return StringUtils.isBlank(super.getTccErrMsg()) ? errCodeStr : errCodeStr + ";" + super.getTccErrMsg();
    }

    public String getTreasuryCodeName() {
        return StrUtils.combineCodeName(super.getTreasuryCode(), super.getTreasuryName());
    }

    public String getRevAuthCodeName() {
        return StrUtils.combineCodeName(super.getRevAuthCode(), super.getRevAuthName());
    }

    public String getAdmAreaCodeName() {
        return StrUtils.combineCodeName(super.getAdmAreaCode(), super.getAdmAreaName());
    }

    public String getChapterCodeName() {
        return StrUtils.combineCodeName(super.getChapterCode(), super.getChapterName());
    }

    public String getEcCodeName() {
        return StrUtils.combineCodeName(super.getEcCode(), super.getEcName());
    }

    public String getEiTypeCodeName() {
        return StrUtils.combineCodeName(super.getEiTypeCode(), super.getEiTypeName());
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }
}
