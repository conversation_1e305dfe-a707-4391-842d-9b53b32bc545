package com.bidv.ibank.dvc.model.dto;

import org.apache.commons.lang3.StringUtils;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TxnTaxFullItemDto extends TxnTaxExtraItemDto implements TxnParamCodeDto, TxnParamNameDto, TxnParamBenBankDto, PayerTypeDto {

    private String treasuryCode;
    private String treasuryName;
    private String admAreaCode;
    private String admAreaName;
    private String revAccCode;
    private String revAccName;
    private String revAuthCode;
    private String revAuthName;
    private String benBankCode;
    private String benBankName;
    private Integer payerType;

    @Override
    public String getTreasuryName() {
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    @Override
    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }

    @Override
    public String getRevAccName() {
        return Translator.toLocale(revAccCode, revAccName);
    }

    @Override
    public String getRevAuthName() {
        return Translator.toLocale(revAuthCode, revAuthName);
    }

    @Override
    public String getBenBankName() {
        return StringUtils.defaultString(benBankName);
    }
}
