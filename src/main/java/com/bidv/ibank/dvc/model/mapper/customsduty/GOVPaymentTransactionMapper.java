package com.bidv.ibank.dvc.model.mapper.customsduty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.bidv.ibank.dvc.model.response.TxnListRes;
import com.bidv.ibank.dvc.model.response.TxnPendingApprovalListRes;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.bidv.ibank.client.common.dto.masterdata.BalanceAccountDto;
import com.bidv.ibank.common.txn.model.dto.InitTransactionRes;
import com.bidv.ibank.common.txn.model.dto.ProcessTransactionResponse;
import com.bidv.ibank.common.txn.util.constant.FeeMethodEnum;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.request.TxnExportReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.model.response.TxnDetailRes;
import com.bidv.ibank.dvc.model.response.TxnInitPushRes;
import com.bidv.ibank.dvc.model.response.TxnPendingListRes;
import com.bidv.ibank.dvc.model.response.TxnProcessResultRes;
import com.bidv.ibank.dvc.model.response.TxnRejectRes;
import com.bidv.ibank.dvc.model.response.TxnReportListRes;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.CcyUtils;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.constant.TxnTaxTypeEnum;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.framework.util.mapper.EntityDtoMapper;
import com.bidv.ibank.framework.util.mapper.ModelMapperUtils;

@Component
public class GOVPaymentTransactionMapper extends GOVPaymentBaseMapper implements EntityDtoMapper<GOVPaymentTransactionEntity, TxnPendingListRes> {

    @Override
    public TxnPendingListRes toDto(GOVPaymentTransactionEntity e) {
        throw new UnsupportedOperationException("Unimplemented method 'toDto'");
    }

    @Override
    public GOVPaymentTransactionEntity toEntity(TxnPendingListRes dto) {
        throw new UnsupportedOperationException("Unimplemented method 'toEntity'");
    }

    public TxnListRes toListDto(GOVPaymentTransactionEntity entity) {
        TxnPendingListRes pendingDto = toPendingDto(entity);
        TxnListRes listDto = ModelMapperUtils.map(pendingDto, TxnListRes.class);
        listDto.setApprovalUsers(entity.getApprovalUsers());
        listDto.setCreatedBy(entity.getCreatedBy());
        return listDto;
    }

    public TxnPendingApprovalListRes toPendingApprovalListRes(GOVPaymentTransactionEntity entity) {
        TxnPendingListRes txnPendingListRes = toPendingDto(entity);
        TxnPendingApprovalListRes txnPendingApprovalListRes = ModelMapperUtils.map(txnPendingListRes, TxnPendingApprovalListRes.class);
        txnPendingApprovalListRes.setRaNote(entity.getRaNote());
        txnPendingApprovalListRes.setApprovalUsers(entity.getApprovalUsers());
        txnPendingApprovalListRes.setTxnType(entity.getTxnType());
        txnPendingApprovalListRes.setOrgId(entity.getOrgId());
        txnPendingApprovalListRes.setTxnItemId(null);
        txnPendingApprovalListRes.setUpdatedDate(entity.getUpdatedDate());
        txnPendingApprovalListRes.setEffDate(entity.getEffDate());
        return txnPendingApprovalListRes;
    }

    public TxnReportListRes toReportListRes(GOVPaymentTransactionEntity entity) {
        TxnPendingListRes txnPendingListRes = toPendingDto(entity);
        TxnReportListRes txnReportListRes = ModelMapperUtils.map(txnPendingListRes, TxnReportListRes.class);
        txnReportListRes.setCreatedBy(entity.getCreatedBy());
        txnReportListRes.setApprovalUsers(entity.getApprovalUsers());
        txnReportListRes.setTxnType(entity.getTxnType());
        txnReportListRes.setOrgId(entity.getOrgId());
        txnReportListRes.setTxnItemId(null);
        return txnReportListRes;
    }

    public TxnPendingListRes toPendingDto(GOVPaymentTransactionEntity entity) {
        return TxnPendingListRes.builder()
                .txnId(entity.getId())
                .debitAccNo(entity.getDebitAccNo())
                .taxCode(entity.getTaxCode())
                .declarationNo(entity.getGovPaymentItemList().stream().map(GOVPaymentItemEntity::getDeclarationNo)
                        .distinct()
                        .collect(Collectors.joining(", ")))
                .amount(entity.getAmount())
                .ccy(entity.getCcy())
                .treasuryCode(entity.getShkb())
                .treasuryName(Optional.ofNullable(entity.getTccDmKhobacEntity()).map(TccDmKhobacEntity::getTen).orElse(null))
                .admAreaCode(entity.getMaDbhc())
                .admAreaName(Optional.ofNullable(entity.getTccDmDbhcEntity()).map(TccDmDbhcEntity::getTen).orElse(null))
                .revAccCode(entity.getMaTk())
                .revAccName(Optional.ofNullable(entity.getTccDmTkNsnnEntity()).map(TccDmTkNsnnEntity::getTen).orElse(null))
                .revAuthCode(entity.getMaCqthu())
                .revAuthName(Optional.ofNullable(entity.getTccDmCqthuEntity()).map(TccDmCqthuEntity::getTen).orElse(null))
                .batchNo(entity.getBatchNo())
                .status(entity.getStatus())
                .priority(entity.isPriority())
                .orgId(entity.getOrgId())
                .createdDate(entity.getCreatedDate())
                .build();
    }

    public TxnDetailRes toDetailDto(GOVPaymentTransactionEntity entity) {
        return TxnDetailRes.builder()
                .txnId(entity.getId())
                .debitAccNo(entity.getDebitAccNo())
                .debitAccName(entity.getDebitAccName())
                .taxCode(entity.getTaxCode())
                .altTaxCode(entity.getAltTaxCode())
                .payerName(entity.getPayerName())
                .altPayerName(entity.getAltPayerName())
                .payerAddr(entity.getPayerAddr())
                .altPayerAddr(entity.getAltPayerAddr())
                .payerType(entity.getPayerType().getValue())
                .amount(entity.getAmount())
                .ccy(entity.getCcy())
                .batchNo(entity.getBatchNo())
                .status(entity.getStatus())
                .state(entity.getState())
                .createdDate(entity.getCreatedDate())
                .updatedDate(entity.getUpdatedDate())
                .effDate(entity.getEffDate())
                .createdBy(entity.getCreatedBy())
                .approvalUsers(entity.getApprovalUsers())
                .tccRefNo(entity.getCoreRef())
                .treasuryCode(entity.getShkb())
                .treasuryName(getTreasuryName(entity))
                .admAreaCode(entity.getMaDbhc())
                .admAreaName(getAdmAreaName(entity))
                .revAccCode(entity.getMaTk())
                .revAccName(getRevAccName(entity))
                .revAuthCode(entity.getMaCqthu())
                .revAuthName(getRevAuthName(entity))
                .benBankCode(entity.getMaNh())
                .benBankName(getBenBankName(entity))
                .feeTotal(entity.getFeeTotal())
                .feeOpt(entity.getFeeOpt())
                .feeCcy(entity.getFeeCcy())
                .raNote(entity.getRaNote())
                .approvalNote(entity.getApprovalNote())
                .taxItems(mapTaxItems(entity, entity.getGovPaymentItemList()))
                .priority(entity.isPriority())
                .orgId(entity.getOrgId())
                .description(entity.getTccErrDesc())
                .channel(entity.getChannel())
                .pmtTime(entity.getPmtTime())
                .build();
    }

    public GOVPaymentTransactionEntity toEntity(ValidateCustomsDutyReq req, String benBankCode, BalanceAccountDto accountDto) {
        GOVPaymentTransactionEntity entity = GOVPaymentTransactionEntity.builder()
                .taxCode(req.getTaxCode())
                .altTaxCode(req.getAltTaxCode())
                .payerName(req.getPayerName())
                .altPayerName(req.getAltPayerName())
                .payerAddr(req.getPayerAddr())
                .altPayerAddr(req.getAltPayerAddr())
                .shkb(req.getTreasuryCode())
                .maDbhc(req.getAdmAreaCode())
                .maCqthu(req.getRevAuthCode())
                .maTk(req.getRevAccCode())
                .maNh(benBankCode)
                .payerType(req.getPayerType())
                .build();

        if (StringUtils.isNotBlank(req.getTxnId())) {
            entity.setId(req.getTxnId());
        }
        entity.setDebitAccNo(req.getDebitAccNo());
        entity.setAmount(new BigDecimal(req.getAmount()));
        entity.setCcy(req.getCcy());
        entity.setDebitCcy(accountDto.getCurrCode());
        entity.setEffDate(LocalDate.now());
        entity.setFeeMethod(FeeMethodEnum.O);
        entity.setTxnType(TxnTaxTypeEnum.CUSTOMS_TAX.getCode());
        entity.setFeeBrcd(accountDto.getBranchCode());
        entity.setRaNote(req.getRaNote());
        entity.setPriority(req.isPriority());
        entity.setOrgId(req.getOrgId());
        entity.setPaymentItems(req.getTaxItems().stream()
                .<GOVPaymentItemEntity>map(item -> GOVPaymentItemEntity.builder()
                        .declarationNo(item.getDeclarationNo())
                        .declarationDate(item.getDeclarationDate())
                        .maNdkt(item.getEcCode())
                        .maChuong(item.getChapterCode())
                        .maLthq(item.getCcCode())
                        .maLh(item.getEiTypeCode())
                        .maSthue(item.getTaxTypeCode())
                        .amount(new BigDecimal(item.getAmount()))
                        .ccy(item.getCcy())
                        .transDesc(item.getTransDesc())
                        .build())
                .collect(Collectors.toSet()));

        return entity;
    }

    public TxnProcessResultRes toProcessResultDto(TxnProcessResultRes res, GOVPaymentTransactionEntity entity) {
        res.setTxnId(entity.getId());
        res.setTotalAmount(entity.getAmount());
        res.setCcy(entity.getCcy());
        res.setFeeTotal(entity.getFeeTotal());
        res.setFeeCcy(entity.getFeeCcy());
        res.setFeeOpt(entity.getFeeOpt());
        res.setCreatedDate(entity.getCreatedDate() != null ? entity.getCreatedDate() : LocalDateTime.now());
        res.setDebitAccNo(entity.getDebitAccNo());
        res.setDebitAccName(entity.getDebitAccName());
        res.setTreasuryCode(entity.getShkb());
        res.setAdmAreaCode(entity.getMaDbhc());
        res.setRevAccCode(entity.getMaTk());
        res.setRevAuthCode(entity.getMaCqthu());
        return res;
    }

    public TxnRejectRes toRejectDto(TxnRejectRes res, GOVPaymentTransactionEntity entity) {
        res.setTxnId(entity.getId());
        res.setTotalAmount(entity.getAmount());
        res.setCcy(entity.getCcy());
        return res;
    }

    public TxnInitPushRes toInitPushRes(ProcessTransactionResponse<GOVPaymentTransactionEntity> response) {
        return TxnInitPushRes.builder()
                .requireAuth(response.isRequireAuth())
                .transAuth(response.getTransAuth())
                .transKey(response.getTransKey())
                .build();
    }

    public TxnInitPushRes toInitPushRes(InitTransactionRes<GOVPaymentTransactionEntity> response) {
        return TxnInitPushRes.builder()
                .requireAuth(response.isRequireAuth())
                .transAuth(response.getTransAuth())
                .transKey(response.getTransKey())
                .build();
    }

    public Map<String, String> toExportParams(TxnExportReq req) {
        Map<String, String> params = new HashMap<>();
        params.put("EXPORT_DATE", DateUtils.convertDateToString(LocalDateTime.now(), AppConstants.DATE_FORMAT_DD_MM_YYYY));
        params.put("START_DATE", DateUtils.convertDateToString(req.getStartDate(), AppConstants.DATE_FORMAT_DD_MM_YYYY));
        params.put("END_DATE", DateUtils.convertDateToString(req.getEndDate(), AppConstants.DATE_FORMAT_DD_MM_YYYY));
        params.put("DEBIT_ACC_NO", req.getDebitAccNo());
        params.put("TAX_CODE", req.getTaxCode());
        params.put("TXN_TYPES", req.getTxnTypes() != null ? req.getTxnTypes().stream().map(
                e -> Translator.toLocale(AppConstants.LANGUAGE.TXN_TYPE + "." + e, e)).collect(Collectors.joining(", ")) : null);
        params.put("MIN_AMOUNT", req.getMinAmount() != null ? req.getMinAmount().toString() : null);
        params.put("MAX_AMOUNT", req.getMaxAmount() != null ? req.getMaxAmount().toString() : null);
        params.put("CCYS", req.getCcys() != null ? req.getCcys().stream().map(CcyUtils::getCodeByCcy).collect(Collectors.joining(", ")) : null);
        params.put("DECLARATION_NO", req.getDeclarationNo());
        params.put("STATUSES", req.getStatuses() != null ? req.getStatuses().stream().map(
                e -> Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + "." + e, e)).collect(Collectors.joining(", ")) : null);
        params.put("BATCH_NO", req.getBatchNo());
        params.put("REF_NO", req.getTccRefNo());
        params.put("CHANNELS", req.getChannels() != null ? req.getChannels().stream().collect(Collectors.joining(", ")) : null);
        params.put("TXN_ITEM_ID", req.getTxnItemId());
        return params;
    }
}
