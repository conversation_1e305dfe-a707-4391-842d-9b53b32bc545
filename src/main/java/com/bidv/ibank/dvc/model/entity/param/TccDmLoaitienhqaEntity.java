package com.bidv.ibank.dvc.model.entity.param;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TCC_DM_LOAITIENHQA")
public class TccDmLoaitienhqaEntity {

    @Id
    @Size(max = 20)
    @Column(name = "MA_LTHQ", length = 20)
    private String maLthq;

    @Nullable
    @Size(max = 200)
    @Column(name = "TEN_LTHQ", length = 200)
    private String tenLthq;
}