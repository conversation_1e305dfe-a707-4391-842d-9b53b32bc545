package com.bidv.ibank.dvc.model.entity.customsduty;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;

public interface ItfGOVPaymentItemBaseEntity {

    String getDeclarationNo();

    LocalDate getDeclarationDate();

    String getMaNdkt();

    String getMaChuong();

    String getMaSthue();

    String getMaLh();

    String getMaLthq();

    BigDecimal getAmount();

    String getCcy();

    String getTransDesc();

    TccDmNdktEntity getTccDmNdktEntity();

    TccDmChuongEntity getTccDmChuongEntity();

    TccDmSthueHqaEntity getTccDmSthueHqaEntity();

    TccDmLhxnkEntity getTccDmLhxnkEntity();

    TccDmLoaitienhqaEntity getTccDmLoaitienhqaEntity();
}