package com.bidv.ibank.dvc.model.request;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.bidv.ibank.dvc.model.dto.TxnSearchBaseDto;
import com.bidv.ibank.dvc.model.filter.StatusesFilter;
import com.bidv.ibank.dvc.util.AppConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TxnExportReq implements TxnSearchBaseDto, StatusesFilter {

    private String search;
    private LocalDate startDate;
    private LocalDate endDate;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private List<String> ccys;
    private List<String> statuses;
    private String debitAccNo;
    private String taxCode;
    private String declarationNo;
    private String batchNo;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TCC_REF_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(description = "Số tham chiếu thuế")
    private String tccRefNo;

    @Schema(example = "04", description = "Loại giao dịch: 01 là thuế nội địa. 03 là phí hạ tầng cảng biển. 04 là thuế hải quan")
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH) String> txnTypes;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ITEM_ID_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(description = "Id khoản nộp")
    private String txnItemId;

    @Schema(example = "[\"WEB\", \"APP\"]", description = "Danh sách kênh giao dịch")
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH) String> channels;

    @JsonIgnore
    public LocalDateTime getStartDateTime() {
        return startDate != null ? startDate.atStartOfDay() : null;
    }

    @JsonIgnore
    public LocalDateTime getEndDateTime() {
        return endDate != null ? endDate.atTime(LocalTime.MAX) : null;
    }

    public List<String> getCcys() {
        return CollectionUtils.isEmpty(ccys) ? null : ccys;
    }

    public List<String> getStatuses() {
        return CollectionUtils.isEmpty(statuses) ? null : statuses;
    }

    public List<String> getTxnTypes() {
        return CollectionUtils.isEmpty(txnTypes) ? null : txnTypes;
    }

    public List<String> getChannels() {
        return CollectionUtils.isEmpty(channels) ? null : channels;
    }
}
