package com.bidv.ibank.dvc.model.mapper.tcc;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.springframework.stereotype.Component;

import com.bidv.ibank.dvc.model.request.InquiryCustomsDutyReq;
import com.bidv.ibank.dvc.model.request.ValidateCustomsDutyReq;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.NumberUtils;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.dvc.util.constant.TxnTaxTypeEnum;
import com.bidv.ibank.framework.util.mapper.EntityDtoMapper;
import com.bidv.ibank.integrate.entity.tcc.TccAccountingReq;
import com.bidv.ibank.integrate.entity.tcc.TccCreateDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccInquiryDeclarationReq;
import com.bidv.ibank.integrate.entity.tcc.TccMessageReq;
import com.bidv.ibank.integrate.entity.tcc.TccQueryTxnInfoReq;
import com.bidv.ibank.integrate.entity.tcc.TccResendDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfo;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfoDetail;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfoDetailRow;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocInfoHdr;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocReq;
import com.bidv.ibank.integrate.entity.tcc.TccValidateDocReq.TccValidateDocLst;
import com.bidv.ibank.common.txn.util.constant.ChargeFeeOpt;
import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import com.bidv.ibank.dvc.model.dto.MappingTreasuryBenBankCodeDto;
import com.bidv.ibank.dvc.model.dto.TxnTaxItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;

import io.micrometer.common.util.StringUtils;

@Component
public class TccReqMapper implements EntityDtoMapper<Object, Object> {

    @Override
    public Object toDto(Object entity) {
        throw new UnsupportedOperationException("Unimplemented method 'toDto'");
    }

    @Override
    public Object toEntity(Object dto) {
        throw new UnsupportedOperationException("Unimplemented method 'toEntity'");
    }

    public TccMessageReq<TccInquiryDeclarationReq> toInquiryDeclarationReq(InquiryCustomsDutyReq request) {
        return TccMessageReq.<TccInquiryDeclarationReq>builder()
                .messageRequest(TccInquiryDeclarationReq.builder()
                        .maDv(request.getTaxCode())
                        .soTk(request.getDeclarationNo())
                        .namDk(request.getDeclarationYear())
                        .build())
                .build();
    }

    public TccValidateDocReq toValidateDocReq(ValidateCustomsDutyReq request, GOVPaymentTransactionEntity govPaymentTransaction,
            MappingTreasuryBenBankCodeDto treasuryInfo, String revAuthName) {

        Double amount = NumberUtils.toDouble(govPaymentTransaction.getAmount(), 0.0);
        Double feeAmount = NumberUtils.toDouble(govPaymentTransaction.getFeeAmount(), 0.0);
        Double feeVAT = NumberUtils.toDouble(govPaymentTransaction.getFeeVAT(), 0.0);
        Double feeTotal = NumberUtils.toDouble(govPaymentTransaction.getFeeTotal(), 0.0);

        return TccValidateDocReq.builder()
                .chungTuLst(TccValidateDocLst.builder()
                        .chungTu(TccValidateDocInfo.builder()
                                .sttCtu(String.valueOf(AppConstants.NUMBER.ONE))
                                .ctuHdr(TccValidateDocInfoHdr.builder()
                                        .srData(AppConstants.TCC_VALIDATE_DOC.BDR)
                                        .soBt(String.valueOf(AppConstants.NUMBER.ONE))
                                        .shkb(request.getTreasuryCode())
                                        .tkNo(treasuryInfo.getDebtAccCode())
                                        .tkCo(request.getRevAccCode())
                                        .maNv(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .tellerId(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .maNvBds(govPaymentTransaction.getDebitBrcd())
                                        .maNnthue(request.getTaxCode())
                                        .tenNnthue(request.getPayerName())
                                        .dcNnthue(request.getPayerAddr())
                                        .maLthue1(TxnTaxTypeEnum.CUSTOMS_TAX.getCode())
                                        .maCqthu(request.getRevAuthCode())
                                        .maDvsdns(request.getRevAuthCode())
                                        .maDbhc(request.getAdmAreaCode())
                                        .maNhA(AppConstants.TCC_VALIDATE_DOC.BANK_CODE)
                                        .maNhB(treasuryInfo.getBenBankCode())
                                        .maNt(request.getCcy())
                                        .tyGia(String.valueOf(AppConstants.NUMBER.ONE))
                                        .ttien(Double.valueOf(request.getAmount()))
                                        .ttienNt(Double.valueOf(request.getAmount()))
                                        .maHthucNop(AppConstants.TCC_VALIDATE_DOC.SUBMISSION_METHOD_CODE)
                                        .lhinhNntThu(PayerTypeEnum.BUSINESS.getValue().toString())
                                        .bdsNhap(govPaymentTransaction.getDebitBrcd())
                                        .branchNhap(govPaymentTransaction.getDebitBrcd())
                                        .maHthucThu(AppConstants.TCC_VALIDATE_DOC.CKCA)
                                        .channelBidv(treasuryInfo.getTccChannel())
                                        .channelSibs(treasuryInfo.getTccChannel())
                                        .stkNo(request.getDebitAccNo())
                                        .stkCo(null)
                                        .nopThay(StringUtils.isBlank(request.getAltTaxCode())
                                                ? AppConstants.TCC_VALIDATE_DOC.NO
                                                : AppConstants.TCC_VALIDATE_DOC.YES)
                                        .tenCqt(revAuthName)
                                        .feeLcn(String.valueOf(AppConstants.NUMBER.ZERO))
                                        .stbCode(AppConstants.TCC_VALIDATE_DOC.BANK_CODE)
                                        .dpcode(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .maNntien(request.getAltTaxCode())
                                        .tenNntien(request.getAltPayerName())
                                        .dcNntien(request.getAltPayerAddr())
                                        .phiDichvu(feeAmount)
                                        .vat(feeVAT)
                                        .ttienDichvu(feeTotal)
                                        .ttienThu(amount + feeTotal)
                                        .build())
                                .ctuDtl(TccValidateDocInfoDetail.builder()
                                        .rowDtl(IntStream.range(0, request.getTaxItems().size())
                                                .<TccValidateDocInfoDetailRow>mapToObj(i -> {
                                                    TxnTaxItemDto item = request.getTaxItems().get(i);
                                                    return TccValidateDocInfoDetailRow.builder()
                                                            .shkb(request.getTreasuryCode())
                                                            .maNdkt(item.getEcCode())
                                                            .maChuong(item.getChapterCode())
                                                            .soTienNt(item.getAmount().toString())
                                                            .soTien(item.getAmount().toString())
                                                            .soTienNop(item.getAmount().toString())
                                                            .dienGiaiDtl(item.getTransDesc())
                                                            .soTk(item.getDeclarationNo())
                                                            .ngayDk(DateUtils.convertDateToString(
                                                                    item.getDeclarationDate()))
                                                            .sttTk(i + 1)
                                                            .sacThue(item.getTaxTypeCode())
                                                            .maLt(item.getCcCode())
                                                            .maLh(item.getEiTypeCode())
                                                            .maHq("")
                                                            .maHqPh("")
                                                            .build();
                                                })
                                                .collect(Collectors.toList()))
                                        .build())
                                .build())
                        .build())
                .build();
    }

    public TccValidateDocReq toValidateDocReq(BatchImportItemDto batchItem) {
        return TccValidateDocReq.builder()
                .chungTuLst(TccValidateDocLst.builder()
                        .chungTu(TccValidateDocInfo.builder()
                                .sttCtu(String.valueOf(AppConstants.NUMBER.ONE))
                                .ctuHdr(TccValidateDocInfoHdr.builder()
                                        .srData(AppConstants.TCC_VALIDATE_DOC.BDR)
                                        .soBt(String.valueOf(AppConstants.NUMBER.ONE))
                                        .shkb(batchItem.getTreasuryCode())
                                        .tkNo(batchItem.getTreasuryInfo().getDebtAccCode())
                                        .tkCo(batchItem.getRevAccCode())
                                        .maNv(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .tellerId(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .maNvBds(batchItem.getAccountInfo().getBrnCode())
                                        .maNnthue(batchItem.getTaxCode())
                                        .tenNnthue(batchItem.getPayerName())
                                        .dcNnthue(batchItem.getPayerAddr())
                                        .maLthue1(TxnTaxTypeEnum.CUSTOMS_TAX.getCode())
                                        .maCqthu(batchItem.getRevAuthCode())
                                        .maDvsdns(batchItem.getRevAuthCode())
                                        .maDbhc(batchItem.getAdmAreaCode())
                                        .maNhA(AppConstants.TCC_VALIDATE_DOC.BANK_CODE)
                                        .maNhB(batchItem.getTreasuryInfo().getBenBankCode())
                                        .maNt(batchItem.getCcy())
                                        .tyGia(String.valueOf(AppConstants.NUMBER.ONE))
                                        .ttien(Double.valueOf(batchItem.getAmount()))
                                        .ttienNt(Double.valueOf(batchItem.getAmount()))
                                        .maHthucNop(AppConstants.TCC_VALIDATE_DOC.SUBMISSION_METHOD_CODE)
                                        .lhinhNntThu(batchItem.getPayerType())
                                        .bdsNhap(batchItem.getAccountInfo().getBrnCode())
                                        .branchNhap(batchItem.getAccountInfo().getBrnCode())
                                        .maHthucThu(AppConstants.TCC_VALIDATE_DOC.CKCA)
                                        .channelBidv(batchItem.getTreasuryInfo().getTccChannel())
                                        .channelSibs(batchItem.getTreasuryInfo().getTccChannel())
                                        .stkNo(batchItem.getDebitAccNo())
                                        .stkCo(null)
                                        .nopThay(StringUtils.isBlank(batchItem.getAltTaxCode())
                                                ? AppConstants.TCC_VALIDATE_DOC.NO
                                                : AppConstants.TCC_VALIDATE_DOC.YES)
                                        .tenCqt(batchItem.getRevAuthName())
                                        .feeLcn(String.valueOf(AppConstants.NUMBER.ZERO))
                                        .stbCode(AppConstants.TCC_VALIDATE_DOC.BANK_CODE)
                                        .dpcode(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .maNntien(batchItem.getAltTaxCode())
                                        .tenNntien(batchItem.getAltPayerName())
                                        .dcNntien(batchItem.getAltPayerAddr())
                                        .phiDichvu(Double.valueOf(0))
                                        .vat(Double.valueOf(0))
                                        .ttienDichvu(Double.valueOf(0))
                                        .ttienThu(Double.valueOf(batchItem.getAmount()))
                                        .build())
                                .ctuDtl(TccValidateDocInfoDetail.builder()
                                        .rowDtl(List.of(TccValidateDocInfoDetailRow.builder()
                                                .shkb(batchItem.getTreasuryCode())
                                                .maNdkt(batchItem.getEcCode())
                                                .maChuong(batchItem.getChapterCode())
                                                .soTienNt(batchItem.getAmount().toString())
                                                .soTien(batchItem.getAmount().toString())
                                                .soTienNop(batchItem.getAmount().toString())
                                                .dienGiaiDtl(batchItem.getTransDesc())
                                                .soTk(batchItem.getDeclarationNo())
                                                .ngayDk(batchItem.getDeclarationDate())
                                                .sttTk(AppConstants.NUMBER.ONE)
                                                .sacThue(batchItem.getTaxTypeCode())
                                                .maLt(batchItem.getCcCode())
                                                .maLh(batchItem.getEiTypeCode())
                                                .maHq("")
                                                .maHqPh("")
                                                .build())).build())
                                .build())
                        .build())
                .build();
    }

    public TccCreateDocReq toCreateDocReq(GOVPaymentTransactionEntity transaction, MappingTreasuryBenBankCodeDto treasuryInfo, String revAuthName) {
        Double amount = NumberUtils.toDouble(transaction.getAmount(), 0.0);
        Double feeAmount = ChargeFeeOpt.INST.code().equals(transaction.getFeeOpt()) ? NumberUtils.toDouble(transaction.getFeeAmount(), 0.0) : 0;
        Double feeVAT = ChargeFeeOpt.INST.code().equals(transaction.getFeeOpt()) ? NumberUtils.toDouble(transaction.getFeeVAT(), 0.0) : 0;
        Double feeTotal = ChargeFeeOpt.INST.code().equals(transaction.getFeeOpt()) ? NumberUtils.toDouble(transaction.getFeeTotal(), 0.0) : 0;

        return TccCreateDocReq.builder()
                .chungTuLst(TccValidateDocLst.builder()
                        .chungTu(TccValidateDocInfo.builder()
                                .sttCtu(String.valueOf(AppConstants.NUMBER.ONE))
                                .ctuHdr(TccValidateDocInfoHdr.builder()
                                        .srData(AppConstants.TCC_VALIDATE_DOC.BDR)
                                        .soBt(String.valueOf(AppConstants.NUMBER.ONE))
                                        .shkb(transaction.getShkb())
                                        .tkNo(treasuryInfo.getDebtAccCode())
                                        .tkCo(transaction.getMaTk())
                                        .maNv(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .tellerId(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .maNvBds(transaction.getDebitBrcd())
                                        .maNnthue(transaction.getTaxCode())
                                        .tenNnthue(transaction.getPayerName())
                                        .dcNnthue(transaction.getPayerAddr())
                                        .maLthue1(transaction.getTxnType())
                                        .maCqthu(transaction.getMaCqthu())
                                        .maDvsdns(transaction.getMaCqthu())
                                        .maDbhc(transaction.getMaDbhc())
                                        .maNhA(AppConstants.TCC_VALIDATE_DOC.BANK_CODE)
                                        .maNhB(treasuryInfo.getBenBankCode())
                                        .maNt(transaction.getDebitCcy())
                                        .tyGia(String.valueOf(AppConstants.NUMBER.ONE))
                                        .ttien(transaction.getAmount().doubleValue())
                                        .ttienNt(transaction.getAmount().doubleValue())
                                        .maHthucNop(AppConstants.TCC_VALIDATE_DOC.SUBMISSION_METHOD_CODE)
                                        .lhinhNntThu(PayerTypeEnum.BUSINESS.getValue().toString())
                                        .bdsNhap(transaction.getDebitBrcd())
                                        .branchNhap(transaction.getDebitBrcd())
                                        .maHthucThu(AppConstants.TCC_VALIDATE_DOC.CKCA)
                                        .channelBidv(treasuryInfo.getTccChannel())
                                        .channelSibs(treasuryInfo.getTccChannel())
                                        .stkNo(transaction.getDebitAccNo())
                                        .stkCo(null)
                                        .nopThay(StringUtils.isBlank(transaction.getAltTaxCode())
                                                ? AppConstants.TCC_VALIDATE_DOC.NO
                                                : AppConstants.TCC_VALIDATE_DOC.YES)
                                        .tenCqt(revAuthName)
                                        .feeLcn(String.valueOf(AppConstants.NUMBER.ZERO))
                                        .stbCode(AppConstants.TCC_VALIDATE_DOC.BANK_CODE)
                                        .dpcode(AppConstants.TCC_VALIDATE_DOC._990BDR)
                                        .maNntien(transaction.getAltTaxCode())
                                        .tenNntien(transaction.getAltPayerName())
                                        .dcNntien(transaction.getAltPayerAddr())
                                        .phiDichvu(feeAmount)
                                        .vat(feeVAT)
                                        .ttienDichvu(feeTotal)
                                        .ttienThu(amount + feeTotal)
                                        .build())
                                .ctuDtl(TccValidateDocInfoDetail.builder()
                                        .rowDtl(IntStream.range(0, transaction.getGovPaymentItemList().size())
                                                .<TccValidateDocInfoDetailRow>mapToObj(i -> {
                                                    GOVPaymentItemEntity item = transaction
                                                            .getGovPaymentItemList().stream()
                                                            .toList().get(i);
                                                    return TccValidateDocInfoDetailRow.builder()
                                                            .shkb(transaction.getShkb())
                                                            .maNdkt(item.getMaNdkt())
                                                            .maChuong(item.getMaChuong())
                                                            .soTienNt(item.getAmount().toString())
                                                            .soTien(item.getAmount().toString())
                                                            .soTienNop(item.getAmount().toString())
                                                            .dienGiaiDtl(item.getTransDesc())
                                                            .soTk(item.getDeclarationNo())
                                                            .ngayDk(DateUtils.convertDateToString(
                                                                    item.getDeclarationDate()))
                                                            .sttTk(i + 1)
                                                            .sacThue(item.getMaSthue())
                                                            .maLt(item.getMaLthq())
                                                            .maLh(item.getMaLh())
                                                            .maHq("")
                                                            .maHqPh("")
                                                            .build();
                                                })
                                                .toList())
                                        .build())
                                .build())
                        .build())
                .build();
    }

    public TccAccountingReq toAccountingReq(String tccDocId) {
        return TccAccountingReq.builder()
                .maNv(AppConstants.TCC_VALIDATE_DOC._990BDR)
                .bds(AppConstants.TCC_ACCOUNTING.BRCD)
                .inqCode(AppConstants.TCC_ACCOUNTING.INQ_CODE_ACCOUNTING)
                .inqName(AppConstants.TCC_ACCOUNTING.INQ_NAME_ACCOUNTING)
                .dataType(AppConstants.TCC_ACCOUNTING.DATA_TYPE)
                .dataId(tccDocId)
                .tellerId(AppConstants.TCC_VALIDATE_DOC._990BDR)
                .build();
    }

    public TccResendDocReq toResendDocReq(String tccDocId) {
        return TccResendDocReq.builder()
                .maNv(AppConstants.TCC_ACCOUNTING._990SMB)
                .inqCode(AppConstants.TCC_ACCOUNTING.INQ_CODE_RESEND)
                .inqName(AppConstants.TCC_ACCOUNTING.INQ_NAME_RESEND)
                .dataType(AppConstants.TCC_ACCOUNTING.DATA_TYPE)
                .dataId(tccDocId)
                .resendKba(AppConstants.TCC_VALIDATE_DOC.NO)
                .resendThue(AppConstants.TCC_VALIDATE_DOC.NO)
                .resendHqa(AppConstants.TCC_VALIDATE_DOC.YES)
                .build();
    }

    public TccQueryTxnInfoReq toQueryTxnInfoReq(String tccDocId) {
        return TccQueryTxnInfoReq.builder()
                .dataType(AppConstants.TCC_ACCOUNTING.DATA_TYPE)
                .dataId(tccDocId)
                .build();
    }
}
