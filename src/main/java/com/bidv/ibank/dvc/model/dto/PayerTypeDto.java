package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;
import io.swagger.v3.oas.annotations.media.Schema;

public interface PayerTypeDto {

    @Schema(example = "1", description = "<PERSON><PERSON><PERSON> hình người nộp thuế: 0 - <PERSON><PERSON><PERSON><PERSON> x<PERSON>c đ<PERSON>nh, 1 - do<PERSON>h nghi<PERSON>, 2 - c<PERSON> nhân")
    Integer getPayerType();

    default String getPayerTypeName() {
        Integer payerType = getPayerType();
        return payerType != null ? Translator.toLocale(AppConstants.LANGUAGE.TAX_PAYER_TYPE + "." + payerType, payerType.toString())
                : null;
    }
}
