package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TccDmLoaitienhqaCodeNameEntityDto implements Exportable, Comparable<TccDmLoaitienhqaCodeNameEntityDto> {
    private String maLoaitienhqa;
    private String tenLoaitienhqa;

    @ExportConfig(colIndex = 1)
    public String getMaLoaitienhqa() {
        return maLoaitienhqa;
    }

    @ExportConfig(colIndex = 2)
    public String getMaTenLoaitienhqa() {
        return StrUtils.combineCodeName(maLoaitienhqa, tenLoaitienhqa);
    }

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }

    @Override
    public int compareTo(TccDmLoaitienhqaCodeNameEntityDto other) {
        if (other == null || other.getMaLoaitienhqa() == null) {
            return 1;
        }
        if (this.getMaLoaitienhqa() == null) {
            return -1;
        }

        String thisValue = this.getMaLoaitienhqa().trim();
        String otherValue = other.getMaLoaitienhqa().trim();

        boolean isThisNumeric = NumberUtils.isParsable(thisValue);
        boolean isOtherNumeric = NumberUtils.isParsable(otherValue);

        if (isThisNumeric && isOtherNumeric) {
            return Double.compare(
                    NumberUtils.toDouble(thisValue),
                    NumberUtils.toDouble(otherValue));
        } else if (isThisNumeric) {
            return -1;
        } else if (isOtherNumeric) {
            return 1;
        }

        return StringUtils.compareIgnoreCase(thisValue, otherValue);
    }
}
