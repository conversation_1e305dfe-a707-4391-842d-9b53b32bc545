package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.dvc.model.dto.BaseTotalDto;
import com.bidv.ibank.dvc.model.dto.TxnParamCodeDto;
import com.bidv.ibank.dvc.model.dto.TxnParamNameDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TxnProcessResultRes extends BaseTotalDto implements TxnParamCodeDto, TxnParamNameDto {

    private String treasuryCode;
    private String admAreaCode;
    private String revAccCode;
    private String revAuthCode;
    private String treasuryName;
    private String admAreaName;
    private String revAccName;
    private String revAuthName;

    @Schema(example = "GOV0125052700000086", description = "Danh sách giao dịch đẩy duyệt thất bại")
    private List<TransactionResDetail> failTxns;

    @Schema(example = "GOV0125052700000086", description = "Mã giao dịch")
    private String txnId;

    @Schema(example = "100000000", description = "Tổng số tiền")
    private BigDecimal totalAmount;

    @Schema(example = "VND", description = "Loại tiền tệ")
    private String ccy;

    @Schema(example = "10000", description = "Phí dịch vụ (bao gồm VAT)")
    private BigDecimal feeTotal;

    @Schema(example = "VND", description = "Loại tiền tệ của phí")
    private String feeCcy;

    @Schema(example = "Phí khoán", description = "Hình thức thu phí")
    private String feeOpt;

    @JsonFormat(pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS)
    @Schema(example = "2025-06-24T16:58:44.006902", description = "Ngày tạo")
    private LocalDateTime createdDate;

    @Schema(example = "1234567890", description = "Số tài khoản trích nợ")
    private String debitAccNo;

    @Schema(example = "1234567890", description = "Tên tài khoản trích nợ")
    private String debitAccName;

    @Schema(example = "10000", description = "Tổng số giao dịch đẩy duyệt")
    private Long total;

    @Schema(example = "10000", description = "Tổng số giao dịch đẩy duyệt thành công")
    private Long totalSuccess;

    @Override
    @Schema(example = "10000", description = "Tổng số giao dịch đẩy duyệt thất bại")
    public Long getTotalFail() {
        return this.total - this.totalSuccess;
    }

    @Schema(example = "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi", description = "Số tiền bằng chữ")
    public String getTotalAmountText() {
        if (this.getTotalAmount() == null)
            return null;
        return ReadNumber.formatAmountToText(Translator.getLocale().toString().toLowerCase(), this.getTotalAmount().toString(), this.getCcy());
    }

    @Override
    public String getTreasuryName() {
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    @Override
    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }

    @Override
    public String getRevAccName() {
        return Translator.toLocale(revAccCode, revAccName);
    }

    @Override
    public String getRevAuthName() {
        return Translator.toLocale(revAuthCode, revAuthName);
    }
}
