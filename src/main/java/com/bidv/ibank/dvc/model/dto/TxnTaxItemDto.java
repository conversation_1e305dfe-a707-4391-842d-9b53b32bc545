package com.bidv.ibank.dvc.model.dto;

import java.time.LocalDate;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TxnTaxItemDto implements TxnItemParamCodeDto, AmountDto {

    @NotBlank
    private String eiTypeCode;
    @NotBlank
    private String taxTypeCode;
    @NotBlank
    private String ccCode;
    @NotBlank
    private String chapterCode;
    @NotBlank
    private String ecCode;
    @NotBlank
    private String amount;
    @NotBlank
    private String ccy;

    @NotNull
    @PastOrPresent
    @Schema(example = "2021-01-01", description = "Ngày tờ khai")
    private LocalDate declarationDate;

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "1234567890", description = "Số tờ khai")
    private String declarationNo;

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TRANS_DESC_LENGTH)
    @Schema(example = "Diễn giải giao dịch", description = "Diễn giải giao dịch")
    private String transDesc;
}
