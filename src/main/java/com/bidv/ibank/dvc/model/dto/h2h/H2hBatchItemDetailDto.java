package com.bidv.ibank.dvc.model.dto.h2h;

import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;
import com.bidv.ibank.dvc.model.dto.TxnDetailStatusDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonPropertyOrder(alphabetic = true)
public class H2hBatchItemDetailDto extends BatchItemDetailDto implements TxnDetailStatusDto {

    private String status;

    @JsonIgnore
    private String state;
}
