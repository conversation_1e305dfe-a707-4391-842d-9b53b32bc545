package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TxnTaxExtraItemDto extends TxnTaxItemDto implements TxnItemParamNameDto {

    private String ccName;
    private String chapterName;
    private String ecName;
    private String eiTypeName;
    private String taxTypeName;

    @Override
    public String getCcName() {
        return Translator.toLocale(super.getCcCode(), ccName);
    }

    @Override
    public String getChapterName() {
        return Translator.toLocale(super.getChapterCode(), chapterName);
    }

    @Override
    public String getEcName() {
        return Translator.toLocale(super.getEcCode(), ecName);
    }

    @Override
    public String getEiTypeName() {
        return Translator.toLocale(super.getEiTypeCode(), eiTypeName);
    }

    @Override
    public String getTaxTypeName() {
        return Translator.toLocale(super.getTaxTypeCode(), taxTypeName);
    }
}
