package com.bidv.ibank.dvc.model.mapper.customsduty;

import com.bidv.ibank.client.common.dto.fee.FeeResult;
import com.bidv.ibank.client.common.dto.masterdata.CustomerDto;
import com.bidv.ibank.common.txn.util.constant.FeeMethodEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionCodeEnum;
import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.bidv.ibank.dvc.model.dto.BatchTaxItemExportDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxResultExportDto;
import com.bidv.ibank.dvc.model.dto.h2h.H2hBatchItemDetailDto;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.bidv.ibank.dvc.model.dto.BatchImportItemDto;
import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;
import com.bidv.ibank.dvc.model.dto.BatchItemExportDto;
import com.bidv.ibank.dvc.model.dto.BatchTaxImportItemDto;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentBatchItemEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentTransactionEntity;
import com.bidv.ibank.dvc.model.entity.customsduty.GOVPaymentItemEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.model.response.BatchCalcFeeRes;
import com.bidv.ibank.dvc.model.response.BatchDetailRes;
import com.bidv.ibank.dvc.model.response.BatchListRes;
import com.bidv.ibank.dvc.model.response.InquiryCustomsDutyRes;
import com.bidv.ibank.dvc.model.response.h2h.H2hBatchDetailRes;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.CcyUtils;
import com.bidv.ibank.dvc.util.DateUtils;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.dvc.util.constant.CoreAccTypeEnum;
import com.bidv.ibank.dvc.util.constant.PayerTypeEnum;
import com.bidv.ibank.dvc.util.constant.TxnTaxTypeEnum;
import com.bidv.ibank.external.microservice.user.AuthenticationUtils;
import com.bidv.ibank.external.microservice.user.CurrentUser;
import com.bidv.ibank.framework.remote.config.database.entity.ProductMapping;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.framework.util.mapper.EntityDtoMapper;
import com.bidv.ibank.framework.util.mapper.ModelMapperUtils;
import com.bidv.ibank.integrate.entity.account.CoreAccount;

import java.util.Comparator;

@Component
public class GOVPaymentBatchMapper implements EntityDtoMapper<Object, Object> {

    @Override
    public Object toDto(Object e) {
        throw new UnsupportedOperationException("Unimplemented method 'toDto'");
    }

    @Override
    public Object toEntity(Object dto) {
        throw new UnsupportedOperationException("Unimplemented method 'toEntity'");
    }

    public GOVPaymentBatchEntity toEntity(MultipartFile file, String checksum, String fileKey, BatchTypeEnum batchType) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String timestamp = LocalDateTime.now().format(formatter);
        String batchNo = "BDR" + timestamp;
        CurrentUser currentUser = AuthenticationUtils.getCurrentUser();
        return GOVPaymentBatchEntity.builder()
                .name(FilenameUtils.getBaseName(file.getOriginalFilename()))
                .batchNo(batchNo)
                .status(BatchStatusEnum.PROCESSING)
                .checksum(checksum)
                .cusId(currentUser.getUser().getCusId())
                .cifNo(currentUser.getUser().getCif())
                .fileKey(fileKey)
                .batchType(batchType)
                .fileSize(file.getSize())
                .batchType(batchType)
                .channel(currentUser.getChannel())
                .build();
    }

    public GOVPaymentBatchItemEntity toItemEntity(BatchImportItemDto batchItem, String batchId) {
        return GOVPaymentBatchItemEntity.builder()
                .batchId(batchId)
                .status(batchItem.isValidRow() ? BatchItemStatusEnum.VALID : BatchItemStatusEnum.INVALID)
                .debitAccNo(StringUtils.substring(batchItem.getDebitAccNo(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_DEBIT_ACC_NO_LENGTH))
                .taxCode(StringUtils.substring(batchItem.getTaxCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH))
                .altTaxCode(StringUtils.substring(batchItem.getAltTaxCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH))
                .payerName(StringUtils.substring(batchItem.getPayerName(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_NAME_LENGTH))
                .altPayerName(StringUtils.substring(batchItem.getAltPayerName(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_NAME_LENGTH))
                .payerAddr(StringUtils.substring(batchItem.getPayerAddr(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_ADDR_LENGTH))
                .altPayerAddr(StringUtils.substring(batchItem.getAltPayerAddr(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_ADDR_LENGTH))
                .declarationNo(StringUtils.substring(batchItem.getDeclarationNo(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH))
                .declarationDate(StringUtils.substring(batchItem.getDeclarationDate(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_DATE_LENGTH))
                .shkb(StringUtils.substring(batchItem.getTreasuryCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_TREASURY_CODE_LENGTH))
                .maTk(StringUtils.substring(batchItem.getRevAccCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_REV_ACC_CODE_LENGTH))
                .maCqthu(StringUtils.substring(batchItem.getRevAuthCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_REV_AUTH_CODE_LENGTH))
                .maDbhc(StringUtils.substring(batchItem.getAdmAreaCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_ADM_AREA_CODE_LENGTH))
                .maChuong(StringUtils.substring(batchItem.getChapterCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_CHAPTER_CODE_LENGTH))
                .maNdkt(StringUtils.substring(batchItem.getEcCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_EC_CODE_LENGTH))
                .maSthue(StringUtils.substring(batchItem.getTaxTypeCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_TYPE_CODE_LENGTH))
                .maLthq(StringUtils.substring(batchItem.getCcCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_CC_CODE_LENGTH))
                .maLh(StringUtils.substring(batchItem.getEiTypeCode(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_EI_TYPE_CODE_LENGTH))
                .amount(StringUtils.substring(batchItem.getAmount(), 0, CcyUtils.getMaxLengthAmountByCcy(batchItem.getCcy())))
                .ccy(StringUtils.substring(batchItem.getCcy(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_CCY_LENGTH))
                .transDesc(StringUtils.substring(batchItem.getTransDesc(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_TRANS_DESC_LENGTH))
                .payerType(StringUtils.substring(batchItem.getPayerType(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_PAYER_TYPE_LENGTH))
                .processFlow(batchItem.getProcessFlow())
                .batchOrder(batchItem.getRownum())
                .errCode(batchItem.getFieldErrors().values().stream()
                        .flatMap(Collection::stream)
                        .filter(e -> e != null)
                        .collect(Collectors.joining(",")))
                .tccErrCode(batchItem.getTccErrCode())
                .tccErrMsg(batchItem.getTccErrMsg())
                .txnCode(batchItem.getTreasuryInfo() != null ? batchItem.getTreasuryInfo().getTxnCode() : null)
                .debitBrcd(batchItem.getAccountInfo() != null ? batchItem.getAccountInfo().getBrnCode() : null)
                .maNh(batchItem.getTreasuryInfo() != null ? batchItem.getTreasuryInfo().getBenBankCode() : null)
                .prodCode(Optional.ofNullable(batchItem.getProductMapping()).map(ProductMapping::getProductCode).orElse(null))
                .subProdCode(Optional.ofNullable(batchItem.getProductMapping()).map(ProductMapping::getSubProductCode).orElse(null))
                .debitAccName(Optional.ofNullable(batchItem.getAccountInfo()).map(CoreAccount::getAccountName).orElse(null))
                .orgId(StringUtils.substring(batchItem.getOrgId(), 0, AppConstants.BATCH_FIELD_LENGTH.MAX_ORG_ID_LENGTH))
                .build();
    }

    public BatchListRes toListDto(GOVPaymentBatchEntity entity) {
        return BatchListRes.builder()
                .batchId(entity.getId())
                .batchName(entity.getName())
                .batchNo(entity.getBatchNo())
                .status(entity.getStatus().name())
                .createdDate(entity.getCreatedDate())
                .processedDate(entity.getProcessedDate())
                .channel(entity.getChannel())
                .build();
    }

    public List<BatchTaxResultExportDto> toTaxResultExportDto(List<GOVPaymentBatchItemEntity> batchItems) {
        return batchItems.stream().map(
                item -> BatchTaxResultExportDto.builder()
                        .status(item.getStatus().name())
                        .errCodes(item.getErrCode())
                        .tccErrMsg(item.getTccErrMsg())
                        .declarationNo(item.getDeclarationNo())
                        .declarationYear(item.getDeclarationYear())
                        .build()).toList();
    }

    public List<BatchTaxItemExportDto> toTaxItemExportDto(List<GOVPaymentBatchItemEntity> batchItems) {
        AtomicInteger index = new AtomicInteger(1);

        return batchItems.stream()
                .map(e -> {
                    BatchItemDetailDto item = toBatchItemDetailDto(e);
                    BatchTaxItemExportDto dto = ModelMapperUtils.map(item, BatchTaxItemExportDto.class);
                    dto.setBatchOrder(((Integer) index.getAndIncrement()).toString());
                    return dto;
                }).toList();
    }

    public List<BatchItemExportDto> toExportDto(Set<GOVPaymentBatchItemEntity> items) {
        return items.stream().map(e -> {
            BatchItemDetailDto item = toBatchItemDetailDto(e);
            BatchItemExportDto exportItem = ModelMapperUtils.map(item, BatchItemExportDto.class);
            exportItem.setBatchOrder(e.getBatchOrder().toString());
            exportItem.setStatus(Translator.toLocale(AppConstants.LANGUAGE.BATCH_ITEM_STATUS + "." + e.getStatus().name()));
            return exportItem;
        }).sorted(Comparator.comparing(BatchItemExportDto::getBatchOrder)).toList();
    }

    public BatchDetailRes toDetailDto(GOVPaymentBatchEntity entity) {
        return BatchDetailRes.builder()
                .validItems(getItems(entity, BatchItemStatusEnum.VALID))
                .invalidItems(getItems(entity, BatchItemStatusEnum.INVALID))
                .fileSize(entity.getFileSize())
                .fileName(entity.getName())
                .batchNo(entity.getBatchNo())
                .batchType(entity.getBatchType().name())
                .build();
    }

    public H2hBatchDetailRes toH2hDetailDto(GOVPaymentBatchEntity batchEntity, List<GOVPaymentTransactionEntity> txnEntities) {
        return H2hBatchDetailRes.builder()
                .validItems(getH2hItems(batchEntity, BatchItemStatusEnum.VALID, txnEntities))
                .invalidItems(getItems(batchEntity, BatchItemStatusEnum.INVALID))
                .fileSize(batchEntity.getFileSize())
                .fileName(batchEntity.getName())
                .batchNo(batchEntity.getBatchNo())
                .batchType(batchEntity.getBatchType().name())
                .build();
    }

    public GOVPaymentBatchItemEntity toBatchTaxItemEntity(BatchTaxImportItemDto batchItem, String batchId,
            CustomerDto customerDto,
            InquiryCustomsDutyRes inquiryRes) {

        GOVPaymentBatchItemEntity entity = GOVPaymentBatchItemEntity.builder()
                .batchId(batchId)
                .status(batchItem.isValidRow() ? BatchItemStatusEnum.VALID : BatchItemStatusEnum.INVALID)
                .declarationNo(StringUtils.substring(batchItem.getDeclarationNo(), 0, AppConstants.TAX_INQUIRY_BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH))
                .declarationYear(StringUtils.substring(batchItem.getDeclarationYear(), 0,
                        AppConstants.TAX_INQUIRY_BATCH_FIELD_LENGTH.MAX_DECLARATION_YEAR_LENGTH))
                .taxCode(customerDto.getTaxCode())
                .errCode(batchItem.getFieldErrors().values().stream()
                        .flatMap(Collection::stream)
                        .filter(e -> e != null)
                        .collect(Collectors.joining(",")))
                .tccErrCode(batchItem.getTccErrCode())
                .tccErrMsg(batchItem.getTccErrMsg())
                .batchOrder(batchItem.getRownum())
                .build();

        if (inquiryRes != null) {
            entity.setPayerName(customerDto.getName());
            entity.setPayerAddr(customerDto.getAddress());
            entity.setDeclarationDate(DateUtils.convertDateToString(inquiryRes.getDeclarationDate()));
            entity.setMaNdkt(inquiryRes.getEcCode());
            entity.setShkb(inquiryRes.getTreasuryCode());
            entity.setMaTk(inquiryRes.getRevAccCode());
            entity.setMaCqthu(inquiryRes.getRevAuthCode());
            entity.setMaDbhc(inquiryRes.getAdmAreaCode());
            entity.setTransDesc(inquiryRes.getTransDesc());
            entity.setMaChuong(inquiryRes.getChapterCode());
            entity.setMaLh(inquiryRes.getEiTypeCode());
            entity.setMaLthq(inquiryRes.getCcCode());
            entity.setMaSthue(inquiryRes.getTaxTypeCode());
            entity.setCcy(inquiryRes.getCcy());
            entity.setAmount(inquiryRes.getAmount());
            entity.setPayerType(inquiryRes.getPayerType().toString());
        }

        return entity;
    }

    public List<GOVPaymentTransactionEntity> toTransactionEntities(List<GOVPaymentBatchItemEntity> items) {
        String batchNo = items.get(0).getGovPaymentBatchEntity().getBatchNo();
        String batchId = items.get(0).getGovPaymentBatchEntity().getId();

        return items.stream().map(item -> {
            GOVPaymentItemEntity govPaymentItemEntity = GOVPaymentItemEntity.builder()
                    .declarationNo(item.getDeclarationNo())
                    .declarationDate(DateUtils.convertStringToDate(item.getDeclarationDate(), AppConstants.DATE_FORMAT_DD_MM_YYYY))
                    .maNdkt(item.getMaNdkt())
                    .maChuong(item.getMaChuong())
                    .maSthue(item.getMaSthue())
                    .maLthq(item.getMaLthq())
                    .maLh(item.getMaLh())
                    .amount(new BigDecimal(item.getAmount()))
                    .ccy(item.getCcy())
                    .transDesc(item.getTransDesc())
                    .build();

            int payerTypeValue = Integer.parseInt(item.getPayerType());
            PayerTypeEnum payerType = Arrays.stream(PayerTypeEnum.values())
                    .filter(type -> type.getValue().equals(payerTypeValue))
                    .findFirst()
                    .orElse(PayerTypeEnum.UNDEFINED);

            GOVPaymentTransactionEntity txnEntity = ModelMapperUtils.map(item, GOVPaymentTransactionEntity.class);
            txnEntity.setStatus(TransactionStatusEnum.INIT.name());
            txnEntity.setTxnType(TxnTaxTypeEnum.CUSTOMS_TAX.getCode());
            txnEntity.setPayerType(payerType);
            txnEntity.setBatchNo(batchNo);
            txnEntity.setPaymentItems(Set.of(govPaymentItemEntity));
            txnEntity.setBrcd(item.getDebitBrcd());
            txnEntity.setTxnDesc(TransactionCodeEnum.valueOf(item.getTxnCode()).desc());
            txnEntity.setDebitAccType(CoreAccTypeEnum.DDA.name());
            txnEntity.setDebitCcy(item.getCcy());
            txnEntity.setEffDate(LocalDate.now());
            txnEntity.setCifNo(AuthenticationUtils.getCurrentUser().getUser().getCif());
            txnEntity.setBatchId(batchId);
            txnEntity.setBatchItemId(item.getId());
            return txnEntity;
        }).toList();
    }

    public List<GOVPaymentBatchItemEntity> toBatchItemEntitiesWithFee(List<GOVPaymentBatchItemEntity> batchItems, List<FeeResult> feeResultList) {
        Map<String, FeeResult> feeResultMap = feeResultList
                .stream()
                .collect(Collectors.toMap(FeeResult::getId, Function.identity()));

        return batchItems.stream().map(item -> {
            item.setFeeMethod(FeeMethodEnum.O);
            item.setFeeBrcd(item.getDebitBrcd());
            FeeResult fee = feeResultMap.get(item.getId());
            if (fee != null) {
                item.setFeeTotal(fee.getTotalFee());
                item.setFeeCode(fee.getFeeCode());
                item.setFeeAmount(fee.getFeeAmount());
                item.setFeeVAT(fee.getVatAmount());
                item.setVatRate(fee.getVat());
                item.setFeeOriginal(fee.getTxnFee());
            }
            return item;
        }).toList();
    }

    public BatchCalcFeeRes toBatchCalcFeeRes(List<GOVPaymentBatchItemEntity> batchItems) {
        GOVPaymentBatchEntity entity = batchItems.get(0).getGovPaymentBatchEntity();
        return BatchCalcFeeRes.builder()
                .items(batchItems.stream().map(this::toBatchItemDetailDto).toList())
                .fileSize(entity.getFileSize())
                .fileName(entity.getName())
                .batchNo(entity.getBatchNo())
                .batchType(entity.getBatchType().name())
                .totalAmount(batchItems.stream().map(GOVPaymentBatchItemEntity::getAmount).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add))
                .totalFee(batchItems.stream().map(GOVPaymentBatchItemEntity::getFeeTotal).reduce(BigDecimal.ZERO, BigDecimal::add))
                .ccy(batchItems.get(0).getCcy())
                .feeCcy(batchItems.get(0).getFeeCcy())
                .feeOpt(batchItems.get(0).getFeeOpt())
                .build();
    }

    public GOVPaymentBatchItemEntity toUpdatedEntity(GOVPaymentBatchItemEntity existingItem, GOVPaymentTransactionEntity govTransactionEntity) {
        Optional<GOVPaymentItemEntity> optionalItem = govTransactionEntity.getPaymentItems().stream().findFirst();

        return GOVPaymentBatchItemEntity.builder()
                .id(existingItem.getId())
                .batchId(existingItem.getBatchId())
                .debitAccNo(govTransactionEntity.getDebitAccNo())
                .status(BatchItemStatusEnum.VALID)
                .taxCode(govTransactionEntity.getTaxCode())
                .payerName(govTransactionEntity.getPayerName())
                .payerAddr(govTransactionEntity.getPayerAddr())
                .altTaxCode(govTransactionEntity.getAltTaxCode())
                .altPayerName(govTransactionEntity.getAltPayerName())
                .altPayerAddr(govTransactionEntity.getAltPayerAddr())
                .declarationNo(optionalItem.get().getDeclarationNo())
                .declarationDate(DateUtils.convertDateToString(optionalItem.get().getDeclarationDate(), AppConstants.DATE_FORMAT_DD_MM_YYYY))
                .shkb(govTransactionEntity.getShkb())
                .maTk(govTransactionEntity.getMaTk())
                .maCqthu(govTransactionEntity.getMaCqthu())
                .maDbhc(govTransactionEntity.getMaDbhc())
                .maChuong(optionalItem.get().getMaChuong())
                .maNdkt(optionalItem.get().getMaNdkt())
                .maSthue(optionalItem.get().getMaSthue())
                .maLthq(optionalItem.get().getMaLthq())
                .maLh(optionalItem.get().getMaLh())
                .amount(govTransactionEntity.getAmount().toString())
                .ccy(govTransactionEntity.getCcy())
                .transDesc(optionalItem.get().getTransDesc())
                .payerType(govTransactionEntity.getPayerType().getValue().toString())
                .batchOrder(existingItem.getBatchOrder())
                .processFlow(govTransactionEntity.getProcessFlow())
                .txnCode(govTransactionEntity.getTxnCode())
                .debitBrcd(govTransactionEntity.getDebitBrcd())
                .maNh(govTransactionEntity.getMaNh())
                .prodCode(govTransactionEntity.getProdCode())
                .subProdCode(govTransactionEntity.getSubProdCode())
                .debitAccName(govTransactionEntity.getDebitAccName())
                .orgId(govTransactionEntity.getOrgId())
                .errCode(null)
                .tccErrCode(null)
                .tccErrMsg(null)
                .build();
    }

    private BatchItemDetailDto toBatchItemDetailDto(GOVPaymentBatchItemEntity item) {
        LocalDate declarationDate = DateUtils.convertStringToDate(item.getDeclarationDate(), AppConstants.DATE_FORMAT_DD_MM_YYYY);
        return BatchItemDetailDto.builder()
                .batchItemId(item.getId())
                .debitAccNo(item.getDebitAccNo())
                .taxCode(item.getTaxCode())
                .altTaxCode(item.getAltTaxCode())
                .payerName(item.getPayerName())
                .altPayerName(item.getAltPayerName())
                .payerAddr(item.getPayerAddr())
                .altPayerAddr(item.getAltPayerAddr())
                .treasuryCode(item.getShkb())
                .treasuryName(Optional.ofNullable(item.getTccDmKhobacEntity()).map(TccDmKhobacEntity::getTen).orElse(null))
                .revAccCode(item.getMaTk())
                .revAccName(Optional.ofNullable(item.getTccDmTkNsnnEntity()).map(TccDmTkNsnnEntity::getTen).orElse(null))
                .revAuthCode(item.getMaCqthu())
                .revAuthName(Optional.ofNullable(item.getTccDmCqthuEntity()).map(TccDmCqthuEntity::getTen).orElse(null))
                .admAreaCode(item.getMaDbhc())
                .admAreaName(Optional.ofNullable(item.getTccDmDbhcEntity()).map(TccDmDbhcEntity::getTen).orElse(null))
                .chapterCode(item.getMaChuong())
                .chapterName(Optional.ofNullable(item.getTccDmChuongEntity()).map(TccDmChuongEntity::getTen).orElse(null))
                .ecCode(item.getMaNdkt())
                .ecName(Optional.ofNullable(item.getTccDmNdktEntity()).map(TccDmNdktEntity::getTen).orElse(null))
                .taxTypeCode(item.getMaSthue())
                .taxTypeName(Optional.ofNullable(item.getTccDmSthueHqaEntity()).map(TccDmSthueHqaEntity::getTenSthue).orElse(null))
                .ccCode(item.getMaLthq())
                .ccName(Optional.ofNullable(item.getTccDmLoaitienhqaEntity()).map(TccDmLoaitienhqaEntity::getTenLthq).orElse(null))
                .eiTypeCode(item.getMaLh())
                .eiTypeName(Optional.ofNullable(item.getTccDmLhxnkEntity()).map(TccDmLhxnkEntity::getTen).orElse(null))
                .amount(item.getAmount())
                .ccy(item.getCcy())
                .createdDate(item.getCreatedDate())
                .declarationNo(item.getDeclarationNo())
                .declarationDate(declarationDate != null ? declarationDate.toString() : null)
                .declarationYear(item.getDeclarationYear())
                .transDesc(item.getTransDesc())
                .payerType(item.getPayerType())
                .feeTotal(item.getFeeTotal())
                .feeCcy(item.getFeeCcy())
                .errCode(item.getErrCode())
                .tccErrCode(item.getTccErrCode())
                .tccErrMsg(item.getTccErrMsg())
                .benBankCode(item.getMaNh())
                .benBankName(Optional.ofNullable(item.getTccDmKbnnNhtmEntity()).map(TccDmKbnnNhtmEntity::getTenNh).orElse(null))
                .orgId(item.getOrgId())
                .build();
    }

    private List<BatchItemDetailDto> getItems(GOVPaymentBatchEntity entity, BatchItemStatusEnum status) {
        if (CollectionUtils.isEmpty(entity.getGovPaymentBatchItemList())) {
            return List.of();
        }
        return entity.getGovPaymentBatchItemList()
                .stream()
                .filter(e -> e.getStatus().equals(status))
                .sorted(Comparator.comparing(GOVPaymentBatchItemEntity::getBatchOrder))
                .map(this::toBatchItemDetailDto).toList();
    }

    private List<H2hBatchItemDetailDto> getH2hItems(GOVPaymentBatchEntity entity, BatchItemStatusEnum status, List<GOVPaymentTransactionEntity> txnEntities) {
        if (CollectionUtils.isEmpty(entity.getGovPaymentBatchItemList())) {
            return List.of();
        }
        Map<String, GOVPaymentTransactionEntity> txnMap = txnEntities.stream()
                .filter(txn -> txn.getBatchItemId() != null)
                .collect(Collectors.toMap(
                        GOVPaymentTransactionEntity::getBatchItemId,
                        Function.identity(),
                        (existing, replacement) -> existing));
        return entity.getGovPaymentBatchItemList()
                .stream()
                .filter(e -> e.getStatus().equals(status))
                .sorted(Comparator.comparing(GOVPaymentBatchItemEntity::getBatchOrder))
                .map(item -> {
                    GOVPaymentTransactionEntity matchingTxn = txnMap.get(item.getId());
                    return toH2hBatchItemDetailDto(item, matchingTxn);
                })
                .collect(Collectors.toList());

    }

    private H2hBatchItemDetailDto toH2hBatchItemDetailDto(GOVPaymentBatchItemEntity item, GOVPaymentTransactionEntity txnEntity) {
        BatchItemDetailDto batchItemDetailDto = toBatchItemDetailDto(item);
        H2hBatchItemDetailDto h2hBatchItemDetailDto = ModelMapperUtils.map(batchItemDetailDto, H2hBatchItemDetailDto.class);
        if (txnEntity != null) {
            h2hBatchItemDetailDto.setStatus(txnEntity.getStatus());
            h2hBatchItemDetailDto.setState(txnEntity.getState());
        }
        return h2hBatchItemDetailDto;
    }
}