package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class TxnDeleteDto {

    private String code;
    private String txnId;

    public String getMessage() {
        return Translator.toLocale(AppConstants.LANGUAGE.RESPONSE_CODE + "." + code, code);
    }
}
