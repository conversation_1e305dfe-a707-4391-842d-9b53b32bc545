package com.bidv.ibank.dvc.model.response.h2h;

import com.bidv.ibank.dvc.model.dto.contracts.TxnDetailDto;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@JsonPropertyOrder(alphabetic = true)
@EqualsAndHashCode(callSuper = true)
public class H2hTxnDetailRes extends TxnDetailDto {

}