package com.bidv.ibank.dvc.model.dto;

import java.util.List;
import java.util.regex.Pattern;

import com.bidv.ibank.dvc.util.constant.FileTypeEnum;
import com.bidv.ibank.util.excel.Importable;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class ValidateFileDto<T extends Importable> {
    private Pattern pattern;
    private long startRow;
    private long maxItem;
    private long maxFileSize;
    private List<FileTypeEnum> fileTypes;
    private Class<T> clazz;
}