package com.bidv.ibank.dvc.model.filter;

import java.time.LocalDate;

import com.bidv.ibank.dvc.annotation.ValidDateRange;
import com.bidv.ibank.dvc.util.AppConstants;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.PastOrPresent;

@ValidDateRange
public interface DateRangeFilter {

    @PastOrPresent
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD)
    @Schema(example = "2024-11-26", description = "Từ ngày")
    LocalDate getStartDate();

    @PastOrPresent
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD)
    @Schema(example = "2024-11-30", description = "<PERSON>ến ngày")
    LocalDate getEndDate();
}
