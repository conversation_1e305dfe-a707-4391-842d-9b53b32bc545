package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TxnReportListRes extends TxnPendingListRes {

    private String createdBy;
    private String approvalUsers;
    private String txnType;
    private String txnItemId;

    public String getTxnTypeName() {
        return Translator.toLocale(AppConstants.LANGUAGE.TXN_TYPE + "." + txnType, txnType);
    }
}
