package com.bidv.ibank.dvc.model.request;

import com.bidv.ibank.dvc.model.filter.Searchable;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.domain.request.PagingQueryRequest;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class TemplateListReq extends PagingQueryRequest implements Searchable {

    @Schema(example = "Mẫu đơn số 1", description = "Tên mẫu giao dịch || Tên người nộp thuế || Tên cơ quan thu || Số tiền")
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH)
    private String search;

    @Schema(example = "04", description = "Loại giao dịch: 01 là thuế nội địa. 03 là phí hạ tầng cảng biển. 04 là thuế hải quan")
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_TYPE_LENGTH)
    private String txnType;

}
