package com.bidv.ibank.dvc.model.entity.customsduty;

import com.bidv.ibank.dvc.util.constant.BatchStatusEnum;
import com.bidv.ibank.dvc.util.constant.BatchTypeEnum;
import com.bidv.ibank.framework.domain.entity.AuditEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "GOV_PAYMENT_BATCH")
public class GOVPaymentBatchEntity extends AuditEntity<String> {

    @Id
    @Column(name = "ID", nullable = false)
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    @NotNull
    @Size(max = 100)
    @Column(name = "NAME", length = 100)
    private String name;

    @NotNull
    @Size(max = 20)
    @Column(name = "BATCH_NO", length = 20)
    private String batchNo;

    @NotNull
    @Column(name = "CUS_ID")
    private Long cusId;

    @NotNull
    @Size(max = 30)
    @Column(name = "CIF_NO", length = 30)
    private String cifNo;

    @NotNull
    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    private BatchStatusEnum status;

    @NotNull
    @Size(max = 100)
    @Column(name = "CHECKSUM", length = 100)
    private String checksum;

    @NotNull
    @Size(max = 1000)
    @Column(name = "FILE_KEY", length = 1000)
    private String fileKey;

    @Column(name = "FILE_SIZE")
    private Long fileSize;

    @Size(max = 20)
    @Column(name = "ERR_CODE", length = 20)
    private String errCode;

    @Column(name = "PROCESSED_DATE")
    private LocalDateTime processedDate;

    @NotNull
    @Size(max = 30)
    @Column(name = "CHANNEL", length = 30)
    private String channel;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "govPaymentBatchEntity")
    @Builder.Default
    @OrderBy("createdDate")
    private Set<GOVPaymentBatchItemEntity> govPaymentBatchItemList = new LinkedHashSet<>();

    @NotNull
    @Column(name = "BATCH_TYPE")
    @Enumerated(EnumType.STRING)
    private BatchTypeEnum batchType;

}
