package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.model.validation.FieldValidator;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.validation.BatchTaxImportValidators;
import com.bidv.ibank.util.excel.ImportConfig;
import com.bidv.ibank.util.excel.Importable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.HashMap;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaxImportItemDto implements Importable, Comparable<BatchTaxImportItemDto>, Serializable {

    @ImportConfig(colIndex = 0)
    private String declarationNo;

    @ImportConfig(colIndex = 1)
    private String declarationYear;

    @Builder.Default
    private Map<String, Set<String>> fieldErrors = new HashMap<>();
    private String tccErrCode;
    private String tccErrMsg;

    private int rowNum;
    private int index;

    @Override
    public int getRownum() {
        return rowNum;
    }

    @Override
    public void setRownum(int rowNum) {
        this.rowNum = rowNum;
    }

    @Override
    public int getIndex() {
        return index;
    }

    @Override
    public void setIndex(int index) {
        this.index = index;
    }

    @Override
    public int compareTo(BatchTaxImportItemDto o) {
        return Integer.compare(this.getRownum(), o.getRownum());
    }

    public void validate() {
        fieldErrors.clear();
        tccErrCode = null;
        tccErrMsg = null;

        // Get validators from the dedicated validator class
        Map<String, FieldValidator> validators = BatchTaxImportValidators.getValidators();

        // Validate each field using validators
        validators.forEach((fieldName, validator) -> {
            try {
                String value = (String) this.getClass().getDeclaredField(fieldName).get(this);
                List<ResponseCode> errors = validator.validate(value);
                if (!errors.isEmpty()) {
                    fieldErrors.put(fieldName, errors.stream().map(ResponseCode::code).collect(Collectors.toSet()));
                }
            } catch (Exception e) {
                fieldErrors.put(fieldName, Set.of(ResponseCode.TIMEOUT_01.code()));
            }
        });
    }

    public boolean isValidRow() {
        return MapUtils.isEmpty(fieldErrors) && StringUtils.isBlank(tccErrCode) && StringUtils.isBlank(tccErrMsg);
    }
}
