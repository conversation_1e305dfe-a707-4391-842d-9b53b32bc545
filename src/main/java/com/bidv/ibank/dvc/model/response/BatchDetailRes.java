package com.bidv.ibank.dvc.model.response;

import java.util.ArrayList;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.BatchGeneralInfoDto;
import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchDetailRes implements BatchGeneralInfoDto {

    private Long fileSize;
    private String fileName;
    private String batchNo;
    private String batchType;

    @Builder.Default
    @Schema(description = "Danh sách item hợp lệ")
    private List<BatchItemDetailDto> validItems = new ArrayList<>();

    @Builder.Default
    @Schema(description = "Danh sách item không hợp lệ")
    private List<BatchItemDetailDto> invalidItems = new ArrayList<>();

    public Integer getTotalValidRecords() {
        if (validItems == null) {
            return 0;
        }
        return validItems.size();
    }

    public Integer getTotalInvalidRecords() {
        if (invalidItems == null) {
            return 0;
        }
        return invalidItems.size();
    }

    public Integer getTotalRecords() {
        if (validItems == null && invalidItems == null) {
            return 0;
        }
        return getTotalValidRecords() + getTotalInvalidRecords();
    }

}
