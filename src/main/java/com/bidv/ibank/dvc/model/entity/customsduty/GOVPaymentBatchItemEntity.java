package com.bidv.ibank.dvc.model.entity.customsduty;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;

import com.bidv.ibank.common.txn.util.constant.FeeMethodEnum;
import com.bidv.ibank.dvc.model.entity.param.TccDmChuongEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmCqthuEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmDbhcEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKbnnNhtmEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmKhobacEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLhxnkEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmLoaitienhqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmNdktEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmSthueHqaEntity;
import com.bidv.ibank.dvc.model.entity.param.TccDmTkNsnnEntity;
import com.bidv.ibank.dvc.util.constant.BatchItemStatusEnum;
import com.bidv.ibank.framework.domain.entity.AuditEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "GOV_PAYMENT_BATCH_ITEM")
public class GOVPaymentBatchItemEntity extends AuditEntity<String> {

    @Id
    @Column(name = "ID", nullable = false)
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "BATCH_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    private GOVPaymentBatchEntity govPaymentBatchEntity;

    @NotNull
    @Size(max = 36)
    @Column(name = "BATCH_ID", length = 36)
    private String batchId;

    @NotNull
    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    private BatchItemStatusEnum status;

    @Size(max = 14)
    @Column(name = "DEBIT_ACC_NO", length = 14)
    private String debitAccNo;

    @Size(max = 20)
    @Column(name = "TAX_CODE", length = 20)
    private String taxCode;

    @Size(max = 20)
    @Column(name = "ALT_TAX_CODE", length = 20)
    private String altTaxCode;

    @Size(max = 140)
    @Column(name = "PAYER_NAME", length = 140)
    private String payerName;

    @Size(max = 140)
    @Column(name = "ALT_PAYER_NAME", length = 140)
    private String altPayerName;

    @Size(max = 140)
    @Column(name = "PAYER_ADDR", length = 140)
    private String payerAddr;

    @Size(max = 140)
    @Column(name = "ALT_PAYER_ADDR", length = 140)
    private String altPayerAddr;

    @Size(max = 30)
    @Column(name = "DECLARATION_NO", length = 30)
    private String declarationNo;

    @Size(max = 10)
    @Column(name = "DECLARATION_DATE")
    private String declarationDate;

    @Size(max = 4)
    @Column(name = "SHKB", length = 4)
    private String shkb;

    @Size(max = 50)
    @Column(name = "MA_TK", length = 50)
    private String maTk;

    @Size(max = 7)
    @Column(name = "MA_CQTHU", length = 7)
    private String maCqthu;

    @Size(max = 5)
    @Column(name = "MA_DBHC", length = 5)
    private String maDbhc;

    @Size(max = 3)
    @Column(name = "MA_CHUONG", length = 3)
    private String maChuong;

    @Size(max = 4)
    @Column(name = "MA_NDKT", length = 4)
    private String maNdkt;

    @Size(max = 10)
    @Column(name = "MA_STHUE", length = 10)
    private String maSthue;

    @Size(max = 20)
    @Column(name = "MA_LTHQ", length = 20)
    private String maLthq;

    @Size(max = 20)
    @Column(name = "MA_LH", length = 20)
    private String maLh;

    @Size(max = 19)
    @Column(name = "AMOUNT", length = 19)
    private String amount;

    @Size(max = 3)
    @Column(name = "CCY", length = 3)
    private String ccy;

    @Size(max = 1000)
    @Column(name = "TRANS_DESC", length = 1000)
    private String transDesc;

    @Size(max = 1)
    @Column(name = "PAYER_TYPE", length = 1)
    private String payerType;

    @Size(max = 30)
    @Column(name = "PROCESS_FLOW", length = 30)
    private String processFlow;

    @NotNull
    @Column(name = "BATCH_ORDER")
    private Integer batchOrder;

    @Size(max = 255)
    @Column(name = "ERR_CODE", length = 255)
    private String errCode;

    @Size(max = 10)
    @Column(name = "TCC_ERR_CODE", length = 10)
    private String tccErrCode;

    @Size(max = 1000)
    @Column(name = "TCC_ERR_MSG", length = 1000)
    private String tccErrMsg;

    @Size(max = 10)
    @Column(name = "DECLARATION_YEAR", length = 10)
    private String declarationYear;

    @Column(name = "FEE_BRCD", length = 10)
    private String feeBrcd;

    @Column(name = "FEE_OPT", length = 30)
    private String feeOpt;

    @Enumerated(EnumType.STRING)
    @Column(name = "FEE_METHOD", length = 30)
    private FeeMethodEnum feeMethod;

    @Column(name = "FEE_TOTAL")
    private BigDecimal feeTotal;

    @Column(name = "FEE_VAT")
    private BigDecimal feeVAT;

    @Column(name = "FEE_AMOUNT")
    private BigDecimal feeAmount;

    @Column(name = "FEE_ACCNO")
    private String feeAccNo;

    @Column(name = "FEE_CCY", length = 3)
    private String feeCcy;

    @Column(name = "FEE_CODE")
    private String feeCode;

    @Column(name = "VAT_RATE")
    private BigDecimal vatRate;

    @Column(name = "FEE_ORIGINAL")
    private BigDecimal feeOriginal;

    @Column(name = "FEE_FREQ", length = 30)
    private String feeFreq;

    @Size(max = 10)
    @Column(name = "TXN_CODE", length = 10)
    private String txnCode;

    @Size(max = 10)
    @Column(name = "DEBIT_BRCD", length = 10)
    private String debitBrcd;

    @Size(max = 8)
    @Column(name = "MA_NH", length = 8)
    private String maNh;

    @Size(max = 30)
    @Column(name = "PROD_CODE", length = 30)
    private String prodCode;

    @Size(max = 30)
    @Column(name = "SUB_PROD_CODE", length = 30)
    private String subProdCode;

    @Size(max = 500)
    @Column(name = "DEBIT_ACCNAME", length = 500)
    private String debitAccName;

    @Size(max = 30)
    @Column(name = "ORG_ID", length = 30)
    private String orgId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_NDKT", referencedColumnName = "MA_NDKT", insertable = false, updatable = false)
    private TccDmNdktEntity tccDmNdktEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_CHUONG", referencedColumnName = "MA_CHUONG", insertable = false, updatable = false)
    private TccDmChuongEntity tccDmChuongEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_STHUE", referencedColumnName = "MA_STHUE", insertable = false, updatable = false)
    private TccDmSthueHqaEntity tccDmSthueHqaEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_LH", referencedColumnName = "MA_LH", insertable = false, updatable = false)
    private TccDmLhxnkEntity tccDmLhxnkEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_LTHQ", referencedColumnName = "MA_LTHQ", insertable = false, updatable = false)
    private TccDmLoaitienhqaEntity tccDmLoaitienhqaEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    private TccDmKhobacEntity tccDmKhobacEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_DBHC", referencedColumnName = "MA_DBHC", insertable = false, updatable = false)
    private TccDmDbhcEntity tccDmDbhcEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_CQTHU", referencedColumnName = "MA_CQTHU", insertable = false, updatable = false)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    private TccDmCqthuEntity tccDmCqthuEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "MA_TK", referencedColumnName = "MA_TK", insertable = false, updatable = false)
    private TccDmTkNsnnEntity tccDmTkNsnnEntity;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SHKB", referencedColumnName = "SHKB", insertable = false, updatable = false)
    @JoinColumn(name = "MA_NH", referencedColumnName = "MA_NH", insertable = false, updatable = false)
    private TccDmKbnnNhtmEntity tccDmKbnnNhtmEntity;

    @PrePersist
    public void prePersist() {
        super.prePersist();
        payerName = StringUtils.trimToEmpty(payerName).toUpperCase();
        altPayerName = StringUtils.trimToEmpty(altPayerName).toUpperCase();
        payerAddr = StringUtils.trimToEmpty(payerAddr).toUpperCase();
        altPayerAddr = StringUtils.trimToEmpty(altPayerAddr).toUpperCase();
    }

    @PreUpdate
    public void preUpdate() {
        super.preUpdate();
        payerName = StringUtils.trimToEmpty(payerName).toUpperCase();
        altPayerName = StringUtils.trimToEmpty(altPayerName).toUpperCase();
        payerAddr = StringUtils.trimToEmpty(payerAddr).toUpperCase();
        altPayerAddr = StringUtils.trimToEmpty(altPayerAddr).toUpperCase();
    }
}