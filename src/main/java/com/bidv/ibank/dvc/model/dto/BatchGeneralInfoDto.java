package com.bidv.ibank.dvc.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;

public interface BatchGeneralInfoDto {

    @Schema(example = "1000000", description = "<PERSON><PERSON><PERSON> thước file")
    Long getFileSize();

    @Schema(example = "GOV_PAYMENT_BATCH_001", description = "Tên file")
    String getFileName();

    @Schema(example = "BDR20250607215402732", description = "Số lô")
    String getBatchNo();

    @Schema(example = "PAYMENT", description = "Loại lô: PAYMENT: <PERSON>ộ<PERSON> thuế theo lô, INQUIRY: Vấn tin theo lô")
    String getBatchType();
}
