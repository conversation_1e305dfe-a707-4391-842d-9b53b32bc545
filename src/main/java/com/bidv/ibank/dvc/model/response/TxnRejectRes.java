package com.bidv.ibank.dvc.model.response;

import com.bidv.ibank.common.txn.model.dto.TransactionResDetail;
import com.bidv.ibank.dvc.model.dto.BaseTotalDto;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TxnRejectRes extends BaseTotalDto {

    @Schema(example = "", description = "Danh sách giao dịch từ chối thất bại")
    private List<TransactionResDetail> failTxns;

    @Schema(example = "GOV0125052700000086", description = "Mã giao dịch")
    private String txnId;

    @Schema(example = "*********", description = "Tổng số tiền")
    private BigDecimal totalAmount;

    @Schema(example = "VND", description = "Loại tiền tệ")
    private String ccy;

    @Schema(example = "Một tỷ hai trăm ba mươi tư triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi", description = "Số tiền bằng chữ")
    public String getTotalAmountText() {
        if (this.getTotalAmount() == null)
            return null;
        return ReadNumber.formatAmountToText(Translator.getLocale().toString().toLowerCase(), this.getTotalAmount().toString(), this.getCcy());
    }

}
