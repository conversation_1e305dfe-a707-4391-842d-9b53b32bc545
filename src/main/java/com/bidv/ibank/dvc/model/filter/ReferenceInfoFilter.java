package com.bidv.ibank.dvc.model.filter;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

public interface ReferenceInfoFilter {

    @Size(max = 20)
    @Schema(example = "**********", description = "<PERSON><PERSON><PERSON> khoản trích nợ")
    String getDebitAccNo();

    @Size(max = 13)
    @Schema(example = "9581856", description = "Mã số thuế")
    String getTaxCode();

    @Size(max = 30)
    @Schema(example = "**********", description = "Số tờ khai hải quan")
    String getDeclarationNo();

    @Size(max = 20)
    @Schema(example = "**********", description = "Số tham chiếu lô")
    String getBatchNo();
}
