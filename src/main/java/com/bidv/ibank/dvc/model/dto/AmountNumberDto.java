package com.bidv.ibank.dvc.model.dto;

import java.math.BigDecimal;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;

public interface AmountNumberDto {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_CCY_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.LETTER)
    @Schema(example = "VND", description = "Loại tiền tệ của số tiền nhập")
    String getCcy();

    @PositiveOrZero
    @Schema(example = "100000", description = "Số tiền")
    BigDecimal getAmount();

    void setCcy(String ccy);

    void setAmount(BigDecimal amount);
}
