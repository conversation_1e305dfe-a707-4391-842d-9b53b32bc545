package com.bidv.ibank.dvc.model.request;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryCustomsDutyReq {

    @NotBlank
    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TAX_CODE_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "**********", description = "Mã số thuế người nộp")
    private String taxCode;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_NO_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "***********", description = "Số tờ khai Hải quan")
    private String declarationNo;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_DECLARATION_YEAR_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER)
    @Schema(example = "2025", description = "Năm tờ khai")
    private String declarationYear;
}
