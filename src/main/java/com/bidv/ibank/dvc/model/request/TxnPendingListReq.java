package com.bidv.ibank.dvc.model.request;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.TxnSearchBaseDto;
import com.bidv.ibank.dvc.model.filter.StatusesFilter;
import com.bidv.ibank.framework.domain.request.PagingQueryRequest;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class TxnPendingListReq extends PagingQueryRequest implements TxnSearchBaseDto, StatusesFilter {

    private String search;
    private LocalDate startDate;
    private LocalDate endDate;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private List<String> ccys;
    private String debitAccNo;
    private String taxCode;
    private String declarationNo;
    private String batchNo;
    private List<String> statuses;
}
