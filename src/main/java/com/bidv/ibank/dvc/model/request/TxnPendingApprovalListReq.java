package com.bidv.ibank.dvc.model.request;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.TxnSearchBaseDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.domain.request.PagingQueryRequest;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class TxnPendingApprovalListReq extends PagingQueryRequest implements TxnSearchBaseDto {

    private String search;
    private LocalDate startDate;
    private LocalDate endDate;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private List<String> ccys;
    private String debitAccNo;
    private String taxCode;
    private String declarationNo;
    private String batchNo;

    @Schema(example = "04", description = "Loại giao dịch: 01 là thuế nội địa. 03 là phí hạ tầng cảng biển. 04 là thuế hải quan")
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH) String> txnTypes;

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TXN_ITEM_ID_LENGTH)
    @Pattern(regexp = AppConstants.REGEX_VALIDATION.NUMBER_AND_LETTER_AND_SPEC_CHAR_NO_SPACE)
    @Schema(example = "9581856", description = "ID khoản nộp")
    private String txnItemId;

    @Schema(example = "", description = "Danh sách kênh giao dịch")
    private List<@Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_SEARCH_LENGTH) String> channels;
}
