package com.bidv.ibank.dvc.model.validation;

import com.bidv.ibank.dvc.util.constant.ResponseCode;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class FieldValidator {
    private final String fieldName;
    private final List<ValidationRule> rules;
    private final ResponseCode requiredErrorCode;
    private final int maxLength;
    private final ResponseCode maxLengthErrorCode;
    private final String pattern;
    private final ResponseCode patternErrorCode;
    private final boolean isRequired;

    public List<ResponseCode> validate(String value) {
        List<ResponseCode> errors = new ArrayList<>();

        if (isRequired && (value == null || value.trim().isEmpty())) {
            errors.add(requiredErrorCode);
            return errors;
        }

        if (value != null && !value.trim().isEmpty()) {
            if (maxLength > 0 && value.length() > maxLength) {
                errors.add(maxLengthErrorCode);
            }

            if (pattern != null && !value.matches(pattern)) {
                errors.add(patternErrorCode);
            }

            if (rules != null) {
                rules.forEach(rule -> rule.validate(value, errors));
            }
        }

        return errors;
    }
}