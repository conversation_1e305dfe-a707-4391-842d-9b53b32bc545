package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.StrUtils;
import com.bidv.ibank.util.excel.ExportConfig;
import com.bidv.ibank.util.excel.Exportable;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonPropertyOrder(alphabetic = true)
public class BatchTaxItemExportDto extends BatchItemDetailDto implements Exportable {

    @ExportConfig(colIndex = 0)
    private String batchOrder;

    @ExportConfig(colIndex = 1)
    private String taxCode;

    @ExportConfig(colIndex = 2)
    private String payerName;

    @ExportConfig(colIndex = 3)
    private String declarationNo;

    @ExportConfig(colIndex = 4)
    private String declarationDate;

    @ExportConfig(colIndex = 5)
    private String treasuryCodeName;

    @ExportConfig(colIndex = 6)
    private String revAccCode;

    @ExportConfig(colIndex = 7)
    private String revAuthCodeName;

    @ExportConfig(colIndex = 8)
    private String chapterCodeName;

    @ExportConfig(colIndex = 9)
    private String ecCodeName;

    @ExportConfig(colIndex = 10)
    private String amount;

    @ExportConfig(colIndex = 11)
    private String ccy;

    @ExportConfig(colIndex = 12)
    private String transDesc;

    @ExportConfig(colIndex = 13)
    private String taxTypeCodeName;

    @ExportConfig(colIndex = 14)
    private String ccCodeName;

    @ExportConfig(colIndex = 15)
    private String eiTypeCodeName;

    @Override
    public String getSeverity() {
        return SEVERITY_INFO;
    }

    public String getTreasuryCodeName() {
        return StrUtils.combineCodeName(super.getTreasuryCode(), super.getTreasuryName());
    }

    public String getRevAuthCodeName() {
        return StrUtils.combineCodeName(super.getRevAuthCode(), super.getRevAuthName());
    }

    public String getChapterCodeName() {
        return StrUtils.combineCodeName(super.getChapterCode(), super.getChapterName());
    }

    public String getEcCodeName() {
        return StrUtils.combineCodeName(super.getEcCode(), super.getEcName());
    }

    public String getTaxTypeCodeName() {
        return StrUtils.combineCodeName(super.getTaxTypeCode(), super.getTaxTypeName());
    }

    public String getCcCodeName() {
        return StrUtils.combineCodeName(super.getCcCode(), super.getCcName());
    }

    public String getEiTypeCodeName() {
        return StrUtils.combineCodeName(super.getEiTypeCode(), super.getEiTypeName());
    }
}
