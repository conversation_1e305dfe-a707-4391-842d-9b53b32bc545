package com.bidv.ibank.dvc.model.entity.param;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TCC_DM_KBNN_NHTM")
public class TccDmKbnnNhtmEntity {

    @Id
    @Column(name = "ID")
    private Long id;

    @Nullable
    @Size(max = 20)
    @Column(name = "SHKB", length = 20)
    private String shkb;

    @Nullable
    @Size(max = 200)
    @Column(name = "TEN_KB", length = 200)
    private String tenKb;

    @Nullable
    @Size(max = 50)
    @Column(name = "MA_NH", length = 50)
    private String maNh;

    @Nullable
    @Size(max = 50)
    @Column(name = "BANK_CODE", length = 50)
    private String bankCode;

    @Nullable
    @Size(max = 200)
    @Column(name = "TEN_NH", length = 200)
    private String tenNh;
}