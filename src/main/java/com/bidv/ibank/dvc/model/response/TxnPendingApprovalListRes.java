package com.bidv.ibank.dvc.model.response;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TxnPendingApprovalListRes extends TxnPendingListRes {

    @Schema(example = "Ghi chú tới người duyệt", description = "Ghi chú tới người duyệt")
    private String raNote;

    @Schema(example = "[\"88060ktv\", \"88060ktv\"]", description = "Danh sách người duyệt")
    private String approvalUsers;

    @Schema(example = "04", description = "Loại giao dịch: 01 là thuế nội địa. 03 là phí hạ tầng cảng biển. 04 là thuế hải quan")
    private String txnType;

    @Schema(example = "**********", description = "Id khoản khoản nộp (chỉ có với thuế nội địa)")
    private String txnItemId;

    @JsonFormat(pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS)
    @Schema(example = "2025-06-24T16:58:44.006902", description = "Ngày cập nhật")
    private LocalDateTime updatedDate;

    @Schema(example = "2021-01-01", description = "Ngày hiệu lực")
    private LocalDate effDate;

    public String getTxnTypeName() {
        return Translator.toLocale(AppConstants.LANGUAGE.TXN_TYPE + "." + txnType, txnType);
    }
}
