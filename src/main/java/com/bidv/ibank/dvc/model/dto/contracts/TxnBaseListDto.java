package com.bidv.ibank.dvc.model.dto.contracts;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.bidv.ibank.dvc.model.dto.TxnParamCodeDto;
import com.bidv.ibank.dvc.model.dto.TxnParamNameDto;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.framework.util.Translator;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class TxnBaseListDto implements TxnParamCodeDto, TxnParamNameDto {

    @Schema(example = "DVC01704202411252339", description = "Mã giao dịch")
    private String txnId;
    @Schema(example = "**********", description = "Tài khoản trích nợ")
    private String debitAccNo;
    @Schema(example = "**********", description = "Mã số thuế người nộp")
    private String taxCode;
    @Schema(example = "***********", description = "Số tờ khai")
    private String declarationNo;
    @Schema(example = "100000", description = "Số tiền")
    private BigDecimal amount;
    @Schema(example = "VND", description = "Loại tiền tệ của số tiền nhập")
    private String ccy;
    @Schema(example = "BDR20250607215402732", description = "Số tham chiếu lô")
    private String batchNo;
    @Schema(example = "INIT", description = "Trạng thái giao dịch")
    private String status;
    @Schema(example = "2021-01-01", description = "Ngày tạo")
    private LocalDateTime createdDate;
    @Schema(example = "12345678", description = "Mã khách hàng")
    private String orgId;

    private String treasuryCode;
    private String treasuryName;
    private String admAreaCode;
    private String admAreaName;
    private String revAccCode;
    private String revAccName;
    private String revAuthCode;
    private String revAuthName;

    @Override
    public String getTreasuryName() {
        return Translator.toLocale(treasuryCode, treasuryName);
    }

    @Override
    public String getAdmAreaName() {
        return Translator.toLocale(admAreaCode, admAreaName);
    }

    @Override
    public String getRevAccName() {
        return Translator.toLocale(revAccCode, revAccName);
    }

    @Override
    public String getRevAuthName() {
        return Translator.toLocale(revAuthCode, revAuthName);
    }

    public String getStatusName() {
        return Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + "." + status, status);
    }
}
