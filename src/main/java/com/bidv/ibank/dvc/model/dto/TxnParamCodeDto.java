package com.bidv.ibank.dvc.model.dto;

import com.bidv.ibank.dvc.util.AppConstants;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

public interface TxnParamCodeDto {

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_TREASURY_CODE_LENGTH)
    @Schema(example = "0022", description = "Mã kho bạc")
    String getTreasuryCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_ADM_AREA_CODE_LENGTH)
    @Schema(example = "009HH", description = "Mã địa bàn hành chính")
    String getAdmAreaCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_REV_ACC_CODE_LENGTH)
    @Schema(example = "1898", description = "Mã tài khoản thu")
    String getRevAccCode();

    @Size(max = AppConstants.BATCH_FIELD_LENGTH.MAX_REV_AUTH_CODE_LENGTH)
    @Schema(example = "1420", description = "Mã cơ quan thu")
    String getRevAuthCode();
}
