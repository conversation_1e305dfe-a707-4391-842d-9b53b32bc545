package com.bidv.ibank.dvc.model.response;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.bidv.ibank.dvc.model.dto.BatchGeneralInfoDto;
import com.bidv.ibank.dvc.model.dto.BatchItemDetailDto;
import com.bidv.ibank.framework.util.Translator;
import com.bidv.ibank.util.ReadNumber;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(alphabetic = true)
public class BatchCalcFeeRes implements BatchGeneralInfoDto {

    private Long fileSize;
    private String fileName;
    private String batchNo;
    private String batchType;

    @Schema(example = "1000000", description = "Tổng tiền")
    private BigDecimal totalAmount;

    @Schema(example = "1000000", description = "Tổng tiền phí dịch vụ (bao gồm VAT)")
    private BigDecimal totalFee;

    @Schema(example = "VND", description = "Mã tiền tệ")
    private String ccy;

    @Schema(example = "VND", description = "Mã tiền tệ phí")
    private String feeCcy;

    @Schema(example = "Phí khoán", description = "Hình thức thu phí")
    private String feeOpt;

    @Builder.Default
    @Schema(description = "Danh sách item")
    private List<BatchItemDetailDto> items = new ArrayList<>();

    public Integer getTotalItems() {
        if (items == null) {
            return 0;
        }
        return items.size();
    }

    public String getTotalAmountText() {
        return this.getTotalAmount() == null ? null
                : ReadNumber.formatAmountToText(Translator.getLocale().toString().toLowerCase(), this.getTotalAmount().toString(), this.getCcy());
    }
}
