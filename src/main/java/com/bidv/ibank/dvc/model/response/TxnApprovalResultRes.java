package com.bidv.ibank.dvc.model.response;

import java.time.LocalDateTime;
import java.util.Objects;

import com.bidv.ibank.common.txn.util.constant.TransactionStatusEnum;
import com.bidv.ibank.dvc.util.AppConstants;
import com.bidv.ibank.dvc.util.constant.ResponseCode;
import com.bidv.ibank.dvc.util.constant.TccAccountingEnum;
import com.bidv.ibank.framework.util.Translator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TxnApprovalResultRes extends TxnProcessResultRes {

    @JsonFormat(pattern = AppConstants.DATE_FORMAT_YYYY_MM_DD_T_HH_MM_SS_SSSSSS)
    @Schema(example = "2025-06-24T16:58:44.006902", description = "Thời gian phê duyệt")
    private LocalDateTime approvedDate;

    @Schema(example = "true", description = "Có phải là giao dịch cuối cùng không")
    private Boolean isFinal;

    @JsonIgnore
    private String underBalanceFlag;

    @Schema(example = "Đang xử lý", description = "Tên trạng thái")
    public String getStatusName() {
        return Translator.toLocale(AppConstants.LANGUAGE.TXN_STATUS + "." + getStatus());
    }

    @Schema(example = "PROCESSING", description = "Trạng thái")
    public String getStatus() {
        if (super.getTotal() > 1 && super.getTotalSuccess() == 0) {
            return TransactionStatusEnum.FAILED.name();
        }
        if (super.getTotal() == 1 && super.getTotalFail() == 1) {
            String errCode = super.getFailTxns().get(0).getCode();
            if (Objects.equals(errCode, ResponseCode.UNDEFINED.code()) || Objects.equals(errCode, TccAccountingEnum.UNDEFINED.getValue()))
                return TransactionStatusEnum.UNDEFINED.name();
            else
                return TransactionStatusEnum.FAILED.name();
        }
        return super.getTotal() > 1 ? TransactionStatusEnum.SUCCESS.name()
                : (AppConstants.UNDER_BALANCE_FLAG.NO.equals(underBalanceFlag) ? TransactionStatusEnum.SUCCESS.name()
                        : TransactionStatusEnum.BANK_PROCESSING.name());
    }
}
