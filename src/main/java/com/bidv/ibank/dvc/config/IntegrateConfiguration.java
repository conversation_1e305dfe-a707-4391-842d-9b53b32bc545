package com.bidv.ibank.dvc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.bidv.ibank.integrate.config.IntegrateProperties;
import com.bidv.ibank.integrate.services.IntegrateServiceFactory;

@Configuration
//@ConditionalOnProperty(prefix = "bidv.integrate", value = "enable", havingValue = "true", matchIfMissing = false)
public class IntegrateConfiguration {

	@Bean
	@ConfigurationProperties(prefix = "bidv.integrate")
	IntegrateProperties createProperties() {
		return new IntegrateProperties();
	}
	
	@Bean
	IntegrateServiceFactory createIntegrateServiceFactory() {
		return IntegrateServiceFactory.builder(createProperties()).build();
	}
}

