package com.bidv.ibank.dvc.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.bidv.ibank.framework.config.datasource.DataSourceConfiguration;

import jakarta.persistence.EntityManagerFactory;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = PrimaryDataSourceConfig.ENTITY_MANAGER_FACTORY_NAME, transactionManagerRef = PrimaryDataSourceConfig.TRANSACTION_MANAGER_NAME, basePackages = {
        "com.bidv.ibank" })
public class PrimaryDataSourceConfig implements DataSourceConfiguration {

    public static final String DATASOURCE_PROPERTIES_PREFIX = "spring.datasource";
    public static final String DATASOURCE_PROPERTIES_HIKARI_PREFIX = DATASOURCE_PROPERTIES_PREFIX + ".hikari";
    public static final String DATASOURCE_PROPERTIES_NAME = "primaryProperties";
    public static final String DATASOURCE_NAME = "primaryDataSource";
    public static final String ENTITY_MANAGER_FACTORY_NAME = "primaryEntityManagerFactory";
    public static final String TRANSACTION_MANAGER_NAME = "primaryTransactionManager";
    protected static final String[] ENTITY_PACKAGE = { "com.bidv" };

    @Primary
    @Bean(name = PrimaryDataSourceConfig.DATASOURCE_PROPERTIES_NAME)
    @ConfigurationProperties(prefix = PrimaryDataSourceConfig.DATASOURCE_PROPERTIES_PREFIX)
    @Override
    public DataSourceProperties dataSourceProperties() {
        return new DataSourceProperties();
    }

    @Primary
    @Bean(name = PrimaryDataSourceConfig.DATASOURCE_NAME)
    @ConfigurationProperties(prefix = PrimaryDataSourceConfig.DATASOURCE_PROPERTIES_HIKARI_PREFIX)
    @Override
    public DataSource dataSource(
            @Qualifier(PrimaryDataSourceConfig.DATASOURCE_PROPERTIES_NAME) DataSourceProperties properties) {
        return properties.initializeDataSourceBuilder().build();
    }

    @Primary
    @Bean(name = PrimaryDataSourceConfig.ENTITY_MANAGER_FACTORY_NAME)
    @Override
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(EntityManagerFactoryBuilder builder,
            @Qualifier(PrimaryDataSourceConfig.DATASOURCE_NAME) DataSource dataSource) {
        return builder.dataSource(dataSource)
                .packages(PrimaryDataSourceConfig.ENTITY_PACKAGE)
                .persistenceUnit(DATASOURCE_NAME)
                .build();
    }

    @Primary
    @Bean(name = PrimaryDataSourceConfig.TRANSACTION_MANAGER_NAME)
    @ConfigurationProperties("spring.jpa")
    public PlatformTransactionManager transactionManager(
            @Qualifier(PrimaryDataSourceConfig.ENTITY_MANAGER_FACTORY_NAME) EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
